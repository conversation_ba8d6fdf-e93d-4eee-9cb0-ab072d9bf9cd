<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.jettech.jettomanager</groupId>
	<artifactId>jettomanager-defect</artifactId>
	<name>jettomanager-defect</name>
	<version>v7.6.3</version>
	<packaging>jar</packaging>
	<url>http://www.jettech.com/jettomanager</url>
	<organization>
		<name> Jettech Software, Inc.</name>
		<url>http://www.jettech.com</url>
	</organization>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.5.15</version>
	</parent>

	<properties>
		<tomcat.version>9.0.86</tomcat.version>
		<mybatis-plus-boot-starter.version>3.0.7.1</mybatis-plus-boot-starter.version>
		<druid-spring-boot-starter.version>1.1.24</druid-spring-boot-starter.version>
		<persistence-api.version>1.0.2</persistence-api.version>

		<json.version>1.9.13</json.version>
		<kisso.version>3.6</kisso.version>
		<!-- <log4j.version>1.2.16</log4j.version> -->
		<org.slf4j-version>1.6.1</org.slf4j-version>
		<kisso.version>3.6</kisso.version>
		<powermock.version>2.0.4</powermock.version>
		<spring-cloud.version>Hoxton.SR11</spring-cloud.version>
		<mysql-connector-java.version>8.0.27</mysql-connector-java.version>
		<jettomanager-common.version>v7.0.0</jettomanager-common.version>
		<log4j.version>2.16.0</log4j.version>
		<snakeyaml.version>2.0</snakeyaml.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>5.3.34</version>
		</dependency>
		<!-- openGauss -->
		<dependency>
			<groupId>org.opengauss</groupId>
			<artifactId>postgresql-opengauss-jdbc</artifactId>
			<version>3.1</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/jarLib/postgresql-opengauss.jar</systemPath>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.opengauss/opengauss-jdbc -->
		<dependency>
			<groupId>org.opengauss</groupId>
			<artifactId>opengauss-jdbc</artifactId>
			<version>3.1.0</version>
		</dependency>
		<!--<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>3.1.0</version>
		</dependency>-->
		<!--<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>${log4j.version}</version>
		</dependency>-->
		<dependency>
			<groupId>com.jettech.jettomanager</groupId>
			<artifactId>jettomanager-common</artifactId>
			<version>${jettomanager-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-jetty</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-undertow</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 移除logback依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<!--排除这个slf4j-log4j12 -->
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- <dependency> -->
		<!-- <groupId>com.jettech.infrastructure</groupId> -->
		<!-- <artifactId>jettech-encry-license</artifactId> -->
		<!-- <version>0.0.1-SNAPSHOT</version> -->
		<!-- <scope>system</scope> -->
		<!-- <systemPath>${project.basedir}/libs/jettech-encry-license-0.0.1-SNAPSHOT.jar</systemPath> -->
		<!-- </dependency> -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.session</groupId>-->
<!--			<artifactId>spring-session-data-redis</artifactId>-->
<!--		</dependency>-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql-connector-java.version}</version>
		</dependency>
		<!-- 开发调试kingwow数据库时，需要把上面的mysql8注释掉 -->
		<!--  obase -->
		<dependency>
			<groupId>cn.com.obase</groupId>
			<artifactId>obase</artifactId>
			<version>2.3.15</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/jarLib/obase-2.3.15.jar</systemPath>
		</dependency>

		<!-- kingwow -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java-kingwow</artifactId>
			<version>5.1.45.6-SNAPSHOT-bin</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/jarLib/mysql-connector-java-5.1.45.6-SNAPSHOT-bin.jar</systemPath>
		</dependency>
		<!--
		<dependency>
			<groupId>com.jettech</groupId>
			<artifactId>driver-spring-boot-starter</artifactId>
			<version>1.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/jarLib/driver-spring-boot-starter-1.0.jar</systemPath>
		</dependency>-->
		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
			<version>8.1.2.79</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/cn.com.kingbase/kingbase8 -->
		<dependency>
			<groupId>cn.com.kingbase</groupId>
			<artifactId>kingbase8</artifactId>
			<version>8.6.0</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/javax.servlet/jstl -->
		<!-- 新增所需jar -->
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-core-asl</artifactId>
			<version>${json.version}</version>
		</dependency>
		<dependency>
			<groupId>javax.persistence</groupId>
			<artifactId>persistence-api</artifactId>
			<version>${persistence-api.version}</version>
		</dependency>
		<!--<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-annotation</artifactId>
			<version>${mybatis-plus-boot-starter.version}</version>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>${mybatis-plus-boot-starter.version}</version>
		</dependency>-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>kisso</artifactId>
			<version>${kisso.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
		</dependency>-->
		<!-- 新增所需jar结束 -->
		<!-- https://mvnrepository.com/artifact/com.alibaba/easyexcel -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>4.0.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20180813</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.corundumstudio.socketio</groupId>
			<artifactId>netty-socketio</artifactId>
			<version>1.7.11</version>
		</dependency>
		<dependency>
		    <groupId>org.jsoup</groupId>
		    <artifactId>jsoup</artifactId>
		    <version>1.11.3</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>hamcrest-core</artifactId>
					<groupId>org.hamcrest</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>2.0.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.25.0-GA</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.3</version>
		</dependency>
		<dependency>
			<groupId>ant</groupId>
			<artifactId>ant</artifactId>
			<version>1.6.5</version>
		</dependency>
		<dependency>
		    <groupId>org.jsoup</groupId>
		    <artifactId>jsoup</artifactId>
		    <version>1.11.3</version>
		</dependency>
		<!--seata-->
		<!-- 分布式事务支持 -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-seata</artifactId>
			<version>2021.1</version>
			<exclusions>
				<exclusion>
					<groupId>io.seata</groupId>
					<artifactId>seata-spring-boot-starter</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.seata</groupId>
			<artifactId>seata-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>druid</artifactId>
				</exclusion>
			</exclusions>
			<version>1.5.2</version>
		</dependency>
		<!--swagger2-->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2020.0.6</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid-spring-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson</groupId>
				<artifactId>jackson-bom</artifactId>
				<version>2.16.1</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>1.9.4</version>
			</dependency>
			<dependency>
				<groupId>org.codehaus.jettison</groupId>
				<artifactId>jettison</artifactId>
				<version>1.5.4</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<finalName>${project.artifactId}-${project.version}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<!-- <version>1.4.2.RELEASE</version> -->
				<configuration>
					<!-- 启动类的路径 -->
					<mainClass>com.jettech.jettomanager.defect.JettomanagerDefectApplication</mainClass>
					<layout>ZIP</layout>
					<includes>
						<include>
							<groupId>nothing</groupId>
							<artifactId>nothing</artifactId>
						</include>
					</includes>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>                <!--指定的依赖路径-->
							<outputDirectory>
								${project.build.directory}/lib
							</outputDirectory>
							<excludeArtifactIds>spring-boot-devtools</excludeArtifactIds>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
