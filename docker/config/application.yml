#服务器配置
server:
  servlet:
    context-path: /
  # Jetty的配置
  jetty:
    max-http-post-size: 81920
  max-http-header-size: 81920
spring:
  profiles:
    active: local
  application:
    name: jettomanager-assets
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 230MB

# mybatis分页插件
pagehelper:
  helper-dialect: mysql
  # 是否启用分页合理化。如果启用，当pagenum<1时，会自动查询第一页的数据，当pagenum>pages时，自动查询最后一页数据；不启用的，以上两种情况都会返回空数据
  reasonable: true
  # 分页插件会从查询方法的参数值中，自动根据上面 params 配置的字段中取值，查找到合适的值时就会自动分页。
  support-methods-arguments: true
  
# mybatis-plus
mybatis-plus:
  type-aliases-package: com.jettech.model
  mapper-locations: 
  - classpath*:mappers/*.xml
  global-config:
    db-config:
      # 数据类型
      db-type: mysql
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: auto
      
admin: 
  password: JettechAdmin
    
logging:
  config: classpath:log4j2.yml
  
vue:
  projectName: JettoManager1.0
    

#服务器注册发现配置
eureka:
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}:@project.version@
  client:    
    # 将自己注册到自己Eureka Server
    register-with-eureka: true
    # 从Eureka Server 获取注册信息
    fetch-registry: true
project:
  version: v7.5.3

swagger:
  enable: true