apiVersion: v1
kind: Service
metadata:
  labels: {name: jettomanager-assets}
  name: jettomanager-assets
  namespace: jettopro-app-poc
spec:
  ports:
  #- {name: t8600, nodePort: 8600, port: 80, protocol: TCP, targetPort: t80}
  - {name: t80, port: 80, protocol: TCP, targetPort: t80}
  selector: {name: jettomanager-assets}
  #type: NodePort
  type: ClusterIP
  #clusterIP: None

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: jettopro-app-poc
  name: jettomanager-assets
  labels: {name: jettomanager-assets}
spec:
  replicas: 1
  selector:
    matchLabels: {name: jettomanager-assets}
  template:
    metadata:
      name: jettomanager-assets
      labels: {name: jettomanager-assets}
    spec:
      containers:
      - name: jettomanager-assets
        image: harbor.jettech.com/jettomanager/jettomanager-assets:v7.5.3.NYYH
        envFrom:
        - configMapRef:
            name: jettopro-app-configmap
            optional: true
        imagePullPolicy: Always #[Always | Never | IfNotPresent]
        securityContext:
          privileged: true
        ports:
        - {containerPort: 80, name: t80, protocol: TCP}
      #hostNetwork: true
      restartPolicy: Always #Never
