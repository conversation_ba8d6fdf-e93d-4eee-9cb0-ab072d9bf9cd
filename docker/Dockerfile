FROM harbor.jettech.com/jettechtools/sles12sp5-jdk:1.8-151
#FROM harbor.jettech.com/jettechtools/alpine-jre-8:3.10
#FROM harbor.jettech.com/jettechtools/centos-jre-8:7.9.2009
MAINTAINER  <EMAIL>
WORKDIR /opt

COPY config /opt/config
COPY lib /opt/lib
COPY *.jar /opt
COPY docker-entrypoint.sh /opt

VOLUME /opt/logs

ENV SERVER_PORT=80 \
MYSQL_HOST=127.0.0.1 \
MYSQL_PORT=3306 \
MYSQL_DATASOURCE=jettopro \
MYSQL_USERNAME=root \
MYSQL_PASSWORD=123456aA \
REDIS_HOST=127.0.0.1 \
REDIS_PORT=6379 \
REDIS_PASSWORD=123456aA \
EUREKA_HOST=127.0.0.1 \
EUREKA_PORT=80 \
BASIC_HOST=127.0.0.1 \
BASIC_PORT=80 \
ZUUL_HOST=127.0.0.1 \
ZUUL_PORT=80 \
IS_FTP_ON=true \
FTP_HOST=127.0.0.1 \
FTP_PORT=21 \
FTP_USERNAME=jettopro \
FTP_PASSWD=123456aA \
FTP_ATTACH_PATH= \
JAVA_OPT="-Xms256m -Xmx512m"
EXPOSE ${SERVER_PORT}

ENTRYPOINT sh /opt/docker-entrypoint.sh && cd /opt && java -Dloader.path=lib -Dfile.encoding=UTF-8 ${JAVA_OPT}  -jar /opt/*.jar
