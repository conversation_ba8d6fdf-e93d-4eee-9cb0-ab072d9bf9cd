apiVersion: v1
kind: Service
metadata:
  labels: {name: jettomanager-datadesign}
  name: jettomanager-datadesign
  namespace: jettomanager-poc
spec:
  ports:
  #- {name: t8600, nodePort: 8600, port: 80, protocol: TCP, targetPort: t80}
  - {name: t80, port: 80, protocol: TCP, targetPort: t80}
  selector: {name: jettomanager-datadesign}
  #type: NodePort
  type: ClusterIP
  #ClusterIP: None

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: jettomanager-poc
  name: jettomanager-datadesign
  labels: {name: jettomanager-datadesign}
spec:
  replicas: 1
  selector:
    matchLabels: {name: jettomanager-datadesign}
  template:
    metadata:
      name: jettomanager-datadesign
      labels: {name: jettomanager-datadesign}
    spec:
      containers:
      - name: jettomanager-datadesign
        image: harbor.jettech.com/jettomanager/jettomanager-datadesign:v7.3.0
        env:
        - {name: SERVER_PORT, value: '80'}
        - {name: MYSQL_HOST, value: 'jettopro-mysql.jettopro-poc.svc.jettech.com.cn'}
        - {name: MYSQL_PORT, value: '3306'}
        - {name: MYSQL_DATASOURCE, value: 'jettomanagerdev'}
        - {name: MYSQL_USERNAME, value: 'root'}
        - {name: MYSQL_PASSWORD, value: '123456aA'}
        - {name: REDIS_HOST, value: 'jettopro-redis.jettopro-poc.svc.jettech.com.cn'}
        - {name: REDIS_PORT, value: '6379'}
        - {name: REDIS_PASSWORD, value: '123456aA'}
        - {name: EUREKA_HOST, value: 'jettomanager-eureka.jettomanager-poc.svc.jettech.com.cn'}
        - {name: EUREKA_PORT, value: '80'}
        - {name: BASIC_HOST, value: 'jettomanager-basic.jettomanager-poc.svc.jettech.com.cn'}
        - {name: BASIC_PORT, value: '80'}
        - {name: IS_FTP_ON, value: 'true'}
        - {name: FTP_HOST, value: 'jettopro-ftp.jettopro-poc.svc.jettech.com.cn'}
        - {name: FTP_PORT, value: '21'}
        - {name: FTP_USERNAME, value: 'jettomanager'}
        - {name: FTP_PASSWD, value: '123456aA'}
        - {name: FTP_ATTACH_PATH, value: ''}
        - {name: ZUUL_HOST, value: 'jettomanager-zuul.jettomanager-poc.svc.jettech.com.cn'}
        - {name: ZUUL_PORT, value: '80'}
        - {name: JAVA_OPT, value: '-Xms256m -Xmx512m'}
        imagePullPolicy: Always #[Always | Never | IfNotPresent]
        securityContext:
          privileged: true
        ports:
        - {containerPort: 80, name: t80, protocol: TCP}
      #hostNetwork: true
      restartPolicy: Always #Never
