apiVersion: v1
kind: Service
metadata:
  labels: {name: jettomanager-datadesign}
  name: jettomanager-datadesign
  namespace: jettomanager-dev
spec:
  ports:
  #- {name: t8600, nodePort: 8600, port: 80, protocol: TCP, targetPort: t80}
  - {name: t80, port: 80, protocol: TCP, targetPort: t80}
  selector: {name: jettomanager-datadesign}
  #type: NodePort
  type: ClusterIP
  #ClusterIP: None

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: jettomanager-dev
  name: jettomanager-datadesign
  labels: {name: jettomanager-datadesign}
spec:
  replicas: 1
  selector:
    matchLabels: {name: jettomanager-datadesign}
  template:
    metadata:
      name: jettomanager-datadesign
      labels: {name: jettomanager-datadesign}
    spec:
      containers:
      - name: jettomanager-datadesign
        image: harbor.jettech.com/jettomanager/jettomanager-datadesign:v7.2.0
        env:
        - {name: ENV_NAME, value: 'dev'}
        imagePullPolicy: Always #[Always | Never | IfNotPresent]
        securityContext:
          privileged: true
        ports:
        - {containerPort: 80, name: t80, protocol: TCP}
      #hostNetwork: true
      restartPolicy: Always #Never
