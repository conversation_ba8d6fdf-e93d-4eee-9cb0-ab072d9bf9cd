apiVersion: v1
kind: Service
metadata:
  labels: {name: jettomanager-assets}
  name: jettomanager-assets
  namespace: jettomanager-dev
spec:
  ports:
  #- {name: t8600, nodePort: 8600, port: 80, protocol: TCP, targetPort: t80}
  - {name: t80, port: 80, protocol: TCP, targetPort: t80}
  selector: {name: jettomanager-assets}
  #type: NodePort
  type: ClusterIP
  #clusterIP: None

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: jettomanager-dev
  name: jettomanager-assets
  labels: {name: jettomanager-assets}
spec:
  replicas: 1
  selector:
    matchLabels: {name: jettomanager-assets}
  template:
    metadata:
      name: jettomanager-assets
      labels: {name: jettomanager-assets}
    spec:
      containers:
      - name: jettomanager-assets
        image: harbor.jettech.com/jettomanager/jettomanager-assets:v7.2.0
        env:
        - {name: ENV_NAME, value: 'dev'}
        imagePullPolicy: Always #[Always | Never | IfNotPresent]
        securityContext:
          privileged: true
        ports:
        - {containerPort: 80, name: t80, protocol: TCP}
      #hostNetwork: true
      restartPolicy: Always #Never
