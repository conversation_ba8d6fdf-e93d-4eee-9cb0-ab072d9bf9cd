FROM harbor.jettech.com/jettechtools/alpine-jre-8:3.10
#FROM harbor.jettech.com/jettechtools/centos-jre-8:7.9.2009
MAINTAINER  <EMAIL>
WORKDIR /jettech/work/

ARG app="*.jar" \
    lib="lib" \
    jvm_args="-Xms256m -Xmx256m" \
    spring_boot_start_args="--spring.profiles.active" \
    env_name="dev"
COPY ${app} .
COPY ${lib} lib
ENV APP=${app} \
    LIB=${lib} \
    SPRING_BOOT_START_ARGS=${spring_boot_start_args} \
    ENV_NAME=${env_name} \
    JVM_ARGS=${jvm_args}

EXPOSE 80
CMD [ "sh","-c","java  -Dloader.path=$LIB -jar $APP -server -Dfile.encoding=UTF-8 $JVM_ARGS $SPRING_BOOT_START_ARGS=${ENV_NAME}"]
