apiVersion: v1
kind: Service
metadata:
  labels: {name: jettomanager-assets}
  name: jettomanager-assets
  namespace: jettomanager-poc
spec:
  ports:
  #- {name: t8600, nodePort: 8600, port: 80, protocol: TCP, targetPort: t80}
  - {name: t80, port: 80, protocol: TCP, targetPort: t80}
  selector: {name: jettomanager-assets}
  #type: NodePort
  type: ClusterIP
  #clusterIP: None

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: jettomanager-poc
  name: jettomanager-assets
  labels: {name: jettomanager-assets}
spec:
  replicas: 1
  selector:
    matchLabels: {name: jettomanager-assets}
  template:
    metadata:
      name: jettomanager-assets
      labels: {name: jettomanager-assets}
    spec:
      containers:
      - name: jettomanager-assets
        image: harbor.jettech.com/jettomanager/jettomanager-assets:v7.3.0
        env:
        - {name: SERVER_PORT, value: '80'}
        - {name: MYSQL_HOST, value: '***********'}
        - {name: MYSQL_PORT, value: '3306'}
        - {name: MYSQL_DATASOURCE, value: 'jettomanagerdev'}
        - {name: MYSQL_USERNAME, value: 'root'}
        - {name: MYSQL_PASSWORD, value: '123456aA'}
        - {name: REDIS_HOST, value: '***********'}
        - {name: REDIS_PORT, value: '6379'}
        - {name: REDIS_PASSWORD, value: '123456aA'}
        - {name: EUREKA_HOST, value: '***********'}
        - {name: EUREKA_PORT, value: '8600'}
        - {name: BASIC_HOST, value: '***********'}
        - {name: BASIC_PORT, value: '8603'}
        - {name: IS_FTP_ON, value: 'true'}
        - {name: FTP_HOST, value: '***********'}
        - {name: FTP_PORT, value: '21'}
        - {name: FTP_USERNAME, value: 'jettomanager'}
        - {name: FTP_PASSWD, value: '123456aA'}
        - {name: FTP_ATTACH_PATH, value: ''}
        - {name: ZUUL_HOST, value: '***********'}
        - {name: ZUUL_PORT, value: '8601'}
        - {name: JAVA_OPT, value: '-Xms256m -Xmx512m'}
        imagePullPolicy: Always #[Always | Never | IfNotPresent]
        securityContext:
          privileged: true
        ports:
        - {containerPort: 80, name: t80, protocol: TCP}
      #hostNetwork: true
      restartPolicy: Always #Never
