#!/bin/bash
containerName=$1
imageName=$2
imageTag=$3
containerExist=`docker inspect --format '{{.State.Running}}' ${containerName}`
if [ "${containerExist}" == "true" ];then
    echo "停止docker容器 ${containerName}"
    docker stop ${containerName}
    echo "删除docker容器 ${containerName}"
    docker rm ${containerName}
elif [ "${containerExist}" == "false" ];then
    echo "删除docker容器 ${containerName}"
    docker rm ${containerName}
fi

# 判断镜像是否存在
if [[ "$(docker images -q ${imageName}:${imageTag} 2> /dev/null)" != "" ]];then
    echo "删除docker镜像 ${imageName}:${imageTag}"
    echo "docker rmi ${imageName}:${imageTag}"
fi