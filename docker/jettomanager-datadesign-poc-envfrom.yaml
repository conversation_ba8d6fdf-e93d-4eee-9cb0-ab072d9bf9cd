apiVersion: v1
kind: Service
metadata:
  labels: {name: jettomanager-datadesign}
  name: jettomanager-datadesign
  namespace: jettopro-app-poc
spec:
  ports:
  #- {name: t8600, nodePort: 8600, port: 80, protocol: TCP, targetPort: t80}
  - {name: t80, port: 80, protocol: TCP, targetPort: t80}
  selector: {name: jettomanager-datadesign}
  #type: NodePort
  type: ClusterIP
  #ClusterIP: None

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: jettopro-app-poc
  name: jettomanager-datadesign
  labels: {name: jettomanager-datadesign}
spec:
  replicas: 1
  selector:
    matchLabels: {name: jettomanager-datadesign}
  template:
    metadata:
      name: jettomanager-datadesign
      labels: {name: jettomanager-datadesign}
    spec:
      containers:
      - name: jettomanager-datadesign
        image: harbor.jettech.com/jettomanager/jettomanager-datadesign:v7.5.3.NYYH
        envFrom:
        - configMapRef:
            name: jettopro-app-configmap
            optional: true
        imagePullPolicy: Always #[Always | Never | IfNotPresent]
        securityContext:
          privileged: true
        ports:
        - {containerPort: 80, name: t80, protocol: TCP}
      #hostNetwork: true
      restartPolicy: Always #Never
