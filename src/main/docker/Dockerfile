FROM  harbor.jettech.com/jettechtools/alpine-jre-8:latest
MAINTAINER  <EMAIL>
WORKDIR /opt
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
# ENV DIR_WEBAPP  /opt
COPY *.jar /opt/
ENV ENV_NAME=dev
ENV LANG C.GBK
VOLUME /opt/config
VOLUME /opt/plugin
VOLUME /opt/logs
VOLUME /opt/files
EXPOSE 80
CMD [ "sh","-c","java -jar /opt/*.jar -Dfile.encoding=UTF-8 -Xms256m -Xmx512m --spring.profiles.active=${ENV_NAME}"]
