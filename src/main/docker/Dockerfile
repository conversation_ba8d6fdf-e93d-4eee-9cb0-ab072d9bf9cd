FROM  harbor.jettech.com/jettechtools/centos-jdk-8:latest
MAINTAINER  <EMAIL>
ENV LANG=zh_CN.UTF-8
RUN echo “Asia/shanghai” > /etc/timezone;
ENV LANG zh_CN.utf8
WORKDIR /opt
ENV JAVA_HOME /opt/jdk1.8.0_231
ENV JRE_HOME /opt/jdk1.8.0_231/java/jre
ENV PATH $JAVA_HOME/bin:$JRE_HOME/bin:$PATH
ENV CLASSPATH .:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar:$JRE_HOME
# ENV DIR_WEBAPP  /opt
COPY *.jar /opt/
ENV ENV_NAME=dev
VOLUME /opt/config
VOLUME /opt/plugin
VOLUME /opt/logs
VOLUME /opt/files
EXPOSE 80
CMD [ "sh","-c","java -jar /opt/*.jar -Dfile.encoding=UTF-8 -Xms256m -Xmx512m --spring.profiles.active=${ENV_NAME}"]
