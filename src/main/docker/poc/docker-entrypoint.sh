#!/bin/bash
sed -i "s#SERVER_PORT#$SERVER_PORT#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#REDIS_HOST#$REDIS_HOST#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#REDIS_PORT#$REDIS_PORT#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#REDIS_PASSWORD#$REDIS_PASSWORD#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#EUREKA_HOST#$EUREKA_HOST#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#EUREKA_PORT#$EUREKA_PORT#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#MYSQL_HOST#$MYSQL_HOST#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#MYSQL_PORT#$MYSQL_PORT#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#MYSQL_DATASOURCE#$MYSQL_DATASOURCE#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#MYSQL_USERNAME#$MYSQL_USERNAME#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#MYSQL_PASSWORD#$MYSQL_PASSWORD#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#FTP_ENABLED#$FTP_ENABLED#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#FTP_HOST#$FTP_HOST#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#FTP_PORT#$FTP_PORT#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#FTP_USERNAME#$FTP_USERNAME#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#FTP_PASSWORD#$FTP_PASSWORD#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#FTP_ATTACHMENTPATH#$FTP_ATTACHMENTPATH#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#FTP_PATH#$FTP_PATH#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#BASIC_HOST#$BASIC_HOST#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#BASIC_PORT#$BASIC_PORT#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#SEATA_ENABLED#$SEATA_ENABLED#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#SEATA_TX_SERVICE_GROUP#$SEATA_TX_SERVICE_GROUP#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#SEATA_SERVER_NAME#$SEATA_SERVER_NAME#g"  /opt/BOOT-INF/classes/config/application-local.yml
sed -i "s#COLLECTION_USER_ACTION#$COLLECTION_USER_ACTION#g"  /opt/BOOT-INF/classes/config/application-local.yml