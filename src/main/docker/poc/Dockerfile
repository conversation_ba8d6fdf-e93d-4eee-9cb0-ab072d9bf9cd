FROM harbor.jettech.com/jettechtools/centos-jdk-8:latest
MAINTAINER  <EMAIL>
WORKDIR /opt
ENV SERVER_PORT=80 \
MYSQL_HOST=127.0.0.1 \
MYSQL_PORT=3306 \
MYSQL_DATASOURCE=JETTOMANAGERDAT \
MYSQL_USERNAME=root \
MYSQL_PASSWORD=Devops@123 \
REDIS_HOST=127.0.0.1 \
REDIS_PORT=6379 \
REDIS_PASSWORD=trp123 \
FTP_ENABLED=true \
FTP_HOST=127.0.0.1 \
FTP_PORT=21 \
FTP_USERNAME=jettomanager \
FTP_PASSWORD=jettomanager \
FTP_ATTACHMENTPATH=D:/ftpfile \
FTP_PATH=dat/basic \
EUREKA_HOST=127.0.0.1 \
EUREKA_PORT=9000 \
BASIC_HOST=127.0.0.1 \
BASIC_PORT=8091 \
SEATA_ENABLED=false \
SEATA_TX_SERVICE_GROUP=hsw_tx_group \
SEATA_SERVER_NAME=seata-server \
COLLECTION_USER_ACTION=true \
JAVA_HOME=/opt/jdk1.8.0_231 \
JRE_HOME=/opt/jdk1.8.0_231/java/jre \
CLASSPATH=.:/opt/jdk1.8.0_231/lib/dt.jar:/opt/jdk1.8.0_231/lib/tools.jar:/opt/jdk1.8.0_231/java/jre

COPY BOOT-INF /opt/BOOT-INF/
COPY *.jar /opt/
COPY docker-entrypoint.sh /opt/
VOLUME /opt/config
VOLUME /opt/plugin
VOLUME /opt/logs
VOLUME /opt/files

EXPOSE ${SERVER_PORT}
ENTRYPOINT sh /opt/docker-entrypoint.sh && cd /opt && jar -u0f *.jar BOOT-INF/classes/config/application.yml BOOT-INF/classes/config/application-local.yml && java -Dfile.encoding=UTF-8 -Xms512m -Xmx512m  -jar /opt/*.jar
