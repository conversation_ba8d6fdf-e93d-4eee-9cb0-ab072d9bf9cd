#服务器配置
server:
  #端口
  port: 8084
  # Tomcat的配置
  tomcat:
    # tomcat并发线程数量
    max-threads: 8
  # undertow的配置
  undertow:
    max-http-post-size: 0
    io-threads: 4
    work-threads: 8
    buffer-size: 1024
    #buffer-per-region: 1024
    direct-buffers: true
  # Jetty的配置
  jetty:
    acceptors: 1
    max-http-post-size: 0
    selectors: 2
spring:
  # 数据源配置
  datasource:
    username: root
    password: ${MYSQL_PASSWORD:root}
    driver-class-name: com.mysql.jdbc.Driver
    url: **********************************************************************************************************************************************************************

    #open gause
#    username: jettech
#    password: ${MYSQL_PASSWORD:Jettech123}
#    driver-class-name: org.postgresql.Driver
#    url: ********************************************
#    username: jettech
#    password: Jettech123
#    driver-class-name: org.postgresql.Driver
#    url: ********************************************

    druid:
      # 初始化大小，最小，最大
      initial-size: 5
      min-idle: 5
      max-active: 50
      # 获取连接等待超时的时间
      max-wait: 60000
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000"
      # 连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效.建议配置为true，不影响性能，并且保证安全性
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小。PSCache对支持游标的数据库性能提升巨大，mysql下建议关闭
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      # 通过别名的方式配置扩展插件，常用的插件有:监控统计用的filter:stat日志用的filter:log4j防御sql注入的filter:wall
      filters: slf4j
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/"
        # IP白名单
        allow:
        deny:
        # 禁用HTML页面上的“Reset All”功能
        reset-enable: false
        login-username: druid
        login-password: ${DRUID_PASSWORD:1234}
      filter:
        slf4j:
          enabled: true
          statement-create-after-log-enabled: false
          statement-close-after-log-enabled: false
          result-set-open-after-log-enabled: false
          result-set-close-after-log-enabled: false
  redis:
    database: 1
    host: ************
    port: 6379
    password: ${REDIS_PASSWORD:trp123}
    jedis:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

#上传文件配置
conf_param:
  # 基础资源平台部署地址
  basicResourcePlatform:  http://${spring.cloud.client.ip-address}:${server.port}/basic/
  server:
    name:  http://${spring.cloud.client.ip-address}:${server.port}
  isFtpOn: false
  ftpIpAddr: ************
  ftpPort: 21
  ftpUserName: jettomanager
  ftpPassword: ${FTP_PASSWORD:jettomanager}
  ftpAttachmentPath: mysql/project_attachment
  attachmentPath: D:/ftpfile
  ftpPath: dat/assets
  ftpProjectAttachmentPublishPath: mysql/project_release
  temporaryFolderName: temporaryfolder_jettech_project
  ftpTimout: 5000
  maxFileSize: 150MB
  maxRequestSize: 200MB
  snowFlakeWorkerId: 1
  snowFlakedataId : 2

eureka:
  environment: jettomanager-local
  instance:
    # 租期更新时间间隔（默认是30秒）
    leaseRenewalIntervalInSeconds: 5
    # 租期到期时间（默认是90秒）
    leaseExpirationDurationInSeconds: 90
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    #hostname: localhost
    # 使用IP地址去注册
    #prefer-ip-address: false
  client:
    serviceUrl:
      #配置服务中心(可配置多个,用逗号隔开)
      defaultZone: http://127.0.0.1:9000/jettomanager-eureka/eureka

trpAutomatedexecution:
  serverURl: http://127.0.0.1:18081/jettomanager-zuul/autotrp
trp:
  serverURl: http://127.0.0.1:18081/jettomanager-zuul/trp
basic:
  serverURl: http://127.0.0.1:8091/

# HttpClientPool的配置
httpClientPool:
  # 是否使用HttpClient连接池模式
  isUse: false
  # 长连接的保持时长（6分钟）
  timeToLive: 360000
  # 最大总计连接数量
  maxTotalConnecte: 128
  # 同一路由的最大连接数量
  perRouteMaxTotalConnecte: 2
  # 连接等待超时
  connectTimeout: 30000
  # socket发包的周期时长
  socketTimeout: 6000
okHttpPool:
  # 是否使用OkHttp连接池模式
  isUse: true
  # 长连接的保持时长（6分钟）
  timeToLive: 360000
  # 最大闲置连接数量
  maxIdleConnections: 4
  # 读取超时
  readTimeout: 60000
  # 写入超时
  writeTimeout: 60000
   # 连接等待超时
  connectTimeout: 30000
  # socket发包的周期时长
  socketTimeout: 6000
seaweed:
  # 是否启用 启用后知识库才能正常使用
  enabled: false
  ip: ************
  #  ip: ***************
  port: 9333
  timeout: 5000
# feign跨服务请求配置
feign:
  client:
    config: 
      default:
        connectTimeout: 30000
        readTimeout: 30000
  hystrix:
    # 在feign中开启hystrix，默认为false
    enabled: true
  httpclient:
    # 使用httpclient线程池
    enabled: ${httpClientPool.isUse}
    # 最大连接数量
    max-connections: ${httpClientPool.maxTotalConnecte}
    # 单个路径的最大连接数量
    max-connetions-per-route: ${httpClientPool.perRouteMaxTotalConnecte}
  okhttp:
    # 使用OkHttp连接池模式
    enabled: ${okHttpPool.isUse}
  # 请求和响应压缩提高通信的效率
  compression:
    request:
      # 是否压缩请求
      enabled: true
      # 压缩最小数据 2048=2M
      min-request-size: 2048
      mime-types: text/xml,application/xml,application/json
    response:
      # 是否压缩响应
      enabled: true

# 熔断配置
hystrix:
  threadpool:
    default:
      # 并发执行的最大线程数量
      coreSize: 16
  command:
    default:
      fallback:
        # 是否开启熔断实现
        enabled: true
      execution:
        isolation:
          thread:
            # 执行超时默认为10*1000
            timeoutInMilliseconds: 36000
# -----------seata--------------
seata:
  enabled: false
  application-id: ${spring.application.name} #服务名
  tx-service-group: hsw_tx_group # 自定义的事务分组名称
  enable-auto-data-source-proxy: true # 启用自动数据源代理
  use-jdk-proxy: true
  #    excludes-for-auto-proxying:
  #    client:
  #        rm:
  #            async-commit-buffer-limit: 1000
  #            report-retry-count: 5
  #            table-meta-check-enable: false
  #            report-success-enable: false
  #            saga-branch-register-enable: false
  #            lock:
  #                retry-interval: 10
  #                retry-times: 30
  #                retry-policy-branch-rollback-on-conflict: true
  #        tm:
  #            commit-retry-count: 5
  #            rollback-retry-count: 5
  #        undo:
  #            data-validation: true
  #            log-serialization: jackson
  #            log-table: undo_log
  #        log:
  #            exceptionRate: 100
  service:
    vgroup-mapping:
      hsw_tx_group: seata-server # 自定义的事务分组名称，value是tc注册到注册中心的服务名称
    #        grouplist:
    #            default: 127.0.0.1:8091 # 	仅注册中心为file时使用
    enable-degrade: false # 是否启用降级
    disable-global-transaction: false # 是否禁用全局事务
  #    transport:
  #        shutdown:
  #            wait: 3
  #        thread-factory:
  #            boss-thread-prefix: NettyBoss
  #            worker-thread-prefix: NettyServerNIOWorker
  #            server-executor-thread-prefix: NettyServerBizHandler
  #            share-boss-worker: false
  #            client-selector-thread-prefix: NettyClientSelector
  #            client-selector-thread-size: 1
  #            client-worker-thread-prefix: NettyClientWorkerThread
  #            worker-thread-size: default
  #            boss-thread-size: 1
  #        type: TCP
  #        server: NIO
  #        heartbeat: true
  #        serialization: seata
  #        compressor: none
  #        enable-client-batch-send-request: true
  config:
    type: file # 配置中心为file模式
  registry:
    type: eureka # 注册中心为eureka
    eureka:
      weight: 1
      service-url: ${eureka.client.serviceUrl.defaultZone} # 注册中心地址
# -----------seata--------------


#是否收集用户行为
collection-user-action: true

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

cluster:
  enable: false

