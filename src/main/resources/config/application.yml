#服务器配置
server:
  servlet:
    context-path: /
  # Jetty的配置
  jetty:
    max-http-post-size: 81920
  max-http-header-size: 81920
spring:
  profiles:
     active: local,self
  application:
    name: jettomanager-datadesign
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 230MB
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    druid:
      # 初始化大小，最小，最大
      initial-size: 5
      min-idle: 5
      max-active: 50
      # 获取连接等待超时的时间
      max-wait: 60000
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000"
      # 连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      validation-query: select 1 from dual
      # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效.建议配置为true，不影响性能，并且保证安全性
      dev-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      dev-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      dev-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小。PSCache对支持游标的数据库性能提升巨大，mysql下建议关闭
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
      # 通过别名的方式配置扩展插件，常用的插件有:监控统计用的filter:stat日志用的filter:log4j防御sql注入的filter:wall
      filters: stat,wall,slf4j
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/"
        # IP白名单
        allow:
        deny:
        # 禁用HTML页面上的“Reset All”功能
        reset-enable: false
        login-username: druid
        login-password: ${DRUID_PASSWORD:druid}
      filter:
        slf4j:
          enabled: true
          statement-create-after-log-enabled: false
          statement-close-after-log-enabled: false
          result-set-open-after-log-enabled: false
          result-set-close-after-log-enabled: false

# mybatis分页插件
pagehelper:
  helper-dialect: mysql
  # 是否启用分页合理化。如果启用，当pagenum<1时，会自动查询第一页的数据，当pagenum>pages时，自动查询最后一页数据；不启用的，以上两种情况都会返回空数据
  reasonable: true
  # 分页插件会从查询方法的参数值中，自动根据上面 params 配置的字段中取值，查找到合适的值时就会自动分页。
  support-methods-arguments: true

# mybatis-plus
mybatis-plus:
  type-aliases-package: com.jettech.model
  mapper-locations:
  - classpath*:mappers/*.xml
  global-config:
    db-config:
      # 数据类型
      db-type: mysql
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: auto

admin:
  password: ${ADMIN_PASSWORD:JettechAdmin}

logging:
  config: classpath:log4j2.xml

vue:
  projectName: JettoManager1.0


#----------------------超时配置-------------------
ribbon:
  ReadTimeout: 360000
  ConnectTimeout: 360000
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 2
  eureka:
    enabled: true

#服务器注册发现配置
eureka:
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}:@project.version@
  client:
    # 将自己注册到自己Eureka Server
    register-with-eureka: true
    # 从Eureka Server 获取注册信息
    fetch-registry: true
project:
  version: @project.version@

swagger:
  enable: true
