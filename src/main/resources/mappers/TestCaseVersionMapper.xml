<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestCaseVersionMapper">
    <resultMap type="com.jettech.model.TestCaseVersion" id="testCaseVersionMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="caseId" column="caseId"/>
        <result property="intent" column="intent"/>
        <result property="isNegative" column="isNegative"/>
        <result property="casetLevel" column="casetLevel"/>
        <result property="caseType" column="caseType"/>
        <result property="testStep" column="testStep"/>
        <result property="expectedResult" column="expectedResult"/>
        <result property="preconditions" column="preconditions"/>
        <result property="testsystem" column="testsystem"/>
        <result property="testsystemResourceID" column="testsystemResourceID"/>
        <result property="systemmodule" column="systemmodule"/>
        <result property="systemmoduleResourceID" column="systemmoduleResourceID"/>
        <result property="projectgroupResourceID" column="projectgroupResourceID"/>
        <result property="leadsource" column="leadsource"/>
        <result property="trade" column="trade"/>
        <result property="tradeResourceID" column="tradeResourceID"/>
        <result property="timingName" column="timingName"/>
        <result property="maintainer" column="maintainer"/>
        <result property="maintenanceTime" column="maintenanceTime"/>
        <result property="comment" column="comment"/>
        <result property="demandResourceID" column="demandResourceID"/>
        <result property="reviewStatus" column="reviewStatus"/>
        <result property="caseEditId" column="caseEditId"/>
        <result property="testEnviroment" column="testEnviroment"/>
        <result column="dataRequirements"  property="dataRequirements" />
        <result column="checkPoint"  property="checkPoint" />
        <result property="testtaskResourceID" column="testtaskResourceID"/>
        <result property="committed" column="committed"/>
        <result property="identitynumber" column="identitynumber"/>
        <result property="comments1" column="comments1"/>
        <result property="comments2" column="comments2"/>
        <result property="comments3" column="comments3"/>
        <result property="comments4" column="comments4"/>
        <result property="comments5" column="comments5"/>
        <result property="comments6" column="comments6"/>
        <result property="comments7" column="comments7"/>
        <result property="comments8" column="comments8"/>
        <result property="comments9" column="comments9"/>
        <result property="comments10" column="comments10"/>
        <result property="testMode" column="testMode"/>
        <result property="versionResourceID" column="versionResourceID"/>
        <result property="testCaseResourceID" column="testCaseResourceID"/>
    </resultMap>

    <sql id="testCaseVersionColumnList">
        id, createTime, createUser, editTime, editUser, resourceID, version,
        caseId, isNegative,casetLevel,caseType,testStep,expectedResult,
        preconditions,testsystem,testsystemResourceID,systemmodule,systemmoduleResourceID,projectgroupResourceID,leadsource,
        trade,tradeResourceID,timingName,maintainer,maintenanceTime,
        `intent`,`comment`,
        demandResourceID,reviewStatus,caseEditId,testEnviroment,dataRequirements,checkPoint,testtaskResourceID,identitynumber,comments1,comments2,
        comments3,comments4,comments5,comments6,comments7,comments8,comments9,comments10,testMode,versionResourceID,testCaseResourceID
    </sql>

    <select id="findByCondition" resultType="com.jettech.view.TestCaseVersionView">
        select a.id,b.name versionName,a.createTime from ds_testcase_version a join version_info b on a.versionResourceID = b.resourceID
        where a.testCaseResourceID = #{testCaseResourceID}
        <if test="name != null and name != ''">
            and b.name like concat('%',#{name},'%')
        </if>
        order by a.createTime desc
    </select>

</mapper>