<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITradeMapper">
    <resultMap type="com.jettech.model.Trade" id="tradeMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>
        <result property="number" column="number"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="comment" column="comment"/>
        <result property="moduleResourceID" column="moduleResourceID"/>
        <result property="testSystemResourceID" column="testSystemResourceID"/>
        <result property="executeMode" column="executeMode"/>
        <result property="versionNumber" column="versionNumber"/>
    </resultMap>
    <sql id="tradeColumnList">
        id, version, createTime,  editTime, createUser,editUser, resourceID, name,status,
        `number`,
        `type`,
        <choose>
            <when test="_databaseId == 'dm'">
                CAST("comment" as VARCHAR) as "comment"
            </when>
            <otherwise>
                `comment`
            </otherwise>
        </choose>
        ,moduleResourceID,testSystemResourceID,
        executeMode,versionNumber
    </sql>

    <sql id="tradeColumnList_t2">
        t2.id, t2.version, t2.createTime,  t2.editTime, t2.createUser,t2.editUser, t2.resourceID, t2.name,t2.status,
        t2.`number`,
        t2.`type`,
        <choose>
            <when test="_databaseId == 'dm'">
                CAST(t2."comment" as VARCHAR) as "comment"
            </when>
            <otherwise>
                t2.`comment`
            </otherwise>
        </choose>

        ,t2.moduleResourceID,t2.testSystemResourceID,
        t2.executeMode,t2.versionNumber
    </sql>
    <select id="selectAll" resultMap="tradeMap">
        SELECT  <include refid="tradeColumnList"/>  FROM at_trade
    </select>
    <select id="findByResourceIds" parameterType="java.util.List" resultMap="tradeMap">
        select <include refid="tradeColumnList"/> FROM at_trade WHERE resourceID in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="findByResourceId" parameterType="java.lang.Long" resultMap="tradeMap">
        select <include refid="tradeColumnList"/> FROM at_trade WHERE resourceID = #{resourceId}
    </select>
    <select id="verifyTradeNameNotRepeatedByModule" resultMap="tradeMap">
        SELECT <include refid="tradeColumnList"/> FROM at_trade WHERE name = #{name}
        AND moduleResourceID = #{moduleResourceID}
        <if test="resourceID != null and resourceID != '' ">
            AND resourceID != #{resourceID}
        </if>

    </select>
    <select id="verifyTradeNameNotRepeatedBySystem" resultMap="tradeMap">
        SELECT <include refid="tradeColumnList"/> FROM at_trade WHERE name = #{name} and
        testSystemResourceID = #{testSystemResourceID}
        <if test="resourceID != null and resourceID != '' ">
            AND resourceID != #{resourceID}
        </if>
    </select>

    <!--检测交易编码是否重复 -->
    <select id="verifyTradeNumberNotRepeatedBySystem" resultMap="tradeMap">
        SELECT <include refid="tradeColumnList"/> FROM at_trade WHERE `number` = #{number} and
        testSystemResourceID = #{testSystemResourceID}
        <if test="resourceID != null and resourceID != '' ">
            AND resourceID != #{resourceID}
        </if>
    </select>

    <!-- 通过被测系统resourceID查询交易数据 -->
    <select id="findByTestSystemResourceIDs" resultType="java.util.HashMap">
        SELECT resourceID as "resourceID",name,
        'trade' AS "type",
        `number`,
        `status`,
        <choose>
            <when test="_databaseId == 'dm'">
                CAST("comment" as VARCHAR) as "comment",
            </when>
            <otherwise>
                `comment`,
            </otherwise>
        </choose>
        moduleResourceID as "moduleResourceID",testSystemResourceID as "testSystemResourceID" from
        at_trade where testSystemResourceID in
        <foreach collection="list" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
        ORDER BY createTime
    </select>
    <!--查询当前被测系统下的交易-->
    <select id="findbyTestSystemResourceID" resultMap="tradeMap">
        select <include refid="tradeColumnList"/> from
        at_trade where at_trade.testSystemResourceID = #{testSystemResourceID}
        <if test="name != null and name != '' ">
            AND name LIKE concat('%',#{name},'%')
        </if>
        <if test="versionNumber != null and versionNumber != '' ">
            AND versionNumber LIKE concat('%',#{versionNumber},'%')
        </if>
        <if test="executeMode != null and executeMode != '' ">
            AND executeMode = #{executeMode}
        </if>
        ORDER BY at_trade.createTime desc
    </select>
    <!--查询模块下的交易-->
    <select id="findBySystemModule" resultMap="tradeMap">
        select <include refid="tradeColumnList"/> from
        at_trade where moduleResourceID in
        <foreach collection="list" item="moduleResourceID" open="(" close=")" separator=",">
            #{moduleResourceID}
        </foreach>
        ORDER BY at_trade.createTime desc
    </select>
    <!--被测系统resourceID查询交易 -->
    <select id="getObjectByTestSystemResourceIDs" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        from at_trade where testSystemResourceID in
        <foreach collection="list" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
    </select>
    <!--根据当前被测系统resourceID查询交易-->
    <select id="findBySystemModuleResourceID" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        from at_trade WHERE moduleResourceID = #{resourceID}
        <if test="name != null and name != '' ">
            AND name LIKE concat('%',#{name},'%')
        </if>
        <if test="versionNumber != null  and versionNumber != '' ">
            AND versionNumber LIKE concat('%',#{versionNumber},'%')
        </if>
        <if test="executeMode != null and executeMode != '' ">
            AND executeMode = #{executeMode}
        </if>
    </select>

    <!--根据当前被测系统resourceID查询交易-->
    <select id="findBySystemModuleResourceIDsIn" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        from at_trade WHERE moduleResourceID IN
        <foreach collection="resourceIDs" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
        <if test="name != null and name != '' ">
            AND name LIKE concat('%',#{name},'%')
        </if>
        <if test="versionNumber != null  and versionNumber != '' ">
            AND versionNumber LIKE concat('%',#{versionNumber},'%')
        </if>
        <if test="executeMode != null and executeMode != '' ">
            AND executeMode = #{executeMode}
        </if>
    </select>

    <!--findByNumberAndTestSystemResourceID-->
    <select id="findByNumberAndTestSystemResourceID" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        from at_trade WHERE testSystemResourceID = #{testSystemResourceID}
        <!--        <if test="list != null and list.size() > 0 ">-->
        <!--            AND number IN-->
        <!--            <foreach collection="list" item="value" open="(" close=")" separator=",">-->
        <!--                #{value}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <if test="nameList != null and nameList.size() > 0">
            AND NAME IN
            <foreach collection="nameList" item="value" open="(" close=")" separator=",">
                #{value}
            </foreach>
        </if>

    </select>

    <select id="findByNumberAndModuleResourceID" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        from at_trade WHERE moduleResourceID = #{moduleResourceID}
        <!--        <if test="list != null and list.size() > 0">-->
        <!--            AND number IN-->
        <!--            <foreach collection="list" item="value" open="(" close=")" separator=",">-->
        <!--                #{value}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <if test="nameList != null and nameList.size()> 0">
            AND NAME IN
            <foreach collection="nameList" item="value" open="(" close=")" separator=",">
                #{value}
            </foreach>
        </if>

    </select>
    <select id="findNextLowerLevelMapByTestSystemResourceID"
            resultType="java.util.HashMap">
        select <include refid="tradeColumnList"/> from at_trade where moduleResourceID is NULL AND testSystemResourceID = #{testSystemResourceID}
    </select>

    <select id="getByTsResourceIDAndModulResourceID" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        from at_trade WHERE testSystemResourceID = #{testSystemResourceID}
        <choose>
            <when test="moduleResourceID != null and moduleResourceID > 0">
                AND moduleResourceID = #{moduleResourceID}
            </when>
            <otherwise>
                AND moduleResourceID IS NULL
            </otherwise>
        </choose>
    </select>

    <select id="findbyTradeName" resultMap="tradeMap">
        select
        <include refid="tradeColumnList"/>
        from at_trade where name = #{tradeName}
    </select>
    <select id="findOnlyTradebyTestSystemResourceID" resultMap="tradeMap">
        select <include refid="tradeColumnList"/> from
        at_trade where at_trade.testSystemResourceID = #{testSystemResourceID} AND at_trade.moduleResourceID is null
        <if test="name != null and name != '' ">
            AND name LIKE concat('%',#{name},'%')
        </if>
        ORDER BY at_trade.createTime desc
    </select>

    <select id="findOnlyTradebyTestSystemResourceID2" resultType="com.jettech.model.Trade">
        select
        t.resourceID, t.name,t.id
        from
        at_trade t where t.testSystemResourceID = #{testSystemResourceID} AND t.moduleResourceID is null ORDER BY t.createTime desc
    </select>
    <select id="findSelectedTradeByTaskResourceID" resultMap="tradeMap">
     	SELECT distinct <include refid="tradeColumnList_t2"/> FROM testtasktrade t1 right JOIN at_trade t2 ON t1.tradeResourceID = t2.resourceID
        <where>
            <if test="taskResourceID != null and taskResourceID.size > 0">
                testTaskResourceID in
                <foreach collection="taskResourceID" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
     </select>

    <select id="findByTestSystemResourceAndName" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        FROM
        at_trade
        WHERE
        testSystemResourceID = #{testSystemResource}
        AND name = #{name} LIMIT 1
    </select>

    <select id="findByModuleResourceID" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        FROM
        at_trade
        WHERE
        moduleResourceID = #{moduleResourceID}
    </select>

    <select id="findByNameAndModuleResourceID" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        FROM
        at_trade
        WHERE
        moduleResourceID = #{modeleResourceID}
        and name=#{name}
    </select>

    <select id="findTradeByTestSystemRidAndModuleIsNotNull" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        FROM at_trade WHERE testSystemResourceID = #{testSystemRid} and moduleResourceID is not null ORDER BY
        moduleResourceID DESC
    </select>

    <select id="findTradeDicsByDicName" resultType="java.util.HashMap">
     	SELECT
        `value` as "value",
     	textName as "textName",defaultvalue as "defaultvalue",parentDefaultvalue as "parentDefaultvalue" FROM datadictionary WHERE name = #{name, typeHandler=com.jettech.typehandler.UpperTypeHandler}
     </select>
    <select id="getDemandToTradeMapperByDemandNameAndTestSystemName" resultType="com.jettech.bean.DemandToTradeDO">
     	SELECT
			t1.name as demandName,
			t1.resourceID as demandResourceID,
			t3.name as testSystemName,
			t3.resourceID as testSystemResourceID,
			t5.name as tradeName,
			t5.resourceID as tradeResourceID
		FROM
			ds_demand t1,
			ds_demandtestsystem t2,
			at_testsystem t3,
			at_systemmodule t4,
			at_trade t5
		WHERE
			t1.resourceID = t2.demandResourceID AND
			t2.testSystemResourceID = t3.resourceID AND
			t3.resourceID = t4.testSystemResourceID AND
			t5.moduleResourceID = t4.resourceID AND
			t1.name = #{demandName} AND
			t3.name = #{testSystemName}
		UNION
		SELECT
			t1.name as demandName,
			t1.resourceID as demandResourceID,
			t3.name as testSystemName,
			t3.resourceID as testSystemResourceID,
			t4.name as tradeName,
			t4.resourceID as tradeResourceID
		FROM
			ds_demand t1,
			ds_demandtestsystem t2,
			at_testsystem t3,
			at_trade t4
		WHERE
			t1.resourceID = t2.demandResourceID AND
			t2.testSystemResourceID = t3.resourceID AND
			t4.testSystemResourceID = t3.resourceID AND
			t1.name = #{demandName} AND
			t3.name = #{testSystemName}
     </select>
    <select id="findTestTaskByTestTaskResourceID" resultType="java.util.HashMap">
     	SELECT
     	id, createTime as "createTime", createUser as "createUser", editTime as "editTime", editUser as "editUser", resourceID as "resourceID", version,
        taskNumber as "taskNumber",name,startTime as "startTime",endTime as "endTime",
        `type`,`level`
        ,managerResourceID as "managerResourceID",status,description,demandResourceID as "demandResourceID",testPlanResourceID as "testPlanResourceID", testProjectId as "testProjectId"
     	FROM testtask WHERE resourceID = #{taskResourceID}
     </select>
    <select id="findTradeBySystemAndNameAndType" resultType="com.jettech.model.Trade">
        select resourceID,name,
        `number`,
        `type`,
        executeMode,testSystemResourceID,moduleResourceID from at_trade
        where testSystemResourceID = #{systemResourceID}
        <if test="name != null and name != '' ">
            AND name LIKE concat('%',#{name},'%')
        </if>
        <if test="executeMode != null and executeMode != '' ">
            AND executeMode = #{executeMode}
        </if>
    </select>
    <select id="findTradeByModuleAndNameAndType" resultType="com.jettech.model.Trade">
        select resourceID,name,
        `number`,`type`,
        executeMode,testSystemResourceID ,moduleResourceID from at_trade
        where moduleResourceID IN
        <foreach collection="moduleResourceIDs" item="moduleResourceID" open="(" close=")" separator=",">
            #{moduleResourceID}
        </foreach>
        <if test="name != null and name != '' ">
            AND name LIKE concat('%',#{name},'%')
        </if>
        <if test="executeMode != null and executeMode != '' ">
            AND executeMode = #{executeMode}
        </if>
    </select>
    <select id="findTradeIDsByTaskResourceID" resultType="java.lang.String">
        SELECT
        t.tradeResourceID as "tradeResourceID"
        FROM testtasktrade t
        WHERE t.testTaskResourceID = #{taskResourceID}
        AND t.tradeResourceID IN
        <foreach collection="tradeRids" item="resourceID" open="(" close=")" separator=",">
            #{resourceID}
        </foreach>
    </select>

    <select id="findBySystemResourceIDsOrModuleResourceIDsOrKeyWord" resultType="com.jettech.model.TreeNode">
        SELECT
        name,
        resourceID,
        'trade' as "type",
        `number`,
        true as "enable",
        CASE WHEN moduleResourceID IS NULL THEN testSystemResourceID ELSE moduleResourceID END AS parentResourceID
        FROM
        at_trade
        where name LIKE concat('%',#{keyWord},'%')
        <if test="enableSystemResourceID != null and enableSystemResourceID.size > 0">
            or testSystemResourceID IN
            <foreach collection="enableSystemResourceID" item="resourceID" index="index"
                     separator="," open="(" close=")">
                #{resourceID}
            </foreach>
        </if>

        <if test="enableModuleResourceID != null and enableModuleResourceID.size > 0">
            or moduleResourceID IN
            <foreach collection="enableModuleResourceID" item="resourceID" index="index"
                     separator="," open="(" close=")">
                #{resourceID}
            </foreach>
        </if>
    </select>

    <select id="findTradeByModuleResourceID" resultType="java.util.HashMap">
    	SELECT  id, name as "tradeName",resourceID as "tradeResourceID" FROM at_trade WHERE moduleResourceID = #{moduleResourceID}
    </select>

    <select id="findByTestSystemResourceAndNumber" resultMap="tradeMap">
        SELECT
        <include refid="tradeColumnList"/>
        FROM
        at_trade
        WHERE
        testSystemResourceID = #{testSystemRid}
        AND `number` = #{number} LIMIT 1

    </select>
    <select id="findTradeByModuleAndNameAndTask" resultType="com.jettech.model.Trade">
        select t1.resourceID from at_trade t1 left JOIN testtasktrade t2 ON t1.resourceID = t2.tradeResourceID
        where t1.moduleResourceID IN
        <foreach collection="moduleResourceIDs" item="moduleResourceID" open="(" close=")" separator=",">
            #{moduleResourceID}
        </foreach>
        <if test="taskResourceID != null and taskResourceID != ''">
            and t2.testTaskResourceID= #{taskResourceID}
        </if>
    </select>
    <select id="findTradeBySystemAndTask" resultType="com.jettech.model.Trade">
        select t1.resourceID from at_trade t1 left JOIN testtasktrade t2 ON t1.resourceID = t2.tradeResourceID
        where t1.testSystemResourceID = #{systemResourceID}
        <if test="taskResourceID != null and taskResourceID != ''">
            and t2.testTaskResourceID= #{taskResourceID}
        </if>
    </select>

    <select id="getTradeSystemModule" resultType="com.jettech.common.dto.datadesign.SystemModuleTradeDTO">
        SELECT
            t1.resourceID tradeResourceID,
            t1.name tradeName,
            t2.resourceID moduleResourceID,
            t2.name moduleName,
            (
              <choose>
                  <when test="_databaseId == 'postgresql'">
                      SELECT STRING_AGG(DISTINCT NAME,'/') AS modulePath
                      FROM
                      (SELECT
                      NAME
                      FROM at_systemmodule
                      CONNECT BY resourceID = PRIOR parentResourceID
                      START WITH resourceID = t2.resourceID
                      ORDER BY LEVEL DESC)
                  </when>
                  <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                      SELECT REPLACE(CAST(WM_CONCAT(name) AS VARCHAR), ',', '/') AS modulePath
                      FROM
                      (SELECT
                      NAME
                      FROM at_systemmodule
                      CONNECT BY resourceID = PRIOR parentResourceID
                      START WITH resourceID = t2.resourceID
                      ORDER BY LEVEL DESC)
                  </when>
                  <when test="_databaseId == 'kingwow' or _databaseId == 'dm'">
                      t4.modulePath
                  </when>
                  <otherwise>
                      t4.NAME
                      /*WITH RECURSIVE temp as (
                      SELECT resourceID, name, parentResourceID, testSystemResourceID FROM at_systemmodule WHERE resourceID = t2.resourceID
                      UNION ALL
                      SELECT a.resourceID, CONCAT(a.name,'/',b.name) name, a.parentResourceID, a.testSystemResourceID
                      FROM at_systemmodule a INNER JOIN temp b on a.resourceID = b.parentResourceID
                      )
                      SELECT name AS modulePath FROM temp WHERE parentResourceID = testSystemResourceID LIMIT 1*/
                  </otherwise>
              </choose>
            ) AS moduleNames,
            t3.resourceID systemResourceID,
            t3.name systemName
        FROM
            at_trade t1
            left JOIN at_testsystem t3 ON t1.testSystemResourceID = t3.resourceID
            left JOIN at_systemmodule t2 ON t1.moduleResourceID = t2.resourceID AND t2.testSystemResourceID = t3.resourceID
            <choose>
                <when test="_databaseId == 'postgresql'"></when>
                <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)"></when>
                <when test="_databaseId == 'kingwow' or _databaseId == 'dm'">
                    LEFT JOIN (SELECT
                    LTRIM(SYS_CONNECT_BY_PATH(name, '/'),'/') AS modulePath,
                    resourceID
                    FROM at_systemmodule
                    START WITH parentResourceID = testSystemResourceID
                    CONNECT BY parentResourceID = PRIOR  resourceID) t4 ON t2.resourceID = t4.resourceID
                </when>
                <otherwise>
                    LEFT JOIN (
                        WITH RECURSIVE temp AS (
                        SELECT resourceID, NAME, parentResourceID, testSystemResourceID, resourceID leafModuleResourceID
                        FROM at_systemmodule
                        UNION ALL
                        SELECT a.resourceID, CONCAT( a.NAME, '/', b.NAME ) NAME, a.parentResourceID, a.testSystemResourceID, b.resourceID leafModuleResourceID
                        FROM at_systemmodule a
                        INNER JOIN temp b ON a.resourceID = b.parentResourceID)
                        select * from temp
                    ) t4 on t2.resourceID = t4.leafModuleResourceID
                </otherwise>
            </choose>
            
            <!--<if test="_databaseId == 'kingwow' or _databaseId == 'dm'">
                LEFT JOIN (SELECT
                LTRIM(SYS_CONNECT_BY_PATH(name, '/'),'/') AS modulePath,
                resourceID
                FROM at_systemmodule
                START WITH parentResourceID = testSystemResourceID
                CONNECT BY parentResourceID = PRIOR  resourceID) t4 ON t2.resourceID = t4.resourceID
            </if>-->
        WHERE
            t1.resourceID in
            <foreach collection="tradeResourceIDList" item="tradeResourceID" separator="," open="(" close=")">
                #{tradeResourceID}
            </foreach>
    </select>
    
    <select id="findTradeByModuleResourceIDs" resultType="java.util.HashMap">
    	SELECT  id, name as "tradeName",resourceID as "tradeResourceID" FROM at_trade WHERE moduleResourceID in
    	<foreach collection="moduleResourceIDs" item="moduleResourceID" separator="," open="(" close=")">
                #{moduleResourceID}
            </foreach>
    </select>
    <select id="findTradeByDemandResourceID" resultType="com.jettech.model.Trade">
        SELECT
        <include refid="tradeColumnList"/>
        FROM at_trade
        WHERE testSystemResourceID IN
          (SELECT DISTINCT testSystemResourceID FROM ds_demandtestsystem WHERE demandResourceID = #{demandResourceID})
    </select>

    <select id="findBySystesmResourceIDIn" resultType="com.jettech.model.Trade">
        SELECT
        <include refid="tradeColumnList"/>
        FROM at_trade
        WHERE testSystemResourceID IN
          <foreach collection="sysList" item="sysRid" separator="," open="(" close=")">
                #{sysRid}
            </foreach>
    </select>

    <!-- 通过任务rids查询交易返回map类型)20240517dwl -->
    <select id="findSelectedTradeByTaskResourceID" resultType="com.jettech.model.Trade">
        SELECT distinct <include refid="tradeColumnList_t2"/>,
            (
                SELECT
                    caseId
                from ds_testcase where tradeResourceID = #{tradeResourceID}
                and caseId = (select max(caseId) from ds_testcase WHERE tradeResourceID = t2.resourceID

                <choose>
                    <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                        AND
                        regexp_like (caseId,'[^0-9]') = false
                    </when>
                    <otherwise>
                        AND
                        <include refid="com.jettech.mapper.CommonSqlMapper._regexp_not_like">
                            <property name="colName" value="caseId"/>
                            <property name="regexpStr" value="[^0-9]"/>
                        </include>
                    </otherwise>
                </choose>
                )
        ) as maxCaseId,
        (
            select max(caseId) caseId from ds_testcaseRecycleBin WHERE tradeResourceID = t2.resourceID
            <choose>
                <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                    AND regexp_like (caseId,'[^0-9]') = false
                </when>
                <otherwise>
                    AND
                    <include refid="com.jettech.mapper.CommonSqlMapper._regexp_not_like">
                        <property name="colName" value="caseId"/>
                        <property name="regexpStr" value="[^0-9]"/>
                    </include>
                </otherwise>
            </choose>
        ) as maxRecycleBinCaseId,
        (
            select max(caseId) as "caseId" from at_testcase WHERE tradeResourceID = t2.resourceID
            <choose>
                <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                    AND
                    regexp_like (caseId,'[^0-9]') = false
                </when>
                <otherwise>
                    AND
                    <include refid="com.jettech.mapper.CommonSqlMapper._regexp_not_like">
                        <property name="colName" value="caseId"/>
                        <property name="regexpStr" value="[^0-9]"/>
                    </include>
                </otherwise>
            </choose>
        ) as maxAssetCaseId
        FROM testtasktrade t1 right JOIN at_trade t2 ON t1.tradeResourceID = t2.resourceID
        <where>
            <if test="taskResourceID != null and taskResourceID.size > 0">
                testTaskResourceID in
                <foreach collection="taskResourceID" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
