<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.JettoApiMapper">
    <select id="findScriptFolderAndTradeFlowByProjectResourceID" resultType="java.util.Map">
        select t.resourceID as "resourceID" from ds_testcase t where t.testProjectId = #{projectResourceID}
        UNION
        select tf.id resourceID from trade_flow tf where tf.script_test_project_id = #{projectResourceID}

    </select>

    <select id="findScriptFolderByProjectResourceID" resultType="java.util.Map">
       SELECT * FROM script_folder
        where script_test_project_id = #{projectResourceID}
        and parent_menu = 0
    </select>
    
    <insert id="saveScriptFolder">
        insert into script_folder(id,name,
        `level`
        ,parent_menu,script_test_project_id,create_user,update_user,
                                create_date,update_date,reserve1,reserve2,reserve3)
        values (#{map.id},#{map.name},#{map.level},#{map.parent_menu},#{map.script_test_project_id},#{map.create_user},#{map.update_user},
        #{map.create_date},#{map.update_date},#{map.reserve1},#{map.reserve2},#{map.reserve3})
    </insert>
    <update id="updateScriptFolder" >
        update script_folder
        set name = #{map.name}
        where id = #{map.id}
    </update>

    <select id="findTradeFolderBySystemResourceID" resultType="java.util.Map">
        select * from trade_folder
        where test_system_id = #{systemResourceID}
        and parent_menu = 0
    </select>

    <insert id="saveTradeFolder">
        insert into trade_folder(id,name,
        `level`
        ,parent_menu,test_system_id,status,create_user,update_user,
                                create_date,update_date,reverse1,reverse2,reverse3)
        values (#{map.id},#{map.name},#{map.level},#{map.parent_menu},#{map.test_system_id},#{map.status},#{map.create_user},
        #{map.update_user}, #{map.create_date},#{map.update_date},#{map.reserve1},#{map.reserve2},#{map.reserve3})
    </insert>
    <update id="updateTradeFolder">
          update trade_folder
        set name = #{map.name}
        where id = #{map.id}
    </update>

    <select id="findTradeFlowCaseFolderByProjectResourceID" resultType="java.util.Map">
        SELECT * FROM trade_flow_case_folder
        where test_project_id = #{projectResourceID}
        and parent_menu = 0
    </select>

    <insert id="saveTradeFlowCaseFolder" >
         insert into trade_flow_case_folder(id,name,
        `level`
         ,parent_menu,test_project_id,create_user,update_user,
                                create_date,update_date,reserve1,reserve2,reserve3)
        values (#{map.id},#{map.name},#{map.level},#{map.parent_menu},#{map.test_project_id},#{map.create_user},#{map.update_user},
        #{map.create_date},#{map.update_date},#{map.reserve1},#{map.reserve2},#{map.reserve3})

    </insert>
    <update id="updateTradeFlowCaseFolder">
      update trade_flow_case_folder
        set name = #{map.name}
        where id = #{map.id}
    </update>
</mapper>