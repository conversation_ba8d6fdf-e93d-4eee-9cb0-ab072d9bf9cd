<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IWikiPageMapper">

    <resultMap id="wikiPageResultMap" type="com.jettech.model.WikiPage">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="parentResourceId" column="PARENT_RESOURCE_ID"/>
        <result property="spaceResourceId" column="SPACE_RESOURCE_ID"/>
        <result property="title" column="TITLE"/>
        <result property="pageType" column="PAGE_TYPE"/>
        <result property="inRecycle" column="IS_INRECYCLE"/>
        <result property="content" column="CONTENT"/>
        <result property="textContent" column="TEXT_CONTENT"/>
        <result property="draft" column="IS_DRAFT"/>
        <result property="createName" column="CREATE_NAME"/>
        <result property="updateName" column="UPDATE_NAME"/>
        <result property="userPageType" column="USER_PAGE_TYPE"/>
        <result property="userPageTypeResourceId" column="user_page_type_resource_id"/>
        <result property="spaceName" column="space_name"/>
    </resultMap>

    <select id="getPage" resultMap="wikiPageResultMap">
        SELECT
            WP.*,
            WPC.CONTENT,
            WPC.TEXT_CONTENT,
            ju.userName AS CREATE_NAME,
            ju1.userName AS UPDATE_NAME,
            wput.`TYPE` AS USER_PAGE_TYPE,
            wput.resourceID as user_page_type_resource_id
        FROM
            WIKI_PAGE AS WP
            LEFT JOIN WIKI_PAGE_CONTENT AS WPC ON WP.resourceID = WPC.page_resource_id
            left join jettechuser ju on wp.createUser = ju.`number`
            left join jettechuser ju1 on wp.editUser = ju1.`number`
            LEFT JOIN WIKI_PAGE_USER_TEM_CONFIG wput ON wput.PAGE_RESOURCE_ID = WP.resourceID and
        wput.`type` = 3
        and wput.createUser = #{loginUserNumber}
        WHERE WP.resourceID =#{resourceID}
    </select>

    <select id="listPageBySpace" resultType="com.jettech.model.WikiPage">
        SELECT * FROM WIKI_PAGE
        WHERE SPACE_RESOURCE_ID =#{spaceId} AND IS_INRECYCLE=0
        AND IS_DRAFT = #{draft}
    </select>

    <select id="listAllPageBySpace" resultType="com.jettech.model.WikiPage">
        SELECT * FROM WIKI_PAGE
        WHERE SPACE_RESOURCE_ID =#{spaceId}
    </select>

    <select id="listDraftPage" resultMap="wikiPageResultMap">
        select wp.*,ws.space_name from wiki_page wp
        left join wiki_space ws on wp.space_resource_id = ws.resourceID
        where wp.is_draft = 1 and (wp.IS_INRECYCLE is null or wp.IS_INRECYCLE = 0)
        <if test="wikiPageDTO != null and wikiPageDTO.spaceResourceId != null">
            and wp.space_resource_id = #{wikiPageDTO.spaceResourceId}
        </if>
        <if test="wikiPageDTO != null and wikiPageDTO.pageName != null and wikiPageDTO.pageName != ''">
            and wp.title like concat('%',#{wikiPageDTO.pageName},'%')
        </if>
    </select>

    <select id="listRecyclePageBySpace" resultMap="wikiPageResultMap">
        SELECT wp.*,ws.space_name,ju.userName CREATE_NAME
        FROM WIKI_PAGE wp
        LEFT JOIN WIKI_SPACE ws ON wp.SPACE_RESOURCE_ID = ws.resourceID
        left join jettechuser ju on wp.createUser = ju.number
        WHERE IS_INRECYCLE= 1
        <if test="wikiPageDTO != null and wikiPageDTO.pageName != null and wikiPageDTO.pageName != ''">
            and wp.title like concat('%',#{wikiPageDTO.pageName},'%')
        </if>
        <if test="wikiPageDTO != null and wikiPageDTO.pageResourceId != null and wikiPageDTO.pageResourceId != ''">
            and wp.resourceID = #{wikiPageDTO.pageResourceId}
        </if>
        ORDER BY wp.id
    </select>
</mapper>
