<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IVersionInfoMapper">
    <resultMap type="com.jettech.model.VersionInfo" id="baseMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>
        <result property="name" column="name"/>
        <result property="remarks" column="remarks"/>
    </resultMap>
    <sql id="versionInfoColumnList">
        id, version, createTime, editTime, createUser, editUser, resourceID, name, remarks
    </sql>
    <sql id="versionInfoColumnList_vi">
        vi.id, vi.version, vi.createTime, vi.editTime, vi.createUser, vi.editUser, vi.resourceID, vi.name, vi.remarks
    </sql>

    <select id="findByCondition" resultType="com.jettech.view.VersionInfoView">
        select <include refid="versionInfoColumnList_vi"/> ,b.userName createUserName,c.userName editUserName from version_info vi left join jettechuser b on vi.createUser = b.number left join jettechuser c on vi.createUser = c.number
        where 1=1
        <if test="name != null and name != ''">
            and vi.name like concat('%',#{name},'%')
        </if>
        order by vi.createTime desc
    </select>
</mapper>