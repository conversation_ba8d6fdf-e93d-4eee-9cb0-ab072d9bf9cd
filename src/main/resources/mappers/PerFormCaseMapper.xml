<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IPerFormCaseMapper">


    <sql id="perFormCaseColumnList">
        id ,version ,createTime ,editTime ,createUser ,editUser ,resourceID ,caseId ,isNegative ,casetLevel ,
        caseType ,testStep ,expectedResult ,preconditions ,testsystemResourceID ,systemmoduleResourceID ,
        tradeResourceID ,testsystem ,systemmodule ,trade ,timingName ,maintainer ,maintenanceTime ,
        `intent` ,
        `comment` ,

        demandResourceID ,reviewStatus ,caseEditId ,testEnviroment ,testtaskResourceID ,projectgroupResourceID ,
        dataRequirements ,checkPoint ,leadsource ,planexecutorNumber ,planexecutor ,executornumber ,identitynumber ,
        executor ,
        `committed`

        ,caseResult ,lastExecuteTime ,comments1 ,comments2 ,comments3 ,comments4 ,comments5 ,
        comments6 ,comments7 ,comments8 ,comments9 ,comments10 ,openUser ,status
    </sql>

    <select id="findByTestcaseResourceID" resultType="com.jettech.model.PerFormCase">
        SELECT
            <include refid="perFormCaseColumnList"/>
        FROM performcase t1 WHERE t1.testCaseResourceID =  #{testcaseResourceID}
    </select>


    <select id="findByGroupResourceIDs" resultType="com.jettech.model.PerFormCase">
        select
        <include refid="perFormCaseColumnList"/>
        from performcase
        where projectgroupResourceID in
        <foreach collection="list" item="resourceID" open="(" close=")" separator=",">
            #{resourceID}
        </foreach>
    </select>

</mapper>