<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestSystemMapper">
    <resultMap type="com.jettech.model.TestSystem" id="testSystemMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="number" column="number"/>
        <result property="name" column="name"/>
        <result property="describes" column="describes"/>
        <result property="manager" column="manager"/>

        <result property="simpleName" column="simpleName"/>
        <result property="busPCentralizedDepartment" column="busPCentralizedDepartment"/>
        <result property="developCompany" column="developCompany"/>
        <result property="projectManager" column="projectManager"/>
        <result property="SITTestManger" column="SITTestManger"/>
        <result property="SITTesters" column="SITTesters"/>
        <result property="important" column="important"/>
        <result property="developPerson" column="developPerson"/>
        <result property="testMode" column="testMode"/>
    </resultMap>

    <sql id="testCaseColumnList">
        id, createTime, createUser, editTime, editUser, resourceID, version,
        caseId, isNegative,casetLevel,caseType,testStep,expectedResult,
        preconditions,testsystem,testsystemResourceID,systemmodule,systemmoduleResourceID,projectgroupResourceID,leadsource,
        trade,tradeResourceID,timingName,maintainer,maintenanceTime,
        <choose>
            <when test="databaseId == 'dm'">
                "intent",
                "comment",
            </when>
            <otherwise>
                intent,
                comment,
            </otherwise>
        </choose>
        demandResourceID,reviewStatus,caseEditId,testEnviroment,dataRequirements,checkPoint,testtaskResourceID,identitynumber,comments1,comments2,
        comments3,comments4,comments5,comments6,comments7,comments8,comments9,comments10
    </sql>


    <sql id="testSystemColumnList">
        id, createTime, createUser, editTime, editUser, resourceID, version,
        `number`
        , name,describes,manager,simpleName,
        busPCentralizedDepartment,developCompany,projectManager,SITTestManger,SITTesters,important,developPerson,
        testMode
    </sql>
    
    <sql id="testSystemColumnList_ts">
        ts.id, ts.createTime, ts.createUser, ts.editTime, ts.editUser, ts.resourceID, ts.version,
        ts.`number`
        , ts.name,ts.describes,ts.manager,ts.simpleName,
        ts.busPCentralizedDepartment,ts.developCompany,ts.projectManager,ts.SITTestManger,ts.SITTesters,ts.important,ts.developPerson,
        ts.testMode
    </sql>
    <!--判断系统名称是否重复-->
    <select id="verifySystemNameNotRepeated" resultMap="testSystemMap">
        SELECT <include refid="testSystemColumnList"/>
        FROM at_testsystem
        WHERE name= #{name}
        <if test="nodeResourceID != null">
            AND at_testsystem.resourceID != #{nodeResourceID}
        </if>
    </select>
    <!--判断系统简称是否重复-->
    <select id="verifySystemSimpleNameNotRepeated" resultMap="testSystemMap">
        SELECT <include refid="testSystemColumnList"/>
        FROM at_testsystem
        <where>
            <if test="simpleName != null and simpleName != '' ">
                AND at_testsystem.simpleName = #{simpleName}
            </if>
            <if test="resourceID != null">
                AND at_testsystem.resourceID != #{resourceID}
            </if>
        </where>
    </select>
    <!-- 查询当前项目下的单点被测系统 -->
    <select id="findByTestProjectResourceID" resultType="java.util.HashMap" parameterType="String">
        select a1.resourceID as "resourceID",a1.name,a1.manager,d1.demandResourceID as "demandResourceID",a1.describes,
        'system' AS "type",
        a1.`number`
        from at_testsystem a1
        JOIN ds_demandtestsystem  d1 ON
        a1.resourceID=d1.testSystemResourceID
        <if test="demandResourceID != null and demandResourceID != ''">
          WHERE d1.demandResourceID = #{demandResourceID}
        </if>
        ORDER BY a1.createTime
    </select>
     <!-- 查询当前项目下的单点被测系统  左连接查询 创建新系统后未关联需求仍可展示 -->
    <select id="findByTestProjectResourceIDAndLeft" resultType="java.util.HashMap" parameterType="String">
        select a1.resourceID as "resourceID",a1.name,a1.manager,d1.demandResourceID as "demandResourceID",a1.describes
        ,a1.`number`,'system' AS "type"
        FROM at_testsystem a1
        LEFT JOIN (SELECT MAX(id) AS id, testSystemResourceID FROM ds_demandtestsystem GROUP BY testSystemResourceID) d ON a1.resourceID = d.testSystemResourceID
        LEFT JOIN ds_demandtestsystem d1 ON d.id = d1.id
        <if test="demandResourceID != null and demandResourceID != ''">
          WHERE d1.demandResourceID = #{demandResourceID}
        </if>
        ORDER BY a1.createTime
    </select>

    <!--系统交易管理初始化被测系统table-->
    <select id="findByTestSystemAndSearch" resultType="com.jettech.DTO.TestSystemDTO">
      SELECT
        ats.id, ats.createTime, ats.createUser, ats.editTime, ats.editUser, ats.resourceID, ats.version,
        ats.`number`
            , ats.name,ats.describes,ats.manager,ats.simpleName,
        ats.busPCentralizedDepartment,ats.developCompany,ats.projectManager,ats.SITTestManger,ats.SITTesters,ats.important,ats.developPerson,
        ats.testMode
            ,je.userName as createUserName,je2.userName as editUserName
        FROM at_testsystem ats
        left join jettechuser je on je.`number` = ats.createUser
        left join jettechuser je2 on je2.`number` = ats.editUser
        <where>
            <if test="params.name != null and params.name != ''">
                AND
                ats.name like concat('%',#{params.name},'%')
            </if>
            <if test="params.busPCentralizedDepartment != null and params.busPCentralizedDepartment != ''">
                AND
                ats.busPCentralizedDepartment = #{params.busPCentralizedDepartment}
            </if>
            <if test="params.projectManager != null and params.projectManager != ''">
                AND
                ats.projectManager = #{params.projectManager}
            </if>
            <if test="params.developPerson != null and params.developPerson != ''">
                AND
                ats.developPerson like concat('%',#{params.developPerson},'%')
            </if>
            <if test="params.SITTestManger != null and params.SITTestManger != ''">
                AND
                ats.SITTestManger like concat('%',#{params.SITTestManger},'%')
            </if>
            <if test="params.SITTesters != null and params.SITTesters != ''">
                AND
                ats.SITTesters like concat('%',#{params.SITTesters},'%')
            </if>
            <if test="params.testMode != null and params.testMode !=''">
                <choose>
                    <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                        and bitand(ats.testMode, #{params.testMode}) > 0
                    </when>
                    <otherwise>
                        and ats.testMode &amp; #{params.testMode}
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY ats.createTime DESC
    </select>
    <!--查询被测系统下已经选择的人员-->
    <select id="selectTestSystemUser" resultType="java.util.HashMap">
      SELECT j.userName as "userName",j.resourceID as "resourceID" FROM at_testsystem AS t
        RIGHT JOIN ds_testsystem_user u ON t.resourceID = u.testSystemResourceID
        RIGHT JOIN jettechuser j ON u.userResourceID = j.resourceID
        WHERE t.resourceID = #{resourceID}
    </select>

    <!-- 查询人员所在得系统 -->
    <select id="findByusernumber" resultMap="testSystemMap">
      SELECT <include refid="testSystemColumnList"/> FROM at_testsystem where manager=#{number};
    </select>

    <select id="findUserBySystem"  resultType = "java.util.Map">
    	SELECT
			<include refid="com.jettech.mapper.IUserMapper.userColumnList"/>
		FROM
			jettechUser 
		WHERE
			resourceID IN ( SELECT d.userResourceID FROM ds_testsystem_user d WHERE d.testSystemResourceID = #{testSystemResourceID} )
	</select>
    <select id="findTestSystemByDemandResourceID" resultMap="testSystemMap">
        select <include refid="testSystemColumnList_ts"/> from ds_demandtestsystem dds left join at_testsystem ts
            on dds.testSystemResourceID = ts.resourceID
        where dds.demandResourceID = #{demandResourceID}

     </select>
    <select id="findAllSystem"  resultType = "java.util.Map">
		SELECT
			a.*
		FROM
			(
				SELECT
					att.id,
					att.resourceID,
					att. name,
                    CASE WHEN d.textName IS NULL THEN '' ELSE d.textName END AS busPCentralizedDepartment
				FROM
					at_testsystem att
                LEFT JOIN datadictionary d ON d.`VALUE` = att.busPCentralizedDepartment AND d.name = 'CENTRALIZEDBUSINESSDEPARTMENT'
			) a
			where 1=1
        <if test="name != null and name != '' ">
            AND a.name LIKE concat(concat('%',#{name}),'%')
        </if>

    </select>

    <select id="findRelevanceSystemID"  resultType = "java.util.Map">
		SELECT
	    u.resourceID as "resourceID"
      FROM
	  ds_demandtestsystem a
     INNER JOIN at_testsystem u ON a.testSystemResourceID = u.resourceID
      WHERE
	  a.demandResourceID =#{resourceID}
	</select>
	
	<select id="findTestSystemByUserResourceID" resultType="java.lang.String">
		select
        GROUP_CONCAT(DISTINCT t1.name)
		from
			at_testsystem t1,ds_testsystem_user t2
		where t1.resourceID = t2.testSystemResourceID
			and t2.userResourceID = #{userResourceID}
	</select>
    <select id="findSubordinateSystemMap" resultMap="testSystemMap">
        select id,name,simpleName,resourceID from at_testsystem
    </select>
    <select id="findTestSystemMap" resultType="java.util.Map">
        select resourceID as "resourceID",name,busPCentralizedDepartment as depart,developCompany as company from at_testsystem
        where resourceID = #{systemRID}
    </select>
    <select id="findTestTaskCaseResourceIDsInTrades" resultType="String">
    	SELECT
			t.tradeResourceID as "tradeResourceID"
		FROM testtasktrade t
		WHERE
			t.createUser = #{userNumber}
		AND t.testTaskResourceID = #{taskResourceID}
		AND t.tradeResourceID IN 
		<foreach collection="tradeRids" item="resourceID" open="(" close=")" separator=",">
			#{resourceID}
	    </foreach>
    </select>
    <select id="findByTestTaskResourceID" resultType="java.util.Map">
    	SELECT t1.resourceID as "taskResourceID",t1.testPlanResourceID as "testPlanResourceID",t2.planName as "planName",t2.testRound as "testRound",t2.testStage as "testStage",t3.resourceID as "testStageResourceID"
    		FROM testtask t1 left JOIN me_testplan t2 on t1.testPlanResourceID = t2.resourceID LEFT JOIN datadictionary t3 ON t2.testStage = t3.textName
    		WHERE t1.resourceID  = #{taskResourceID}
    </select>
    <select id="findCaseQuoteListByInfomation" resultType="java.util.Map">
    	SELECT t2.testCaseResourceID as "testCaseResourceID",t2.caseResult as "caseResult",t2.resourceID as "resourceID",t1.tradeResourceID as "tradeResourceID" FROM testtasktestcase t1 JOIN me_testcasequote t2 ON t1.testCaseResourceID = t2.testCaseResourceID
			and t1.testTaskResourceID = t2.testTaskResourceID where t2.testTaskResourceID = #{taskResourceID} AND t2.executorNumber = #{userNumber}
			<if test="startTime != null">
	            and t2.editTime &gt;= #{startTime,jdbcType=TIMESTAMP}
	        </if>
	        <if test=" endTime != null">
	            and t2.editTime &lt;= #{endTime,jdbcType=TIMESTAMP}
	        </if>
	        <if test="testCaseState != null and testCaseState != ''">
	            and t2.caseResult = #{testCaseState}
	        </if>
			
    </select>
       <select id="findCaseQuoteListByTradeInformation" resultType="java.util.Map">
           SELECT
           tt1.*,
           (select COUNT( tt2.resourceID ) from me_caseresultrecordfile tt2
           <choose>
               <when test="_databaseId == 'kingwow'">
                  WHERE tt1."newResultRecordResourceID" = tt2.caseResultRecordResourceID )
               </when>
               <otherwise>
                   WHERE tt1.`newResultRecordResourceID` = tt2.caseResultRecordResourceID )
               </otherwise>
           </choose>
           as oldFilesCount,
           (select COUNT( tt2.id ) from file_detail tt2
           <choose>
               <when test="_databaseId == 'kingwow'">
                   WHERE tt1."newResultRecordResourceID" = tt2.object_resource_Id )
               </when>
               <otherwise>
                   WHERE tt1.`newResultRecordResourceID` = tt2.object_resource_Id )
               </otherwise>
           </choose>
           as newFilesCount
           FROM
           (
           SELECT
           DISTINCT
           t2.id,
           t2.createTime as"createTime",
           t2.createUser as "createUser",
           t2.editTime as "editTime",
           t2.editUser as "editUser",
           t2.resourceID as "resourceID",
           t2.version,
           t2.caseId as "caseId",
           t2.isNegative as "isNegative",
           t2.casetLevel as "casetLevel",
           t2.caseType as "caseType",
           t2.timingName as "timingName",
           t2.maintainer,
           t2.maintenanceTime as "maintenanceTime",
           <choose>
               <when test="_databaseId == 'dm'">
                   CAST(t2.testStep AS varchar) as "testStep",
                   cast(t2.expectedResult as varchar) as "expectedResult",
                   cast(t2.preconditions as varchar) as "preconditions",
                   cast(t2."intent" as varchar) as "intent",
                   cast(t2."comment" as varchar) as "comment",
                   cast(t2.dataRequirements as varchar) as "dataRequirements",
                   cast(t2.checkPoint as varchar) as "checkPoint",
               </when>
               <otherwise>
                   t2.`intent` as "intent",
                   t2.`comment` as "comment",
                   t2.testStep as "testStep",
                   t2.expectedResult as "expectedResult",
                   t2.preconditions as "preconditions",
                   t2.dataRequirements as "dataRequirements",
                   t2.checkPoint as "checkPoint",
               </otherwise>
           </choose>
           t2.demandResourceID as "demandResourceID",
           t2.reviewStatus as "reviewStatus",
           t2.caseEditId as "caseEditId",
           t2.testEnviroment as "testEnviroment",
           t2.testtaskResourceID as "testtaskResourceID",
           t2.comments1,
           t2.comments2,
           t2.comments3,
           t2.comments4,
           t2.comments5,
           t2.comments6,
           t2.comments7,
           t2.comments8,
           t2.comments9,
           t2.comments10,
           t2.`committed`,
           t2.testsystem,
           t2.testsystemResourceID as "testsystemResourceID",
           t2.systemmodule,
           t2.systemmoduleResourceID as "systemmoduleResourceID",
           t2.projectgroupResourceID as "projectgroupResourceID",
           t2.leadsource,
           t2.trade,
           t2.identitynumber,
           t2.tradeFlowCaseFolderId as "tradeFlowCaseFolderId",
           t2.name,
           t2.testProjectId as "testProjectId",
           t2.isHaveScript as "isHaveScript",
           t2.tradeFlowId as "tradeFlowId",
           t2.numCase as "numCase",
           t2.systemType as "systemType",
           t2.status,
           t2.scriptInfoID as "scriptInfoID",
           t2.testMode as "testMode",
           t2.versionResourceID as "versionResourceID",
           t2.timeout,
           t2.sceneCase as "sceneCase",
           t1.tradeResourceID as "tradeResourceID",
           t1.quoteCaseResourceID as "quoteCaseResourceID",
           t1.testPlanResourceID as "testPlanResourceID",
           t1.caseResult as "caseResult",
           t1.executor,
           t1.testCaseResourceID as "testCaseResourceID",
           t1.recordCount as "recordCount",
           (SELECT max( t3.resourceID ) from me_caseresultrecord t3 WHERE t1.quoteCaseResourceID = t3.testCaseQuoteResourceID ) as "newResultRecordResourceID",
           (SELECT max( t3.executeTime ) from me_caseresultrecord t3 WHERE t1.quoteCaseResourceID = t3.testCaseQuoteResourceID) as "executeTime"
           FROM
           (
           SELECT
           a.resourceID AS quoteCaseResourceID,
           a.testPlanResourceID,
           a.caseResult,
           a.testCaseResourceID,
           a.tradeResourceID,
           a.executor,
           (select count( b.resourceID ) from me_caseresultrecord b WHERE a.resourceID = b.testCaseQuoteResourceID ) as recordCount
           FROM
           me_testcasequote a
           where a.testTaskResourceID = #{taskResourceID}
           <if test="testCaseStates != null and testCaseStates.size()>0 " >
               and a.caseResult in
               <foreach collection="testCaseStates" item="state" open="(" close=")" separator=",">
                   #{state}
               </foreach>
           </if>
           ) t1
           LEFT JOIN
           ds_testcase t2
           ON t2.resourceID = t1.testCaseResourceID
           <if test="tagResourceIDs != ''">
               left JOIN tagrelation ta on t2.resourceID = ta.relationResourceID
           </if>
           where t2.tradeResourceID in
           <foreach collection="tradeResIDs" item="tradeResID" open="(" close=")" separator=",">
               #{tradeResID}
           </foreach>
           <if test="tagResourceIDs != ''">
               and ta.tagResourceID in
               <foreach collection="tagResourceIDs.split(',')" item="tagResourceID" open="("
                        close=")" separator=",">
                   #{tagResourceID}
               </foreach>
           </if>
           <if test="caseTypeList != null and caseTypeList.size()>0">
               and caseType in
               <foreach collection="caseTypeList" item="caseType" open="(" close=")" separator=",">
                   #{caseType}
               </foreach>
           </if>
            <if test="caseName != null and caseName != ''">
                    and
                    t2.name LIKE CONCAT('%',#{caseName},'%')
                </if>
           <if test="caseId != null and caseId != ''">
               and
               t2.caseId LIKE CONCAT('%',#{caseId},'%')
           </if>
           ) tt1
           WHERE
           1 = 1
           <if test="timeStart != null">
               AND tt1.`executeTime` BETWEEN #{timeStart} AND #{timeEnd}
           </if>
           <choose>
               <when test="_databaseId == 'kingwow'">
                   order by tt1."caseId"
               </when>
               <otherwise>
                   order by tt1.`caseId`
               </otherwise>
           </choose>

           limit #{begin}, #{pageSize}
    </select>

    <select id="findCaseQuoteListByTradeInformationCount" resultType="java.lang.Integer">
        select count(*) from (
        SELECT
        tt1.*,
        (SELECT COUNT( tt2.resourceID ) from me_caseresultrecordfile tt2 WHERE  tt1.newResultRecordResourceID = tt2.caseResultRecordResourceID) as newRecordFilesCount
        FROM
        (
        SELECT
        t2.resourceID,
        (SELECT max( t3.resourceID ) from me_caseresultrecord t3 WHERE t1.quoteCaseResourceID = t3.testCaseQuoteResourceID) as newResultRecordResourceID
        <if test="timeStart != null">
            ,(SELECT max( t3.executeTime ) from me_caseresultrecord t3 WHERE t1.quoteCaseResourceID = t3.testCaseQuoteResourceID) as executeTime
        </if>
        FROM
        (
        SELECT
        a.resourceID AS quoteCaseResourceID,
        a.testCaseResourceID
        FROM
        me_testcasequote a
        where a.testTaskResourceID = #{taskResourceID}
        <if test="testCaseStates != null and testCaseStates.size()>0 ">
            and a.caseResult in
            <foreach collection="testCaseStates" item="state" open="(" close=")" separator=",">
                #{state}
            </foreach>
        </if>
        ) t1
        LEFT JOIN
        ds_testcase t2
        ON t2.resourceID = t1.testCaseResourceID
        <if test="tagResourceIDs != ''">
            left JOIN tagrelation ta on t2.resourceID = ta.relationResourceID
        </if>
        where t2.tradeResourceID in
        <foreach collection="tradeResIDs" item="tradeResID" open="(" close=")" separator=",">
            #{tradeResID}
        </foreach>
        <if test="tagResourceIDs != ''">
            and ta.tagResourceID in
            <foreach collection="tagResourceIDs.split(',')" item="tagResourceID" open="("
                     close=")" separator=",">
                #{tagResourceID}
            </foreach>
        </if>
        <if test="caseTypeList != null and caseTypeList.size()>0">
            and t2.caseType in
            <foreach collection="caseTypeList" item="caseType" open="(" close=")" separator=",">
                #{caseType}
            </foreach>
        </if>
         <if test="caseName != null and caseName != ''">
              and t2.name LIKE CONCAT('%',#{caseName},'%')
         </if>
        ) tt1
        WHERE
        1 = 1
        <if test="timeStart != null">
            AND tt1.executeTime BETWEEN #{timeStart} AND #{timeEnd}
        </if>
        group by resourceID,newResultRecordResourceID
        ) tc 
    </select>
    
    <select id="findBugsByCaseResourceID" resultType = "int">
    	SELECT count(*) FROM at_defect WHERE testCaseResourceID = #{caseRid}
    </select>
    
     <select id="findByName" resultMap="testSystemMap">
        SELECT
         <include refid="testSystemColumnList"/>
        FROM at_testsystem
        WHERE name= #{name}
    </select>

	<select id="findRelevanceSystemByDemandResourceID" resultType = "int">
		SELECT COUNT(resourceID) num FROM ds_demandtestsystem WHERE demandResourceID = #{demandResourceID}
    </select>

    <select id="findAllByDataAuth" resultType="com.jettech.model.TestSystem">
        select <include refid="testSystemColumnList"/> from at_testsystem
    </select>

    <delete id="deleteTradeFolderBySystem">
        delete from trade_folder where test_system_id = #{resourceID}
    </delete>

    <select id="countQuoteRecord" resultType="java.util.Map">
        SELECT
        testCaseQuoteResourceID as "testCaseQuoteResourceID",
        count( * ) as "recordCount"
        FROM
        me_caseresultrecord
        WHERE
        testCaseQuoteResourceID IN
        <foreach collection="quoteResourceIDList" item="quoteResourceID" open="(" close=")" separator=",">
            #{quoteResourceID}
        </foreach>
        <if test="timeStart != null">
            AND executeTime BETWEEN #{timeStart} AND #{timeEnd}
        </if>
        GROUP BY
        testCaseQuoteResourceID
    </select>

    <select id="countCaseResultFile" resultType="java.util.Map">
        SELECT
        caseResultRecordResourceID as "caseResultRecordResourceID",
        count( * ) as "newRecordFilesCount"
        FROM
        me_caseresultrecordfile
        WHERE
        caseResultRecordResourceID IN
        <foreach collection="recordResourceIDList" item="recordResourceID" open="(" close=")" separator=",">
            #{recordResourceID}
        </foreach>
        GROUP BY
        caseResultRecordResourceID
    </select>
</mapper>