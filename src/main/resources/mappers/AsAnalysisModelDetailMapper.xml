<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.AsAnalysisModelDetailMapper">

    <select id="reviewRecords" resultType="com.jettech.vo.AsAnalysisModelDetailVO">
        select
            d.model_id modelId,
            d.update_date updateDate,
            d.type,d.remarks,
            d.status,
            j.userName updateUser
        from as_analysis_model_detail d
        inner join jettechuser j on j.number = d.create_user
        <where>
            <if test="id!= null and id!=''" >
                AND d.model_id = #{id}
            </if>
        </where>
        order by d.update_date desc
    </select>
</mapper>