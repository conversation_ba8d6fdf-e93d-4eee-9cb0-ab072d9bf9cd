<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITradeTestManagerMapper">
    <resultMap type="com.jettech.model.TradeAndTestManager" id="tradeTestManagerMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>
        <result property="belongTestManager" column="belongTestManager"/>
        <result property="tradeResourceID" column="status"/>
    </resultMap>
    <sql id="tradeColumnList">
        id, version, createTime,  editTime, createUser,editUser, resourceID, belongTestManager,tradeResourceID
       
    </sql>

    
   
   

    
    <select id="findByTradeResourceID" resultMap="tradeTestManagerMap">
        select
        <include refid="tradeColumnList"/>
        from at_trade_belongTestManager where tradeResourceID = #{tradeName}
    </select>
    <select id="findByTradeResourceIDIn" resultMap="tradeTestManagerMap">
        select
        <include refid="tradeColumnList"/>
        from at_trade_belongTestManager where tradeResourceID in 
        <foreach collection="ridList" item="rid" open="(" close=")" separator=",">
            #{rid}
        </foreach>
    </select>
   
</mapper>
