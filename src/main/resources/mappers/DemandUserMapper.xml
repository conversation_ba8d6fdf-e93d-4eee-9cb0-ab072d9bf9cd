<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IDemandUserMapper">
    <resultMap type="com.jettech.model.DemandUser" id="demandUserMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>
        <result property="userResourceID" column="userResourceID"/>
        <result property="demandResourceID" column="demandResourceID"/>
    </resultMap>
	<sql id="demandUserColumnList">
		id, version, createTime, editTime, createUser, editUser, resourceID, userResourceID, demandResourceID
	</sql>
	<sql id="demandUserColumnList_du">
		du.id, du.version, du.createTime, du.editTime, du.createUser, du.editUser, du.resourceID, du.userResourceID, du.demandResourceID
	</sql>

    <select id="findUserNotINDemandUser"  resultType = "java.util.Map">
		SELECT
			u.id,
			u.resourceID,
			u.userName,
			u.number AS "userNumber"
		FROM
			jettechUser u
		WHERE
			u.resourceID NOT IN ( SELECT p.userResourceID FROM ds_demanduser p WHERE p.demandResourceID = #{demandResourceID} )
	</select>

    <select id="findUserByDemandResourceID"  resultType = "java.util.Map">
		SELECT
			u.id,
			u.resourceID as "resourceID",
			u.userName as "userName",
			u.number AS "userNumber",
			p.role as "role",
			p.resourceID as "duResourceID"
		FROM
			ds_demanduser p
			INNER JOIN jettechUser u ON p.userResourceID = u.resourceID
		WHERE
			p.demandResourceID = #{demandResourceID}
	</select>

	 <select id="findByUserResourceIDAndDemandResourceID"   resultMap = "demandUserMap">
		SELECT
			<include refid="demandUserColumnList"/>
		FROM
			ds_demanduser
		WHERE
			demandResourceID = #{demandResourceID}
		 <if test="userResourceID!=null and userResourceID!='null' and userResourceID!=''">
			 AND userResourceID=#{userResourceID}
		 </if>
	</select>
	<select id="findBydemandResourceID" resultMap="demandUserMap">
		SELECT
		<include refid="demandUserColumnList"/>
		FROM
			ds_demanduser
		WHERE
			demandResourceID = #{demandResourceID}
	</select>
	<!-- 根据人员和需求查询 -->
	<select id="findUserResIDAnddemandResIDList" resultMap="demandUserMap">
		SELECT <include refid="demandUserColumnList"/> FROM ds_demanduser WHERE userResourceID = #{userResID} AND demandResourceID IN 
		<foreach collection="demandResIDList" item="demandResourceID" open="(" close=")" separator=",">
            #{demandResourceID}
        </foreach>
	</select>

	<select id="findDemandUserBydemandResourceIDs" resultMap="demandUserMap">
		select <include refid="demandUserColumnList"/> from ds_demanduser where demandResourceID in
		<foreach collection="demandResourceID" item="demandResourceID" open="(" close=")" separator=",">
			#{demandResourceID}
		</foreach>
	</select>
	
	<!-- 根据人员，需求ResourceID和需求类型查询  -->
	<select id="findUserResIDAndDemandResIDListAndDemandType" resultMap="demandUserMap">
        SELECT
			<include refid="demandUserColumnList_du"/>
		FROM
			ds_demanduser du
		LEFT JOIN ds_demand d ON d.resourceID = du.demandResourceID
		WHERE
			du.userResourceID = #{userResID}
		AND du.demandResourceID IN
		<foreach collection="demandResIDList" item="demandResourceID" open="(" close=")" separator=",">
            #{demandResourceID}
        </foreach>
        AND d.type != #{type}
		AND d.typename != #{typeName}
	</select>

	<select id="findByUserResourceID" resultMap="demandUserMap">
		select <include refid="demandUserColumnList"/>
		from ds_demanduser
		where userResourceID = #{userResourceID}
	</select>

	<select id="findUserByDemandRIDAndRole"  resultType = "java.lang.String">
		SELECT
			u.userName as "userName"
		FROM
			ds_demanduser p
			INNER JOIN jettechUser u ON p.userResourceID = u.resourceID
		WHERE
			p.demandResourceID = #{demandResourceID}
		AND
			P.role LIKE concat(concat('%',#{role}),'%')
	</select>
    <select id="findDemandUserMapByDemandResourceIDAndUserResourceID" resultMap="demandUserMap">
		SELECT <include refid="demandUserColumnList"/> from ds_demanduser WHERE demandResourceID = #{demandResourceID} AND userResourceID = #{userResourceID}
	</select>
	
	<select id="findByTestTaskResourceID" resultMap="demandUserMap">
		SELECT
		<include refid="demandUserColumnList_du"/>
		FROM
			ds_demanduser du
			JOIN testtask t ON t.demandResourceID=du.demandResourceID
		WHERE
			t.resourceID=#{testTaskResourceID}
	</select>

    <select id="findBydemandResourceIDAndUserGroupResourceIDs" resultMap="demandUserMap">
		SELECT
		<include refid="demandUserColumnList_du"/>
		FROM
			ds_demanduser du
		LEFT JOIN
				usergroupuser uu
			 ON du.userResourceID = uu.userResourceID
		WHERE
			 du.demandResourceID = #{demandResourceID}
		 AND
		uu.userGroupResourceID IN
		<foreach collection="userGroupResourceIDs" item="userGroupResourceID" open="(" close=")" separator=",">
			#{userGroupResourceID}
		</foreach>
	</select>

	<select id="findByTestManagerResourceIDAndDemandResourceIDAndRole" resultMap="demandUserMap">
		SELECT
		<include refid="demandUserColumnList"/>
		FROM
			ds_demanduser
		WHERE
			demandResourceID = #{demandResourceID}
			AND userResourceID = #{oldTestManagerResourceID}
			AND role = #{roleName}
	</select>
</mapper>
