<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IWikiPageCommentMapper">

    <resultMap id="wikiPageCommentMap" type="com.jettech.model.WikiPageComment">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="parentResourceId" column="PARENT_RESOURCE_ID"/>
        <result property="pageResourceId" column="PAGE_RESOURCE_ID"/>
        <result property="comment" column="COMMENT"/>
        <result property="userName" column="USER_NAME"/>

    </resultMap>

    <!--根据页面ID获取评论-->
    <select id="listCommentsByPageId" resultMap="wikiPageCommentMap">
        SELECT WPC.*, SU.userName AS USER_NAME FROM WIKI_PAGE_COMMENT WPC
        LEFT JOIN jettechuser AS SU ON SU.`number` = WPC.createUser
        WHERE PAGE_RESOURCE_ID =  #{pageResourceId} ORDER BY WPC.createTime
    </select>

</mapper>
