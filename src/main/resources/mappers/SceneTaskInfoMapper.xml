<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ISceneTaskInfoMapper">
    <resultMap id="BaseResultMap" type="com.jettech.model.SceneTaskInfo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="editTime" jdbcType="TIMESTAMP" property="editTime"/>
        <result column="editUser" jdbcType="VARCHAR" property="editUser"/>
        <result column="resourceID" jdbcType="BIGINT" property="resourceID"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="testTaskResourceID" jdbcType="BIGINT" property="testTaskResourceID"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="testCaseResourceIDs" jdbcType="LONGVARCHAR" property="testCaseResourceIDs"/>
        <result column="params" jdbcType="VARCHAR" property="params"/>
        <result column="envIds" jdbcType="VARCHAR" property="envIds"/>
        <result column="timeStart" jdbcType="TIMESTAMP" property="timeStart"/>
        <result column="timeEnd" jdbcType="TIMESTAMP" property="timeEnd"/>
        <result column="priorityLevel" jdbcType="INTEGER" property="priorityLevel"/>
        <result column="parentId" jdbcType="INTEGER" property="parentId"/>
        <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
        <result column="caseTotal" jdbcType="INTEGER" property="caseTotal"/>
        <result column="undoTotal" jdbcType="INTEGER" property="undoTotal"/>
        <result column="doingTotal" jdbcType="INTEGER" property="doingTotal"/>
        <result column="successTotal" jdbcType="INTEGER" property="successTotal"/>
        <result column="failTotal" jdbcType="INTEGER" property="failTotal"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, createTime, createUser, editTime, editUser, resourceID, `version`, testTaskResourceID, name, `status`, testCaseResourceIDs, params, envIds, timeStart, timeEnd, priorityLevel, parentId, `remark`, caseTotal, undoTotal, doingTotal, successTotal, failTotal
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jettech.model.SceneTaskInfo" useGeneratedKeys="true">
        insert into me_scenetaskinfo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                createTime,
            </if>
            <if test="createUser != null">
                createUser,
            </if>
            <if test="editTime != null">
                editTime,
            </if>
            <if test="editUser != null">
                editUser,
            </if>
            <if test="resourceID != null">
                resourceID,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="testTaskResourceID != null">
                testTaskResourceID,
            </if>
            <if test="name != null">
                `name`,

            </if>
            <if test="status != null">
                status,
            </if>
            <if test="params != null">
                params,
            </if>
            <if test="envIds != null">
                envIds,
            </if>
            <if test="startTime != null">
                startTime,
            </if>
            <if test="endTime != null">
                endTime,
            </if>
            <if test="priorityLevel != null">
                priorityLevel,
            </if>
            <if test="parentId != null">
                parentId,
            </if>
            <if test="totalCase != null">
                totalCase,
            </if>
            <if test="totalSuccess != null">
                totalSuccess,
            </if>
            <if test="totalFailed != null">
                totalFailed,
            </if>
            <if test="totalNonExecution != null">
                totalNonExecution,
            </if>
            <if test="totalRunning != null">
                totalRunning,
            </if>
            <if test="testCaseResourceIDs != null">
                testCaseResourceIDs,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="editTime != null">
                #{editTime,jdbcType=TIMESTAMP},
            </if>
            <if test="editUser != null">
                #{editUser,jdbcType=VARCHAR},
            </if>
            <if test="resourceID != null">
                #{resourceID,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="testTaskResourceID != null">
                #{testTaskResourceID,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="params != null">
                #{params,jdbcType=VARCHAR},
            </if>
            <if test="envIds != null">
                #{envIds,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="priorityLevel != null">
                #{priorityLevel,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=INTEGER},
            </if>
            <if test="totalCase != null">
                #{totalCase,jdbcType=INTEGER},
            </if>
            <if test="totalSuccess != null">
                #{totalSuccess,jdbcType=INTEGER},
            </if>
            <if test="totalFailed != null">
                #{totalFailed,jdbcType=INTEGER},
            </if>
            <if test="totalNonExecution != null">
                #{totalNonExecution,jdbcType=INTEGER},
            </if>
            <if test="totalRunning != null">
                #{totalRunning,jdbcType=INTEGER},
            </if>
            <if test="testCaseResourceIDs != null">
                #{testCaseResourceIDs,jdbcType=LONGVARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
</mapper>