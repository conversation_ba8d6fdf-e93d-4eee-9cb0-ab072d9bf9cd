<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.DemandAccessoryMapper">
  <resultMap id="BaseResultMap" type="com.jettech.model.DemandAccessory">
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="createTime" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="createUser" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="editTime" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="editUser" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="version" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="resourceID" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="defectResourceID" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="extname" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="size" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="uploadUserName" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="path" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.jettech.model.DemandAccessory">
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="createTime" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="createUser" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="editTime" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="editUser" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="version" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="resourceID" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="defectResourceID" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="extname" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="size" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="uploadUserName" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="path" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="comments" javaType="java.lang.String" jdbcType="LONGVARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, createTime, createUser, editTime, editUser, version, resourceID, defectResourceID, 
    name, extname, size, uploadUserName, path
  </sql>
  <sql id="Blob_Column_List">
    comments
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ds_demandaccessory
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ds_demandaccessory
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.jettech.model.DemandAccessory">
    insert into ds_demandaccessory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        createTime,
      </if>
      <if test="createUser != null">
        createUser,
      </if>
      <if test="editTime != null">
        editTime,
      </if>
      <if test="editUser != null">
        editUser,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="resourceID != null">
        resourceID,
      </if>
      <if test="defectResourceID != null">
        defectResourceID,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="extname != null">
        extname,
      </if>
      <if test="size != null">
        size,
      </if>
      <if test="uploadUserName != null">
        uploadUserName,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="comments != null">
        comments,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="editTime != null">
        #{editTime,jdbcType=TIMESTAMP},
      </if>
      <if test="editUser != null">
        #{editUser,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="resourceID != null">
        #{resourceID,jdbcType=BIGINT},
      </if>
      <if test="defectResourceID != null">
        #{defectResourceID,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="extname != null">
        #{extname,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="uploadUserName != null">
        #{uploadUserName,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jettech.model.DemandAccessory">
    update ds_demandaccessory
    <set>
      <if test="createTime != null">
        createTime = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        createUser = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="editTime != null">
        editTime = #{editTime,jdbcType=TIMESTAMP},
      </if>
      <if test="editUser != null">
        editUser = #{editUser,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="resourceID != null">
        resourceID = #{resourceID,jdbcType=BIGINT},
      </if>
      <if test="defectResourceID != null">
        defectResourceID = #{defectResourceID,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="extname != null">
        extname = #{extname,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        size = #{size,jdbcType=VARCHAR},
      </if>
      <if test="uploadUserName != null">
        uploadUserName = #{uploadUserName,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.jettech.model.DemandAccessory">
    update ds_demandaccessory
    set createTime = #{createTime,jdbcType=TIMESTAMP},
      createUser = #{createUser,jdbcType=VARCHAR},
      editTime = #{editTime,jdbcType=TIMESTAMP},
      editUser = #{editUser,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      resourceID = #{resourceID,jdbcType=BIGINT},
      defectResourceID = #{defectResourceID,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      extname = #{extname,jdbcType=VARCHAR},
      size = #{size,jdbcType=VARCHAR},
      uploadUserName = #{uploadUserName,jdbcType=VARCHAR},
      path = #{path,jdbcType=VARCHAR},
      comments = #{comments,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jettech.model.DemandAccessory">
    update ds_demandaccessory
    set createTime = #{createTime,jdbcType=TIMESTAMP},
      createUser = #{createUser,jdbcType=VARCHAR},
      editTime = #{editTime,jdbcType=TIMESTAMP},
      editUser = #{editUser,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      resourceID = #{resourceID,jdbcType=BIGINT},
      defectResourceID = #{defectResourceID,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      extname = #{extname,jdbcType=VARCHAR},
      size = #{size,jdbcType=VARCHAR},
      uploadUserName = #{uploadUserName,jdbcType=VARCHAR},
      path = #{path,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>