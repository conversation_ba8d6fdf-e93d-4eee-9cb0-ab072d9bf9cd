<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.CommonSqlMapper">
    <sql id="_regexp_not_like">
        <choose>
            <when test="_databaseId == 'kingwow' or _databaseId == 'dm' or @com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                regexp_like(${colName}, '${regexpStr}') = false
            </when>
            <otherwise>
                ${colName} regexp '${regexpStr}' = false
            </otherwise>
        </choose>
    </sql>
</mapper>

