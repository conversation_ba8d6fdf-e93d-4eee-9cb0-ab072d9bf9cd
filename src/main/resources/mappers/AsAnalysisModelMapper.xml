<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.AsAnalysisModelMapper">
    <select id="selectPageByDto" resultType="com.jettech.vo.AsAnalysisModelVO">
        select
            m.id,m.trade_resource_id tradeResourceId,m.name,m.case_num caseNum,
            m.repeated_num repeatedNum,m.status,m.content,j.userName createUser,m.create_date createDate,
            j1.userName reviewUser,m.review_date reviewDate,u.userName updateUser,u.update_date updateDate,
            s.name systemName,t.name tradeName
        from as_analysis_model m
        <!--
        <choose>
            <when test="asAnalysisModelQueryDTO.reviewUser!= null and asAnalysisModelQueryDTO.reviewUser!=''">
                inner
            </when>
            <otherwise>
                left
            </otherwise>
        </choose>
         join ( select d.model_id,d.update_date,j.userName from as_analysis_model_detail d
            inner join jettechuser j on j.number = d.create_user
            <if test="asAnalysisModelQueryDTO.reviewUser!= null and asAnalysisModelQueryDTO.reviewUser!=''" >
                AND j.userName like CONCAT(CONCAT('%', #{asAnalysisModelQueryDTO.reviewUser}), '%')
            </if>
            &lt;!&ndash;必须是审核过的状态&ndash;&gt;
            <where>
                d.status in(2,3) and d.type = 0
            </where>
            having 1
            order by d.create_date desc
        ) r on r.model_id = m.id
        -->
        <choose>
            <when test="asAnalysisModelQueryDTO.updateUser!= null and asAnalysisModelQueryDTO.updateUser!=''">
                inner
            </when>
            <otherwise>
                left
            </otherwise>
        </choose>
         join ( select d.model_id,d.update_date,j.userName from as_analysis_model_detail d
            inner join jettechuser j on j.number = d.create_user  and d.type = 1
            <if test="asAnalysisModelQueryDTO.updateUser!= null and asAnalysisModelQueryDTO.updateUser!=''" >
                AND j.userName like CONCAT(CONCAT('%', #{asAnalysisModelQueryDTO.updateUser}), '%')
            </if>
            having 1
            order by d.create_date desc
        ) u on u.model_id = m.id
        left join at_trade t on t.resourceID = m.trade_resource_id
        left join at_testsystem s on s.resourceID = t.testSystemResourceID
        left join jettechuser j on j.number = m.create_user
        left join jettechuser j1 on j1.number = m.review_user
        <if test="asAnalysisModelQueryDTO.createUser!= null and asAnalysisModelQueryDTO.createUser!=''" >
            AND j.userName like CONCAT(CONCAT('%', #{asAnalysisModelQueryDTO.createUser}), '%')
        </if>
        <where>
            <if test="asAnalysisModelQueryDTO.tradeResourceIdList !=null and asAnalysisModelQueryDTO.tradeResourceIdList.size()>0">
                and m.trade_resource_id in
                <foreach collection="asAnalysisModelQueryDTO.tradeResourceIdList" item="tradeResourceId" open="(" separator="," close=")">
                    #{tradeResourceId}
                </foreach>
            </if>
            <if test="asAnalysisModelQueryDTO.content!= null and asAnalysisModelQueryDTO.content!=''" >
                AND m.content like CONCAT(CONCAT('%', #{asAnalysisModelQueryDTO.content}), '%')
            </if>
            <if test="asAnalysisModelQueryDTO.name!= null and asAnalysisModelQueryDTO.name!=''" >
                AND m.name like CONCAT(CONCAT('%', #{asAnalysisModelQueryDTO.name}), '%')
            </if>
            <if test="asAnalysisModelQueryDTO.status!= null" >
                AND m.status = #{asAnalysisModelQueryDTO.status}
            </if>
        </where>
        group by m.id
        order by m.id desc
    </select>

    <select id="reviewPage" resultType="com.jettech.vo.AsAnalysisModelVO">
        select
            m.id,m.trade_resource_id tradeResourceId,m.name,m.case_num caseNum,
            m.repeated_num repeatedNum,m.status,m.content,d.userName updateUser,d.update_date updateDate,
            s.name systemName,t.name tradeName
        from as_analysis_model m
        inner join (
            select
                ad.id id,ad.model_id,ad.update_user,
                ad.update_date,ad.status,j.userName
            from
              as_analysis_model_detail ad
            <choose>
                <when test="updateUser!= null and updateUser!=''">
                    inner
                </when>
                <otherwise>
                    left
                </otherwise>
            </choose>
            join jettechuser j on j.number = ad.update_user
            <if test="updateUser!= null and updateUser!=''" >
                AND j.userName like CONCAT(CONCAT('%', #{updateUser}), '%')
            </if>
            where ad.type = 0
                <if test="status!= null and status!=''" >
                    AND ad.status = #{status}
                </if>
                having 1
                order by ad.update_date desc
         ) d on d.model_id = m.id and d.status = m.status
        left join at_trade t on t.resourceID = m.trade_resource_id
        left join at_testsystem s on s.resourceID = t.testSystemResourceID
        <where>
            <if test="name!= null and name!=''" >
                AND m.name like CONCAT(CONCAT('%', #{name}), '%')
            </if>
            <if test="number!= null and number!=''" >
                AND find_in_set(#{number},m.review_user)
            </if>
            <if test="status!= null and status!=''" >
                AND m.status = #{status}
            </if>
        </where>
        group by m.id
        order by m.id desc
    </select>

    <select id="attachmentPage" resultType="com.jettech.vo.AsAnalysisModelVO">
         select
            m.id,m.trade_resource_id tradeResourceId,m.name,m.case_num caseNum,
            m.repeated_num repeatedNum,m.status,m.content,j.userName createUser,m.create_date createDate,
            r.userName reviewUser,r.update_date reviewDate,u.userName updateUser,u.update_date updateDate,
            s.name systemName,t.name tradeName
        from as_analysis_model m
        left join ( select d.model_id,d.update_date,j.userName from as_analysis_model_detail d
        inner join jettechuser j on j.number = d.create_user  and d.type = 0
        order by d.create_user desc
        ) r on r.model_id = m.id
        left join ( select d.model_id,d.update_date,j.userName from as_analysis_model_detail d
        inner join jettechuser j on j.number = d.create_user  and d.type = 1
        order by d.create_user desc
        ) u on u.model_id = m.id
        left join at_trade t on t.resourceID = m.trade_resource_id
        left join at_testsystem s on s.resourceID = t.testSystemResourceID
        inner join jettechuser j on j.number = m.create_user
        <if test="attachmentQueryDTO.createUser!= null and attachmentQueryDTO.createUser!=''" >
            AND j.userName like CONCAT(CONCAT('%', #{attachmentQueryDTO.createUser}), '%')
        </if>
        <where>
            <if test="status!= null and status!=''" >
                AND m.status = #{status}
            </if>
            <if test="attachmentQueryDTO.tradeResourceIdList !=null and attachmentQueryDTO.tradeResourceIdList.size()>0">
                and m.trade_resource_id in
                <foreach collection="attachmentQueryDTO.tradeResourceIdList" item="tradeResourceId" open="(" separator="," close=")">
                    #{tradeResourceId}
                </foreach>
            </if>
            <if test="attachmentQueryDTO.testPlanResourceID!= null and attachmentQueryDTO.testPlanResourceID!=''" >
                and m.trade_resource_id in(select  distinct  tt.tradeResourceID
                from testtasktrade tt
                inner join testtask t on t.resourceID = tt.testTaskResourceID and t.testPlanResourceID = #{attachmentQueryDTO.testPlanResourceID} and t.`type` in(1,2)
                )
            </if>
            <if test="attachmentQueryDTO.content!= null and attachmentQueryDTO.content!=''" >
                AND m.content like CONCAT(CONCAT('%', #{attachmentQueryDTO.content}), '%')
            </if>
            <if test="attachmentQueryDTO.name!= null and attachmentQueryDTO.name!=''" >
                AND m.name like CONCAT(CONCAT('%', #{attachmentQueryDTO.name}), '%')
            </if>
        </where>
        order by m.create_date
    </select>

    <update id="updateRepeatedOrCase">
        update as_analysis_model
        <set>
            <if test="dto.caseNum!= null and dto.caseNum!=''" >
                case_num = #{dto.caseNum},
            </if>
            <if test="dto.repeatedNum!= null and dto.repeatedNum!=''" >
                repeated_num = repeated_num + 1,
            </if>
        </set>
        <where>
            <if test="dto.id!= null" >
                AND id = #{id}
            </if>
        </where>
    </update>
</mapper>