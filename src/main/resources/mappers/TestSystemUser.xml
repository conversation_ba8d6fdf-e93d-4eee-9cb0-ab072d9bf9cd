<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestSystemUserMapper">
	<resultMap type="com.jettech.model.TestSystemUser" id="testSystemUserMap">
         <id property="id" column="id"/>
         <result property="version" column="version"/>
         <result property="createTime" column="createTime"/>
         <result property="editTime" column="editTime"/>
         <result property="createUser" column="createUser"/>
         <result property="editUser" column="editUser"/> 
         <result property="resourceID" column="resourceID"/>
         
         <result property="userResourceID" column="userResourceID"/>
         <result property="testSystemResourceID" column="testSystemResourceID"/>
    </resultMap>

	<sql id="testSystemUserColumnList">
		id,version, createTime,  editTime, createUser,editUser, resourceID,
		userResourceID,testSystemResourceID
	</sql>
    <sql id="testSystemUserColumnList_tsu">
        tsu.id,tsu.version, tsu.createTime,  tsu.editTime, tsu.createUser,tsu.editUser, tsu.resourceID,
        tsu.userResourceID,tsu.testSystemResourceID
    </sql>

    <!--根据被测系统查询关联关系-->
    <select id="findbyTestSystemResourceID" resultMap="testSystemUserMap">
        SELECT
        <include refid="testSystemUserColumnList"/>
        FROM ds_testsystem_user WHERE testSystemResourceID = #{testSystemResourceID}
    </select>
    
    
        <!--根据被测系统查询关联关系-->
    <select id="findUserResourceID" resultMap="testSystemUserMap">
        SELECT <include refid="testSystemUserColumnList"/>
        FROM ds_testsystem_user WHERE userResourceID in
        <foreach collection="userResourceID" item="userResourceID" open="(" close=")" separator=",">
			#{userResourceID}
	    </foreach>
    </select>
    <select id="isOperationAuthority" resultMap="testSystemUserMap">
        select
        <include refid="testSystemUserColumnList_tsu"/>
        from ds_testsystem_user tsu
        INNER JOIN jettechuser u on u.resourceID = tsu.userResourceID
        where
        u.number = #{userNumber}
    </select>
    <select id = "findbyTestSystemResourceIDsIn" resultMap="testSystemUserMap">
    	SELECT <include refid="testSystemUserColumnList"/> FROM ds_testsystem_user t WHERE t.testSystemResourceID in 
    	<foreach collection="testSystemResourceIDs" item="testSystemResourceID" open="(" close=")" separator=",">
			#{testSystemResourceID}
	    </foreach>
    </select>

    <select id="findByTestSystemResourceIDAndUserNumber" resultMap="testSystemUserMap">
        select
        <include refid="testSystemUserColumnList" />
        from ds_testsystem_user tu
        where tu.testSystemResourceID = #{testSystemResourceID}
        and tu.userResourceID = #{userResourceID}
    </select>

    <select id="findNotRelateUser" resultType="java.util.Map">
        SELECT
        j.resourceID as "resourceID",
        j.userName as "userName",
        d.resourceID AS "deptResourceID",
        d.name AS "deptName",
        GROUP_CONCAT(DISTINCT ug.name) AS "roleName"
        FROM
        jettechuser j
        LEFT JOIN usergroupuser g ON j.resourceID = g.userResourceID
        LEFT JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
        LEFT JOIN dept d ON j.deptResourceID = d.resourceID
        WHERE
        j.resourceID NOT IN ( SELECT u.userResourceID FROM ds_testsystem_user u WHERE 
       u.testSystemResourceID=#{testSystemResourceID})
         <if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
         	AND g.userGroupResourceID in
             <foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
                 #{userGroupReourceID}
             </foreach>
         </if>
        <if test="name != null and name != '' ">
            AND	j.userName LIKE concat(concat('%',#{name}),'%')
        </if>
        <if test="deptName != null and deptName != '' ">
            AND	d.name LIKE concat(concat('%',#{deptName}),'%')
        </if>
        GROUP BY j.resourceID,d.resourceID,j.userName,d.name
    </select>

    <select id="findRelatedUser" resultType="java.util.Map">
        SELECT
        DISTINCT
        j.resourceID as "resourceID",
        j.userName as "userName",
        d.resourceID AS "deptResourceID",
        d.name AS "deptName",
        GROUP_CONCAT(DISTINCT ug.name) AS "roleName"

        FROM
        ds_testsystem_user u
        LEFT JOIN jettechuser j ON j.resourceID = u.userResourceID
        LEFT JOIN usergroupuser g ON u.userResourceID = g.userResourceID
        LEFT JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
        LEFT JOIN dept d ON j.deptResourceID = d.resourceID
        WHERE
        u.testSystemResourceID = #{testSystemResourceID}
        <if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
            AND g.userGroupResourceID in
            <foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
                #{userGroupReourceID}
            </foreach>
        </if>
        <if test="name != null and name != '' ">
            AND	j.userName LIKE concat(concat('%',#{name}),'%')
        </if>
        <if test="deptName != null and deptName != '' ">
            AND	d.name LIKE concat(concat('%',#{deptName}),'%')
        </if>
        GROUP BY j.resourceID,d.resourceID,j.userName,d.name
    </select>

    <delete id="deletebytestSystemtouser">
        delete  from ds_testsystem_user
        WHERE
        testSystemResourceID=#{testSystemResourceID}
        AND  userResourceID IN
        <foreach collection="userResourceIDs" item="userResourceID" open="(" close=")" separator=",">
            #{userResourceID}
        </foreach>
    </delete>

   <select id="findRelatedUserOfMultipleSystem" resultType="java.util.Map">
       select DISTINCT
        j.resourceID as "resourceID",
        j.userName as "userName",
        d.resourceID AS "deptResourceID",
        d.name AS "deptName",
        GROUP_CONCAT(DISTINCT ug.name) AS "roleName"

        from (
		select u.userResourceID from ds_testsystem_user u  where 1=1 
		 <if test="testSystemResourceIDs != null and testSystemResourceIDs.size > 0">
           AND u.testSystemResourceID in
        <foreach collection="testSystemResourceIDs" index="index" item="testSystemResourceID" open="(" separator="," close=")">
            #{testSystemResourceID}
        </foreach>
        </if>
		GROUP BY u.userResourceID  having count(u.userResourceID)>=#{num} )  u
		inner JOIN jettechuser j ON j.resourceID = u.userResourceID
        inner  JOIN usergroupuser g ON u.userResourceID = g.userResourceID
        inner JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
        inner JOIN dept d ON j.deptResourceID = d.resourceID
        
        <if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
            AND g.userGroupResourceID in
            <foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
                #{userGroupReourceID}
            </foreach>
        </if>
        <if test="name != null and name != '' ">
            AND	j.userName LIKE concat(concat('%',#{name}),'%')
        </if>
        <if test="deptName != null and deptName != '' ">
            AND	d.name LIKE concat(concat('%',#{deptName}),'%')
        </if>
        GROUP BY j.resourceID,d.resourceID,j.userName,d.name
    </select>
    
   <select id="findNotRelatedUserOfMultipleSystem" resultType="java.util.Map">
        SELECT
        j.resourceID as "resourceID",
        j.userName as "userName",
        d.resourceID AS "deptResourceID",
        d.name AS "deptName",
        GROUP_CONCAT(DISTINCT ug.name) AS "roleName"
        FROM
        jettechuser j
        LEFT JOIN usergroupuser g ON j.resourceID = g.userResourceID
        LEFT JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
        LEFT JOIN dept d ON j.deptResourceID = d.resourceID
        WHERE
        j.resourceID NOT IN (select u.userResourceID from ds_testsystem_user u  where 1=1 
		 <if test="testSystemResourceIDs != null and testSystemResourceIDs.size > 0">
           AND u.testSystemResourceID in
        <foreach collection="testSystemResourceIDs" index="index" item="testSystemResourceID" open="(" separator="," close=")">
            #{testSystemResourceID}
        </foreach>
        </if>
        GROUP BY u.userResourceID  having count(u.userResourceID)>=#{num})
         <if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
         	AND g.userGroupResourceID in
             <foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
                 #{userGroupReourceID}
             </foreach>
         </if>
        <if test="name != null and name != '' ">
            AND	j.userName LIKE concat(concat('%',#{name}),'%')
        </if>
        <if test="deptName != null and deptName != '' ">
            AND	d.name LIKE concat(concat('%',#{deptName}),'%')
        </if>
        GROUP BY j.resourceID,d.resourceID,j.userName,d.name
    </select>



</mapper>