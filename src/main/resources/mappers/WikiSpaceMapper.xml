<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IWikiSpaceMapper">

    <resultMap id="wikiSpaceResultMap" type="com.jettech.model.WikiSpace">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="spaceName" column="space_name"/>
        <result property="spaceType" column="space_type"/>
        <result property="spacePublic" column="is_space_public"/>
        <result property="description" column="description"/>
        <result property="notice" column="notice"/>
        <result property="updateTime" column="update_time"/>
        <result property="spaceKey" column="space_key"/>
        <result property="delFlag" column="del_flag"/>
        <result property="pageCount" column="page_count"/>
        <result property="pageResourceId" column="page_resource_id"/>
    </resultMap>


 <sql id="spaceColumnList">
    ws.id, ws.createTime, ws.createUser, ws.editTime, js.userName as editUser, ws.resourceID, ws.version,ws.space_name,
    ws.space_type,ws.is_space_public,ws.description,ws.notice,ws.update_time,
    ws.space_key,ws.del_flag,wpc.page_count
 </sql>
    <select id="getSpaceList" resultMap="wikiSpaceResultMap">
        select <include refid="spaceColumnList"/>   from wiki_space ws left join (select * from wiki_page where IS_INRECYCLE = 0) wp on ws.resourceID = wp.space_resource_id
        left join (select SPACE_RESOURCE_ID,count(resourceID) page_count from wiki_page where IS_INRECYCLE = 0 group by SPACE_RESOURCE_ID) wpc on ws.resourceID = wpc.space_resource_id
        left join jettechuser js  on js.number=ws.editUser
        where ws.DEL_FLAG = 0
        <if test="wikiSpacePageDTO != null and wikiSpacePageDTO.currentSpaceName != null and wikiSpacePageDTO.currentSpaceName != ''">
            and wp.title like concat('%',#{wikiSpacePageDTO.currentSpaceName},'%')
        </if>
        group by ws.id, ws.createTime, ws.createUser, ws.editTime, js.userName, ws.resourceID, ws.version,ws.space_name,
        ws.space_type,ws.is_space_public,ws.description,ws.notice,ws.update_time,
        ws.space_key,ws.del_flag,wpc.page_count ORDER BY ws.createTime DESC
    </select>
</mapper>
