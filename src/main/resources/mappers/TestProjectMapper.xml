<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestProjectMapper">
    <resultMap type="com.jettech.model.TestProject" id="testProjectMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="number" column="number"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="statusName" column="statusName"/>
        <result property="startDate" column="startDate"/>
        <result property="endDate" column="endDate"/>
        <result property="managerResourceID" column="managerResourceID"/>
        <result property="managerName" column="managerName"/>
        <result property="describeInfo" column="describeInfo"/>
        <result property="parentResourceID" column="parentResourceID"/>
        <result property="rootResourceID" column="rootResourceID"/>
        <result property="projectType" column="projectType"/>
        <result property="testMode" column="testMode"/>

    </resultMap>

    <sql id="testProjectColumnList">
		id, createTime, createUser, editTime, editUser, resourceID, version,
		`number`
		,name,status,statusName,startDate,endDate,managerResourceID,managerName,describeInfo,
		parentResourceID,rootResourceID,projectType,testMode
	</sql>

	<sql id="testProjectColumnList_tp">
		tp.id, tp.createTime, tp.createUser, tp.editTime, tp.editUser, tp.resourceID, tp.version,
		tp.`number`
		,tp.name,tp.status,tp.statusName,tp.startDate,tp.endDate,tp.managerResourceID,tp.managerName,tp.describeInfo,
		tp.parentResourceID,tp.rootResourceID,tp.projectType,tp.testMode
	</sql>

    <select id="findTestProject" resultMap="testProjectMap">
		SELECT
			 <include refid="testProjectColumnList"/>
		FROM
			testproject tp
		WHERE
			parentResourceID IS NULL
			<if test="name != null and name != '' ">
				AND	tp.name LIKE concat(concat('%',#{name}),'%')
			</if>
			<if test="status != null and status != '' ">
				AND	tp.`status` = #{status}
			</if>
			<if test="number != null and number != '' ">
				AND	tp.`number` like concat(concat('%',#{number}),'%')
			</if>
			<if test="projectTypeList != null and projectTypeList.size > 0">
				AND tp.projectType in
				<foreach collection="projectTypeList" index="index" item="projectType" open="(" separator="," close=")">
					#{projectType}
				</foreach>
			</if>
			<if test="testMode != null and testMode !=''">
				<choose>
					<when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
						and bitand(tp.testMode,#{testMode}) > 0
					</when>
					<otherwise>
						and tp.testMode &amp; #{testMode}
					</otherwise>
				</choose>
			</if>
		ORDER BY tp.createTime DESC
	</select>

	<select id="findByNumber" resultMap="testProjectMap">
		SELECT
			 <include refid="testProjectColumnList"/>
		FROM
			testproject tp
		WHERE
			tp.number=#{number}
	</select>

	<select id="findByName" resultMap="testProjectMap">
		SELECT
			 <include refid="testProjectColumnList"/>
		FROM
			testproject tp
		WHERE
			tp.name=#{name}
	</select>

	<select id="findAllTestProjectByUser" resultMap="testProjectMap">
		SELECT
			t1.name,
			t1.resourceID
		FROM testproject t1
		LEFT JOIN testprojectuser t2 ON t1.resourceID = t2.testProjectResourceID
		LEFT JOIN jettechuser t3 ON t2.userResourceID = t3.resourceID
		WHERE
			t3.number = #{userNumber}
		  AND t1.resourceID not in(
			SELECT DISTINCT parentResourceID FROM testproject  where parentResourceID = t1.resourceID)
		  AND t1.`status` != 3
		ORDER BY t1.createTime desc
	</select>

	<select id="findAllParent" resultMap="testProjectMap">
		SELECT
			 <include refid="testProjectColumnList"/>
		FROM
			testproject
		WHERE
			resourceID IN ( SELECT DISTINCT parentResourceID FROM testproject )
	</select>

	<select id="findChildTestProjectByResourceID" resultMap="testProjectMap">
		SELECT
			 <include refid="testProjectColumnList"/>
		FROM
			testproject
		WHERE
			parentResourceID=#{resourceID}
	</select>

    <select id="loadParentTestProject" resultMap="testProjectMap">
		SELECT
		<include refid="testProjectColumnList" />
		FROM
		testproject
		where 1=1
		<choose>
			<when test="name != null and name != ''">
				and name like CONCAT('%',#{name},'%')
			</when>
			<otherwise>
				and parentResourceID is null
				and rootResourceID  is null
			</otherwise>
		</choose>
	</select>
	<select id="getChildrenTestProject" resultMap="testProjectMap">
		SELECT
		<include refid="testProjectColumnList" />
		FROM testproject
		where 1=1
		and parentResourceID  = #{parentResourceID}
	</select>

	<select id="findByUserNumber" resultMap="testProjectMap">
		SELECT
			 <include refid="testProjectColumnList_tp"/>
		FROM
			jettechuser j
			JOIN testprojectuser u ON j.resourceID=u.userResourceID
			JOIN testproject tp ON tp.resourceID=u.testProjectResourceID
		WHERE
			j.number=#{userNumber}
			<if test="status != null">
				and tp.statusName != #{status}
			</if>
	</select>

	<select id="findByRootResourceIDIn" resultMap="testProjectMap">
		SELECT
			 <include refid="testProjectColumnList"/>
		FROM
			testproject
		WHERE
			rootResourceID IN
			<foreach collection="resourceIDs" item="resourceID" open="(" separator="," close=")">
				#{resourceID}
			</foreach>
			<if test="status != null">
				and statusName != #{status}
			</if>
	</select>

	<select id="findParentbyResourceIDIn" resultMap="testProjectMap">
		SELECT
		<include refid="testProjectColumnList"/>
		FROM
		testproject
		WHERE
		parentResourceID IN
		<foreach collection="resourceIDs" item="resourceID" open="(" separator="," close=")">
		#{resourceID}
		</foreach>
	</select>

	<select id="findTestProjectByCondition" resultMap="testProjectMap">
		SELECT
		<include refid="testProjectColumnList"/>
		FROM
		testproject tp
		<where>
			<if test="name != null and name != '' ">
				AND	tp.name LIKE concat(concat('%',#{name}),'%')
			</if>
			<if test="status != null and status != '' ">
				AND	tp.status = #{status}
			</if>
			<if test="number != null and number != '' ">
				AND	tp.number like concat(concat('%',#{number}),'%')
			</if>
			<if test="projectTypeList != null and projectTypeList.size > 0">
				AND tp.projectType in
				<foreach collection="projectTypeList" index="index" item="projectType" open="(" separator="," close=")">
					#{projectType}
				</foreach>
			</if>
			<if test="testMode != null and testMode !=''">
				<choose>
					<when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
						and bitand(tp.testMode,#{testMode}) > 0
					</when>
					<otherwise>
						and tp.testMode &amp; #{testMode}
					</otherwise>
				</choose>
			</if>
		</where>
		ORDER BY tp.id DESC
	</select>
	<select id="findByRootResourceID" resultMap="testProjectMap">
		select
		<include refid="testProjectColumnList"/>
		from testproject
		where rootResourceID = #{rootResourceID}
	</select>

	<select id="findEndStateBytestProjectResourceID" resultType="String">
		SELECT t.stateKey  as "stateKey" FROM at_processstate  t INNER JOIN at_defectprocess m on t.defectProcessResourceID=m.resourceID
		INNER JOIN defectprocessquote p on p.defectProcessResourceID=m.resourceID and p.projectResourceID=#{testProjectResourceID}
		WHERE t.isEnd=1
	</select>

	<select id="findAllByDataAuth" resultType="com.jettech.model.TestProject">
		select <include refid="testProjectColumnList"/> from testproject order by createTime
	</select>

	<select id="findTradeFlowCaseFolder" resultType="java.util.Map">
		select * from trade_flow_case_folder where test_project_id = #{testProjectResourceId}
	</select>

	<select id="findAllByDataAuthByCondition" resultType="com.jettech.model.TestProject">
		select <include refid="testProjectColumnList"/>
		from testproject
		<where>
			<if test="projectName != null and projectName != ''">
				name like concat(concat('%',#{projectName}),'%')
			</if>
		</where>
		order by createTime asc
	</select>
</mapper>
