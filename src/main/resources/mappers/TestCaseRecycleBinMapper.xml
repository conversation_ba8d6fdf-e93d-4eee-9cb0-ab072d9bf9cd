<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestCaseRecycleBinMapper">
	
	<select id="findBytestsystemResourceidAndtaskResourceID" resultType="java.lang.String">
		select t.tradeResourceID  from testtasktrade t
		where 1=1
		<if test="testSystemResourceID != null and testSystemResourceID != ''">
            and t.testSystemResourceID = #{testSystemResourceID}
        </if>
        <if test="testTaskResourceID != null and testTaskResourceID != ''">
            and t.testTaskResourceID = #{testTaskResourceID}
        </if>
        <if test="systemModuleResourceID != null and systemModuleResourceID != ''">
            and t.systemModuleResourceID = #{systemModuleResourceID}
        </if>
	</select>

	<select id="findByTestTaskResourceID" resultType="java.util.Map">
    	SELECT t1.resourceID as "taskResourceID",t1.testPlanResourceID as "testPlanResourceID",t2.planName as "planName",t2.testRound as "testRound",t2.testStage as "testStage",t3.resourceID as "testStageResourceID"
    		FROM testtask t1 JOIN me_testplan t2 on t1.testPlanResourceID = t2.resourceID LEFT JOIN datadictionary t3 ON t2.testStage = t3.textName
    		WHERE t1.resourceID  = #{taskResourceID}
    </select>

    <select id="findTradeResIDsByTestTaskResourceIDAndTradeResID" resultType="java.lang.String">
    	SELECT DISTINCT t.tradeResourceID as "tradeResourceID" FROM ds_testcaserecyclebin t
    	WHERE t.tradeResourceID IN
        <foreach collection="tradeResourceIDs" item="resourceID" open="(" close=")" separator=",">
            #{resourceID}
        </foreach>
    	<if test="taskResourceID != null and taskResourceID != ''">
            and t.testtaskResourceID = #{taskResourceID}
        </if>
    </select>

	<select id="findTestCaseRecycleBinPage" resultType="com.jettech.model.TestCaseRecycleBin">
    	SELECT <include refid="com.jettech.mapper.ITestCaseMapper.testCaseColumnList"/> FROM ds_testcaserecyclebin WHERE 1=1
    	and tradeResourceID IN
    	<foreach collection="tradeResIds" item="tradeResourceID" open="(" close=")" separator=",">
            #{tradeResourceID}
        </foreach>
        <if test="taskResourceID != null and taskResourceID != ''">
            and testtaskResourceID = #{taskResourceID}
        </if>
    </select>
    <select id="findByLECreateTime" resultType="com.jettech.model.TestCaseRecycleBin">
    	SELECT <include refid="com.jettech.mapper.ITestCaseMapper.testCaseColumnList"/> FROM ds_testcaserecyclebin WHERE createTime &lt;=#{createTime}
    </select>
    <!--根据当前交易查询其下案例编号最大的案例-->
    <select id="findMaxTestCasebyTradeResourceID" resultType="com.jettech.model.TestCaseRecycleBin">
        select max(caseId) caseId from ds_testcaseRecycleBin WHERE tradeResourceID = #{tradeResourceID}
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                AND regexp_like (caseId,'[^0-9]') = false
            </when>
            <otherwise>
                AND
                <include refid="com.jettech.mapper.CommonSqlMapper._regexp_not_like">
                    <property name="colName" value="caseId"/>
                    <property name="regexpStr" value="[^0-9]"/>
                </include>
            </otherwise>
        </choose>

    </select>

    <select id="findByTradeResourceID" resultType="com.jettech.model.TestCaseRecycleBin">
    	SELECT <include refid="com.jettech.mapper.ITestCaseMapper.testCaseColumnList"/> FROM ds_testcaserecyclebin WHERE tradeResourceID = #{tradeResourceID}
    </select>

    <select id="findCaseIDByTradeResourceID" resultType="java.util.Map">
    	SELECT t.caseId  as "caseId" FROM ds_testcaserecyclebin t WHERE t.tradeResourceID = #{tradeResourceID}
    </select>
    
    <select id="findCaseByTradeResourceIDList" resultType="java.util.Map">
    	SELECT DISTINCT t.caseId as "caseId", tradeResourceID as "tradeResourceID" FROM ds_testcaserecyclebin t
    	WHERE t.tradeResourceID IN
        <foreach collection="tradeResourceIDList" item="tradeResourceID" open="(" close=")" separator=",">
            #{tradeResourceID}
        </foreach>
    </select>
    
    
</mapper>
