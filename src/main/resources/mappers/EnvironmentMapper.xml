<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IEnvironmentMapper">
    <resultMap type="com.jettech.model.Environment" id="environmentMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="ip" column="ip"/>
        <result property="state" column="state"/>
        <result property="systemName" column="systemName"/>
        <result property="systemManager" column="systemManager"/>
        <result property="cpu" column="cpu"/>
        <result property="memoryInformation" column="memoryInformation"/>
        <result property="operatingSystemInformation" column="operatingSystemInformation"/>
        <result property="dataBaseInformation" column="dataBaseInformation"/>
        <result property="detailedInformation" column="detailedInformation"/>
        <result property="middlewareInformation" column="middlewareInformation"/>
        <result property="hostname" column="hostname"/>
        <result property="editUserName" column="editUserName"/>
        <result property="department" column="department"/>
        <result property="envUserName" column="envUserName"/>
        <result property="systemComponents" column="systemComponents"/>
    </resultMap>
    
    <resultMap type="com.jettech.common.dto.assets.EnvironmentPageDTO" id="environmentPageMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="ip" column="ip"/>
        <result property="state" column="state"/>
        <result property="systemName" column="systemName"/>
        <result property="systemManager" column="systemManager"/>
        <result property="cpu" column="cpu"/>
        <result property="memoryInformation" column="memoryInformation"/>
        <result property="operatingSystemInformation" column="operatingSystemInformation"/>
        <result property="dataBaseInformation" column="dataBaseInformation"/>
        <result property="detailedInformation" column="detailedInformation"/>
        <result property="middlewareInformation" column="middlewareInformation"/>
        <result property="hostname" column="hostname"/>
        <result property="editUserName" column="editUserName"/>
        <result property="department" column="department"/>
        <result property="envUserName" column="envUserName"/>
        <result property="systemComponents" column="systemComponents"/>
    </resultMap>

    <sql id="environmentColumnList">
        id, createTime as "createTime", createUser as "createUser", editTime as "editTime", editUser as "editUser", resourceID as "resourceID", version,
        name,`type`,ip, state, systemName as "systemName", systemManager as "systemManager", cpu, memoryInformation as "memoryInformation", operatingSystemInformation as "operatingSystemInformation",
        dataBaseInformation as "dataBaseInformation", detailedInformation as "detailedInformation", middlewareInformation as "middlewareInformation", hostname, editUserName as "editUserName", department, envUserName as "envUserName", systemComponents as "systemComponents"
    </sql>

    <select id="findEnvironmentPage" resultMap="environmentMap">
        SELECT
        <include refid="environmentColumnList"/>
        FROM environment 
		<where>
		    1=1
			<if test="name != null and name != ''">
				AND name like concat(concat('%',#{name}),'%')
			</if>
			<if test="ip != null and ip != ''">
				AND	ip like concat(concat('%',#{ip}),'%')
			</if>
			<if test="systemName != null and systemName != ''">
				AND	systemName like concat(concat('%',#{systemName}),'%')
			</if>
			<if test="type != null and type != ''">
				AND	type = #{type}
			</if>
			<if test="state != null and state != ''">
				AND state = #{state}
			</if>
			<if test="systemManager != null and systemManager != ''">
				AND	systemManager like concat(concat('%',#{systemManager}),'%')
			</if>
            <if test="department != null and department != ''">
                AND	department = #{department}
            </if>
            <if test="envUserName != null and envUserName != ''">
                AND	envUserName like concat(concat('%',#{envUserName}),'%')
            </if>
            <if test="systemComponents != null and systemComponents != ''">
                AND	systemComponents like concat(concat('%',#{systemComponents}),'%')
            </if>
		</where>
		ORDER BY createTime DESC
    </select>
    
    <select id="findByNameAndType" resultMap="environmentMap">
        SELECT <include refid="environmentColumnList"/> 
        FROM environment  WHERE `name` = #{name} AND type = #{type} 
    </select>
    
    <select id="findByEnvironment" resultType ="java.util.Map">
        SELECT
        (case when name is null then '' else name end) as name,
        (case when type is null then '' else type end) as "type",
        (case when state is null then '' else state end) as state,
        (case when ip is null then '' else ip end) as ip,
        (case when systemName is null then '' else systemName end) as "systemName",
        (case when systemManager is null then '' else systemManager end) as "systemManager",
        (case when hostname is null then '' else hostname end) as hostname,
        (case when cpu is null then '' else cpu end) as cpu,
        (case when memoryInformation is null then '' else memoryInformation end) as "memoryInformation",
        (case when operatingSystemInformation is null then '' else operatingSystemInformation end) as "operatingSystemInformation",
        (case when dataBaseInformation is null then '' else dataBaseInformation end) as "dataBaseInformation",
        (case when middlewareInformation is null then '' else middlewareInformation end) as "middlewareInformation",
        (case when editUserName is null then '' else editUserName end) as "editUserName",
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                (case when detailedinformation  is null then '' else detailedinformation end) as "detailedInformation",
                (case when editTime is null then '' else TO_CHAR(editTime, 'YYYY-MM-DD') end) as "editTime",
            </when>
            <otherwise>
                (case when CAST(detailedinformation as CHAR) is null then '' else CAST(detailedinformation as CHAR) end) as "detailedInformation",
                (case when editTime is null then '' else editTime end) as "editTime",
            </otherwise>
        </choose>
        (case when department is null then '' else department end) as department,
        (case when envUserName is null then '' else envUserName end) as "envUserName",
        (case when systemComponents is null then '' else systemComponents end) as "systemComponents"
        FROM environment 
		<where>
		    1=1
			<if test="name != null and name != ''">
				AND name like concat(concat('%',#{name}),'%')
			</if>
			<if test="ip != null and ip != ''">
				AND	ip like concat(concat('%',#{ip}),'%')
			</if>
			<if test="systemName != null and systemName != ''">
				AND	systemName = #{systemName}
			</if>
			<if test="type != null and type != ''">
				AND	type = #{type}
			</if>
			<if test="state != null and state != ''">
				AND state = #{state}
			</if>
			<if test="systemManager != null and systemManager != ''">
				AND	systemManager like concat(concat('%',#{systemManager}),'%')
			</if>
            <if test="department != null and department != ''">
                AND	department = #{department}
            </if>
            <if test="envUserName != null and envUserName != ''">
                AND	envUserName like concat(concat('%',#{envUserName}),'%')
            </if>
            <if test="systemComponents != null and systemComponents != ''">
                AND	systemComponents like concat(concat('%',#{systemComponents}),'%')
            </if>
		</where>
		ORDER BY createTime DESC
    </select>

    <select id="findByEnvironmentByResourceIds" resultType ="java.util.Map">
        SELECT
        (case when name is null then '' else name end) as "name",
        (case when type is null then '' else type end) as "type",
        (case when state is null then '' else state end) as state,
        (case when ip is null then '' else ip end) as ip,
        (case when systemName is null then '' else systemName end) as "systemName",
        (case when systemManager is null then '' else systemManager end) as "systemManager",
        (case when hostname is null then '' else hostname end) as hostname,
        (case when cpu is null then '' else cpu end) as cpu,
        (case when memoryInformation is null then '' else memoryInformation end) as "memoryInformation",
        (case when operatingSystemInformation is null then '' else operatingSystemInformation end) as "operatingSystemInformation",
        (case when dataBaseInformation is null then '' else dataBaseInformation end) as "dataBaseInformation",
        (case when middlewareInformation is null then '' else middlewareInformation end) as "middlewareInformation",
        (case when editUserName is null then '' else editUserName end) as "editUserName",
        <choose>
            <when test="_databaseId=='dm'">
                (case when CAST(detailedinformation as VARCHAR) is null then '' else CAST(detailedinformation as VARCHAR) end) as detailedInformation,
                (case when editUserName is null then '' else editUserName end) as editUserName,
            </when>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                (case when detailedinformation  is null then '' else detailedinformation end) as "detailedInformation",
                (case when editTime is null then '' else TO_CHAR(editTime, 'YYYY-MM-DD') end) as "editTime",
            </when>
            <otherwise>
                (case when CAST(detailedinformation as CHAR) is null then '' else CAST(detailedinformation as CHAR) end) as "detailedInformation",
                (case when editTime is null then '' else editTime end) as "editTime",
            </otherwise>
        </choose>
        (case when department is null then '' else department end) as department,
        (case when envUserName is null then '' else envUserName end) as "envUserName",
        (case when systemComponents is null then '' else systemComponents end) as "systemComponents"
        FROM environment
        where resourceID in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        ORDER BY createTime DESC
    </select>

	<select id="findUpdateEnvironmentByResID" resultMap="environmentPageMap">
		SELECT
        <include refid="environmentColumnList"/>
        FROM environment WHERE resourceID = #{resourceID}
	</select>
</mapper>