<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IDemandTestSystemMapper">
    <resultMap type="com.jettech.model.DemandTestSystem" id="demandTestSystemMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>
        <result property="testSystemResourceID" column="testSystemResourceID"/>
        <result property="demandResourceID" column="demandResourceID"/>
		<result property="isPrincipal" column="isPrincipal"/>
    </resultMap>
	
	<sql id="demandTestSystemColumnList">
		id, version, createTime, editTime, createUser, editUser, resourceID, testSystemResourceID, demandResourceID, isPrincipal
	</sql>

    <select id="findRelevanceSystem"  resultType = "java.util.Map">
	SELECT
			a.id,
			a.resourceID as "resourceID",
			a.name,
			a.busPCentralizedDepartment as "busPCentralizedDepartment",
			u.resourceID as "demandtestsystemRid",
			u.isPrincipal
		FROM
			(
				SELECT
					att.id,
					att.resourceID,
					att. name,
					CASE WHEN d.textName IS NULL THEN '' ELSE d.textName END AS busPCentralizedDepartment
				FROM
					at_testsystem att
				LEFT JOIN datadictionary d ON d.`value` = att.busPCentralizedDepartment AND d.name = 'CENTRALIZEDBUSINESSDEPARTMENT'
			) a          left JOIN ds_demandtestsystem u ON a.resourceID = u.testSystemResourceID
    WHERE u.demandResourceID  IN
    <foreach collection="resourceIDs" item="resourceID" open="(" close=")" separator=",">
		#{resourceID}
	</foreach>
	</select>

    <select id="getNotRelevanceSystem"  resultType = "java.util.Map">
		SELECT
			a.*
		FROM
			(
				SELECT
					att.id,
					att.resourceID,
					att. name,
					CASE WHEN d.textName IS NULL THEN '' ELSE d.textName END AS "busPCentralizedDepartment"
				FROM
					at_testsystem att
				LEFT JOIN datadictionary d ON d.`value` = att.busPCentralizedDepartment AND d.name = 'CENTRALIZEDBUSINESSDEPARTMENT'
			) a          WHERE a.resourceID not in (
SELECT  p.testSystemResourceID from ds_demandtestsystem  p where p.demandResourceID =#{resourceID}) 
 		<if test="name != null and name != '' ">
            AND a.name LIKE concat(concat('%',#{name}),'%')
        </if>
	</select>
    <select id="findNotRelevanceSystem"  resultType = "java.util.Map">
                SELECT
              a.*
            FROM
            at_testsystem a
          WHERE
            a.resourceID NOT IN (
                SELECT
                    u.testSystemResourceID
                FROM
                    ds_demandtestsystem u
             WHERE u.demandResourceID = #{resourceID})
	</select>
	<select id="findBydemandResourceID" resultMap="demandTestSystemMap">
		SELECT
			<include refid="demandTestSystemColumnList"/>
		FROM ds_demandtestsystem
		WHERE
			demandResourceID = #{demandResourceID};
	</select>

    <select id="findBytestSystemResourceID" resultMap="demandTestSystemMap">
        SELECT <include refid="demandTestSystemColumnList"/> FROM ds_demandtestsystem
         WHERE
        testSystemResourceID = #{testSystemResourceID}
    </select>



    <select id="findByTestSystemResourceIDAndDemandResourceID"   resultMap = "demandTestSystemMap">
		SELECT
        <include refid="demandTestSystemColumnList"/>
		FROM
			ds_demandtestsystem
		WHERE
			demandResourceID = #{demandResourceID}
			AND testSystemResourceID=#{testSystemResourceID}
	</select>

	<select id="findByDemandResourceIDIn" resultMap="demandTestSystemMap">
		select
        <include refid="demandTestSystemColumnList"/>
		from ds_demandtestsystem
		where 1=1
		and demandResourceID IN
		<foreach collection="list" item="resourceID" open="(" close=")" separator=",">
			#{resourceID}
		</foreach>

	</select>
</mapper>