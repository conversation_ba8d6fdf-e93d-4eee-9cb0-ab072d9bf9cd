<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestCasequoteMapper">

    <sql id="testCasequoteColumnList">
        id ,version ,createTime ,editTime ,createUser ,editUser ,resourceID ,testCaseResourceID ,testPlanResourceID ,
        tradeResourceID ,isAutoExecute ,executorNumber ,executor ,caseResult ,testTaskResourceID ,caseResultType ,
        caseResultTypeName ,caseResultDescription ,openUser ,status
    </sql>

    <sql id="testCasequoteColumnList_tcq">
        tcq.id ,tcq.version ,tcq.createTime ,tcq.editTime ,tcq.createUser ,tcq.editUser ,tcq.resourceID ,tcq.testCaseResourceID ,tcq.testPlanResourceID ,
        tcq.tradeResourceID ,tcq.isAutoExecute ,tcq.executorNumber ,tcq.executor ,tcq.caseResult ,tcq.testTaskResourceID ,tcq.caseResultType ,
        tcq.caseResultTypeName ,tcq.caseResultDescription ,tcq.openUser ,tcq.status
    </sql>

    <select id="findByTestcaseResourceID" resultType="com.jettech.model.TestCasequote">
        SELECT
            <include refid="testCasequoteColumnList_tcq"/>
        FROM me_testcasequote tcq
        INNER JOIN me_caseresultrecord t2 on tcq.resourceID = t2.testCaseQuoteResourceID
        WHERE t2.resourceID =  #{resourceID}
    </select>

    <select id="findByCaseResourceID" resultType="com.jettech.model.TestCasequote">
        SELECT
        <include refid="testCasequoteColumnList"/>
        FROM me_testcasequote
        WHERE testCaseResourceID =  #{resourceID}
    </select>

    <select id="findByCaseResourceIDs" resultType="com.jettech.model.TestCasequote">
        SELECT
        <include refid="testCasequoteColumnList"/>
        FROM me_testcasequote
        WHERE testCaseResourceID IN
        <foreach collection="testcaseResourceIDList" item="testcaseResourceID" open="(" close=")" separator=",">
            #{testcaseResourceID}
        </foreach>
    </select>

</mapper>