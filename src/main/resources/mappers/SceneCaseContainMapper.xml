<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ISceneCaseContainMapper">
  <resultMap id="BaseResultMap" type="com.jettech.model.SceneCaseContain">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
    <result column="createUser" jdbcType="VARCHAR" property="createUser" />
    <result column="editTime" jdbcType="TIMESTAMP" property="editTime" />
    <result column="editUser" jdbcType="VARCHAR" property="editUser" />
    <result column="resourceID" jdbcType="BIGINT" property="resourceID" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="sceneCaseResourceID" jdbcType="BIGINT" property="sceneCaseResourceID" />
    <result column="testCaseResourceID" jdbcType="BIGINT" property="testCaseResourceID" />
    <result column="caseOrder" jdbcType="INTEGER" property="caseOrder" />
    <result column="initParam" jdbcType="VARCHAR" property="initParam" />
  </resultMap>
  <sql id="Base_Column_List">
    id, createTime, createUser, editTime, editUser, resourceID, version, sceneCaseResourceID,
    testCaseResourceID, caseOrder, initParam
  </sql>
  <sql id="Base_Column_List_sc">
    sc.id, sc.createTime, sc.createUser, sc.editTime, sc.editUser, sc.resourceID, sc.version, sc.sceneCaseResourceID,
    sc.testCaseResourceID, sc.caseOrder, sc.initParam
  </sql>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jettech.model.SceneCaseContain" useGeneratedKeys="true">
    insert into ds_scenecase_contain
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
          id,
      </if>
      <if test="createTime != null">
        createTime,
      </if>
      <if test="createUser != null">
        createUser,
      </if>
      <if test="editTime != null">
        editTime,
      </if>
      <if test="editUser != null">
        editUser,
      </if>
      <if test="resourceID != null">
        resourceID,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="sceneCaseResourceID != null">
        sceneCaseResourceID,
      </if>
      <if test="testCaseResourceID != null">
        testCaseResourceID,
      </if>
      <if test="caseOrder != null">
        caseOrder,
      </if>
      <if test="initParam != null">
        initParam,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="editTime != null">
        #{editTime,jdbcType=TIMESTAMP},
      </if>
      <if test="editUser != null">
        #{editUser,jdbcType=VARCHAR},
      </if>
      <if test="resourceID != null">
        #{resourceID,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="sceneCaseResourceID != null">
        #{sceneCaseResourceID,jdbcType=BIGINT},
      </if>
      <if test="testCaseResourceID != null">
        #{testCaseResourceID,jdbcType=BIGINT},
      </if>
      <if test="caseOrder != null">
        #{caseOrder,jdbcType=INTEGER},
      </if>
      <if test="initParam != null">
        #{initParam,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

    <select id="selectByCondition" parameterType="map" resultType="com.jettech.DTO.SceneCaseContainDTO">
      SELECT
        <include refid="Base_Column_List_sc"/> ,
        CASE WHEN b.systemType=2 THEN c.name WHEN b.systemType=4 THEN d.name END scriptName
      FROM ds_scenecase_contain sc
      LEFT JOIN ds_testcase b ON sc.testCaseResourceID = b.resourceID
      LEFT JOIN trade_flow c ON b.tradeFlowId = c.id
      LEFT JOIN ui_script_info d ON b.scriptInfoID = d.id
      WHERE sc.sceneCaseResourceID = #{params.sceneCaseResourceID}
      <if test="params.caseId!=null and params.caseId!=''">
          AND INSTR(b.caseId, #{params.caseId}) > 0
      </if>
      <if test="params.intent!=null and params.intent!=''">
          AND INSTR(b.intent, #{params.intent}) > 0
      </if>
      <if test="params.testMode!=null and params.testMode!=''">
          AND b.testMode = #{params.testMode}
      </if>
      <!--根据案例类型筛选名称-->
      <if test="params.systemType!=null and params.systemType!=''">
        AND b.systemType = #{params.systemType}
        <if test="params.name!=null and params.name!=''">
          <if test="params.systemType==2">
              AND c.name LIKE CONCAT('%',#{params.name},'%')
          </if>
          <if test="params.systemType==4">
              AND d.name LIKE CONCAT('%',#{params.name},'%')
            </if>
          </if>
      </if>
      <if test="params.systemType ==null or params.systemType ==''">
        <if test="params.name!=null and params.name!=''">
          AND (d.name LIKE CONCAT('%',#{params.name},'%')
          or c.name LIKE CONCAT('%',#{params.name},'%'))
        </if>
      </if>
        ORDER BY sc.caseOrder
    </select>
  <select id="selectMaxId" resultType="java.lang.Integer">
    SELECT CASE WHEN MAX(id) IS NULL THEN 0 ELSE MAX(id) END FROM ds_scenecase_contain
  </select>
</mapper>