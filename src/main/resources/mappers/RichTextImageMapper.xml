<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IRichTextImageMapper">
    <resultMap id="richTextImageResultMap" type="com.jettech.model.RichTextImage">
            <id property="id" column="id"/>
            <result property="createTime" column="createTime"/>
            <result property="createUser" column="createUser" />
            <result property="editTime" column="editTime"/>
            <result property="editUser" column="editUser"/>
            <result property="resourceID" column="resourceID"/>
            <result property="version" column="version"/>
            <!-- 表字段 -->
            <result property="imagePath" column="image_path"/>
    </resultMap>

    <sql id="richTextImage_Column_List">
        id, createTime, createUser,
        editTime, editUser, resourceID,
        version, image_path
    </sql>

    <select id="findByImagePath" resultMap="richTextImageResultMap">
        select <include refid="richTextImage_Column_List"/> from rich_text_image
        where image_path = #{imagePath}
        order by resourceID desc
    </select>
</mapper>
