<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestCaseMapper">
    <resultMap type="com.jettech.model.TestCase" id="testCaseMap">
        <!-- 字段变更需要同步到 ds_testcaserecyclebin ds_testcase_version两张表 -->
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="caseId" column="caseId"/>
        <result property="intent" column="intent"/>
        <result property="isNegative" column="isNegative"/>
        <result property="casetLevel" column="casetLevel"/>
        <result property="caseType" column="caseType"/>
        <result property="testStep" column="testStep"/>
        <result property="expectedResult" column="expectedResult"/>
        <result property="preconditions" column="preconditions"/>
        <result property="testsystem" column="testsystem"/>
        <result property="testsystemResourceID" column="testsystemResourceID"/>
        <result property="systemmodule" column="systemmodule"/>
        <result property="systemmoduleResourceID" column="systemmoduleResourceID"/>
        <result property="projectgroupResourceID" column="projectgroupResourceID"/>
        <result property="leadsource" column="leadsource"/>
        <result property="trade" column="trade"/>
        <result property="tradeResourceID" column="tradeResourceID"/>
        <result property="timingName" column="timingName"/>
        <result property="maintainer" column="maintainer"/>
        <result property="maintenanceTime" column="maintenanceTime"/>
        <result property="comment" column="comment"/>
        <result property="demandResourceID" column="demandResourceID"/>
        <result property="reviewStatus" column="reviewStatus"/>
        <result property="caseEditId" column="caseEditId"/>
        <result property="testEnviroment" column="testEnviroment"/>
        <result column="dataRequirements"  property="dataRequirements" />
        <result column="checkPoint"  property="checkPoint" />
        <result property="testtaskResourceID" column="testtaskResourceID"/>
        <result property="committed" column="committed"/>
        <result property="identitynumber" column="identitynumber"/>
        <result property="comments1" column="comments1"/>
        <result property="comments2" column="comments2"/>
        <result property="comments3" column="comments3"/>
        <result property="comments4" column="comments4"/>
        <result property="comments5" column="comments5"/>
        <result property="comments6" column="comments6"/>
        <result property="comments7" column="comments7"/>
        <result property="comments8" column="comments8"/>
        <result property="comments9" column="comments9"/>
        <result property="comments10" column="comments10"/>
        <result property="testMode" column="testMode"/>
        <result property="versionResourceID" column="versionResourceID"/>
        <result property="sceneCase" column="sceneCase"/>
        <result property="scriptInfoID" column="scriptInfoID"/>
        <result property="name" column="name"/>

        <!-- 字段变更需要同步到 ds_testcaserecyclebin ds_testcase_version两张表 -->
    </resultMap>
    <resultMap id="testCaseDtoMap" type="com.jettech.common.dto.datadesign.TestCaseDTO">
        <result property="TestCaseDTO" column="TestCaseDTO"/>
        <result property="caseId" column="caseId"/>
        <result property="caseEditId" column="caseEditId"/>
        <result property="intent" column="intent"/>
        <result property="isNegative" column="isNegative"/>
        <result property="casetLevel" column="casetLevel"/>
        <result property="caseType" column="caseType"/>
        <result property="testStep" column="testStep"/>
        <result property="expectedResult" column="expectedResult"/>
        <result property="preconditions" column="preconditions"/>
        <result property="caseResultDescription" column="caseResultDescription"/>
        <result property="caseExecuteDescription" column="caseExecuteDescription"/>
        <result property="testsystem" column="testsystem"/>
        <result property="testsystemResourceID" column="testsystemResourceID"/>
        <result property="systemmodule" column="systemmodule"/>
        <result property="systemmoduleResourceID" column="systemmoduleResourceID"/>
        <result property="projectgroupResourceID" column="projectgroupResourceID"/>
        <result property="leadsource" column="leadsource"/>
        <result property="demand" column="demand"/>
        <result property="planexecutorNumber" column="planexecutorNumber"/>
        <result property="planexecutor" column="planexecutor"/>
        <result property="executornumber" column="executornumber"/>
        <result property="executor" column="executor"/>
        <result property="recordCount" column="recordCount"/>
        <result property="newRecordFilesCount" column="newRecordFilesCount"/>
        <result property="newResultRecordResourceID" column="newResultRecordResourceID"/>
        <result property="caseResult" column="caseResult"/>
        <result property="dataRequirements" column="dataRequirements"/>
        <result property="checkPoint" column="checkPoint"/>
        <result property="trade" column="trade"/>
        <result property="tradeResourceID" column="tradeResourceID"/>
        <result property="demandResourceID" column="demandResourceID"/>
        <result property="timingName" column="timingName"/>
        <result property="comment" column="comment"/>
        <result property="maintainer" column="maintainer"/>
        <result property="maintenanceTime" column="maintenanceTime"/>
        <result property="reviewStatus" column="reviewStatus"/>
        <result property="testtaskResourceID" column="testtaskResourceID"/>
        <result property="testcaseResourceID" column="testcaseResourceID"/>
        <result property="committed" column="committed"/>
        <result property="bankSerialNumber" column="bankSerialNumber"/>
        <result property="comments1" column="comments1"/>
        <result property="comments2" column="comments2"/>
        <result property="comments3" column="comments3"/>
        <result property="comments4" column="comments4"/>
        <result property="comments5" column="comments5"/>
        <result property="comments6" column="comments6"/>
        <result property="comments7" column="comments7"/>
        <result property="comments8" column="comments8"/>
        <result property="comments9" column="comments9"/>
        <result property="comments10" column="comments10"/>
        <result property="bug" column="bug"/>
        <result property="caseResultEnding" column="caseResultEnding"/>
        <result property="name" column="name"/>
        <result property="finalResult" column="finalResult"/>
        <result property="testMode" column="testMode"/>
        <result property="testModeName" column="testModeName"/>
    </resultMap>

    <sql id="testCaseColumnList">
        id, createTime as "createTime", createUser as "createUser", editTime as "editTime", editUser as "editUser", resourceID as "resourceID", version,
        caseId as "caseId", isNegative as "isNegative",casetLevel as "casetLevel",caseType as "caseType",testStep as "testStep",expectedResult as "expectedResult",
        preconditions,testsystem,testsystemResourceID as "testsystemResourceID",systemmodule,systemmoduleResourceID as "systemmoduleResourceID",projectgroupResourceID as "projectgroupResourceID",leadsource,
        trade,tradeResourceID as "tradeResourceID",timingName as "timingName",maintainer,maintenanceTime as  "maintenanceTime",
        `comment`,`intent`,
        demandResourceID as "demandResourceID",reviewStatus as "reviewStatus",caseEditId as "caseEditId",testEnviroment as "testEnviroment",dataRequirements as "dataRequirements",checkPoint as "checkPoint",testtaskResourceID as "testtaskResourceID",identitynumber,comments1,comments2,
        comments3,comments4,comments5,comments6,comments7,comments8,comments9,comments10,testMode as "testMode",versionResourceID as "versionResourceID",sceneCase as "sceneCase",scriptInfoID as "scriptInfoID",name
    </sql>
    <sql id="testCaseColumnList_tc">
        tc.id, tc.createTime as "createTime", tc.createUser as "createUser", tc.editTime as "editTime", tc.editUser as "editUser", tc.resourceID as "resourceID", tc.version,
        tc.caseId as "caseId", tc.isNegative as "isNegative",tc.casetLevel as "casetLevel",tc.caseType as "caseType",tc.testStep as "testStep",tc.expectedResult as "expectedResult",
        tc.preconditions,tc.testsystem,tc.testsystemResourceID as "testsystemResourceID",tc.systemmodule,tc.systemmoduleResourceID as "systemmoduleResourceID",tc.projectgroupResourceID as "projectgroupResourceID",tc.leadsource,
        tc.trade,tc.tradeResourceID as "tradeResourceID",tc.timingName as "timingName",tc.maintainer,tc.maintenanceTime as "maintenanceTime",
        tc.`intent`,
        tc.`comment`,
        tc.demandResourceID as "demandResourceID",tc.reviewStatus as "reviewStatus",tc.caseEditId as "caseEditId",tc.testEnviroment as "testEnviroment",tc.dataRequirements as "dataRequirements",tc.checkPoint as "checkPoint",tc.testtaskResourceID as "testtaskResourceID",tc.identitynumber,tc.comments1,tc.comments2,
        tc.comments3,tc.comments4,tc.comments5,tc.comments6,tc.comments7,tc.comments8,tc.comments9,tc.comments10,tc.testMode as "testMode",tc.versionResourceID as "versionResourceID",tc.sceneCase as "sceneCase",tc.scriptInfoID as "scriptInfoID",tc.name,tc.tradeFlowId as "tradeFlowId"
    </sql>

    <sql id="testCaseColumnList_tc" databaseId="kingwow">
        tc.id, tc.createTime, tc.createUser, tc.editTime, tc.editUser, tc.resourceID, tc.version,
        tc.caseId, tc.isNegative,tc.casetLevel,tc.caseType,tc.testStep,tc.expectedResult,
        tc.preconditions,tc.testsystem,tc.testsystemResourceID,tc.systemmodule,tc.systemmoduleResourceID,tc.projectgroupResourceID,tc.leadsource,
        tc.trade,tc.tradeResourceID,tc.timingName,tc.maintainer,tc.maintenanceTime,
        tc.intent,
        tc.comment,
        tc.demandResourceID,tc.reviewStatus,tc.caseEditId,tc.testEnviroment,tc.dataRequirements,tc.checkPoint,tc.testtaskResourceID,tc.identitynumber,tc.comments1,tc.comments2,
        tc.comments3,tc.comments4,tc.comments5,tc.comments6,tc.comments7,tc.comments8,tc.comments9,tc.comments10,tc.testMode,tc.versionResourceID,tc.sceneCase,tc.scriptInfoID,tc.name,tc.tradeFlowId
    </sql>

    <select id="findByResourceId" parameterType="java.lang.Long" resultMap="testCaseMap">
        select <include refid="testCaseColumnList"/> from ds_testcase where resourceID=#{resourceId}
    </select>
    <select id="findByResourceIds" parameterType="java.util.List" resultMap="testCaseMap">
        select <include refid="testCaseColumnList"/> from ds_testcase where resourceID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findSceneCaseByTradeId" resultMap="testCaseMap">
        select <include refid="testCaseColumnList"/> from ds_testcase where tradeResourceID=#{tradeResourceID} and sceneCase=1 order by id desc
    </select>
    <select id="findByTestTaskResourceIdList" parameterType="java.util.List" resultMap="testCaseMap">
        select <include refid="testCaseColumnList"/> from ds_testcase where testtaskResourceID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--根据当前交易查询其下的所有案例-->
    <select id="findbyTradeResourceID" resultMap="testCaseMap">
        SELECT
        <include refid="testCaseColumnList"/>
        from ds_testcase where tradeResourceID = #{arg0}
    </select>
    <!--根据当前交易查询其下案例编号最大的案例-->
    <select id="findMaxTestCasebyTradeResourceID" resultMap="testCaseMap">
        SELECT
        <include refid="testCaseColumnList"/>
        from ds_testcase where tradeResourceID = #{tradeResourceID}
        and caseId = (select max(caseId) from ds_testcase WHERE tradeResourceID = #{tradeResourceID}

        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                AND
                regexp_like (caseId,'[^0-9]') = false
            </when>
            <otherwise>
                AND
                <include refid="com.jettech.mapper.CommonSqlMapper._regexp_not_like">
                    <property name="colName" value="caseId"/>
                    <property name="regexpStr" value="[^0-9]"/>
                </include>
            </otherwise>
        </choose>
        )
    </select>

    <select id="findbyTradeResourceIDList" resultMap="testCaseMap">
        SELECT
        <include refid="testCaseColumnList"/>
        from ds_testcase where tradeResourceID in
        <foreach collection="list" item="tradeResourceID" open="(" close=")" separator=",">
            #{tradeResourceID}
        </foreach>
    </select>
    <!-- 查询符合条件的案例总数 -->
    <select id="findCountByTradeResourceID" resultType="java.lang.Integer">
     SELECT count(at1.id) FROM(
        SELECT id from ds_testcase WHERE ds_testcase.tradeResourceID = #{tradeResourceID}
	        <if test="demandResourceID != null and demandResourceID != ''">
	            AND demandResourceID = #{demandResourceID}
	        </if>
	        <if test="createUser != null and createUser != ''">
	            and
	            maintainer LIKE CONCAT('%',#{createUser},'%')
	        </if>
	        <if test="caseId != null and caseId != ''">
	            and
	       caseId LIKE CONCAT('%',#{caseId},'%')
	        </if>
	        <if test="isNegative != null and isNegative != ''">
	            and isNegative = #{isNegative}
	        </if>
	        <if test="caseType != null and caseType != ''">
	            and caseType=#{caseType}
	        </if>
		)at1

    </select>

    <!-- 查询符合条件的数据带分页 -->
    <select id="findByTradeAndOptions" resultMap="testCaseMap">
       SELECT
		att.id,
		att.createUser,
		att.editUser,
        att.caseId,
        att.caseEditId,
        att.isNegative,
        att.caseType,
        att.casetLevel,
        att.preconditions,
        att.testStep,
        att.expectedResult,
        att.timingName,
        att.`intent`,
        att.`comment`,
        att.maintainer,
        att.maintenanceTime,
        att.resourceID,
        att.tradeResourceID,
        att.reviewStatus,
        att.testEnviroment,
        att.dataRequirements,
        att.checkPoint,
        att.sceneCase,
        att.scriptInfoID
        FROM
        ds_testcase att
        WHERE
        att.tradeResourceID = #{tradeResourceID}
        <if test="demandResourceID != null and demandResourceID != ''">
            AND
            att.demandResourceID = #{demandResourceID}
        </if>
        <if test="createUser != null and createUser != ''">
            and
            att.maintainer LIKE CONCAT('%',#{createUser},'%')
        </if>
        <if test="caseId != null and caseId != ''">
            and
            caseId LIKE CONCAT('%',#{caseId},'%')
        </if>
        <if test="isNegative != null and isNegative != ''">
            and
            att.isNegative = #{isNegative}
        </if>
        <if test="caseType != null and caseType != ''">
            and
            att.caseType=#{caseType}
        </if>
        ORDER BY
        att.caseId
        LIMIT #{startIndex},
        #{pageSizeInt}
    </select>

    <select id="findByCaseIdsAndTradeResourceID" resultMap="testCaseMap">
        SELECT <include refid="testCaseColumnList"/> FROM ds_testcase
        WHERE
        ds_testcase.tradeResourceID = #{tradeResourceID}
        and ds_testcase.demandResourceID = #{demandResourceID}
        AND ds_testcase.caseId IN
        <foreach collection="list" item="caseId" open="(" close=")" separator=",">
            #{caseId}
        </foreach>

    </select>
    <select id="findCountNumberByTradeResourceID" resultType="java.lang.Integer">
        SELECT count(id) FROM ds_testcase WHERE tradeResourceID = #{tradeResourceID}
        <if test="demandResourceID != null and demandResourceID != ''">
            and demandResourceID = #{demandResourceID}
        </if>
        <if test="type != 'null' and type != '' and type != null">
            and testEnviroment = #{type}
        </if>

    </select>

    <select id="findBydemandResourceID" resultMap="testCaseMap">
		SELECT <include refid="testCaseColumnList"/>
		FROM
		 ds_testcase
		where demandResourceID=#{demandResourceID}


    </select>
    <select id="findCountNumberByDemandRids" resultType="java.util.Map">
    	SELECT t1.demandResourceID as "demandResourceID",count(1) dstescasecount FROM ds_testcase t1 WHERE t1.createUser = #{userNumber} and t1.demandResourceID IN
        <foreach collection="demandResourceIDs" item="demandResourceID" open="(" close=")" separator=",">
            #{demandResourceID}
        </foreach>
 		GROUP BY t1.demandResourceID
    </select>

    <select id="findBySystemModule" resultMap="testCaseMap">
        select <include refid="testCaseColumnList_tc"/>
            from ds_testcase tc
            join at_trade d on tc.tradeResourceID = d.resourceID
        where d.moduleResourceID = #{moduleResourceID}
        and tc.demandResourceID = #{demandResourceID}
    </select>

    <!-- 统计测试案例下的缺陷数量  maqiang -->
     <select id="countByTestCaseResourceID" resultType="java.util.Map">
     	SELECT testCaseResourceID as "testCaseResourceID", count(testCaseResourceID) as "defectNum" FROM at_defect WHERE testCaseResourceID IN
        <foreach collection="resourceIDList" item="resourceID" open="(" close=")" separator=",">
            #{resourceID}
        </foreach>
        GROUP BY testCaseResourceID
    </select>

    	<!-- 查询需求下的案例总数  ma_qiang -->
      <select id="findTotalNumberByDemandRids" resultType="java.util.Map">
    	SELECT t1.demandResourceID as "demandResourceID",count(1) as "totalCount" FROM ds_testcase t1 WHERE t1.demandResourceID IN
        <foreach collection="demandResourceIDs" item="demandResourceID" open="(" close=")" separator=",">
            #{demandResourceID}
        </foreach>
 		GROUP BY t1.demandResourceID
    </select>

    <!--根据交易查询案例和根据搜索选项查询案例列表 xpp -->
    <select id="findtestCaseTableByTradeAndOptions" resultType="java.util.Map">
        select
        <include refid="testCaseColumnList"/>
        FROM
        ds_testcase
        <where>
                tradeResourceID = #{tradeResourceID} and createUser = #{userNumber}
                AND testtaskResourceID = #{testTaskResourceID}
            <if test="createUser != null and createUser != ''">
                and
                maintainer LIKE CONCAT('%',#{createUser},'%')
            </if>
            <if test="caseId != null and caseId != ''">
                and
                caseId LIKE CONCAT('%',#{caseId},'%')
            </if>
            <if test="isNegative != null and isNegative != ''">
                and
                isNegative = #{isNegative}
            </if>
            <if test="caseType != null and caseType != ''">
                and
                caseType=#{caseType}
            </if>
        </where>
        ORDER BY
        resourceID ASC
        LIMIT #{startIndex},
        #{pageSizeInt}
    </select>

    <!-- 根据交易查询案例和根据搜索选项查询案例返回案例总数 -->
    <select id="findTestCaseCountByTradeResourceID" resultType="java.lang.Integer">
        SELECT count(1) FROM
        ds_testcase as ds
        <where>
                tradeResourceID = #{tradeResourceID} and createUser = #{userNumber}
                AND testtaskResourceID = #{testTaskResourceID}
            <if test="createUser != null and createUser != ''">
                and
                maintainer LIKE CONCAT('%',#{createUser},'%')
            </if>
            <if test="caseId != null and caseId != ''">
                and
                caseId LIKE CONCAT('%',#{caseId},'%')
            </if>
            <if test="isNegative != null and isNegative != ''">
                and
                isNegative = #{isNegative}
            </if>
            <if test="caseType != null and caseType != ''">
                and
                caseType=#{caseType}
            </if>
        </where>
    </select>
    <select id="findByTradeRidAndTestEnviroment" resultMap="testCaseMap">
        select
            <include refid="testCaseColumnList"/>
        from ds_testcase
        where tradeResourceID = #{tradeResourceID}
        and  testEnviroment = #{testEnviroment}
        and demandResourceID = #{demandResourceID}
		and createUser = #{userNumber}
    </select>
    <select id="findByTradeResourceIDAndDemandResourceID" resultMap="testCaseMap">
        select
        <include refid="testCaseColumnList"/>
        from ds_testcase
        where tradeResourceID = #{tradeResourceID}
        and demandResourceID = #{demandResourceID}

    </select>
    <select id="findTradeRidsBySelectedTestCaseByTaskResourceID"  resultType="java.lang.String">
    	<!-- SELECT DISTINCT tradeResourceID FROM testtasktestcaseWHERE testTaskResourceID = #{taskResourceID} -->
    	SELECT
			DISTINCT t1.tradeResourceID as "tradeResourceID"
		FROM
		testtasktestcase t1
		JOIN me_testcasequote t2 ON t1.testCaseResourceID = t2.testCaseResourceID  and t1.testTaskResourceID = t2.testTaskResourceID
		where t2.testTaskResourceID = #{taskResourceID}
		AND t2.executorNumber = #{userNumber} and t1.testCaseResourceID in
		<foreach collection="hasQuoteSearchCaseRids" item="caseRid" open="(" close=")" separator=",">
            #{caseRid}
        </foreach>

    </select>
    <select id="findTradeRidsBySelectedTestCaseByTaskResourceID2"  resultType="java.lang.String">
    	<!-- SELECT DISTINCT tradeResourceID FROM testtasktestcaseWHERE testTaskResourceID = #{taskResourceID} -->
    	SELECT
			DISTINCT t1.tradeResourceID as "tradeResourceID"
		FROM
		testtasktestcase t1
		JOIN me_testcasequote t2 ON t1.testCaseResourceID = t2.testCaseResourceID
		where t1.testTaskResourceID = #{taskResourceID} and t2.testPlanResourceID = #{testPlanResourceID}
		AND t2.executorNumber = #{userNumber} and t1.testCaseResourceID in
		<foreach collection="hasQuoteSearchCaseRids" item="caseRid" open="(" close=")" separator=",">
            #{caseRid}
        </foreach>

    </select>
    <select id="findTestCaseByTaskAndTradeResourceID" resultMap="testCaseMap" >
    	SELECT
    	id, createTime, createUser, editTime, editUser, resourceID, version,
        testTaskResourceID,tradeResourceID,testCaseResourceID,userResourceID
    	FROM testtasktestcase WHERE testTaskResourceID = #{taskResourceID} AND tradeResourceID = #{tradeResourceID}
    </select>
    <select id="findTestCasesByTaskAndTrades" resultMap="testCaseMap">
    SELECT <include refid="testCaseColumnList_tc"/> FROM testtasktestcase t1 join ds_testcase tc  ON t1.testCaseResourceID = tc.resourceID JOIN me_testcasequote t3 on tc.resourceID = t3.testCaseResourceID
			where t1.testTaskResourceID = #{taskResourceID} and  t1.tradeResourceID = #{tradeResourceID} AND t3.executorNumber = #{userNumber} and t1.testCaseResourceID in
		<foreach collection="hasQuoteSearchCaseRids" item="caseRid" open="(" close=")" separator=",">
            #{caseRid}
        </foreach>
    	<!-- SELECT t2.* FROM testtasktestcase t1 join ds_testcase t2 ON t1.testCaseResourceID = t2.resourceID
			AND t1.testTaskResourceID = #{taskResourceID} and  t1.tradeResourceID = #{tradeResourceID} -->
    </select>
    <select id="findTestCaseDictionaryData" resultType="java.util.Map">
    	select name,infoName as "infoName",value as "value",textName as "textName",dicDirResourceID as "dicDirResourceID" from DataDictionary  where name = #{dicName, typeHandler=com.jettech.typehandler.UpperTypeHandler}
    </select>

    <select id="findTestCaseDictionaryDataByInfoName" resultType="java.util.Map">
    	select name,infoName as "infoName",value as "value",textName as "textName",dicDirResourceID as "dicDirResourceID" from DataDictionary  where infoName = #{infoName}
    </select>

    <select id="findAllUsersNameAndNumber" resultType="java.util.Map">
    	SELECT userName as "userName", `number`
    	 FROM jettechuser
    </select>
    <select id="getAssetMaxTestCaseCaseId"  resultType="java.lang.String">
    	select max(caseId) as "caseId" from at_testcase WHERE tradeResourceID = #{tradeResourceID}
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                AND
                regexp_like (caseId,'[^0-9]') = false
            </when>
            <otherwise>
                AND
                <include refid="com.jettech.mapper.CommonSqlMapper._regexp_not_like">
                    <property name="colName" value="caseId"/>
                    <property name="regexpStr" value="[^0-9]"/>
                </include>
            </otherwise>
        </choose>

    </select>

    <select id="findCaseInfoPage" resultType="java.util.Map">
        select
            <include refid="testCaseColumnList"/>
        from ds_testcase
        where resourceID in
    	<foreach collection="caseResourceIDs" item="caseResourceID" open="(" close=")" separator=",">
            #{caseResourceID}
        </foreach>
      </select>

    <select id="findTotalZXNumberByDemandRids" resultType="java.util.Map">
        SELECT
        COUNT(dd.id) as num,
        dd.resourceID as "resourceID"
        FROM
        ds_demand dd
        INNER JOIN me_testplan tp ON dd.resourceID=tp.demandResourceID
        INNER JOIN me_testcasequote tq ON tq.testPlanResourceID = tp.resourceID
        WHERE dd.resourceID in
        <foreach collection="demandResourceIDs" item="demandResourceID" open="(" close=")" separator=",">
            #{demandResourceID}
        </foreach>
        GROUP BY dd.resourceID
    </select>

    <select id="findByCaseResourceID" resultType="java.util.Map">
     	SELECT
        	<include refid="testCaseColumnList"/>
        from ds_testcase where resourceID = #{resourceID}
     </select>

     <select id="findByTestTaskResourceIDAndNotCommitted" resultMap="testCaseMap">
     	SELECT
        	<include refid="testCaseColumnList"/>
        from ds_testcase where testtaskResourceID = #{testTaskResourceID}
         <choose>
             <when test="_databaseId == 'dm'">
                 AND committed != 1
             </when>
             <otherwise>
                 AND `committed` IS NOT TRUE
             </otherwise>
         </choose>
     </select>

	<select id="findCaseIDByTradeResourceID" resultType="java.lang.String">
     	SELECT t.caseId as "caseId" FROM ds_testcase t where t.tradeResourceID = #{tradeResourceID}
     </select>

     <select id="findTestPhaseByTestTaskResourceID" resultType="java.lang.String">
     	SELECT t2.planName as "planName" FROM testtask t1 JOIN me_testplan t2 on t1.testPlanResourceID = t2.resourceID WHERE t1.resourceID = #{testtaskResourceID};
     </select>


     <select id="findByTradeRidAndTaskRid" resultMap="testCaseMap">
     	SELECT <include refid="testCaseColumnList"/>
		 FROM ds_testcase t WHERE t.tradeResourceID = #{tradeResourceID}
         <if test="taskResourceID != null and taskResourceID != ''">
             and t.testtaskResourceID = #{taskResourceID}
         </if>
         <if test="maintainer != null and maintainer != ''">
             and
             maintainer LIKE CONCAT('%',#{maintainer},'%')
         </if>
         <if test="caseId != null and caseId != ''">
             and
             caseId LIKE CONCAT('%',#{caseId},'%')
         </if>
         <if test="isNegative != null and isNegative != ''">
             and
             isNegative = #{isNegative}
         </if>
         <if test="leadsource != null and leadsource != ''">
             and leadsource = #{leadsource}
         </if>
         <if test="caseType != null and caseType != ''">
             and
             caseType=#{caseType}
         </if>
      	 <if test="testCaseResourceIDs != null and testCaseResourceIDs !='' and testCaseResourceIDs !='[]'">
             and FIND_IN_SET(resourceID, #{testCaseResourceIDs})>0
         </if>
      	 <if test="testMode != null and testMode !=''">
             <choose>
                 <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                     and bitand(testMode,#{testMode}) > 0
                 </when>
                 <otherwise>
                     and testMode &amp; #{testMode}
                 </otherwise>
             </choose>
         </if>
     </select>

      <select id="findProjectTestCase" resultType="com.jettech.common.dto.datadesign.TestCaseDTO">
          SELECT
          t.resourceID,t.caseId,t.isNegative,t.casetLevel,t.caseType,t.preconditions,t.testStep,t.expectedResult,t.timingName,
          t.reviewStatus,
          t.`intent`,
          t.`comment`
          ,t.maintainer,t.maintenanceTime,t.comments1,t.comments2,t.comments3,t.comments4,t.comments5,
          t.comments6,t.comments7,t.comments8,t.comments9,t.comments10,t.dataRequirements,t.checkPoint,t.testMode,
          testsystem,systemmodule,trade,d.`name` AS demand,t.`name`, CASE WHEN pc.caseResult IS NULL THEN '新建' ELSE pc.caseResult END finalResult
          FROM
          ds_testcase t
          LEFT JOIN ds_demand d ON d.resourceID = t.demandResourceID
          LEFT JOIN
          (SELECT t.testcaseResourceID, t.caseResult
          FROM performcase t
          WHERE resourceID IN (SELECT MAX(resourceID)
          FROM performcase
          GROUP BY testcaseResourceID)) pc ON t.resourceID = pc.testcaseResourceID
          WHERE
          t.leadsource=1
          <if test="resourceIDlist != null and resourceIDlist.size()>0">
              AND t.projectgroupResourceID  in
              <foreach collection="resourceIDlist" item="resourceID" open="(" close=")" separator=",">
                  #{resourceID}
              </foreach>
          </if>
          <if test="testSystem != null and testSystem != ''">
              and
              t.testSystem LIKE CONCAT('%',#{testSystem},'%')
          </if>
          <if test="trade != null and trade != ''">
              and
              t.trade LIKE CONCAT('%',#{trade},'%')
          </if>
          <if test="caseId != null and caseId != ''">
              and
              t.caseId LIKE CONCAT('%',#{caseId},'%')
          </if>
          <if test="intent != null and intent != ''">
              and
              t.intent LIKE CONCAT('%',#{intent},'%')
          </if>
          <if test="caseType != null and caseType != ''">
              and
              t.caseType =#{caseType}
          </if>
          <if test="casetLevel != null and casetLevel != ''">
              and
              t.casetLevel =#{casetLevel}
          </if>
          <!-- 没有关联需求 -->
          <if test="demandFlag == '0'.toString() ">
              and
              t.demandResourceID is null
          </if>
          <!-- 有关联需求 -->
          <if test="demandFlag == '1'.toString() ">
              and
              t.demandResourceID is not null
          </if>
          <if test="testCaseRids != null and testCaseRids.size() > 0">
              and t.resourceID in
              <foreach collection="testCaseRids" item="resourceID" open="(" close=")" separator=",">
                  #{resourceID}
              </foreach>
          </if>
          <if  test="caseFinalResult != null and caseFinalResult != ''">
              <choose>
                  <when test="caseFinalResult == '新建'">
                      AND pc.caseResult IS NULL
                  </when>
                  <otherwise>
                      AND pc.caseResult = #{caseFinalResult}
                  </otherwise>
              </choose>
          </if>
          order by t.`caseId`
     </select>

    <select id="findByProjectGroupResourceIDs" resultMap="testCaseMap">
        select
        <include refid="testCaseColumnList" />
        from ds_testcase t
        where
        t.leadsource=1
        <if test="list != null and list.size()>0">
            AND t.projectgroupResourceID  in
            <foreach collection="list" item="resourceID" open="(" close=")" separator=",">
                #{resourceID}
            </foreach>
        </if>
        <if test="testSystem != null and testSystem != ''">
            and
            t.testSystem LIKE CONCAT('%',#{testSystem},'%')
        </if>
        <if test="trade != null and trade != ''">
            and
            t.trade LIKE CONCAT('%',#{trade},'%')
        </if>
        <if test="caseId != null and caseId != ''">
            and
            t.caseId LIKE CONCAT('%',#{caseId},'%')
        </if>
        <if test="intent != null and intent != ''">
            and
            t.intent LIKE CONCAT('%',#{intent},'%')
        </if>
        <if test="caseType != null and caseType != ''">
            and
            t.caseType =#{caseType}
        </if>
        <if test="casetLevel != null and casetLevel != ''">
            and
            t.casetLevel =#{casetLevel}
        </if>
        <!-- 没有关联需求 -->
        <if test="demandFlag == '0'.toString() ">
            and
            t.demandResourceID is null
        </if>
        <!-- 有关联需求 -->
        <if test="demandFlag == '1'.toString() ">
            and
            t.demandResourceID is not null
        </if>

    </select>

    <select id="findAllTaskTestCase" resultType="com.jettech.DTO.TaskTestCaseDTO">
        select  tt.`name` taskName,tt.resourceID taskResourceID,tt.type taskType,tt.demandResourceID
        from testtask tt join jettechuser ju on tt.managerResourceID = ju.resourceID where tt.demandResourceID is not null
    </select>

    <select id="findPerformCaseByProjectGroupResourceIDs" resultType="int">
        select
        	count(*)
        from performcase
        where 1=1
        and projectgroupResourceID in
        <foreach collection="list" item="resourceID" open="(" close=")">
            #{resourceID}
        </foreach>
    </select>

    <select id="findInfoByTestCaseResourceIDNoModule" resultType="java.util.Map">
		SELECT
			t0.caseId as "caseId",
            t1.`name` AS "tradeName",
            t3.`name` AS "systemName",
            t4.`name` AS "demandName",
			t1.resourceID as "tradeResourceID",
			t3.resourceID as "systemResourceID",
			t4.resourceID as "demandResourceID",
			t4.testProjectName as "testProjectName",
			t4.testProjectResourceID as "testProjectResourceID"
		FROM
			ds_testcase t0
		LEFT JOIN at_trade t1 ON t0.tradeResourceID = t1.resourceID
		LEFT JOIN at_testsystem t3 ON t1.testSystemResourceID = t3.resourceID
		LEFT JOIN ds_demand t4 ON t0.demandResourceID = t4.resourceID
		WHERE
			t0.resourceID = #{resourceID}
    </select>
    <select id="findInfoByTestCaseResourceIDHasModule" resultType="java.util.Map">
      SELECT
        t1.`name` AS "tradeName",
        t2.`name` AS "moduleName",
        t3.`name` AS "systemName",
        t4.`name` AS "demandName",
        t0.caseId as "caseId",
        t1.resourceID as "tradeResourceID",
        t2.resourceID as "moduleResourceID",
        t3.resourceID as "systemResourceID",
        t4.resourceID as "demandResourceID",
        t4.testProjectName as "testProjectName",
        t4.testProjectResourceID as "testProjectResourceID"
    FROM
        ds_testcase t0
        LEFT JOIN at_trade t1 ON t0.tradeResourceID = t1.resourceID
        LEFT JOIN at_systemmodule t2 ON t1.moduleResourceID = t2.resourceID
        LEFT JOIN at_testsystem t3 ON t2.testSystemResourceID = t3.resourceID
        LEFT JOIN (
        SELECT
            c.`name`,
            c.resourceID,
            c.testProjectName,
            c.testProjectResourceID,
            a.testCaseResourceID
        FROM
            me_testcasequote a
            JOIN testtask b ON a.testTaskResourceID = b.resourceID
            JOIN ds_demand c ON b.demandResourceID = c.resourceID
        WHERE
            b.resourceID = #{taskResourceID}
        ) t4 ON t0.resourceID = t4.testCaseResourceID
    WHERE
        t0.resourceID = #{resourceID}
    </select>
    <select id="countAllPerformCaseAddToExecute" resultType="int">
    	SELECT
			count(t1.resourceID) as "counComplete"
		FROM
			ds_testcase t1
		LEFT JOIN performcase t2 ON t1.resourceID = t2.testcaseResourceID
		WHERE
			t1.demandResourceID = #{demandResourceID} AND t1.leadsource = 1 AND t2.resourceID is NULL
    </select>
    <select id="countAllTestCaseAddToExecute" resultType="int">
		SELECT
			count(t1.resourceID) as "counComplete"
		FROM
			ds_testcase t1
		LEFT JOIN me_testcasequote t2 ON t1.resourceID = t2.testCaseResourceID
		WHERE
			t1.demandResourceID = #{demandResourceID} AND t1.leadsource != 1 AND t2.resourceID is NULL
    </select>

    <select id="findPerformcaseByCaseResourceID"  resultType="Integer">
    	SELECT 1 FROM performcase WHERE testcaseResourceID IN

		<foreach collection="ceseRids" item="resourceID" open="(" separator="," close=")">
            #{resourceID}
        </foreach>
        <if test="flag == 'flag'">
            AND caseResult != '成功' AND caseResult != '取消'
        </if>
		 limit 1
    </select>

     <select id="findTestCaseByCaseResourceID"  resultType="Integer">
    	SELECT 1 countNum FROM me_testcasequote WHERE testCaseResourceID IN
		<foreach collection="ceseRids" item="resourceID" open="(" separator="," close=")">
            #{resourceID}
        </foreach>
         <if test="flag == 'flag'">
             AND caseResult != '成功' AND caseResult != '取消'
         </if>
		 limit 1
    </select>

    <select id="findCaseInfoByDemandResourceID" resultType="java.util.Map">
		SELECT a.resourceID as "resourceID",a.leadsource
		FROM
		 ds_testcase a
		where a.demandResourceID=#{demandResourceID}


    </select>

    <!--根据交易ID列表及任务编号等条件分页查询案例 -->
    <select id="findByTradeResIdsAndTaskResIdPage" resultType="java.util.Map">
        SELECT
        a.resourceID as "resourceID",
        a.coverage,
        a.id,
        a.createTime as "createTime",
        a.createUser as "createUser",
        a.editTime as "editTime",
        a.editUser as "editUser",
        a.version,
        a.caseId as "caseId",
        a.isNegative as "isNegative",
        a.casetLevel as "casetLevel",
        a.caseType as "caseType",
        a.testsystem,
        a.testsystemResourceID as "testsystemResourceID",
        a.systemmodule,
        a.systemmoduleResourceID as "systemmoduleResourceID",
        a.projectgroupResourceID as "projectgroupResourceID",
        a.leadsource,
        a.trade,
        a.tradeResourceID as "tradeResourceID",
        a.timingName as "timingName",
        a.maintainer,
        a.maintenanceTime as "maintenanceTime",
        <choose>
            <when test="_databaseId == 'dm'">
                a."comment",
                a."intent",
            </when>
            <otherwise>
                a.`comment`,
                a.`intent`,
            </otherwise>
        </choose>
        a.testStep as "testStep",
        a.expectedResult as "expectedResult",
        a.preconditions,
        a.dataRequirements as "dataRequirements",
        a.checkpoint as "checkPoint",
        a.demandResourceID as "demandResourceID",
        a.reviewStatus as "reviewStatus",
        a.caseEditId as "caseEditId",
        a.testEnviroment as "testEnviroment",
        a.testtaskResourceID as "testtaskResourceID",
        a.identitynumber,
        a.comments1,
        a.comments2,
        a.comments3,
        a.comments4,
        a.comments5,
        a.comments6,
        a.comments7,
        a.comments8,
        a.comments9,
        a.comments10,
        a.testMode as "testMode",
        a.versionResourceID as "versionResourceID",
        a.sceneCase as "sceneCase",
        a.scriptInfoID as "scriptInfoID",
        a.name,
        a.relationResourceID as "relationResourceID",
        a.tradeFlowId as "tradeFlowId"
        FROM
	        (
            select
	        <choose>
                <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                    tc.resourceID,
                    CONCAT_WS(',',CASE WHEN t2.testCaseResourceID IS NULL THEN NULL ELSE 1 END,CASE WHEN t3.id IS NULL THEN NULL ELSE 2 END,CASE WHEN t4.id IS NULL THEN NULL ELSE 3 END) coverage,
                    tc.id, tc.createTime, tc.createUser, tc.editTime, tc.editUser,  tc.version,
                    tc.caseId, tc.isNegative,tc.casetLevel,tc.caseType,
                    tc.testsystem,tc.testsystemResourceID,tc.systemmodule,tc.systemmoduleResourceID,tc.projectgroupResourceID,tc.leadsource,
                    tc.trade,tc.tradeResourceID,tc.timingName,tc.maintainer,tc.maintenanceTime,
                    cast(tc."comment" as VARCHAR) as "comment",
                    cast(tc."intent" as VARCHAR) as "intent",
                    cast(tc.testStep as VARCHAR) as testStep,
                    cast(tc.expectedResult as VARCHAR) as expectedResult,
                    cast(tc.preconditions as VARCHAR) as preconditions,
                    cast(tc.dataRequirements as VARCHAR) as dataRequirements,
                    cast(tc.checkPoint as VARCHAR) as checkPoint,
                    tc.demandResourceID,tc.reviewStatus,tc.caseEditId,tc.testEnviroment,tc.testtaskResourceID,tc.identitynumber,tc.comments1,tc.comments2,
                    tc.comments3,tc.comments4,tc.comments5,tc.comments6,tc.comments7,tc.comments8,tc.comments9,tc.comments10,tc.testMode,tc.versionResourceID,tc.sceneCase,tc.scriptInfoID,tc.name
                    ,t1.relationResourceID,tc.tradeFlowId
                </when>
                <otherwise>
                    <!-- coverage(测试覆盖): 1-手工测试-TM, 2-接口自动化-API, 3-UI自动化-UI,20211012dwl改) -->
                    CONCAT_WS(',', CASE WHEN t2.testCaseResourceID IS NULL THEN NULL ELSE 1 END,
                    CASE WHEN t3.id IS NULL THEN NULL ELSE 2 END,
                    CASE WHEN t4.id IS NULL THEN NULL ELSE 3 END) coverage,
                    tc.id,
                    tc.createTime,
                    tc.createUser,
                    tc.editTime,
                    tc.editUser,
                    tc.resourceID,
                    tc.version,
                    tc.caseId,
                    tc.isNegative,
                    tc.casetLevel,
                    tc.caseType,
                    tc.testStep,
                    tc.expectedResult,
                    tc.preconditions,
                    tc.testsystem,
                    tc.testsystemResourceID,
                    tc.systemmodule,
                    tc.systemmoduleResourceID,
                    tc.projectgroupResourceID,
                    tc.leadsource,
                    tc.trade,
                    tc.tradeResourceID,
                    tc.timingName,
                    tc.maintainer,
                    tc.maintenanceTime,
                    tc.intent,
                    tc.comment,
                    tc.demandResourceID,
                    tc.reviewStatus,
                    tc.caseEditId,
                    tc.testEnviroment,
                    tc.dataRequirements,
                    tc.checkPoint,
                    tc.testtaskResourceID,
                    tc.identitynumber,
                    tc.comments1,
                    tc.comments2,
                    tc.comments3,
                    tc.comments4,
                    tc.comments5,
                    tc.comments6,
                    tc.comments7,
                    tc.comments8,
                    tc.comments9,
                    tc.comments10,
                    tc.testMode,
                    tc.versionResourceID,
                    tc.sceneCase,
                    tc.scriptInfoID,
                    tc.name,
                    tc.tradeFlowId,
                    t1.relationResourceID,
                    t1.relationType,
                    t1.tagResourceID
                </otherwise>
            </choose>

	        FROM
	        ds_testcase tc
	        left JOIN tagrelation t1 on tc.resourceID = t1.relationResourceID
	        LEFT JOIN (
						SELECT mt.testCaseResourceID FROM me_testcasequote mt
						WHERE mt.tradeResourceID IN
						<foreach collection="tradeResIds" item="resourceID" open="(" separator="," close=")">
			                #{resourceID}
			            </foreach>
						UNION
						SELECT p.testcaseResourceID testCaseResourceID FROM performcase p
						WHERE p.tradeResourceID IN
						<foreach collection="tradeResIds" item="resourceID" open="(" separator="," close=")">
			                #{resourceID}
			            </foreach>
					  ) t2 ON t2.testCaseResourceID = tc.resourceID
			LEFT JOIN trade_flow t3 ON tc.tradeFlowId = t3.id
			LEFT JOIN ui_script_info t4 ON tc.scriptInfoID = t4.id
	        <where>
	            tc.tradeResourceID IN
	            <foreach collection="tradeResIds" item="resourceID" open="(" separator="," close=")">
	                #{resourceID}
	            </foreach>
	            <if test="testTaskResId != null and testTaskResId != ''">
	                AND tc.testtaskResourceID = #{testTaskResId}
	            </if>
	            <if test="maintainer != null and maintainer != ''">
	                and
	                tc.maintainer LIKE CONCAT('%',#{maintainer},'%')
	            </if>
	            <if test="caseId != null and caseId != ''">
	                and
	                tc.caseId LIKE CONCAT('%',#{caseId},'%')
	            </if>
                <if test="caseName != null and caseName != ''">
                    and
                    tc.name LIKE CONCAT('%',#{caseName},'%')
                </if>
                <if test="intent != null and intent != ''">
                    and
                    tc.intent LIKE CONCAT('%',#{intent},'%')
                </if>
	            <if test="isNegative != null and isNegative != ''">
	                and
	                tc.isNegative = #{isNegative}
	            </if>
	            <if test="caseType != null and caseType != ''">
	                and
	                tc.caseType=#{caseType}
	            </if>
	            <if test="testMode != null and testMode !=''">
	                <choose>
                        <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                            and bitand(testMode, #{testMode}) > 0
                        </when>
                        <otherwise>
                            and testMode &amp; #{testMode}
                        </otherwise>
                    </choose>
	            </if>
	            <if test="tagResourceIDs != ''">
	                and
	                t1.tagResourceID in
	                <foreach collection="tagResourceIDs.split(',')" item="tagResourceID" open="("
	                         close=")" separator=",">
	                    #{tagResourceID}
	                </foreach>
	            </if>
	        </where>
	        <choose>
                <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                    GROUP BY tc.resourceID,tc.id, tc.createTime, tc.createUser, tc.editTime, tc.editUser,  tc.version,
                    tc.caseId, tc.isNegative,tc.casetLevel,tc.caseType,
                    tc.testsystem,tc.testsystemResourceID,tc.systemmodule,tc.systemmoduleResourceID,tc.projectgroupResourceID,tc.leadsource,
                    tc.trade,tc.tradeResourceID,tc.timingName,tc.maintainer,tc.maintenanceTime,
                    cast(tc."comment" as VARCHAR),
                    cast(tc."intent" as VARCHAR),
                    cast(tc.testStep as VARCHAR),
                    cast(tc.expectedResult as VARCHAR),
                    cast(tc.preconditions as VARCHAR),
                    cast(tc.dataRequirements as VARCHAR),
                    cast(tc.checkPoint as VARCHAR),
                    tc.demandResourceID,tc.reviewStatus,tc.caseEditId,tc.testEnviroment,tc.testtaskResourceID,
                    tc.identitynumber,
                    tc.comments1,
                    tc.comments2,
                    tc.comments3,
                    tc.comments4,
                    tc.comments5,
                    tc.comments6,tc.comments7,tc.comments8,tc.comments9,tc.comments10,
                    tc.testMode,
                    tc.versionResourceID,
                    tc.sceneCase,
                    tc.scriptInfoID,
                    tc.name,
                    tc.tradeFlowId,
                    t1.relationResourceID,
                    t1.relationtype,
                    t1.tagresourceid,
                    t2.testCaseResourceID,
                    t4.id,
                    t3.id
                </when>
            </choose>
	        ) a
        WHERE 1=1
        	<if test="coverage != null and coverage != ''">
        		and
        		<foreach collection="coverage.split(',')" item="cid" separator="and">
                    a.coverage like CONCAT('%',#{cid},'%')
                </foreach>
        	</if>
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                GROUP BY
                a.id,
                a.coverage,
                a.createtime,
                a.createuser,
                a.edittime,
                a.edituser,
                a.version,
                a.caseid,
                a.isnegative,
                a.casetlevel,
                a.casetype,
                a.testsystem,
                a.testsystemresourceid,
                a.systemmodule,
                a.systemmoduleresourceid,
                a.projectgroupresourceid,
                a.leadsource,
                a.trade,
                a.timingname,
                a.maintainer,
                a."comment",
                a."intent",
                a.teststep,
                a.expectedresult,
                a.preconditions,
                a.datarequirements,
                a.checkpoint,
                a.demandresourceid,
                a.caseeditid,
                a.testenviroment,
                a.testtaskresourceid,
                a.identitynumber,
                a.comments1,
                a.comments2,
                a.comments3,
                a.comments4,
                a.comments5,
                a.comments6,
                a.comments7,
                a.comments8,
                a.comments9,
                a.comments10,
                a.versionresourceid,
                a.scenecase,
                a.scriptinfoid,
                a.name,
                a.relationresourceid,
                a.tradeflowid,
                a.resourceID,
                a.tradeResourceID,
                a.maintenanceTime,
                a.reviewStatus,
                a.testMode
            </when>
            <otherwise>
                GROUP BY a.resourceID
            </otherwise>
        </choose>

        ORDER BY
        a.resourceID ASC
    </select>

    <update id="updateTestCaseTradeNameByTradeId">
        UPDATE ds_testcase SET trade = #{tradeName} WHERE tradeResourceID = #{tradeResourceID}
    </update>

    <update id="updateTestCaseSystemNameBySystemId">
        UPDATE ds_testcase SET testsystem = #{systemName} WHERE testsystemResourceID = #{systemResourceId}
    </update>

    <update id="updateTestCaseSystemModuleNameByNameAndSystemId">
        UPDATE ds_testcase
        SET systemmodule = REPLACE(systemmodule,#{oldName},#{newName})
        WHERE testsystemResourceID = #{testSystemResourceID}
    </update>

    <select id="findPerformCaseInfo" resultType="java.util.Map">
        SELECT
            resourceID as "resourceID",
			caseId as "caseId",
            CASE WHEN testsystemResourceID IS NULL THEN '' ELSE CONCAT(testsystemResourceID,'') END as "testsystemResourceID",
            CASE WHEN testsystem IS NULL THEN '' ELSE testsystem END as "testsystem",
            CASE WHEN systemmoduleResourceID IS NULL THEN '' ELSE CONCAT(systemmoduleResourceID,'') END as "systemmoduleResourceID",
            CASE WHEN systemmodule IS NULL THEN '' ELSE systemmodule END systemmodule,
            CASE WHEN trade IS NULL THEN '' ELSE trade END trade,
            CASE WHEN tradeResourceID IS NULL THEN '' ELSE CONCAT(tradeResourceID,'') END as "tradeResourceID",
            CASE WHEN demandResourceID IS NULL THEN '' ELSE CONCAT(demandResourceID,'') END as "demandResourceID",
            CASE WHEN projectgroupResourceID IS NULL THEN '' ELSE CONCAT(projectgroupResourceID,'') END as "projectgroupResourceID"
        FROM
           performcase
        WHERE
            resourceID = #{performCaseResourceID}

    </select>


    <select id="initMyCaseToAssesWithBynumber" resultType="java.util.Map">
        SELECT
        *
        FROM
        (
        SELECT DISTINCT
        t4.id,
        t1.name,
        t4.caseType as "caseType",
        t4.caseId as "caseId",
        t1.`number`,
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                CAST(t4.intent as VARCHAR) as "intent",
            </when>
            <otherwise>
                t4.`intent`,
            </otherwise>
        </choose>
        t7.resourceID as "resourceID",
        t7.caseResult as "caseResult",
        t7.caseResultDescription as "caseResultDescription",
        t7.caseResultTypeName as "caseResultTypeName",
        t7.executor,
        t7.executeTime as "executeTime"
        FROM
        ds_demand t1
        INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID
        INNER JOIN me_testplan t3 ON t1.resourceID = t3.demandResourceID
        INNER JOIN me_testcasequote t5 ON t5.testPlanResourceID = t3.resourceID
        INNER JOIN ds_testcase t4 ON t4.resourceID = t5.testCaseResourceID
        <if test="params.caseType !=null and params.caseType !=''">
            AND t4.caseType in
            <foreach item="str" index="index" collection="params.caseType.split(',')" open="(" close=")" separator=",">

                #{str}

            </foreach>
        </if>
        INNER JOIN (SELECT * FROM me_caseresultrecord WHERE ID IN (SELECT MAX(id) FROM me_caseresultrecord GROUP BY testCaseQuoteResourceID)) t7 ON t7.testCaseQuoteResourceID = t5.resourceID
        <where>
            AND t5.STATUS = 0
            <if test="params.demandName !=null and params.demandName !=''">
              and  t1.name LIKE CONCAT('%', #{params.demandName}, '%')
            </if>
            <if test="params.number !=null and params.number !=''">
                and t1.number like CONCAT('%', #{params.number}, '%')
            </if>
            <if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
                and t1.testProjectResourceID=#{params.testProjectResourceID}
            </if>
            <if test="params.caseResult ==null or params.caseResult ==''" >
                AND t5.caseResult in('取消','阻塞')
            </if>
            <if test="params.caseResult !=null and params.caseResult !=''" >
                AND t5.caseResult in
                <foreach item="str" index="index" collection="params.caseResult.split(',')" open="(" close=")" separator=",">
                    #{str}
                </foreach>
            </if>
        </where>
<!--        UNION-->
<!--        SELECT DISTINCT-->
<!--        t4.id,-->
<!--        t1.number,-->
<!--        t1.name,-->
<!--        t4.caseType,-->
<!--        t4.caseId,-->
<!--        t4.intent,-->
<!--        t7.resourceID,-->
<!--        t7.caseResult,-->
<!--        t7.caseResultDescription,-->
<!--        t7.caseResultTypeName,-->
<!--        t7.executor,-->
<!--        t7.executeTime-->
<!--        FROM-->
<!--        ds_demand t1-->
<!--        INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID-->
<!--        INNER JOIN ds_testcase t4 ON t4.demandResourceID = t1.resourceID-->
<!--        <if test="params.caseType !=null and params.caseType !=''">-->
<!--            AND t4.caseType in-->
<!--            <foreach item="str" index="index" collection="params.caseType.split(',')" open="(" close=")" separator=",">-->

<!--                #{str}-->

<!--            </foreach>-->

<!--        </if>-->
<!--        INNER JOIN performcase t6 ON t6.testcaseResourceID = t4.resourceID-->
<!--        INNER JOIN me_caseresultrecord t7 ON t7.performcaseResourceID = t6.resourceID-->
<!--        AND t7.id IN ( SELECT Max( t8.id ) FROM me_caseresultrecord t8 WHERE t8.testCaseQuoteResourceID = t6.resourceID )-->
<!--        <where>-->
<!--            t6.status = 0-->
<!--            <if test="params.demandName !=null and params.demandName !=''">-->
<!--               and  t1.name LIKE CONCAT('%', #{params.demandName}, '%')-->
<!--            </if>-->
<!--            <if test="params.number !=null and params.number !=''">-->
<!--                and t1.number like CONCAT('%', #{params.number}, '%')-->
<!--            </if>-->
<!--            <if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">-->
<!--                and t1.testProjectResourceID=#{params.testProjectResourceID}-->
<!--            </if>-->
<!--            <if test="params.caseResult !=null and params.caseResult !=''">-->
<!--                AND t7.caseResult in-->
<!--                <foreach item="str" index="index" collection="params.caseResult.split(',')" open="(" close=")" separator=",">-->
<!--                    #{str}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="params.caseResult ==null or params.caseResult ==''" >-->
<!--                AND t7.caseResult in('取消','阻塞')-->
<!--            </if>-->
<!--        </where>-->
        ) t
    </select>

    <select id="findByCaseIdAndTradeResId" resultMap="testCaseMap">
        SELECT
            <include refid="testCaseColumnList"/>
        FROM ds_testcase
        WHERE
        ds_testcase.tradeResourceID = #{tradeRresourceID}
        AND ds_testcase.caseId = #{caseId}
    </select>

    <select id="findBydemandResourceIDs" resultMap="testCaseMap">
        SELECT
            <include refid="testCaseColumnList"/>
        FROM ds_testcase WHERE  demandResourceID IN
        <foreach collection="demandsResourceIDList" item="demandResourceID" open="(" close=")" separator=",">
            #{demandResourceID}
        </foreach>
    </select>

    <select id="findRelationResourceTags"  resultType="java.util.Map">
        SELECT
        t2.`name`,
        t2.color,
        t1.tagResourceID as "tagResourceID",
        t1.resourceID as "tagRelationResourceID",
        t1.relationResourceID as "relationResourceID"
        FROM
       tagrelation t1
        JOIN tag t2 ON t1.tagResourceID = t2.resourceID
        where t1.relationResourceID IN
        <foreach collection="relationResourceIDs" item="resourceResourceID" open="(" close=")" separator=",">
            #{resourceResourceID}
        </foreach>
        and t1.relationType = #{relationType}
    </select>

    <select id="findTaskTree" resultType="java.util.Map">
        SELECT
            t1.resourceID as "taskResourceID",
            t1.`name` as "taskName",
            t2.resourceID as "planResourceID",
            t2.planName as "planName",
            t3.resourceID as "demandResourceID",
            t3.`name` as "demandName",
            t4.resourceID as "projectResourceID",
            t4.`name` as "projectName"

        FROM
            testtask t1
            JOIN me_testplan t2 ON t1.testPlanResourceID = t2.resourceID
            JOIN ds_demand t3 ON t1.demandResourceID = t3.resourceID
            JOIN testproject t4 ON t3.testProjectResourceID = t4.resourceID
        ORDER BY
            t4.createTime DESC
    </select>

    <select id="findTradeTree" resultType="java.util.Map">
        SELECT
            t1.resourceID as "systemResourceID",
            t1.`name` as "systemName",
            t2.resourceID as "moduleResourceID",
            t2.parentResourceID as "parentResourceID",
            t2.`name` as "moduleName",
            t3.resourceID as "tradeResourceID",
            t3.`name` as "tradeName"

        FROM
            at_testsystem t1
            JOIN at_systemmodule t2 ON t1.resourceID = t2.testSystemResourceID
            LEFT JOIN at_trade t3 ON t2.resourceID = t3.moduleResourceID
            JOIN testtasktrade t4 ON t1.resourceID = t4.testSystemResourceID AND t3.resourceID = t4.tradeResourceID
        <where>
            <if test="taskResourceIDs != null and taskResourceIDs.size > 0">
                and t4.testTaskResourceID in
                <foreach collection="taskResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
            t1.createTime DESC
    </select>

    <select id="findTaskTradeCase" resultType="com.jettech.view.TaskTradeCaseView">
        SELECT
        <include refid="testCaseColumnList_tc"/>,
        t2.userName createUserName,
        t3.runCount
        FROM
        ds_testcase tc
        LEFT JOIN jettechuser t2 ON tc.createUser = t2.number
        JOIN (
        SELECT
        m.testCaseResourceID,
        sum( m.runCount ) runCount
        FROM
        (
        SELECT
        x.testCaseResourceID,
        count(a.resourceID) runCount
        FROM
        me_testcasequote x
        LEFT JOIN
        me_caseresultrecord a ON x.resourceID = a.testCaseQuoteResourceID
        LEFT JOIN jettechuser b ON a.createUser = b.number
        <where>
            <if test="executor !=null and executor != ''">
                and (a.createUser like concat('%',#{executor},'%') or b.userName like concat('%',#{executor},'%'))
            </if>
           <!--  <if test="executeResult !=null and executeResult != ''">
                and a.caseResult like concat('%',#{executeResult},'%')
            </if> -->
            <if test="executeTimeStrList != null and executeTimeStrList.size > 0">
                and DATE_FORMAT(a.executeTime,'%Y-%m-%d') &gt;= #{executeTimeStrList[0]}
                and DATE_FORMAT(a.executeTime,'%Y-%m-%d') &lt;= #{executeTimeStrList[1]}
            </if>

            <if test="taskResourceIDs != null and taskResourceIDs.size > 0">
                and x.testTaskResourceID in
                <foreach collection="taskResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
             <if test="executeResult !=null and executeResult != ''">
                and a.caseResult like concat('%',#{executeResult},'%')
            </if>

        </where>
        GROUP BY
        x.testCaseResourceID UNION ALL
        SELECT
        resourceID,
        0 runCount
        FROM
        ds_testcase
        <where>
            <if test="taskResourceIDs != null and taskResourceIDs.size > 0">
                and testtaskResourceID in
                <foreach collection="taskResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ) m
        GROUP BY
        m.testCaseResourceID
        ) t3 ON tc.resourceID = t3.testCaseResourceID
        <where>
            <if test="tradeResourceIDs != null and tradeResourceIDs.size > 0">
                and tc.tradeResourceID  in
                <foreach collection="tradeResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="createUser !=null and createUser != ''">
                and (tc.createUser like concat('%',#{createUser},'%') or t2.userName like concat('%',#{createUser},'%'))
            </if>
            <if test="caseId != null and caseId != ''">
                and tc.caseId LIKE CONCAT('%',#{caseId},'%')
            </if>
            <if test="intent != null and intent != ''">
                and tc.intent LIKE CONCAT('%',#{intent},'%')
            </if>
            <if test="caseTypes != null and caseTypes.size > 0">
                and tc.caseType in
                <foreach collection="caseTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeStrList != null and createTimeStrList.size > 0">
                and DATE_FORMAT(tc.createTime,'%Y-%m-%d') &gt;= #{createTimeStrList[0]}
                and DATE_FORMAT(tc.createTime,'%Y-%m-%d') &lt;= #{createTimeStrList[1]}
            </if>
            <if test="executor !=null and executor != ''">
                and t3.runCount &gt; 0
            </if>
            <if test="executeResult !=null and executeResult != '' and executeResult!='待执行'">
                and t3.runCount &gt; 0
            </if>
            <if test="executeTimeStrList != null and executeTimeStrList.size > 0">
                and t3.runCount &gt; 0
            </if>
        </where>
    </select>

    <select id="findCaseQuote" resultType="java.lang.String">
        select resourceID as "resourceID" from me_testcasequote
        <where>
            <if test="taskResourceIDs != null and taskResourceIDs.size > 0">
                and testTaskResourceID in
                <foreach collection="taskResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="testCaseResourceID != null and testCaseResourceID != ''">
                and testCaseResourceID = #{testCaseResourceID}
            </if>
        </where>
    </select>

    <!-- 查询案例加入的执行范围的案例的resourceid集合,通过交易resourceID查询20211009dingwl -->
    <select id="queryExecuteScopeResourceIdList" resultType="java.lang.String">
    	SELECT t1.testCaseResourceID as "testCaseResourceID" FROM me_testcasequote t1
    	WHERE t1.tradeResourceID IN
    		<foreach collection="tradeResIds" item="resourceID" open="(" separator="," close=")">
                #{resourceID}
            </foreach>
		UNION
		SELECT t2.testcaseResourceID as "testCaseResourceID" FROM performcase t2
		WHERE t2.tradeResourceID IN
            <foreach collection="tradeResIds" item="resourceID" open="(" separator="," close=")">
                #{resourceID}
            </foreach>
    </select>
    <!-- 查询案例是否关联ui脚本,通过ui脚本ids,20211011dingwl -->
    <select id="queryUiScriptByIds" resultType="java.util.Map">
    	SELECT * FROM ui_script_info s
		WHERE s.id IN
            <foreach collection="scriptIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    <!-- 查询案例是否关联api脚本,通过api脚本ids,20211011dingwl -->
    <select id="queryApiScriptByIds" resultType="java.util.Map">
    	SELECT * FROM trade_flow t
		WHERE t.id IN
            <foreach collection="tradeFlowIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    <!-- 查询api脚本,通过api脚本id,20211013dingwl -->
    <select id="findApiScriptById" resultType="java.util.Map">
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                SELECT
                ( SELECT COUNT(*) FROM ds_testcase dt WHERE dt.tradeFlowId = t.id) caseNum,
                (SELECT REPLACE(CAST(WM_CONCAT(f2.nodeName) AS VARCHAR),',','->') as tradeList from
                (select f.* from
                (SELECT CASE
                WHEN t.trade_name is not null THEN t.trade_name
                when tt.sql_name is not null then tt.sql_name
                when ms.code is not null then ms.code
                when st.script_name is not null then st.script_name
                when dnt.name is not null then dnt.name
                when cst.name is not null then cst.name
                end nodeName,n.trade_flow_id,m.node_order
                FROM flow_node_mapping m
                LEFT JOIN trade_flow_node n ON m.trade_flow_node_id = n.id
                LEFT JOIN trade t ON n.trade_id = t.id
                LEFT JOIN sql_reuse tt ON n.trade_id = tt.id
                LEFT JOIN methods ms ON n.trade_id = ms.id
                LEFT JOIN script_template st ON n.id = st.trade_flow_node_id
                LEFT JOIN def_node_template dnt ON n.id = dnt.trade_flow_node_id
                LEFT JOIN call_sql_template cst ON n.id = cst.trade_flow_node_id
                ) f ORDER By f.node_order
                ) f2
                WHERE f2.trade_flow_id = t.id

                ) tradeList,
                t.id,t.name,t.script_folder_id scriptFolderId,t.script_test_project_id scriptTestProjectId
                FROM trade_flow t
                WHERE t.id = #{tradeFlowId}
            </when>
            <otherwise>
                SELECT
                <!-- 关联案例数量 -->
                (SELECT COUNT(*) FROM ds_testcase dt WHERE dt.tradeFlowId = t.id) caseNum,
                <!-- 交易集合tradeList -->
                (SELECT GROUP_CONCAT(f.nodeName ORDER BY node_order SEPARATOR '->') as tradeList from
                (SELECT CASE
                WHEN t.trade_name is not null THEN t.trade_name
                when tt.sql_name is not null then tt.sql_name
                when ms.`code` is not null then ms.`code`
                when st.script_name is not null then st.script_name
                when dnt.`name` is not null then dnt.`name`
                when cst.`name` is not null then cst.`name`
                end nodeName,n.trade_flow_id,m.node_order
                FROM flow_node_mapping m
                LEFT JOIN trade_flow_node n ON m.trade_flow_node_id = n.id
                LEFT JOIN trade t ON n.trade_id = t.id
                LEFT JOIN sql_reuse tt ON n.trade_id = tt.id
                LEFT JOIN methods ms ON n.trade_id = ms.id
                LEFT JOIN script_template st ON n.id = st.trade_flow_node_id
                LEFT JOIN def_node_template dnt ON n.id = dnt.trade_flow_node_id
                LEFT JOIN call_sql_template cst ON n.id = cst.trade_flow_node_id
                ) f
                WHERE f.trade_flow_id = t.id) tradeList,
                <!-- api脚本信息 -->
                t.id,t.`name`,t.script_folder_id scriptFolderId,t.script_test_project_id scriptTestProjectId
                FROM trade_flow t
                WHERE t.id = #{tradeFlowId}
            </otherwise>
        </choose>
    </select>

    <select id="findProjectCase" resultType="com.jettech.model.TestCase">
        SELECT
        <include refid="testCaseColumnList_tc"/>,
            CASE
                WHEN t2.testProjectResourceID IS NOT NULL THEN t2.testProjectResourceID
                WHEN t3.testProjectResourceID IS NOT NULL THEN t3.testProjectResourceID
                WHEN tc.testProjectId IS NOT NULL THEN tc.testProjectId
            END testProjectResourceID
        FROM
            ds_testcase tc
            LEFT JOIN projectgroup t2 ON tc.projectgroupResourceID = t2.resourceID
            LEFT JOIN (
                SELECT
                    x.resourceID testTaskResourceID,
                    y.resourceID testPlanResourceID,
                    z.resourceID demandResourceID,
                    z.testProjectResourceID
                FROM
                    testtask x
                        JOIN me_testplan y ON x.testPlanResourceID = y.resourceID
                        JOIN ds_demand z ON y.demandResourceID = z.resourceID
            ) t3 ON tc.testtaskResourceID = t3.testTaskResourceID
            JOIN at_trade t4 on tc.tradeResourceID = t4.resourceID
            LEFT JOIN jettechuser t5 on tc.createUser = t5.number
        WHERE
            (t2.testProjectResourceID = #{resourceID} or t3.testProjectResourceID = #{resourceID} or tc.testProjectId = #{resourceID})
        <if test="systemResourceID != null and systemResourceID != ''">
            and t4.testSystemResourceID = #{systemResourceID}
        </if>
        <if test="demandResourceID != null and demandResourceID != ''">
            and t3.demandResourceID = #{demandResourceID}
        </if>
        <if test="createUser != null and createUser != ''">
            and (t5.number like concat('%',#{createUser},'%') or t5.userName like concat('%',#{createUser},'%'))
        </if>
        <if test="startTime != null and startTime != ''">
            <choose>
                <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                    and tc.createTime &gt;= to_date(#{startTime}, 'YYYY-MM-DD')
                </when>
                <otherwise>
                    and date(tc.createTime) &gt;= #{startTime}
                </otherwise>
            </choose>
        </if>
        <if test="endTime != null and endTime != ''">
            <choose>
                <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
                    and tc.createTime &lt;= to_DATE(concat(#{endTime},' 23:59:59'), 'YYYY-MM-DD HH24:MI:SS')
                </when>
                <otherwise>
                    and date(tc.createTime) &lt;= #{endTime}
                </otherwise>
            </choose>
        </if>
        ORDER BY tc.createTime, tc.caseId
    </select>

    <select id="findByProjectGroupResourceID" resultType="com.jettech.model.TestCase">
        select caseId,tradeResourceID from ds_testcase t where t.leadsource = 1 and t.projectgroupResourceID = #{projectGroupResourceID}
    </select>
    <select id="findByTradeResourceIdAndleadSource" resultType="com.jettech.model.TestCase">
        select <include refid="testCaseColumnList_tc"/> from ds_testcase tc left join jettechuser j on tc.createUser = j.number where tc.leadsource = 2 
        <if test="params.tradeResourceId != null and params.tradeResourceId != ''">
            and tc.tradeResourceID = #{params.tradeResourceId}
        </if>
        <if test="params.caseId != null and params.caseId != ''">
            and tc.caseId like concat('%',#{params.caseId},'%') 
        </if>
        <if test="params.caseType != null and params.caseType != ''">
            and tc.caseType = #{params.caseType}
        </if>
        <if test="params.createUser != null and params.createUser != ''">
            and (tc.createUser like concat('%',#{params.createUser},'%') or j.userName like concat('%',#{params.createUser},'%'))
        </if>
        <if test="params.createTime != null and params.createTime != ''">
            and date(tc.createTime) = #{params.createTime}
        </if>
    </select>
    
     <select id="findTaskTradeCaseExport" resultType="com.jettech.view.TaskTradeCaseView">
        SELECT
        <include refid="testCaseColumnList_tc"/>,
        t2.userName createUserName,
        t3.runCount,t3.executor,t3.caseResult
        FROM
        ds_testcase tc
        LEFT JOIN jettechuser t2 ON tc.createUser = t2.number
        JOIN (
        SELECT
        m.testCaseResourceID,
        sum( m.runCount ) runCount,executor,caseResult
        FROM
        (
        SELECT
        x.testCaseResourceID,
        count(a.resourceID) runCount,x.executor,x.caseResult
        FROM
        me_testcasequote x
        LEFT JOIN
        me_caseresultrecord a ON x.resourceID = a.testCaseQuoteResourceID
        LEFT JOIN jettechuser b ON a.createUser = b.number
        <where>
            <if test="executor !=null and executor != ''">
                and (a.createUser like concat('%',#{executor},'%') or b.userName like concat('%',#{executor},'%'))
            </if>
           <!--  <if test="executeResult !=null and executeResult != ''">
                and a.caseResult like concat('%',#{executeResult},'%')
            </if> -->
            <if test="executeTimeStrList != null and executeTimeStrList.size > 0">
                and DATE_FORMAT(a.executeTime,'%Y-%m-%d') &gt;= #{executeTimeStrList[0]}
                and DATE_FORMAT(a.executeTime,'%Y-%m-%d') &lt;= #{executeTimeStrList[1]}
            </if>

            <if test="taskResourceIDs != null and taskResourceIDs.size > 0">
                and x.testTaskResourceID in
                <foreach collection="taskResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
             <if test="executeResult !=null and executeResult != ''">
                and a.caseResult like concat('%',#{executeResult},'%')
            </if>

        </where>
        GROUP BY
        x.testCaseResourceID UNION ALL
        SELECT
        resourceID,
        0 runCount,
        '' as  executor,
		'' as caseResult
        FROM
        ds_testcase
        <where>
            <if test="taskResourceIDs != null and taskResourceIDs.size > 0">
                and testtaskResourceID in
                <foreach collection="taskResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ) m
        GROUP BY
        m.testCaseResourceID
        ) t3 ON tc.resourceID = t3.testCaseResourceID
        <where>
            <if test="tradeResourceIDs != null and tradeResourceIDs.size > 0">
                and tc.tradeResourceID  in
                <foreach collection="tradeResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="createUser !=null and createUser != ''">
                and (tc.createUser like concat('%',#{createUser},'%') or t2.userName like concat('%',#{createUser},'%'))
            </if>
            <if test="caseId != null and caseId != ''">
                and tc.caseId LIKE CONCAT('%',#{caseId},'%')
            </if>
            <if test="intent != null and intent != ''">
                and tc.intent LIKE CONCAT('%',#{intent},'%')
            </if>
            <if test="caseTypes != null and caseTypes.size > 0">
                and tc.caseType in
                <foreach collection="caseTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeStrList != null and createTimeStrList.size > 0">
                and DATE_FORMAT(tc.createTime,'%Y-%m-%d') &gt;= #{createTimeStrList[0]}
                and DATE_FORMAT(tc.createTime,'%Y-%m-%d') &lt;= #{createTimeStrList[1]}
            </if>
            <if test="executor !=null and executor != ''">
                and t3.runCount &gt; 0
            </if>
            <if test="executeResult !=null and executeResult != '' and executeResult!='待执行'">
                and t3.runCount &gt; 0
            </if>
            <if test="executeTimeStrList != null and executeTimeStrList.size > 0">
                and t3.runCount &gt; 0
            </if>
        </where>
    </select>

</mapper>
