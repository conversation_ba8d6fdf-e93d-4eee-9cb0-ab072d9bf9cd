<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestCaseMapper">
    <resultMap type="com.jettech.model.AtTestCase" id="testCaseMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="caseId" column="caseId"/>
        <result property="intent" column="intent"/>
        <result property="isNegative" column="isNegative"/>
        <result property="casetLevel" column="casetLevel"/>
        <result property="caseType" column="caseType"/>
        <result property="testStep" column="testStep"/>
        <result property="expectedResult" column="expectedResult"/>
        <result property="preconditions" column="preconditions"/>
        <result property="tradeResourceID" column="tradeResourceID"/>
        <result property="timingName" column="timingName"/>
        <result property="maintainer" column="maintainer"/>
        <result property="maintenanceTime" column="maintenanceTime"/>
        <result property="comment" column="comment"/>
        <result property="demandResourceID" column="demandResourceID"/>
        <result property="reviewStatus" column="reviewStatus"/>
        <result property="caseEditId" column="caseEditId"/>
        <result property="testEnviroment" column="testEnviroment"/>
        <result property="comments1" column="comments1"/>
        <result property="comments2" column="comments2"/>
        <result property="comments3" column="comments3"/>
        <result property="comments4" column="comments4"/>
        <result property="comments5" column="comments5"/>
        <result property="comments6" column="comments6"/>
        <result property="comments7" column="comments7"/>
        <result property="comments8" column="comments8"/>
        <result property="comments9" column="comments9"/>
        <result property="comments10" column="comments10"/>
        <result property="name" column="name"/>
        <result property="testMode" column="testMode"/>
    </resultMap>

    <sql id="testCaseColumnList">
        id, createTime as "createTime", createUser as "createUser", editTime as "editTime", editUser as "editUser", resourceID as "resourceID", version,
        caseId as "caseId", isNegative as "isNegative",casetLevel as "casetLevel",caseType as "caseType",testStep as "testStep",expectedResult as "expectedResult",
        preconditions,tradeResourceID as "tradeResourceID",timingName as "timingName",maintainer,maintenanceTime as "maintenanceTime",checkPoint as "checkPoint",`intent`,`comment`
        ,demandResourceID as "demandResourceID",reviewStatus as "reviewStatus",caseEditId as "caseEditId",testEnviroment as "testEnviroment",comments1,comments2,
        comments3,comments4,comments5,comments6,comments7,comments8,comments9,comments10,`name`,testMode as "testMode"
    </sql>

    <sql id="testSystemColumnList">
        id, createTime, createUser, editTime, editUser, resourceID, version,`number`, `name`,describes,manager,simpleName,
        busPCentralizedDepartment,developCompany,projectManager,SITTestManger,SITTesters,important,developPerson,
        testMode
    </sql>


    <!--根据当前交易查询其下的所有案例-->
    <select id="findbyTradeResourceID" resultMap="testCaseMap">
        SELECT
        <include refid="testCaseColumnList"/>
        from at_testcase where tradeResourceID = #{arg0}
    </select>
    <select id="findbyTradeResourceIDList" resultMap="testCaseMap">
        SELECT
        <include refid="testCaseColumnList"/>
        from at_testcase where tradeResourceID in
        <foreach collection="list" item="tradeResourceID" open="(" close=")" separator=",">
            #{tradeResourceID}
        </foreach>
    </select>
    <!-- 查询符合条件的案例总数 -->
    <select id="findCountByTradeResourceID" resultType="java.lang.Integer">
        SELECT count(1) from at_testcase WHERE
         1=1
        <if test="tradeResourceIDs != null and tradeResourceIDs.size() > 0">
            and at_testcase.tradeResourceID
            in
            <foreach collection="tradeResourceIDs" item="tradeResourceID" open="(" close=")" separator=",">
                #{tradeResourceID}
            </foreach>
        </if>
        <if test="testEnvironment != null and testEnvironment != ''">
            and testEnviroment = #{testEnvironment}
        </if>
        <if test="maintainer != null and maintainer != ''">
            and
                maintainer LIKE CONCAT('%',#{maintainer},'%')
        </if>
        <if test="isNegative != null and isNegative != ''">
            and isNegative = #{isNegative}
        </if>
        <if test="caseType != null and caseType != ''">
            and caseType=#{caseType}
        </if>
        <!-- AND reviewStatus = '0' -->
    </select>

    <!-- 查询符合条件的数据带分页 -->
    <select id="findByTradeAndOptions" resultType="java.util.Map">
        SELECT
            <include refid="testCaseColumnList"/>
        FROM
            at_testcase
        WHERE
        1=1
        <if test="tradeResourceIDs != null and tradeResourceIDs.size() > 0">
            and tradeResourceID in
            <foreach collection="tradeResourceIDs" item="tradeResourceID" open="(" close=")" separator=",">
                #{tradeResourceID}
            </foreach>
        </if>
        <if test="testEnvironment != null and testEnvironment != ''">
            and testEnviroment = #{testEnvironment}
        </if>
        <if test="maintainer != null and maintainer != ''">
            and
            maintainer LIKE CONCAT('%',#{maintainer},'%')
        </if>
        <if test="isNegative != null and isNegative != ''">
            and isNegative = #{isNegative}
        </if>
        <if test="caseType != null and caseType != ''">
            and caseType=#{caseType}
        </if>
        <!-- AND att.reviewStatus = '0' -->
        ORDER BY
            caseId
        LIMIT #{startIndex},
         #{pageSizeInt}
    </select>

    <select id="findByCaseIdsAndTradeResourceID" resultMap="testCaseMap">
        SELECT
          <include refid="testCaseColumnList"/>
        FROM at_testcase
        WHERE
            at_testcase.tradeResourceID = #{tradeResourceID}
         AND at_testcase.caseId IN
        <foreach collection="list" item="caseId" open="(" close=")" separator=",">
            #{caseId}
        </foreach>

    </select>
    <select id="findCountNumberByTradeResourceID" resultType="java.lang.Integer">
        SELECT count(id) FROM at_testcase WHERE tradeResourceID = #{tradeResourceID}
    </select>
    <select id="findCountByCaseID" resultType="java.lang.Integer">
        SELECT count(1) from at_testcase WHERE at_testcase.caseID = #{caseID} and at_testcase.tradeResourceID = #{tradeResourceID}
    </select>

    <!--根据当前交易查询其下案例编号最大的案例-->
    <select id="findMaxTestCasebyTradeResourceID" resultMap="testCaseMap">
        SELECT
        <include refid="testCaseColumnList"/>
        from at_testcase where tradeResourceID = #{tradeResourceID}
        and caseId = (select max(caseId) from at_testcase WHERE tradeResourceID = #{tradeResourceID})
    </select>

    <select id="findByDemandResourceIDAndTestEnviroment" resultMap="testCaseMap">
        select
          <include refid="testCaseColumnList"/>
        from ds_testcase where demandResourceID = #{demandResourceID}
        <if test="env != null and env != ''">
            and testEnviroment = #{env}
        </if>
    </select>

    <insert id="replace"  parameterType="java.util.List">
        replace into at_testcase
        (id, createTime, createUser, editTime, editUser, resourceID, version, caseId, intent, isNegative, casetLevel, caseType, testStep, expectedResult, preconditions, tradeResourceID, timingName, maintainer, maintenanceTime,`comment`, reviewStatus, demandResourceID, caseEditId,testEnviroment,`name`,testMode)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.createTime}, #{item.createUser}, #{item.editTime}, #{item.editUser}, #{item.resourceID}, #{item.version}, #{item.caseId}, #{item.intent}, #{item.isNegative}, #{item.casetLevel}, #{item.caseType}, #{item.testStep}, #{item.expectedResult}, #{item.preconditions}, #{item.tradeResourceID}, #{item.timingName}, #{item.maintainer}, #{item.maintenanceTime}, #{item.comment}, #{item.reviewStatus},
            #{item.demandResourceID}, #{item.caseEditId}, #{item.testEnviroment},#{item.name},#{item.testMode})
        </foreach>
    </insert>

    <select id="findbyTradeResourceIDAndTestEnviroment" resultMap="testCaseMap" >
    	 SELECT
        <include refid="testCaseColumnList"/>
        from at_testcase where tradeResourceID = #{tradeRsourceID}
        <if test="maintainer != null and maintainer != ''">
            and
            maintainer LIKE CONCAT('%',#{maintainer},'%')
        </if>
        <if test="isNegative != null and isNegative != ''">
            and isNegative = #{isNegative}
        </if>
        <if test="caseType != null and caseType != ''">
            and caseType=#{caseType}
        </if>
    </select>
     <select id="findByCaseResourceID" resultType="java.util.Map">
     	SELECT
        	<include refid="testCaseColumnList"/>
        from at_testcase where resourceID = #{resourceID}
     </select>
     <select id="findTestCaseDictionaryData" resultType="java.util.Map">
    	select `name`,infoName as "infoName",value as "value",textName as "textName",dicDirResourceID as "dicDirResourceID" from DataDictionary  where name = #{dicName, typeHandler=com.jettech.typehandler.UpperTypeHandler}
    </select>

    <select id="findTestCaseDictionaryDataByInfoName" resultType="java.util.Map">
    	select `name`,infoName as "infoName",value as "value",textName as "textName",dicDirResourceID as "dicDirResourceID" from DataDictionary  where infoName = #{nameDescription}
    </select>

    <select id="findAllUsersNameAndNumber" resultType="java.util.Map">
        SELECT userName as "userName",`number` FROM jettechuser
    </select>

    <select id="findTestSystemByTradeResourceID" resultType="java.util.Map">
    	SELECT
        <include refid="testSystemColumnList"/>
    	FROM at_testsystem WHERE resourceID = (SELECT testSystemResourceID FROM at_trade WHERE resourceID = #{tradeResourceID})
    </select>

    <select id="findCaseIDByTradeResourceID" resultType="java.lang.String">
     	SELECT t.caseId as "caseId" FROM at_testcase t where t.tradeResourceID = #{tradeResourceID}
     </select>

    <select id="initMyCaseToAssesWithBynumber" resultType="java.util.Map">
        SELECT
	        *
        FROM
        (
        SELECT DISTINCT
            t4.id,
            t1.`number`,
            t4.`intent`,
            t1.name,
            t4.caseType as "caseType",
            t4.caseId as "caseId",
            t7.caseResult as "caseResult",
            t7.caseResultDescription as "caseResultDescription",
            t7.executeTime as "executeTime"
        FROM
            ds_demand t1
            INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID
            INNER JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID
            AND t3.number = "shanbaoxin"
            INNER JOIN ds_testcase t4 ON t4.demandResourceID = t1.resourceID
            <if test="params.caseType !=null and params.caseType !=''">
                AND t4.caseType = #{params.caseType}
            </if>
            LEFT JOIN me_testcasequote t5 ON t5.testCaseResourceID = t4.resourceID
            <choose>
                <when test="params.caseResult !=null and params.caseResult !=''">
                    AND t5.caseResult = #{params.caseResult}
                </when>
                <otherwise>
                    AND t5.caseResult = '阻塞'
                    OR t5.testCaseResourceID = t4.resourceID
                    AND t5.caseResult = '取消'
                </otherwise>
            </choose>
            LEFT JOIN performcase t6 ON t6.testcaseResourceID = t4.resourceID
            <choose>
                <when test="params.caseResult !=null and params.caseResult !=''">
                    AND t6.caseResult = #{params.caseResult}
                </when>
                <otherwise>
                    AND t6.caseResult = '取消'
                    OR t6.testcaseResourceID = t4.resourceID
                    AND t6.caseResult = '阻塞'
                </otherwise>
            </choose>
            INNER JOIN me_caseresultrecord t7 ON t7.testCaseQuoteResourceID = t4.resourceID
            <if test="params.caseResult !=null and params.caseResult !=''">
                AND t7.caseResult = #{params.caseResult}
            </if>
            AND t7.id IN ( SELECT Max( t8.id ) FROM me_caseresultrecord t8 WHERE t8.testCaseQuoteResourceID = t4.resourceID )
            <where>
                <if test="params.demandName !=null and params.demandName !=''">
                    t1.name LIKE CONCAT('%', #{params.demandName}, '%')
                </if>
                <if test="params.number !=null and params.number !=''">
                    and t1.number like CONCAT('%', #{params.number}, '%')
                </if>
                <if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
                    and t1.testProjectResourceID=#{params.testProjectResourceID}
                </if>
            </where>
             UNION
        SELECT DISTINCT
            t4.id,
            t1.`number`,
            t4.`intent`,
            t1.name,
            t4.caseType as "caseType",
            t4.caseId as "caseId",
            t7.caseResult as "caseResult",
            t7.caseResultDescription as "caseResultDescription",
            t7.executeTime as "executeTime"
        FROM
            ds_demand t1
            INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID
            INNER JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID
            AND t3.number = "shanbaoxin"
            INNER JOIN ds_testcase t4 ON t4.demandResourceID = t1.resourceID
        <if test="params.caseType !=null and params.caseType !=''">
            AND t4.caseType = #{params.caseType}
        </if>
        LEFT JOIN me_testcasequote t5 ON t5.testCaseResourceID = t4.resourceID
        <choose>
            <when test="params.caseResult !=null and params.caseResult !=''">
                AND t5.caseResult = #{params.caseResult}
            </when>
            <otherwise>
                AND t5.caseResult = '阻塞'
                OR t5.testCaseResourceID = t4.resourceID
                AND t5.caseResult = '取消'
            </otherwise>
        </choose>
        LEFT JOIN performcase t6 ON t6.testcaseResourceID = t4.resourceID
        <choose>
            <when test="params.caseResult !=null and params.caseResult !=''">
                AND t6.caseResult = #{params.caseResult}
            </when>
            <otherwise>
                AND t6.caseResult = '取消'
                OR t6.testcaseResourceID = t4.resourceID
                AND t6.caseResult = '阻塞'
            </otherwise>
        </choose>
        INNER JOIN me_caseresultrecord t7 ON t7.testCaseQuoteResourceID = t4.resourceID
        <if test="params.caseResult !=null and params.caseResult !=''">
            AND t7.caseResult = #{params.caseResult}
        </if>
        AND t7.id IN ( SELECT Max( t8.id ) FROM me_caseresultrecord t8 WHERE t8.testCaseQuoteResourceID = t4.resourceID )
        <where>
            <if test="params.demandName !=null and params.demandName !=''">
                t1.name LIKE CONCAT('%', #{params.demandName}, '%')
            </if>
            <if test="params.number !=null and params.number !=''">
                and t1.number like CONCAT('%', #{params.number}, '%')
            </if>
            <if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
                and t1.testProjectResourceID=#{params.testProjectResourceID}
            </if>
        </where>
        ) t
    </select>
    
     <select id="findTradeBytypeResourceID" resultType="java.lang.String">
        select resourceID as "resourceID" from
        at_trade where 1=1 
        <if test="type=='system'">
              and   testSystemResourceID=#{resourceID}
        </if>
        <if test="type=='module'">
              and   moduleResourceID=#{resourceID}
        </if>
        
    </select>
    
</mapper>
