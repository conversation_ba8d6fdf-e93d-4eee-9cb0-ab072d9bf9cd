<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestSystemApplyMapper">
    <resultMap type="com.jettech.model.TestSystemApply" id="testSystemApplyMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="applyNumber" column="applyNumber"/>
        <result property="applyName" column="applyName"/>
        <result property="applyType" column="applyType"/>
        <result property="applyDescribes" column="applyDescribes"/>
        <result property="systemResourceID" column="systemResourceID"/>
    </resultMap>

    <sql id="testSystemApplyColumnList">
        id, createTime, createUser, editTime, editUser, resourceID, version,
        applyNumber,applyName,applyType,applyDescribes,systemResourceID
    </sql>

    <!-- 系统应用 -->
    <select id="findTestSystemApply" resultMap="testSystemApplyMap">
        SELECT
        <include refid="testSystemApplyColumnList"/>
        FROM at_apply
        <where>
            <if test="params.systemResourceID != null and params.systemResourceID != ''">
                AND systemResourceID = #{params.systemResourceID}
            </if>
            <if test="params.applyNumber != null and params.applyNumber != ''">
                AND applyNumber like concat('%',#{params.applyNumber},'%')
            </if>
            <if test="params.applyName != null and params.applyName != ''">
                AND applyName like concat('%',#{params.applyName},'%')
            </if>
            <if test="params.applyType != null and params.applyType != ''">
                AND applyType = #{params.applyType}
            </if>
            <if test="params.applyDescribes != null and params.applyDescribes != ''">
                AND applyDescribes like concat('%',#{params.applyDescribes},'%')
            </if>
        </where>
        ORDER BY at_apply.createTime DESC
    </select>

    <!-- 查询系统下应用最大编号 -->
    <select id="findMaxApplyNumber" resultType="String">
        SELECT applyNumber FROM at_apply  WHERE applyNumber like concat(#{systemName},'%') and systemResourceID = #{sysResourceID} ORDER BY LENGTH(applyNumber) DESC,applyNumber DESC  LIMIT 1;
    </select>
</mapper>