<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IDemandMapper">
	<resultMap type="com.jettech.model.Demand" id="demandMap">
         <id property="id" column="id"/>
         <result property="version" column="version"/>
         <result property="createTime" column="createTime"/>
         <result property="editTime" column="editTime"/>
         <result property="createUser" column="createUser"/>
         <result property="editUser" column="editUser"/>
         <result property="resourceID" column="resourceID"/>
         <result property="name" column="name"/>
         <result property="number" column="number"/>
         <result property="centralizedDepartment" column="centralizedDepartment"/>
         <result property="type" column="type"/>
         <result property="typename" column="typename"/>
         <result property="projectManagerResourceID" column="projectManagerResourceID"/>
         <result property="testManagerResourceID" column="testManagerResourceID"/>
         <result property="demandCreateTime" column="demandCreateTime"/>
		 <result property="level" column="level"/>
		 <result property="proposerResourceID" column="proposerResourceID"/>
		 <result property="testProjectResourceID" column="testProjectResourceID"/>
         <result property="testProjectName" column="testProjectName"/>
		 <result property="remarks" column="remarks"/>
    </resultMap>

	<resultMap type="com.jettech.DTO.DemandItemDto" id="demandItemMap">
		<result property="resourceID" column="resourceID"/>
		<result property="name" column="name"/>
		<result property="number" column="number"/>
	</resultMap>

	<sql id="demandColumnList">
        id, createTime as "createTime", createUser as "createUser", editTime as "editTime", editUser as "editUser", resourceID as "resourceID", version,
        name,centralizedDepartment as "centralizedDepartment",typename,projectManagerResourceID as "projectManagerResourceID",testManagerResourceID as "testManagerResourceID",
        demandCreateTime as "demandCreateTime",proposerResourceID as "proposerResourceID",testProjectResourceID as "testProjectResourceID",testProjectName as "testProjectName",
		<choose>
			<when test="_databaseId == 'dm'">
				CAST(remarks as VARCHAR) as remarks,
			</when>
			<otherwise>
				remarks,
			</otherwise>
		</choose>
		`type`,`number`,`level`
    </sql>

	<sql id="demandColumnList_t">
		t.id, t.createTime as "createTime", t.createUser as "createUser", t.editTime as "editTime", t.editUser as "editUser", t.resourceID as "resourceID", t.version,
		t.name,t.typename,t.centralizedDepartment as "centralizedDepartment",t.`type`,t.`number`,t.`level`,
		<choose>
			<when test="_databaseId == 'dm'">
				CAST(t.remarks as VARCHAR) as remarks,
			</when>
			<otherwise>
				t.remarks,
			</otherwise>
		</choose>
		t.projectManagerResourceID as "projectManagerResourceID",t.testManagerResourceID as "testManagerResourceID",
		t.demandCreateTime as "demandCreateTime",t.proposerResourceID as "proposerResourceID",t.testProjectResourceID as "testProjectResourceID",t.testProjectName as "testProjectName"
	</sql>

	<!-- 查询需求 -->
	<select id="queryDemand" resultType="java.util.Map">
		select
		    <include refid="demandColumnList_t"/>
		from
		    ds_demand t
		<choose>
			<when test="projectManagerName != null and projectManagerName != '' ">
				left join jettechuser ju1 ON t.projectManagerResourceID = ju1.resourceID
			</when>
		</choose>
		<choose>
			<when test="testManagerName != null and testManagerName != ''">
				left join jettechuser ju2 ON t.testManagerResourceID = ju2.resourceID
			</when>
		</choose>
		<where>
			<if test="name != null and name != ''">
				and t.name like concat(concat('%',#{name}),'%')
			</if>
			<if test="number != null and number != ''">
				and	t.number like concat(concat('%',#{number}),'%')
			</if>
			<if test="typeName != null and typeName != ''">
				and t.typename in
				<foreach collection="typeName.split(',')" item="typeNameOne" open="(" close=")" separator=",">
					#{typeNameOne}
				</foreach>
			</if>
			<if test="level != null and level != ''">
				and t.`level` in
				<foreach collection="level.split(',')" item="levelOne" open="(" close=")" separator=",">
					#{levelOne}
				</foreach>
			</if>
			<if test="testProjectResourceID != null and testProjectResourceID != ''">
				and t.testProjectResourceID in
				<foreach collection="testProjectResourceID.split(',')" item="testProjectResourceIDOne" open="(" close=")" separator=",">
					#{testProjectResourceIDOne}
				</foreach>
			</if>
			<if test="testSystemResourceID != null and testSystemResourceID != ''">
				and exists (
					select * from ds_demandtestsystem
					where demandResourceID = t.resourceID
					and testSystemResourceID in
					<foreach collection="testSystemResourceID.split(',')" item="testSystemResourceIDOne" open="(" close=")" separator=",">
						#{testSystemResourceIDOne}
					</foreach>
				)
			</if>
			<if test="projectManagerName != null and projectManagerName != ''">
				and ju1.userName like concat(concat('%',#{projectManagerName}),'%')
			</if>
			<if test="testManagerName != null and testManagerName != ''">
				and ju2.userName like concat(concat('%',#{testManagerName}),'%')
			</if>
		</where>
		order by t.createTime desc,t.resourceID desc
	</select>

	<!-- 读取经理姓名 -->
	<select id="getManagerName" resultType="java.lang.String">
		select
		    t.userName as "managerName"
		from
		    jettechuser t
		where
		    t.resourceID = #{projectManagerResourceID}
	</select>

	<!-- 查询中心名称 -->
	<select id="getCentName" resultType="java.lang.String">
		select
		    t.textName as "centName"
		from
		    datadictionary t
		where
		    t.name = 'CENTRALIZEDBUSINESSDEPARTMENT'
		    and t.value = #{centralizedDepartment}
	</select>

	<!-- 查询系统名称 -->
	<select id="getSystemNameList" resultType="java.lang.String">
		select
		    distinct t.name
		from
		    at_testsystem t
		where
		    t.resourceID in (
		        select testSystemResourceID from ds_demandtestsystem
		        where demandResourceID = #{demandResourceID}
		    )
	</select>

	<select id="delectDemand">
	DELETE from ds_demand where resourceID=#{resourceID}
	</select>
	<select id="getDemandTotal" resultType="java.lang.Integer">
	 select count(1) from ds_demand
	</select>
	<select id="delectDemandAndDemanduser">
	DELETE from ds_demanduser where demandResourceID=#{resourceID}
	</select>

	<select id="delectDemandAndDemandtestsystem">
	DELETE from ds_demandtestsystem where demandResourceID=#{resourceID}
	</select>

	<select id="findByNumber" resultMap="demandMap">
		select <include refid="demandColumnList"/> from ds_demand
		where number =#{number}
	</select>

	<!-- 查询需求下的案例和引用待执行的案例  chencong  -->
	<select id="findCaseAnduUnexecutedUnderDemand" resultType="java.util.Map">
		SELECT
			t.resourceID as "resourceID",
			t.name,
			t.`number`,
			t.`type`,
			t.typename,
			t.dstestcaseNumber as "dstestcaseNumber",t1.dstestcaseNumberNot as "dstestcaseNumberNot"
		from
			(SELECT
			 	dsd.name,
			 	dsd.resourceID,
			 	dsd.number,
			 	dsd.type,
			 	dsd.typename,
			 	count(a.resourceID) dstestcaseNumber
			FROM
				ds_demand dsd
				left JOIN ds_demanduser ddu on dsd.resourceID=ddu.demandResourceID
				left JOIN jettechuser  ju on ddu.userResourceID=ju.resourceID
				left JOIN (
				select dst.* from ds_testcase dst
				) a on  a.demandResourceID = dsd.resourceID
				and a.createUser=#{params.userNumber}
				where (
				ju.resourceID=#{params.userResourceID}
				<if test="params.number !=null and params.number !=''">
					and dsd.number like CONCAT('%', #{params.number}, '%')
				</if>
				<if test="params.typename !=null and params.typename !=''">
					and dsd.typename=#{params.typename}
				</if>
				)
				GROUP BY dsd.resourceID,dsd.NAME,dsd.number,dsd.type,dsd.typename)t
		INNER JOIN
			(SELECT
				dsd.resourceID,
				dsd.name,
				dsd.number,
				dsd.type,
				dsd.typename,
			    count(a.rid) dstestcaseNumberNot
			FROM
				ds_demand dsd
				left JOIN ds_demanduser ddu on dsd.resourceID=ddu.demandResourceID
				left JOIN jettechuser  ju on ddu.userResourceID=ju.resourceID
				left JOIN (
				select metq.* ,metp.resourceID as rid ,metp.demandResourceID from me_testcasequote  metq
				left JOIN  me_testplan  metp on metq.testPlanResourceID=metp.resourceID
				and metq.caseResult='待执行'
				) a
				on  a.demandResourceID = dsd.resourceID
				and a.executorNumber=#{params.userNumber}
				where (
				ju.resourceID=#{params.userResourceID}
				<if test="params.number !=null and params.number !=''">
					and dsd.number like CONCAT('%', #{params.number}, '%')
				</if>
				<if test="params.typename !=null and params.typename !=''">
					and dsd.typename=#{params.typename}
				</if>
				)
				GROUP BY dsd.resourceID,dsd.NAME,dsd.number,dsd.type,dsd.typename ) t1
		on t.resourceID=t1.resourceID
		ORDER  BY  t.resourceID desc
	</select>
    <select id="queryNotOnLineDemand" resultMap="demandMap">
		select <include refid="demandColumnList"/> from ds_demand where type != '6'
	</select>
	<select id="findDemandPageByNumber" resultType="java.util.Map">
		SELECT DISTINCT
			t1.`number`,
			t1.name,
			t1.typename,
			t1.resourceID as "resourceID",
			t1.createTime as "createTime"
		FROM
			ds_demand t1
			LEFT JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID
			JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID
			AND t3.number = #{userNumber}
		WHERE
			t1.type!=6
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		 ORDER BY t1.createTime DESC,t1.resourceID  DESC
	</select>

	<select id="findWorkbenchAlreadyDemandPageByNumber" resultType="java.util.Map">
		SELECT DISTINCT
			t1.number,
			t1.name,
			t1.typename,
			t1.resourceID,
			t1.createTime
		FROM
			ds_demand t1
			LEFT JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID
			JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID
			AND t3.number = #{userNumber}
		WHERE
			t1.type=6
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		 ORDER BY t1.createTime DESC,t1.resourceID  DESC

	</select>

	<select id="findWorkbenchCreateDemandPageByNumber" resultType="java.util.Map">
		SELECT * FROM (SELECT DISTINCT
			t1.`number`,
			t1.name,
			t1.typename,
			t1.resourceID,
			t1.createTime,
			t2.planName as "planName",
			t2.finishTime as "finishTime",
			t2.startTime as "startTime",
			<choose>
				<when test="_databaseId == 'dm'">
					CAST(t3.guidelineinfo AS VARCHAR) as guidelineinfo,
				</when>
				<otherwise>
					t3.guidelineinfo,
				</otherwise>
			</choose>
			t3.score
		FROM
			ds_demand t1
			JOIN me_testplan t2 ON t2.demandResourceID = t1.resourceID and t1.createUser = #{userNumber} and t1.type not in (4,6)
		<if test="params.planName !=null and params.planName !=''">
			and t2.planName like CONCAT('%', #{params.planName}, '%')
		</if>
			LEFT JOIN application_guideline t3 ON t3.application_resourceID = t2.resourceID
			INNER JOIN testtask t4 ON t1.resourceID = t4.demandResourceID AND t4.`status` != 3
		<where>
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		</where>
		 UNION
		 SELECT DISTINCT
			t1.`number`,
			t1.name,
			t1.typename,
			t1.resourceID,
			t1.createTime,
			t2.planName as "planName",
			t2.finishTime as "finishTime",
			t2.startTime as "startTime",
			<choose>
				<when test="_databaseId == 'dm'">
					CAST(t3.guidelineinfo AS VARCHAR) as guidelineinfo,
				</when>
				<otherwise>
					t3.guidelineinfo,
				</otherwise>
			</choose>
			t3.score
		FROM
			ds_demand t1
			JOIN me_testplan t2 ON t2.demandResourceID = t1.resourceID  and t1.type not in (4,6)
		<if test="params.planName !=null and params.planName !=''">
			and t2.planName like CONCAT('%', #{params.planName}, '%')
		</if>
			LEFT JOIN testtask t6 ON  t6.testPlanResourceID=t2.resourceID
			LEFT JOIN application_guideline t3 ON t3.application_resourceID = t2.resourceID
	<where>
		t1.createUser = #{userNumber}
		and t6.demandResourceID is NULL
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
	</where>
	) t
		 ORDER BY t.score DESC, t.createTime DESC,t.resourceID  DESC
	</select>

	<select id="findUnExecuteUnderDemand" resultType="java.util.Map">
	  SELECT t1.resourceID as "demandResourceID", count(1) unexecute FROM ds_demand t1 JOIN me_testplan t2 ON t1.resourceID = t2.demandResourceID
	  	LEFT JOIN me_testcasequote t3 on t2.resourceID = t3.testPlanResourceID WHERE t3.executorNumber = #{userNumber} and t1.resourceID IN
        <foreach collection="demandResourceIDs" item="demandResourceID" open="(" close=")" separator=",">
            #{demandResourceID}
        </foreach>
		AND t3.caseResult = '待执行' GROUP BY t1.resourceID
    </select>

  <update id="updateDemand" >
      update ds_demand
      <set>
      		<if test="demand.createTime != null">createTime=#{demand.createTime},</if>
			<if test="demand.editTime != null">editTime=#{demand.editTime},</if>
			<if test="demand.createUser != null">createUser=#{demand.createUser},</if>
			<if test="demand.editUser != null">editUser=#{demand.editUser},</if>
			<if test="demand.resourceID != null">resourceID=#{demand.resourceID},</if>
			<if test="demand.name != null">name=#{demand.name},</if>
			<if test="demand.number != null">number=#{demand.number},</if>
			<if test="demand.centralizedDepartment != null">centralizedDepartment=#{demand.centralizedDepartment},</if>
			<!-- <if test="demand.hostTribe != null">hostTribe=#{demand.hostTribe},</if>
			<if test="demand.hostTeam != null">hostTeam=#{demand.hostTeam},</if> -->
			<if test="demand.testProjectResourceID != null">testProjectResourceID=#{demand.testProjectResourceID},</if>
			<if test="demand.testProjectName != null">testProjectName=#{demand.testProjectName},</if>
			<if test="demand.type != null">type=#{demand.type},</if>
			<if test="demand.typename != null">typename=#{demand.typename},</if>
			projectManagerResourceID=#{demand.projectManagerResourceID},
			demandCreateTime=#{demand.demandCreateTime},
		  	<if test="demand.testManagerResourceID != null and demand.testManagerResourceID != ''">
		  		testManagerResourceID = #{demand.testManagerResourceID},
			</if>
			<if test="demand.level != null">`level`=#{demand.level},</if>
			<if test="demand.proposerResourceID != null">proposerResourceID=#{demand.proposerResourceID},</if>
		    <if test="demand.remarks != null">remarks=#{demand.remarks},</if>
      </set>
      <where>
        <if test="demand.id != null">
            id = #{demand.id}
        </if>
      </where>
  </update>

      <select id="findAllUsersForDemand"  resultType = "java.util.Map">
		SELECT
			u.id,
			u.resourceID as "resourceID",
			u.userName as "userName",
			u.number AS "userNumber"
		FROM
		  testprojectuser t
			LEFT JOIN jettechUser u ON t.userResourceID=u.resourceID
		WHERE
			t.testProjectResourceID=#{testProjectResourceID}
</select>

	<select id="testVenture" resultType="java.util.Map">
	SELECT
		mt.planName as "planName",
		ma.name  testname,
		mt.testStage as "testStage",
		mt.testRound as "testRound",
		mp.venture venture
	FROM me_testplan as mt
	LEFT JOIN me_testplansystem as mp on mt.resourceID=mp.testPlanResourceID
	LEFT JOIN at_testsystem as ma on ma.resourceID=mp.systemResourceID
	 where  mt.demandResourceID=#{demandResourceID}
	</select>

    <select id="findDemandIDs" resultType="java.util.Map">
		SELECT
			<choose>
				<when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId) or _databaseId == 'dm'">
					dd.number as "需求编号",
					dd.name as "需求名称",
					dd.typename as "需求状态",
					CASE WHEN dd.testProjectName IS NULL THEN '' ELSE dd.testProjectName END as "所属项目",
					CASE WHEN a.textName IS NULL THEN '' ELSE a.textName END AS "业务归口部门",
					CASE WHEN b.textName IS NULL THEN '' ELSE b.textName END AS "需求级别",
					CASE WHEN c.username IS NULL THEN '' ELSE c.username END AS "需求提出人",
					CASE WHEN d.username IS NULL THEN '' ELSE d.username END AS "需求项目经理",
					CASE WHEN e.username IS NULL THEN '' ELSE e.username END AS "测试经理"
				</when>
				<otherwise>
					dd.number '需求编号',
					dd.name '需求名称',
					dd.typename '需求状态',
					CASE WHEN dd.testProjectName IS NULL THEN '' ELSE dd.testProjectName END '所属项目',
					CASE WHEN a.textName IS NULL THEN '' ELSE a.textName END AS '业务归口部门',
					CASE WHEN b.textName IS NULL THEN '' ELSE b.textName END AS '需求级别',
					CASE WHEN c.username IS NULL THEN '' ELSE c.username END AS '需求提出人',
					CASE WHEN d.username IS NULL THEN '' ELSE d.username END AS '需求项目经理',
					CASE WHEN e.username IS NULL THEN '' ELSE e.username END AS '测试经理'
				</otherwise>
			</choose>
		FROM
			ds_demand dd
		LEFT JOIN (SELECT DISTINCT dt.`value`, infoName, dt.textName FROM datadictionary dt) a ON a.`value` = dd.centralizedDepartment AND a.infoName = '业务归口部门'
		LEFT JOIN (SELECT DISTINCT dt.`value`, infoName, dt.textName FROM datadictionary dt) b ON b.`value` = dd.`level` AND b.infoName = '需求级别'
		LEFT JOIN jettechuser c ON dd.proposerResourceID = c.resourceID
		LEFT JOIN jettechuser d ON dd.projectManagerResourceID = d.resourceID
		LEFT JOIN jettechuser e ON dd.testManagerResourceID = e.resourceID
        <where>
            <if test="ids!=null and ids.size()!=0">
                dd.resourceID in
                <foreach collection="ids" item="resourceID" open="(" separator="," close=")" >
                    #{resourceID}
                </foreach>
            </if>
        </where>

    </select>
    <select id="findDemandsNotOnLine" resultMap="demandMap">
		select
		`number`
		,name,resourceID from ds_demand where typename != '已上线' and type != '6'
	</select>
	<delete id="deleteTestTaskByDemandResourceID">
		DELETE FROM testtask WHERE demandResourceID = #{demandResourceID}
	</delete>
	<select id="findTestTaskByDemandResourceID" resultType="java.util.Map">
		SELECT
		id, createTime as "createTime", createUser as "createUser", editTime as "editTime", editUser as "editUser", resourceID as "resourceID", version,
        taskNumber as "taskNumber",name,startTime as "startTime",endTime as "endTime",
		`type`,`level`,
        managerResourceID as "managerResourceID",status,description,demandResourceID as "demandResourceID",testPlanResourceID as "testPlanResourceID", testProjectId as "testProjectId"
		FROM testtask WHERE demandResourceID = #{demandResourceID}
	</select>

	<select id="findByTestProjectResourceID" resultMap="demandMap">
		select <include refid="demandColumnList"/> FROM ds_demand WHERE testProjectResourceID=#{testProjectResourceID}
	</select>

	<select id="findByTestProjectResourceIDs" resultMap="demandMap">
		SELECT
		dd.resourceID as resourceID,
		dd.testProjectResourceID,
		dd.`type`
		FROM
		ds_demand dd
		<where>
			<if test="ids!=null and ids.size()!=0">
				dd.testProjectResourceID in
				<foreach collection="ids" item="resourceID" open="(" separator="," close=")" >
					#{resourceID}
				</foreach>
			</if>
		</where>
	</select>

	<select id="initMyDemandToDealWithByNumber" resultType="java.util.Map">
		SELECT * FROM (SELECT DISTINCT
			t1.name,
			t1.typename,
			t1.resourceID,
			t1.createTime,
			t4.planName as "planName",
			t4.startTime as "startTime",
			t4.finishTime as "finishTime",
			t4.resourceID as "testCaseResourceID",
			t1.`number`,
			<choose>
				<when test="_databaseId == 'dm'">
					CAST(t5.guidelineinfo AS VARCHAR) as guidelineinfo,
				</when>
				<otherwise>
					t5.guidelineinfo,
				</otherwise>
			</choose>
			t5.score
		FROM
			ds_demand t1
		    INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID and  t1.type not in (4,6)
		    INNER JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID  and t3.number = #{userNumber}
		    INNER JOIN me_testplan t4 ON t4.demandResourceID = t1.resourceID
		<if test="params.planName !=null and params.planName !=''">
			and t4.planName like CONCAT('%', #{params.planName}, '%')
		</if>
			INNER JOIN testtask t6 ON  t6.testPlanResourceID=t4.resourceID  AND t6.`status` != 3
		    LEFT JOIN application_guideline t5 ON t5.application_resourceID = concat(t4.resourceID, '')
		<where>
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		</where>
		 UNION
		 SELECT DISTINCT
			t1.name,
			t1.typename,
			t1.resourceID,
			t1.createTime as "createTime",
			t4.planName as "planName",
			t4.startTime as "startTime",
			t4.finishTime as "finishTime",
			t4.resourceID as "testCaseResourceID",
			t1.`number`,
			<choose>
				<when test="_databaseId == 'dm'">
					CAST(t5.guidelineinfo AS VARCHAR) as "guidelineinfo",
				</when>
				<otherwise>
					t5.guidelineinfo as "guidelineinfo",
				</otherwise>
			</choose>
			t5.score
		FROM
			ds_demand t1
			INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID and  t1.type not in (4,6)
		    INNER JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID and  t3.number = #{userNumber}
		    INNER JOIN me_testplan t4 ON t4.demandResourceID = t1.resourceID
		<if test="params.planName !=null and params.planName !=''">
			and t4.planName like CONCAT('%', #{params.planName}, '%')
		</if>
			LEFT JOIN testtask t6 ON  t6.testPlanResourceID=t4.resourceID
			LEFT JOIN application_guideline t5 ON t5.application_resourceID = concat(t4.resourceID, '')
		<where>
			t6.demandResourceID is NULL
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		</where>
		) t
		 ORDER BY t.score DESC, t.createTime DESC,t.resourceID  DESC
	</select>

	<select id="initMyDemandNumberToDealWith" resultType="java.lang.Integer">
		SELECT COUNT(t.resourceID)
		from(
		SELECT
			DISTINCT t1.resourceID
		FROM
			ds_demand t1
		    INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID and t1.type not in (4,6)
		    INNER JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID and t3.number = #{params.userNumber}
			INNER JOIN me_testplan t4 ON t4.demandResourceID = t1.resourceID
			INNER JOIN testtask t6 ON  t6.testPlanResourceID=t4.resourceID AND t6.`status` != 3
			LEFT JOIN application_guideline t5 ON t5.application_resourceID = t4.resourceID

		<where>
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		</where>
		UNION
		SELECT
			DISTINCT t1.resourceID
		FROM
			ds_demand t1
		INNER JOIN ds_demanduser t2 ON t1.resourceID = t2.demandResourceID and t1.type not in (4,6)
		INNER JOIN jettechuser t3 ON t3.resourceID = t2.userResourceID and t3.number = #{params.userNumber}
		INNER JOIN me_testplan t4 ON t4.demandResourceID = t1.resourceID
		LEFT JOIN testtask t6 ON  t6.testPlanResourceID=t4.resourceID
		LEFT JOIN application_guideline t5 ON t5.application_resourceID = t4.resourceID
		<where>
			t6.demandResourceID is NULL
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		</where>
		) t
	</select>
	<select id="findAllByName" resultMap="demandMap">
		select
			<include refid="demandColumnList_t"/>
		from ds_demand t
		where exists (select 1 from me_testplan where demandResourceID = t.resourceID)
		<if test="name !=null and name !=''">
			and t.name like concat('%',#{name},'%')
		</if>
	</select>

	<select id="initWorkbenchCreateMyDemandNumber" resultType="java.lang.Integer">
		SELECT COUNT(t.resourceID) from
		(SELECT
		DISTINCT t1.resourceID
		FROM
		ds_demand t1
		INNER JOIN me_testplan t2 ON t2.demandResourceID = t1.resourceID and  t1.type not in (4,6)
		LEFT JOIN application_guideline t3 ON t3.application_resourceID = concat(t2.resourceID, '')
		INNER JOIN testtask t4 ON t1.resourceID = t4.demandResourceID AND t4.`status` != 3
		WHERE
		t1.createUser = #{params.userNumber}
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>
		UNION
		SELECT
		DISTINCT t1.resourceID
		FROM
		ds_demand t1
		JOIN me_testplan t2 ON t2.demandResourceID = t1.resourceID and  t1.type not in (4,6)
		LEFT JOIN testtask t6 ON  t6.testPlanResourceID=t2.resourceID
		LEFT JOIN application_guideline t3 ON t3.application_resourceID = concat(t2.resourceID, '')
		WHERE
		t1.createUser = #{params.userNumber}
		and t6.demandResourceID is NULL
		<if test="params.demandName !=null and params.demandName !=''">
			and t1.name like CONCAT('%', #{params.demandName}, '%')
		</if>
		<if test="params.number !=null and params.number !=''">
			and t1.number like CONCAT('%', #{params.number}, '%')
		</if>
		<if test="params.typename !=null and params.typename !=''">
			and t1.typename=#{params.typename}
		</if>
		<if test="params.testProjectResourceID !=null and params.testProjectResourceID !=''">
			and t1.testProjectResourceID=#{params.testProjectResourceID}
		</if>) t
	</select>
	<select id="findByTestProjectResourceIDIn" resultMap="demandMap">
		select
		<include refid="demandColumnList"/>
		from
			ds_demand
		where
			testProjectResourceID in
			<foreach collection="resourceIDList" item="resourceID" open="(" separator="," close=")">
				#{resourceID}
			</foreach>
	</select>
	<select id="findBugsClosedStsate" resultType="java.lang.String">
		SELECT
			t2.stateValue as "stateValue"
		FROM
			defectprocessquote t1
		JOIN at_processstate t2 ON t1.defectProcessResourceID = t2.defectProcessResourceID
		WHERE
			t2.isEnd = '1'
		AND t1.projectResourceID = #{testProjectResourceID}
	</select>
	<select id="findBugsByDemandResourceID"  resultType="java.util.Map">
    	SELECT t.resourceID as "resourceID",t.demandResourceID as "demandResourceID",t.defectState as "defectState" FROM at_defect t  WHERE t.demandResourceID = #{demandResourceID}
    </select>

	<select id="findDemandByTypeNames" resultMap="demandMap">
		SELECT
			<include refid = "demandColumnList" />
		FROM ds_demand t
		WHERE
			t.typename IN
			<foreach collection="typeNames" item="typeName" open="(" separator="," close=")">
				#{typeName}
			</foreach>
	</select>
<!--根据条件查询-->
    <select id="findDemandItemByCondition" resultMap="demandItemMap">
		SELECT
			t.resourceID,
			t.name,
			t.`number`
		FROM ds_demand t
		WHERE 1=1
		<if test="condition != null">
			AND
			(t.name LIKE concat('%',#{condition},'%') OR t.number LIKE concat(#{condition},'%'))
		</if>
		<if test="projectIDs !=null and projectIDs.size()>0">
			AND t.testProjectResourceID IN
			<foreach collection="projectIDs" item="projectID" open="(" separator="," close=")">
				#{projectID}
			</foreach>
		</if>
        <if test="demandStatus !=null and demandStatus.size()>0">
            AND typename IN
            <foreach collection="demandStatus"
                     item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
	</select>

	<select id="findDemandNotRelevanceUser" resultType = "java.util.Map">
		SELECT
			j.id,
			j.resourceID as "resourceID",
			j.userName as "userName",
			d.resourceID AS "deptResourceID",
			d.name AS "deptName",
			m.roleName as "roleName"
		FROM
			jettechuser j
		left join (select
		a.userResourceID,
		GROUP_CONCAT(DISTINCT b.name) AS roleName

		from usergroupuser a join usergroup b on a.userGroupResourceID = b.resourceID group by a.userResourceID) m on j.resourceID = m.userResourceID
		LEFT JOIN usergroupuser g ON j.resourceID = g.userResourceID
		LEFT JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
		LEFT JOIN dept d ON j.deptResourceID = d.resourceID
		<if test="testSystemResourceID != null and testSystemResourceID != ''">
			LEFT JOIN ds_testsystem_user t ON t.userResourceID = j.resourceID
		</if>
		WHERE
			j.resourceID NOT IN ( SELECT u.userResourceID FROM ds_demanduser u WHERE u.demandResourceID = #{demandResourceID} )
			<if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
				AND g.userGroupResourceID in
				<foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
					#{userGroupReourceID}
				</foreach>
			</if>
			<if test="name != null and name != '' ">
				AND	j.userName LIKE concat(concat('%',#{name}),'%')
			</if>
			<if test="testSystemResourceID != null and testSystemResourceID != ''">
				AND t.testSystemResourceID = #{testSystemResourceID}
			</if>
		    <if test="deptResourceIDs != null and deptResourceIDs.size > 0">
				and d.resourceID in
				<foreach collection="deptResourceIDs" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		GROUP BY
			j.resourceID,d.resourceID,j.id,j.userName,d.name,m.roleName
	</select>

	<select id="findDemandRelevanceUser" resultType = "java.util.Map">
		SELECT
		    u.resourceID as "duResourceID",
			j.resourceID as "resourceID",
			j.userName as "userName",
			d.resourceID AS "deptResourceID",
			d.name AS "deptName",
			m.roleName as "roleName"
		FROM
			ds_demanduser u
		LEFT JOIN jettechuser j ON j.resourceID = u.userResourceID
		left join (select a.userResourceID,
		GROUP_CONCAT(DISTINCT b.name) AS roleName
		from usergroupuser a join usergroup b on a.userGroupResourceID = b.resourceID group by a.userResourceID) m on j.resourceID = m.userResourceID
		LEFT JOIN usergroupuser g ON u.userResourceID = g.userResourceID
		LEFT JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
		LEFT JOIN dept d ON j.deptResourceID = d.resourceID
		<if test="testSystemResourceID != null and testSystemResourceID != ''">
			LEFT JOIN ds_testsystem_user t ON t.userResourceID = j.resourceID
		</if>
		WHERE
			u.demandResourceID = #{demandResourceID}
			<if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
				AND g.userGroupResourceID in
				<foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
					#{userGroupReourceID}
				</foreach>
			</if>
			<if test="name != null and name != '' ">
				AND	j.userName LIKE concat(concat('%',#{name}),'%')
			</if>
			<if test="testSystemResourceID != null and testSystemResourceID != ''">
				AND t.testSystemResourceID = #{testSystemResourceID}
			</if>
            <if test="deptResourceIDs != null and deptResourceIDs.size > 0">
                and d.resourceID in
                <foreach collection="deptResourceIDs" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
		GROUP BY
			j.resourceID,d.resourceID,u.resourceID, j.userName, d.name, m.roleName
	</select>
	<select id="findByDemandNames" resultMap="demandMap">
		select
		<include refid="demandColumnList" />
		FROM ds_demand
		where 1=1
		and name in
		<foreach collection="list" item="name" open="(" close=")" separator=",">
			#{name}
		</foreach>
	</select>

	<select id="findDemandsByTestProjectResourceID" resultType = "java.util.Map">
		SELECT
			CONCAT(number, '---', NAME) name,
			resourceID as "resourceID"
		FROM
			ds_demand
		WHERE
			testProjectResourceID = #{projectResourceID}
	</select>
	<select id="findProjectBydemandResourceID" resultType="java.util.Map">
		SELECT
			t1.name "label",
			t1.resourceID as "value"
		FROM testproject t1
		JOIN ds_demand t4 ON t1.resourceID = t4.testProjectResourceID
		WHERE
			t4.typename != '已关闭'
		AND t4.resourceID = #{demandResourceID}
	</select>
</mapper>
