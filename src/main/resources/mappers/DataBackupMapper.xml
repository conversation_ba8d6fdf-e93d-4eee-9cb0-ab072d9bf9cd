<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IDataBackupMapper">

    <select id="getCreateTableSql" resultType="java.util.Map">
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                with t as (
                select schema_name,table_name,string_agg(column_name||' '||column_type||' '||column_default_value ||' '||column_not_null||chr(10),',') as aaa from(
                SELECT
                b.nspname as schema_name,
                b.relname as table_name,
                a.attname as column_name,
                pg_catalog.format_type(a.atttypid, a.atttypmod) as column_type,
                CASE WHEN
                (SELECT substring(pg_catalog.pg_get_expr(d.adbin, d.adrelid) for 128)
                FROM pg_catalog.pg_attrdef d
                WHERE d.adrelid = a.attrelid AND d.adnum = a.attnum AND a.atthasdef) IS NOT NULL THEN
                'DEFAULT '|| (SELECT substring(pg_catalog.pg_get_expr(d.adbin, d.adrelid) for 128)
                FROM pg_catalog.pg_attrdef d
                WHERE d.adrelid = a.attrelid AND d.adnum = a.attnum AND a.atthasdef)
                ELSE
                ''
                END as column_default_value,
                CASE WHEN a.attnotnull = true THEN
                'NOT NULL'
                ELSE
                'NULL'
                END as column_not_null,
                a.attnum as attnum,
                e.max_attnum as max_attnum
                FROM
                pg_catalog.pg_attribute a
                INNER JOIN
                (SELECT c.oid,
                n.nspname,
                c.relname
                FROM pg_catalog.pg_class c
                LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
                WHERE c.relname ~ ('^('||'${tableName}'||')$')
                AND pg_catalog.pg_table_is_visible(c.oid)
                ORDER BY 2, 3) b
                ON a.attrelid = b.oid
                INNER JOIN
                (SELECT
                a.attrelid,
                max(a.attnum) as max_attnum
                FROM pg_catalog.pg_attribute a
                WHERE a.attnum > 0
                AND NOT a.attisdropped
                GROUP BY a.attrelid) e
                ON a.attrelid=e.attrelid
                WHERE a.attnum > 0
                AND NOT a.attisdropped
                ORDER BY a.attnum) as f
                GROUP by schema_name,table_name)
                select 'create table '||schema_name||'.'||table_name||' ('||aaa||')' as "Create Table" from t;
            </when>
            <when test="@com.jettech.common.DbIdUtil@isOracleType(_databaseId)">
                SELECT DBMS_METADATA.GET_DDL('TABLE','${tableName}','jettopro') as "Create Table" FROM dual
            </when>
            <otherwise>
                show create table ${tableName}
            </otherwise>
        </choose>
    </select>

    <select id="queryTableData" resultType="java.util.Map">
        select * from ${tableName}
        <foreach collection="params" index="key" item="vals" separator="and" open="where">
            ${key} in
            <foreach collection="vals" item="val" separator="," open="(" close=")">
                #{val}
            </foreach>
        </foreach>
    </select>

    <select id="countTableData" resultType="int">
        select count(*) from ${tableName}
        <foreach collection="params" index="key" item="vals" separator="and" open="where">
            ${key} in
            <foreach collection="vals" item="val" separator="," open="(" close=")">
                #{val}
            </foreach>
        </foreach>
    </select>

    <select id="getAllTable" resultType="java.util.Map">
        <choose>
            <when test="@com.jettech.common.DbIdUtil@isPgType(_databaseId)">
                SELECT table_name FROM information_schema.tables WHERE table_schema in ('public','jettopro');
            </when>
            <when test="@com.jettech.common.DbIdUtil@isOracleType(_databaseId)">
                select table_name from all_tab_comments WHERE owner='jettopro'
            </when>
            <otherwise>
                show tables
            </otherwise>
        </choose>

    </select>

    <delete id="deleteTableData">
        delete from ${tableName}
        <foreach collection="params" index="key" item="vals" separator="and" open="where">
            ${key} in
            <foreach collection="vals" item="val" separator="," open="(" close=")">
                #{val}
            </foreach>
        </foreach>
    </delete>

</mapper>