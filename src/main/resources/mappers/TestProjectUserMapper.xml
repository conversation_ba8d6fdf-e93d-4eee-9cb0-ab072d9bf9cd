<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ITestProjectUserMapper">
	<resultMap type="com.jettech.model.TestProjectUser" id="testProjectUserMap">
         <id property="id" column="id"/>
         <result property="version" column="version"/>
         <result property="createTime" column="createTime"/>
         <result property="editTime" column="editTime"/>
         <result property="createUser" column="createUser"/>
         <result property="editUser" column="editUser"/> 
         <result property="resourceID" column="resourceID"/>
         <result property="userResourceID" column="userResourceID"/>
         <result property="testProjectResourceID" column="testProjectResourceID"/>
    </resultMap>

	<sql id="testProjectUserColumnList">
		id,version, createTime,  editTime, createUser,editUser, resourceID,
		userResourceID,testProjectResourceID
	</sql>
	
	<select id="findByTestProjectResourceID" resultMap="testProjectUserMap">
		SELECT
			<include refid="testProjectUserColumnList"/>
		FROM
			testprojectUser t 
		WHERE
			t.testProjectResourceID=#{resourceID}
	</select>
	
	<select id="findNotRelateUser" resultType="java.util.Map">
		SELECT
			j.resourceID AS "resourceID",
			j.userName AS "userName",
			d.resourceID AS "deptResourceID",
			d.name AS "deptName",
			GROUP_CONCAT(DISTINCT ug.name) AS "roleName"
		FROM
			jettechuser j
			LEFT JOIN usergroupuser g ON j.resourceID = g.userResourceID
			LEFT JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
			LEFT JOIN dept d ON j.deptResourceID = d.resourceID
		WHERE
			j.resourceID NOT IN ( SELECT u.userResourceID FROM testprojectuser u WHERE u.testProjectResourceID = #{testProjectResourceID} )
			<if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
				AND g.userGroupResourceID in
				<foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
					#{userGroupReourceID}
				</foreach>
			</if>
			<if test="name != null and name != '' ">
				AND	j.userName LIKE concat(concat('%',#{name}),'%')
			</if>
			<if test="deptName != null and deptName != '' ">
				AND	d.name LIKE concat(concat('%',#{deptName}),'%')
			</if>
		GROUP BY
			j.resourceID,d.resourceID,d.name, j.userName
	</select>
	
	<select id="findRelatedUser" resultType="java.util.Map">
		SELECT
			j.resourceID AS "resourceID",
			j.userName AS "userName",
			d.resourceID AS "deptResourceID",
			d.name AS "deptName",
			GROUP_CONCAT(DISTINCT ug.name) AS "roleName"
		FROM
			testprojectuser u
			LEFT JOIN jettechuser j ON j.resourceID = u.userResourceID
			LEFT JOIN usergroupuser g ON u.userResourceID = g.userResourceID
			LEFT JOIN usergroup ug ON g.userGroupResourceID = ug.resourceID
			LEFT JOIN dept d ON j.deptResourceID = d.resourceID
		WHERE
			u.testProjectResourceID = #{testProjectResourceID}
			<if test="userGroupReourceIDs != null and userGroupReourceIDs.size > 0">
				AND g.userGroupResourceID in
				<foreach collection="userGroupReourceIDs" index="index" item="userGroupReourceID" open="(" separator="," close=")">
					#{userGroupReourceID}
				</foreach>
			</if>
			<if test="name != null and name != '' ">
				AND	j.userName LIKE concat(concat('%',#{name}),'%')
			</if>
			<if test="deptName != null and deptName != '' ">
				AND	d.name LIKE concat(concat('%',#{deptName}),'%')
			</if>
		GROUP BY
			j.resourceID,d.resourceID ,j.userName,d.name
	</select>
	
	<select id="findByTestProjectResourceIDAndUserResourceIDIn" resultMap="testProjectUserMap">
		SELECT
			<include refid="testProjectUserColumnList"/> 
		FROM
			testprojectUser t 
		WHERE
			t.testProjectResourceID=#{testProjectResourceID}
			AND t.userResourceID IN
			<foreach collection="userResourceIDs" item="userResourceID" open="(" close=")" separator=",">
            #{userResourceID}
        </foreach>
	</select>
	
	
	<select id="findByTestProjectResourceIDIN" resultMap="testProjectUserMap">
		SELECT
			<include refid="testProjectUserColumnList"/> 
		FROM
			testprojectUser t 
		WHERE
			t.testProjectResourceID IN
			<foreach collection="resourceIDList" item="resourceID" open="(" close=")" separator=",">
            #{resourceID}
        </foreach>
	</select>

	<select id="findALlProjectAndUser" resultType="com.jettech.DTO.ProjectUserDTO">
		select
		pu.testProjectResourceID,ju.userName,ju.number userNumber,p.name projectName
		from testprojectuser pu
		INNER JOIN jettechuser ju on ju.resourceID = pu.userResourceID
		INNER JOIN testproject p on p.resourceID = pu.testProjectResourceID
	</select>
</mapper>