<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ILeftTreeConfigurationMapper">
    <resultMap type="com.jettech.model.LeftTreeConfiguration" id="leftTreeConfigurationMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <!-- 表字段start -->
        <result property="demandResourceID" column="demandResourceID"/>
        <result property="configurationType" column="configurationType"/>
        <result property="configuration" column="configuration"/>
        <!-- 表字段end -->
    </resultMap>

    <sql id="leftTreeConfigurationColumnList">
        id, createTime, createUser, editTime, editUser, resourceID,
        demandResourceID, configurationType, configuration
    </sql>

    <!--根据用户和项目的id以及配置等级查询配置数据-->
    <select id="findLeftTreeConfigurationByDemandResrouceIDAndType" resultMap="leftTreeConfigurationMap">
        SELECT
        <include refid="leftTreeConfigurationColumnList"/>
        FROM ds_lefttree_configuration
        WHERE demandResourceID = #{demandResourceID}
        AND configurationType = #{leftType}
    </select>
</mapper>