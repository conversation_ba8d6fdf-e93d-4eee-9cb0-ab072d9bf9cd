<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IProjectGroupMapper">
    <resultMap type="com.jettech.model.ProjectGroup" id="projectGroupMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="name" column="name"/>
        <result property="testProjectResourceID" column="testProjectResourceID"/>
        <result property="parentResourceID" column="parentResourceID"/>
        <result property="isSmallPoints" column="isSmallPoints"/>
        <result property="groupType" column="groupType"/>
    </resultMap>

    <sql id="projectGroupColumnList">
        id, createTime, createUser, editTime, editUser, resourceID, version,
        name,testProjectResourceID,parentResourceID,isSmallPoints,groupType
    </sql>

    <select id="findByTestProjectResourceID" resultMap="projectGroupMap">
    	SELECT
    		<include refid="projectGroupColumnList"/>
    	FROM
    		projectGroup
    	WHERE
    		testProjectResourceID=#{resourceID}
        <if test="groupType != null and groupType != ''">
            and groupType=#{groupType}
        </if>
    </select>

    <select id="findByParentResourceID" resultMap="projectGroupMap">
        select
        <include refid="projectGroupColumnList" />
        from projectgroup
        where
        parentResourceID = #{resourceID}
        <if test="groupType != null and groupType != ''">
            and groupType=#{groupType}
        </if>
    </select>
    <select id="findByProjectRidsAndIsSmallPoint"  resultType="java.lang.String">
        select
        resourceID as "resourceID"
        from projectgroup
        where
        isSmallPoints = 1
        and groupType=#{groupType}
        <if test="projectResourceIDs!=null and projectResourceIDs.size()>0">
            and
            testProjectResourceID in
            <foreach collection="projectResourceIDs" item="projectResoureID" open="(" close=")" separator=",">
                #{projectResoureID}
            </foreach>
        </if>


    </select>

    <select id="findByParentIdAndName" resultMap="projectGroupMap">
        select
        <include refid="projectGroupColumnList" />
        from projectgroup
        where
        groupType = #{groupType}
        <choose>
            <when test="parentResourceID != null">
                and name = #{name} and parentResourceID = #{parentResourceID} and testProjectResourceID = #{testProjectResourceID}
            </when>
            <otherwise>
                and name = #{name} and parentResourceID is null and testProjectResourceID = #{testProjectResourceID}
            </otherwise>
        </choose>
    </select>


    <select id="findByTestProjectIdAndParentIdAndName" parameterType="com.jettech.model.ProjectGroup" resultMap="projectGroupMap">
        select
        <include refid="projectGroupColumnList" />
        from projectgroup
        where name = #{projectGroup.name} and testProjectResourceID = #{projectGroup.testProjectResourceID} and groupType = #{projectGroup.groupType}
        <choose>
            <when test="projectGroup.parentResourceID != null">
                and parentResourceID = #{projectGroup.parentResourceID}
            </when>
        </choose>
    </select>


    <select id="findDefectByGroupResourceIDs"  resultType="java.util.Map">
        SELECT t1.createUser as "createUser",t1.handleUserNumber as "handleUserNumber" FROM at_defect t1
			WHERE t1.projectgroupResourceID in
        <foreach collection="projectGroups" item="projectGroup" open="(" close=")" separator=",">
            #{projectGroup}
        </foreach>
    </select>

    <select id="findByProjectRIDsAndGroupType"  resultType="com.jettech.model.ProjectGroup">
        select
        <include refid="projectGroupColumnList"/>
        from projectgroup
        where groupType=#{groupType}
        <if test="projectResourceIDs!=null and projectResourceIDs.size()>0">
            and
            testProjectResourceID in
            <foreach collection="projectResourceIDs" item="projectResoureID" open="(" close=")" separator=",">
                #{projectResoureID}
            </foreach>
        </if>
    </select>
</mapper>
