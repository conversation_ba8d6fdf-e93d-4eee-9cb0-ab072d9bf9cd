<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.ISystemmoduleMapper">
    <resultMap type="com.jettech.model.SystemModule" id="systemModuleMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>
        <result property="name" column="name"/>
        <result property="testSystemResourceID" column="testSystemResourceID"/>
        <result property="parentResourceID" column="parentResourceID"/>
        <result property="simpleName" column="simpleName"/>
        <result property="isSystemChild" column="isSystemChild"/>
    </resultMap>
    <sql id="systemModuleColumnList">
        id,version, createTime,  editTime, createUser,editUser, resourceID,name,testSystemResourceID,parentResourceID,simpleName,isSystemChild
    </sql>


    <select id="verifyModuleNameNotRepeatedOfSystem" resultMap="systemModuleMap">
        SELECT <include refid="systemModuleColumnList"/> FROM at_systemModule WHERE name = #{name}
        AND parentResourceID = #{testSystemResourceID}
        AND testSystemResourceID = #{testSystemResourceID}
        <if test="resourceID != null ">
            AND resourceID != #{resourceID}
        </if>

    </select>
    <select id="verifyModuleNameNotRepeatedOfModule" resultMap="systemModuleMap">
        SELECT <include refid="systemModuleColumnList"/> FROM at_systemModule WHERE name = #{name}
        AND parentResourceID = #{parentResourceID}
        <if test="resourceID != null and resourceID !=''">
            AND resourceID != #{resourceID}
        </if>
    </select>

    <select id="verifyModuleSimpleNameNotRepeatedOfModule" resultMap="systemModuleMap">
        SELECT <include refid="systemModuleColumnList"/> FROM at_systemModule WHERE simpleName = #{name}
        AND parentResourceID = #{parentResourceID}
        <if test="resourceID != null ">
            AND resourceID != #{resourceID}
        </if>
    </select>

    <select id="findByParentResourceID" resultMap="systemModuleMap">
        SELECT <include refid="systemModuleColumnList"/> FROM at_systemModule WHERE parentResourceID = #{parentResourceID}
    </select>
    <!-- 通过被测系统resourceID查询模块 -->
    <select id="findByTestSystemResourceIDs" resultType="java.util.HashMap" >
        SELECT resourceID as "resourceID",name,testSystemResourceID as "testSystemResourceID",parentResourceID as "parentResourceID",'module' AS "type" from at_systemmodule where testSystemResourceID in
         <foreach collection="list" item="value" open="(" close=")" separator=",">
             #{value}
         </foreach>
         order by createTime
    </select>
    <!--查询当前被测系统下所有的模块（包含子模块）-->
    <select id="findbyTestSystemResourceID" resultMap="systemModuleMap">
        SELECT <include refid="systemModuleColumnList"/> FROM at_systemModule where testSystemResourceID = #{testSystemResourceID}
    </select>

    <select id="findbyTestSystemResourceID2" resultType="com.jettech.model.SystemModule">
        select
        t.resourceID,
        t.id,
        t.name,
        t.parentResourceID
        from at_systemModule t WHERE t.testSystemResourceID = #{testSystemResourceID}
    </select>
    <select id="findNextLowerLevelMapByTestSystemResourceID"
            resultType="java.util.HashMap">
        select <include refid="systemModuleColumnList"/> from at_systemmodule where parentResourceID = #{testSystemResourceID} and testSystemResourceID = #{testSystemResourceID}
    </select>
    <select id="findbySystemModuleName" resultMap="systemModuleMap">
        select
        <include refid="systemModuleColumnList"/>
        from at_systemmodule where name = #{systemModuleName}
    </select>
    <select id="findUserUnderModuleTaskTradeCount" resultType="java.util.HashMap">
			<if test="flag == 'system' ">
			SELECT
					testSystemResourceID as "systemOrModuleResourceID",
					COUNT(testSystemResourceID) as "quoteSum"
				FROM testtasktrade tt
				WHERE
					 tt.createUser = #{userNumber}
					AND tt.testTaskResourceID = #{taskResourceID}
				AND tt.testSystemResourceID IS not NULL
				GROUP BY
				testSystemResourceID
			</if>
			<if test="flag == 'module' ">
			   SELECT
					systemModuleResourceID as "systemOrModuleResourceID",
					COUNT(systemModuleResourceID) as "quoteSum"
				FROM testtasktrade tt
				WHERE
					 tt.createUser = #{userNumber}
					AND tt.testTaskResourceID = #{taskResourceID}
				AND tt.testSystemResourceID IS NULL
			GROUP BY
				systemModuleResourceID
			</if>
    </select>
    
    <select id="findUnderSystemORModuleTradeCount" resultType="java.util.Map">
    		<if test="flag == 'module' ">
			SELECT
				tr.moduleResourceID AS "systemOrModuleResourceID",
					count(tr.moduleResourceID) as "sumCount"
				FROM at_trade tr
				WHERE
					tr.moduleResourceID IS NOT NULL
				AND tr.testSystemResourceID IS NOT NULL
				GROUP BY
					tr.moduleResourceID
			</if>
			<if test="flag == 'system' ">
				SELECT
					tr.testSystemResourceID AS "systemOrModuleResourceID",
					count(tr.testSystemResourceID) as "sumCount"
				FROM at_trade tr
				WHERE
					tr.moduleResourceID IS NULL
				AND tr.testSystemResourceID IS NOT NULL
				GROUP BY
					tr.testSystemResourceID;
			</if>
    </select>
    
     <select id="findByNameAndTestSystemResourceIDAndParentResourceID" resultMap="systemModuleMap">
        select
        <include refid="systemModuleColumnList"/>
        from at_systemmodule where name = #{name} and testSystemResourceID=#{testSystemResourceID} 
         <if test="parentResourceID != null ">
        	and parentResourceID=#{parentResourceID}
        </if>
    </select>

    <select id="findByDemandResourceID" resultType="com.jettech.model.TreeNode">
        SELECT
            DISTINCT asy.resourceID AS "resourceID",
            asy.name AS name,
            'module' as "type",
            asy.parentResourceID AS parentResourceID
        FROM
            ds_demandtestsystem ds,
            at_systemmodule asy
        WHERE
            ds.testSystemResourceID = asy.testSystemResourceID
        <if test="demandResourceID != null and demandResourceID != ''">
            AND ds.demandResourceID = #{demandResourceID}
        </if>

    </select>
    <select id="findParentModuleByRid" resultMap="systemModuleMap">
        SELECT <include refid="systemModuleColumnList"/> FROM at_systemmodule WHERE resourceID = #{moduleResourceID}
    </select>

    <select id="findByTestSystemResIdIn" resultType="com.jettech.model.SystemModule">
        select * from at_systemmodule
        where testSystemResourceID in
        <foreach collection="systemResIdList" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
    </select>

</mapper>