<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IWikiPageUserTemConfigMapper">

    <delete id="deleteByParam" >
        delete from WIKI_PAGE_USER_TEM_CONFIG
        <where>
            <if test="pageResourceId != null" >
                and PAGE_RESOURCE_ID = #{pageResourceId}
            </if>
            <if test="spaceResourceId != null" >
                and SPACE_RESOURCE_ID = #{spaceResourceId}
            </if>
            <if test="createUser != null" >
                and createUser = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="type != null" >
                and `TYPE` = #{type}
            </if>
        </where>
    </delete>

    <select id="listPageUserByParam" resultType="com.jettech.model.WikiPage">
        SELECT wp.id,
        wput.createTime,wp.createUser,wput.editTime,wp.editUser,wp.resourceID,wp.parent_resource_id,wp.space_resource_id,wp.title,wp.page_type,wp.version,wp.is_inrecycle,wp.is_draft,
        ws.SPACE_NAME,
        ws.SPACE_TYPE
        FROM WIKI_PAGE_USER_TEM_CONFIG wput
        LEFT JOIN WIKI_PAGE wp ON wp.resourceID = wput.page_resource_id
        LEFT JOIN WIKI_SPACE ws ON ws.resourceID = wp.space_resource_id
        WHERE 1=1
        <if test="config.spaceList != null and config.spaceList.size > 0">
            and wput.space_resource_id IN
            <foreach collection="config.spaceList" open="(" close=")" item="item" separator=",">
                #{item.resourceID}
            </foreach>
        </if>
        <if test="config.pageName != null and config.pageName != ''">
            and wp.title like concat('%',#{config.pageName},'%')
        </if>
        and wput.createUser = #{config.createUser}
        and wput.`TYPE`= #{config.type}
        and wp.id is not null
        and wp.IS_INRECYCLE = 0
        order by wput.editTime DESC
    </select>
</mapper>
