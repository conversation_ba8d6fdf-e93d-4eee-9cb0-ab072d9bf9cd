<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IApplyRroundMapper">
    <resultMap type="com.jettech.model.ApplyRround" id="applyRroundMap">
        <id property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="createTime" column="createTime"/>
        <result property="editTime" column="editTime"/>
        <result property="createUser" column="createUser"/>
        <result property="editUser" column="editUser"/>
        <result property="resourceID" column="resourceID"/>

        <result property="applyNumber" column="applyNumber"/>
        <result property="applyName" column="applyName"/>
        <result property="applyType" column="applyType"/>
        <result property="applyDescribes" column="applyDescribes"/>
        <result property="applyResourceID" column="applyResourceID"/>
    </resultMap>

    <sql id="applyRroundColumnList">
        id, createTime, createUser, editTime, editUser, resourceID, version,
        round,ip,port,remarks,applyResourceID
    </sql>

    <!-- 轮次 -->
    <select id="findApplyRround" resultMap="applyRroundMap">
        SELECT
        <include refid="applyRroundColumnList"/>
        FROM at_apply_round
        where applyResourceID = #{params.applyResourceID}
        ORDER BY at_apply_round.createTime DESC
    </select>
</mapper>