<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.mapper.IUserMapper">
	<resultMap type="com.jettech.model.User" id="userMap">
         <id property="id" column="id"/>
         <result property="version" column="version"/>
         <result property="createTime" column="createTime"/>
         <result property="editTime" column="editTime"/>
         <result property="createUser" column="createUser"/>
         <result property="editUser" column="editUser"/> 
         <result property="resourceID" column="resourceID"/>
         <result property="userName" column="userName"/>
         <result property="password" column="password"/>
         <result property="number" column="number"/>
         <result property="status" column="status"/>
         <result property="code" column="code"/>
         <result property="userLevel" column="userLevel"/>
         <result property="deptResourceID" column="deptResourceID"/>
         <result property="phone" column="phone"/>
         <result property="email" column="email"/>
         <result property="remarks" column="remarks"/>
         <result property="extUserId" column="extUserId"/>
         <result property="idcard" column="idcard"/>
         <result property="address" column="address"/>
         <result property="teller" column="teller"/>
         <result property="userType" column="userType"/>
    </resultMap>
	<sql id="userColumnList">
		id, version, createTime, editTime, createUser, editUser, resourceID, userName, password,
		`number`,
		 deptResourceID, depttype, sex, phone, developmentCenter, email, projectManagerResourceID,
		status, centralizedBusinessDepartment, userType
	</sql>
	<select id="findByUserName" resultMap="userMap">
		SELECT
			<include refid="userColumnList"/>
		FROM
			jettechuser
		WHERE
			userName = #{userName}
	</select>
	<select id="findByNumber" resultMap="userMap">
		SELECT
		<include refid="userColumnList"/>
		FROM
			jettechuser
		WHERE
			number = #{number}
	</select>
    <select id="findByUserResourceIDs" resultType="java.lang.String" parameterType="java.util.List">
		SELECT
			t1.userName
		FROM
			jettechuser t1
		WHERE
			t1.resourceID in
			<foreach collection="list" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
	</select>
    <select id="findByUserNumberResourceIDs" resultType="java.lang.String" parameterType="java.util.List">
		SELECT
			t1.`number`
		FROM
			jettechuser t1
		WHERE
			t1.resourceID in
			<foreach collection="list" open="(" separator="," close=")" item="item">
				#{item}
			</foreach>
	</select>
</mapper>