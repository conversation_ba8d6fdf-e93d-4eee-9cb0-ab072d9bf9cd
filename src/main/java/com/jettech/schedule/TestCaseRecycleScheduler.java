package com.jettech.schedule;

import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.service.iservice.ITestCaseRecycleBinService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class TestCaseRecycleScheduler {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(TestCaseRecycleScheduler.class);
	
	@Autowired
	private IFeignDataDesignToBasicService iFeignDataDesignToBasicService;
	// 案例回收月份系统字典key
	private static final String TESTCASE_RECYCLE_MONTH_KEY = "testCaseRecycleMonth";
	
	@Autowired
	private ITestCaseRecycleBinService testCaseRecycleBinService;
	//@Scheduled(fixedRate = 2 * 60 * 1000)
	@Scheduled(fixedRate = 5 * 1000)
	public void testTasks() {
//		String token = HttpRequestUtils.getCurrentRequestToken();
//		String monthString = iFeignDataDesignToBasicService.findValueByKey(TESTCASE_RECYCLE_MONTH_KEY,"");
//		
//		Integer month = Integer.parseInt(monthString.replaceAll("\"", "").trim());
//		
//		LocalDate ld = LocalDate.now();
//		LocalDate minusMonthsLD = ld.minusMonths(month);
//		ZonedDateTime zonedDateTime = minusMonthsLD.atStartOfDay(ZoneId.systemDefault());
//
//		List<TestCaseRecycleBin> tsetcaseCaseRecycleBinList = testCaseRecycleBinService.findByLECreateTime(Date.from(zonedDateTime.toInstant()));
//		if(tsetcaseCaseRecycleBinList.size() > 0) {
//			LOGGER.info("需要清理的案例回收站条数："+tsetcaseCaseRecycleBinList.size());
//			testCaseRecycleBinService.deleteInBatch(tsetcaseCaseRecycleBinList, "Admin");
//		}
		
	}
}
