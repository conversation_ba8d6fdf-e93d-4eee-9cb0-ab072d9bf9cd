package com.jettech.schedule;

import com.jettech.model.DataBackupHistory;
import com.jettech.model.Demand;
import com.jettech.service.iservice.IDataBackupHistoryService;
import com.jettech.service.iservice.IDataBackupService;
import com.jettech.service.iservice.IDemandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class DataBackupScheduler {

    @Autowired
    private IDemandService demandService;

    @Autowired
    private IDataBackupService dataBackupService;

    @Autowired
    private IDataBackupHistoryService dataBackupHistoryService;

    @Scheduled(cron = "0 0 5 * * ?")
    public void dataBackupScheduler() {
        List<DataBackupHistory> dataBackupHistoryList = dataBackupHistoryService.findUnBackup();
        List<DataBackupHistory> updateList = new ArrayList<>();
        if (!dataBackupHistoryList.isEmpty()) {
            for (DataBackupHistory dataBackupHistory : dataBackupHistoryList) {
                List<Demand> demandList = demandService.findByResourceIDIn(Arrays.asList(dataBackupHistory.getDemandResourceID().split(",")));
                int status = this.dataBackupService.dataBackup(demandList.stream().map(Demand::getResourceID).collect(Collectors.toList()), dataBackupHistory.getFilePath(), dataBackupHistory.getFileName(), dataBackupHistory.isClear());
                dataBackupHistory.setStatus(status);
                dataBackupHistory.setBackupTime(new Date());
                updateList.add(dataBackupHistory);
            }
        }
        if (!updateList.isEmpty()) {
            dataBackupHistoryService.update(updateList, "Admin");
        }
    }
}
