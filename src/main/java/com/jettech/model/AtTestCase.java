package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jettech.util.FieldNote;
import com.jettech.util.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName TestCase
 * @description 案例
 * <AUTHOR>
 * @create 2019-11-04 18:54
 */
@Entity
@TableName("at_testCase")
//@ClassDataDicTionaryTab(true)
public class AtTestCase extends BaseModelHB implements Serializable {

	private static final long serialVersionUID = 5210854557029131062L;

	/**
	 * 案例编号
	 */
	@FieldNote(value = "案例编号", type = FieldType.String)
	@TableField(value = "caseId")
	private String caseId;

	/**
	 * 案例名称
	 */
	@FieldNote(value = "案例名称", type = FieldType.String)
	@TableField(value = "name")
	private String name;

	/**
	 * 案例编写专用ID
	 */
	@TableField(value = "caseEditId")
	private String caseEditId;
	/**
	 * 测试环境
	 */
	@TableField(value = "testEnviroment")
	private String testEnviroment;
	/**
	 * 测试意图
	 */
	@FieldNote(value = "测试意图", type = FieldType.String)
	@TableField(value = "intent")
	private String intent;
	/**
	 * 正反例（0反例1正例（默认正例1））
	 */
	@FieldNote(value = "正反例", type = FieldType.String)
	@TableField(value = "isNegative")
	private String isNegative;
	/**
	 * 案例级别(高、中、低)
	 */
	@FieldNote(value = "案例级别", type = FieldType.String)
	@TableField(value = "casetLevel")
	//@DataDicTionaryConverter("案例级别")
	private String casetLevel;
	/**
	 * 案例类型（0:功能案例，1:界面案例）
	 */
	@FieldNote(value = "案例类型", type = FieldType.String)
	@TableField(value = "caseType")
	//@DataDicTionaryConverter("案例类型")
	private String caseType;
	/**
	 * 测试步骤
	 */
	@FieldNote(value = "测试步骤", type = FieldType.String)
	@TableField(value = "testStep")
	private String testStep;
	/**
	 * 预期结果
	 */
	@FieldNote(value = "预期结果", type = FieldType.String)
	@TableField(value = "expectedResult")
	private String expectedResult;
	/**
	 * 前置条件
	 */
	@FieldNote(value = "前置条件", type = FieldType.String)
	@TableField(value = "preconditions")
	private String preconditions;
	/**
	 * 案例所属交易，指向at_Trade表
	 */
	@TableField(value = "tradeResourceID")
	private Long tradeResourceID;
	/**
	 * 案例所属需求，指向ds_demand表
	 */
	@TableField(value = "demandResourceID")
	private Long demandResourceID;
	/**
	 * 运行条件名称
	 */
	@FieldNote(value = "运行条件", type = FieldType.String)
	@TableField(value = "timingName")
	private String timingName;
	/**
	 * 备注
	 */
	@FieldNote(value = "备注",type = FieldType.String)
	@TableField(value = "comment")
	private String comment;
	/**
	 * 维护人
	 */
	@FieldNote(value = "维护人",type = FieldType.String)
	@TableField(value = "maintainer")
	private String maintainer;
	/**
	 * 维护时间
	 */
	@FieldNote(value = "维护时间",type = FieldType.Date)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 入参
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 出参
	@TableField(value = "maintenanceTime")
	private Date maintenanceTime;

	/**
	 * 评审状态
	 */
	@FieldNote(value = "评审状态",type = FieldType.String)
	@TableField(value = "reviewStatus")
	//@DataDicTionaryConverter("评审状态")
	private String reviewStatus;

	/**
	 * 检查点
	 */
	@FieldNote(value = "检查点",type = FieldType.String)
	@TableField(value = "checkPoint")
	private String checkPoint;

	/**
	 * 备用字段1
	 */
	@FieldNote(value = "案例备用字段1",type = FieldType.String)
	@TableField(value = "comments1")
	private String comments1;

	/**
	 * 备用字段2
	 */
	@FieldNote(value = "案例备用字段2",type = FieldType.String)
	@TableField(value = "comments2")
	private String comments2;

	/**
	 * 备用字段3
	 */
	@FieldNote(value = "案例备用字段3",type = FieldType.String)
	@TableField(value = "comments3")
	private String comments3;

	/**
	 * 备用字段4
	 */
	@FieldNote(value = "案例备用字段4",type = FieldType.String)
	@TableField(value = "comments4")
	private String comments4;

	/**
	 * 备用字段5
	 */
	@FieldNote(value = "案例备用字段5",type = FieldType.String)
	@TableField(value = "comments5")
	private String comments5;
	/**
	 * 备用字段6
	 */
	@FieldNote(value = "案例备用字段6",type = FieldType.String)
	@TableField(value = "comments6")
	private String comments6;

	/**
	 * 备用字段7
	 */
	@FieldNote(value = "案例备用字段7",type = FieldType.String)
	@TableField(value = "comments7")
	private String comments7;
	/**
	 * 备用字段8
	 */
	@FieldNote(value = "案例备用字段8",type = FieldType.String)
	@TableField(value = "comments8")
	private String comments8;
	/**
	 * 备用字段9
	 */
	@FieldNote(value = "案例备用字段9",type = FieldType.String)
	@TableField(value = "comments9")
	private String comments9;
	/**
	 * 备用字段10
	 */
	@FieldNote(value = "案例备用字段10",type = FieldType.String)
	@TableField(value = "comments10")
	private String comments10;


	  /**
     *  0:JettoManager,1:JettoAPI,2:JettoUI
     */
    @TableField(value = "testMode")
    private Integer testMode;

	/**
	 * 所属系统名称
	 */
	@FieldNote(value = "所属系统",type = FieldType.String)
	@TableField(value="testsystem")
	private String testsystem;
	/**
	 * 所属系统ResourceID
	 */
	@TableField(value="testsystemResourceID")
	private Long testsystemResourceID;
	/**
	 * 所属模块名称（多级/分开）
	 */
	@FieldNote(value = "所属模块",type = FieldType.String)
	@TableField(value="systemmodule")
	private String systemmodule;
	/**
	 * 所属模块的ResourceID
	 */
	@TableField(value="systemmoduleResourceID")
	private Long systemmoduleResourceID;

	/**
	 * 所属交易名称
	 */
	@FieldNote(value = "所属交易",type = FieldType.String)
	@TableField(value="trade")
	private String trade;

	@FieldNote(value = "测试数据", type = FieldType.String)
	@TableField(value = "dataRequirements")
	private String dataRequirements;

	public String getTestsystem() {
		return testsystem;
	}

	public void setTestsystem(String testsystem) {
		this.testsystem = testsystem;
	}

	public Long getTestsystemResourceID() {
		return testsystemResourceID;
	}

	public void setTestsystemResourceID(Long testsystemResourceID) {
		this.testsystemResourceID = testsystemResourceID;
	}

	public String getSystemmodule() {
		return systemmodule;
	}

	public void setSystemmodule(String systemmodule) {
		this.systemmodule = systemmodule;
	}

	public Long getSystemmoduleResourceID() {
		return systemmoduleResourceID;
	}

	public void setSystemmoduleResourceID(Long systemmoduleResourceID) {
		this.systemmoduleResourceID = systemmoduleResourceID;
	}

	public String getTrade() {
		return trade;
	}

	public void setTrade(String trade) {
		this.trade = trade;
	}

	public String getComments1() {
		return comments1;
	}

	public void setComments1(String comments1) {
		this.comments1 = comments1;
	}

	public String getComments2() {
		return comments2;
	}

	public void setComments2(String comments2) {
		this.comments2 = comments2;
	}

	public String getComments3() {
		return comments3;
	}

	public void setComments3(String comments3) {
		this.comments3 = comments3;
	}

	public String getComments4() {
		return comments4;
	}

	public void setComments4(String comments4) {
		this.comments4 = comments4;
	}

	public String getComments5() {
		return comments5;
	}

	public void setComments5(String comments5) {
		this.comments5 = comments5;
	}

	public String getComments6() {
		return comments6;
	}

	public void setComments6(String comments6) {
		this.comments6 = comments6;
	}

	public String getComments7() {
		return comments7;
	}

	public void setComments7(String comments7) {
		this.comments7 = comments7;
	}

	public String getComments8() {
		return comments8;
	}

	public void setComments8(String comments8) {
		this.comments8 = comments8;
	}

	public String getComments9() {
		return comments9;
	}

	public void setComments9(String comments9) {
		this.comments9 = comments9;
	}

	public String getComments10() {
		return comments10;
	}

	public void setComments10(String comments10) {
		this.comments10 = comments10;
	}


	public String getCaseEditId() {
		return caseEditId;
	}

	public void setCaseEditId(String caseEditId) {
		this.caseEditId = caseEditId;
	}

	public String getReviewStatus() {
		return reviewStatus;
	}

	public void setReviewStatus(String reviewStatus) {
		this.reviewStatus = reviewStatus;
	}

	public Long getDemandResourceID() {
		return demandResourceID;
	}

	public void setDemandResourceID(Long demandResourceID) {
		this.demandResourceID = demandResourceID;
	}

	public String getCaseId() {
		return caseId;
	}

	public void setCaseId(String caseId) {
		this.caseId = caseId;
	}

	public String getIntent() {
		return intent;
	}

	public void setIntent(String intent) {
		this.intent = intent;
	}

	public String getIsNegative() {
		return isNegative;
	}

	public void setIsNegative(String isNegative) {
		this.isNegative = isNegative;
	}

	public String getCasetLevel() {
		return casetLevel;
	}

	public void setCasetLevel(String casetLevel) {
		this.casetLevel = casetLevel;
	}

	public String getCaseType() {
		return caseType;
	}

	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}

	public String getTestStep() {
		return testStep;
	}

	public void setTestStep(String testStep) {
		this.testStep = testStep;
	}

	public String getExpectedResult() {
		return expectedResult;
	}

	public void setExpectedResult(String expectedResult) {
		this.expectedResult = expectedResult;
	}

	public String getPreconditions() {
		return preconditions;
	}

	public void setPreconditions(String preconditions) {
		this.preconditions = preconditions;
	}

	public Long getTradeResourceID() {
		return tradeResourceID;
	}

	public void setTradeResourceID(Long tradeResourceID) {
		this.tradeResourceID = tradeResourceID;
	}

	public String getTimingName() {
		return timingName;
	}

	public void setTimingName(String timingName) {
		this.timingName = timingName;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getMaintainer() {
		return maintainer;
	}

	public void setMaintainer(String maintainer) {
		this.maintainer = maintainer;
	}

	public Date getMaintenanceTime() {
		return maintenanceTime;
	}

	public void setMaintenanceTime(Date maintenanceTime) {
		this.maintenanceTime = maintenanceTime;
	}

	public String getTestEnviroment() {
		return testEnviroment;
	}

	public void setTestEnviroment(String testEnviroment) {
		this.testEnviroment = testEnviroment;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getTestMode() {
		return testMode;
	}

	public void setTestMode(Integer testMode) {
		this.testMode = testMode;
	}

	public String getCheckPoint() {
		return checkPoint;
	}

	public void setCheckPoint(String checkPoint) {
		this.checkPoint = checkPoint;
	}

	public String getDataRequirements() {
		return dataRequirements;
	}

	public void setDataRequirements(String dataRequirements) {
		this.dataRequirements = dataRequirements;
	}
}
