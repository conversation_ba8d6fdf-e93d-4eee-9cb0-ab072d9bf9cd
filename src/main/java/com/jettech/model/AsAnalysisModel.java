package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
* <p>
    * 测试分析模型表
    * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/

@Data
//@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("as_analysis_model")
public class AsAnalysisModel {

    private static final long serialVersionUID = 1L;


    private int id;
    /**
     * 关联的交易
     */
    @TableField("trade_resource_id")
    private Long tradeResourceId;

    /**
     * 测试分析模型名称
     */
    @TableField("name")
    private String name;

    /**
     * 产生用例数
     */
    @TableField("case_num")
    private Integer caseNum;

    /**
     * 模型复用次数
     */
    @TableField("repeated_num")
    private Integer repeatedNum;

    /**
     * 状态0-新建,1-审核中,2-通过,3-已拒绝,4-已变更
     */
    @TableField("status")
    private Integer status;

    /**
     * xmind对应的json数据内容
     */
    @TableField("content")
    private String content;

    /**
     * 案例生成人
     */
    @TableField("review_user")
    private String reviewUser;

    /**
     * 案例生成时间
     */
    @TableField("review_date")
    private Date reviewDate;

    @TableField("update_user")
    private String updateUser;

    @TableField("create_user")
    private String createUser;
    @TableField("update_date")
    private Date updateDate;
}
