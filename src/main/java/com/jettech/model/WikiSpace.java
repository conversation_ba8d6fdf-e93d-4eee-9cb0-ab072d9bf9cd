package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Entity;
import java.util.Date;

@Entity
@TableName("wiki_space")
public class WikiSpace extends BaseModel {

    private String spaceName;

    /**
     * 空间类型
     * 0组织空间
     * 1项目空间
     * 2项目集空间
     */
    private int spaceType;

    /**
     * 空间是否公开 0否 1 是
     */
    @TableField("is_space_public")
    private boolean spacePublic;

    private String description;

    /**
     * 空间公告
     */
    private String notice;

    private Date updateTime;

    /**
     * 项目空间标识
     */
    private String spaceKey;

    /**
     * 删除标志 0 未删除 ；1 已删除
     */
    private boolean delFlag;

    @TableField(exist = false)
    private Integer pageCount;

    @TableField(exist = false)
    private Long pageResourceId;

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public int getSpaceType() {
        return spaceType;
    }

    public void setSpaceType(int spaceType) {
        this.spaceType = spaceType;
    }

    public boolean isSpacePublic() {
        return spacePublic;
    }

    public void setSpacePublic(boolean spacePublic) {
        this.spacePublic = spacePublic;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNotice() {
        return notice;
    }

    public void setNotice(String notice) {
        this.notice = notice;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSpaceKey() {
        return spaceKey;
    }

    public void setSpaceKey(String spaceKey) {
        this.spaceKey = spaceKey;
    }

    public boolean isDelFlag() {
        return delFlag;
    }

    public void setDelFlag(boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getPageCount() {
        return pageCount;
    }

    public void setPageCount(Integer pageCount) {
        this.pageCount = pageCount;
    }

    public Long getPageResourceId() {
        return pageResourceId;
    }

    public void setPageResourceId(Long pageResourceId) {
        this.pageResourceId = pageResourceId;
    }
}
