package com.jettech.model;

import javax.persistence.Column;
import javax.persistence.Entity;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.codehaus.jackson.annotate.JsonProperty;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

@Entity
@TableName("jettechuser")
public class User extends BaseModel {

	/**
	 * 
	 * Modified By: zhangjieying Modified Date: 2013-12-10
	 */
	private static final long serialVersionUID = 5210854557029131062L;

	@JsonProperty(value = "userName")
	@TableField(value="userName")
	private String userName;
	
	@JsonProperty(value = "password")
	@Column(length = 255, name = "pwd")
	@TableField(value="password")
	private String password;
	
	@JsonProperty(value = "number")
	@Column(name = "number")
	@TableField(value="number")
	private String number; // 员工号
	
	@JsonProperty(value = "status")
	@Column(columnDefinition = "int(10) default 0 comment '账号状态  0禁用,1启用已修改密码，2启用未修改密码，3注销'")
	@TableField(value="status")
	private Integer status; // 人员状态（出厂/入场）

	@TableField(value="code")
	private String code;// 登录码
	
	@TableField(value="userLevel")
	private String userLevel;// 级别

	@TableField(value="deptResourceID")
	private Long deptResourceID; // 所属机构
	
	@TableField(value="phone")
	private String phone;// 联系方式
	
	@TableField(value="email")
	private String email;// 电子邮件
	
 	@Column(columnDefinition = "text")
 	@TableField(value="remarks")
 	private String remarks;// 备注

	@Column(columnDefinition = "varchar(255) comment '福农信用户编号'")
	@TableField(value="extUserId")
	private String extUserId;// 外部第三方平台主键===福农信
	
	@Column(columnDefinition = "varchar(255) comment '身份证号'")
	@TableField(value="idcard")
	private String idcard;
	
	@Column(columnDefinition = "varchar(255) comment '地址'")
	@TableField(value="address")
	private String address;
	
	@Column(columnDefinition = "varchar(255) comment ''")
	@TableField(value="teller")
	private String teller;
	
	@Column(columnDefinition = "varchar(255) comment '用户类型'")
	@TableField(value="userType")
	private String userType;//用户类型
	
	@JsonIgnore
	public String getUserName() {
		return userName;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	@JsonIgnore
	public void setUserName(String userName) {
		this.userName = userName;
	}

	@JsonIgnore
	public String getPassword() {
		return password;
	}

	@JsonIgnore
	public void setPassword(String password) {
		this.password = password;
	}

	@JsonIgnore
	public String getNumber() {
		return number;
	}

	@JsonIgnore
	public void setNumber(String number) {
		this.number = number;
	}

	@JsonIgnore
	public Integer getStatus() {
		return status;
	}

	@JsonIgnore
	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getUserLevel() {
		return userLevel;
	}

	public void setUserLevel(String userLevel) {
		this.userLevel = userLevel;
	}

	public String getChineseName() {
		return "用户";
	}

	public Long getDeptResourceID() {
		return deptResourceID;
	}

	public void setDeptResourceID(Long deptResourceID) {
		this.deptResourceID = deptResourceID;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getExtUserId() {
		return extUserId;
	}

	public void setExtUserId(String extUserId) {
		this.extUserId = extUserId;
	}

	public String getIdcard() {
		return idcard;
	}

	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getTeller() {
		return teller;
	}

	public void setTeller(String teller) {
		this.teller = teller;
	}

}
