package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
* <p>
    * 测试分析模型审核表
    * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/

@Data
//@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("as_analysis_model_detail")
public class AsAnalysisModelDetail  {

    private static final long serialVersionUID = 1L;

    private int id;
    /**
     * 评审的分析模型ID
     */
    @TableField("model_id")
    private Long modelId;

    /**
     * 0-审核记录,1-修改记录
     */
    @TableField("type")
    private Integer type;

    /**
     * 状态0-新建,1-审核中,2-通过,3-已拒绝,4-已变更
     */
    @TableField("status")
    private Integer status;

    /**
     * xmind对应的json数据内容
     */
    @TableField("content")
    private String content;

    /**
     *备注
     */
    @TableField("remarks")
    private String remarks;

    @TableField("update_user")
    private String updateUser;
    @TableField("update_date")
    private Date updateDate;
    @TableField("create_date")
    private Date createDate;
    @TableField("create_user")
    private String createUser;
}
