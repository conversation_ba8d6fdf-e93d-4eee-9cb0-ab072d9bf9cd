package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Entity;
import java.util.List;

@Entity
@TableName("wiki_page_comment")
public class WikiPageComment extends BaseModel {

    private Long parentResourceId;

    private Long pageResourceId;

    private String comment;

    @TableField(exist = false)
    private List<WikiPageComment> children;

    @TableField(exist = false)
    private String userName;

    public Long getParentResourceId() {
        return parentResourceId;
    }

    public void setParentResourceId(Long parentResourceId) {
        this.parentResourceId = parentResourceId;
    }

    public Long getPageResourceId() {
        return pageResourceId;
    }

    public void setPageResourceId(Long pageResourceId) {
        this.pageResourceId = pageResourceId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<WikiPageComment> getChildren() {
        return children;
    }

    public void setChildren(List<WikiPageComment> children) {
        this.children = children;
    }
}
