package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Entity;
import java.util.List;

@Entity
@TableName("wiki_page_user_tem_config")
public class WikiPageUserTemConfig extends BaseModel {

    private Long pageResourceId;

    private Long spaceResourceId;

    private int type;

    private boolean delFlag;

    /**
     * 非数据库字段 收藏 0取消收藏 1收藏
     * */
    @TableField(exist = false)
    private Integer collect;

    @TableField(exist = false)
    private int pageNumber;

    @TableField(exist = false)
    private int pageSize;

    @TableField(exist = false)
    private List<WikiSpace> spaceList;

    @TableField(exist = false)
    private String pageName;

    public Long getPageResourceId() {
        return pageResourceId;
    }

    public void setPageResourceId(Long pageResourceId) {
        this.pageResourceId = pageResourceId;
    }

    public Long getSpaceResourceId() {
        return spaceResourceId;
    }

    public void setSpaceResourceId(Long spaceResourceId) {
        this.spaceResourceId = spaceResourceId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isDelFlag() {
        return delFlag;
    }

    public void setDelFlag(boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getCollect() {
        return collect;
    }

    public void setCollect(Integer collect) {
        this.collect = collect;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<WikiSpace> getSpaceList() {
        return spaceList;
    }

    public void setSpaceList(List<WikiSpace> spaceList) {
        this.spaceList = spaceList;
    }

    public String getPageName() {
        return pageName;
    }

    public void setPageName(String pageName) {
        this.pageName = pageName;
    }
}
