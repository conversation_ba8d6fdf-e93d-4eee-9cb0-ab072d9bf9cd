package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.common.annotation.ClassDataDicTionaryTab;
import com.jettech.common.annotation.DataDicTionaryConverter;

import javax.persistence.Entity;

/**
 * @ClassName Environment
 * @description 被测系统环境信息记录
 * <AUTHOR>
 * @create 20200630
 */
@Entity
@TableName("environment")
@ClassDataDicTionaryTab(true)
public class Environment extends BaseModel{
	/**
	 * 
	 */
	private static final long serialVersionUID = 2467240968799705078L;
	/**
	 * 环境名称
	 */
	@TableField(value = "name")
	private String name;
	/**
	 * 环境类型
	 */
	@DataDicTionaryConverter("环境类型")
	@TableField(value = "type")
	private String type;
	/**
	 * ip地址
	 */
	@TableField(value = "ip")
	private String ip;
	/**
	 * 环境状态
	 */
	@TableField(value = "state")
	@DataDicTionaryConverter("环境状态")
	private String state;
	/**
	 * 系统名称
	 */
	@TableField(value = "systemName")
	private String systemName;
	/**
	 * 系统负责人
	 */
	@TableField(value = "systemManager")
	private String systemManager;
	/**
	 * CPU
	 */
	@TableField(value = "cpu")
	private String cpu;
	/**
	 * 内存信息
	 */
	@TableField(value = "memoryInformation")
	private String memoryInformation;
	/**
	 * 操作系统及版本
	 */
	@TableField(value = "operatingSystemInformation")
	private String operatingSystemInformation;
	/**
	 * 数据库及版本
	 */
	@TableField(value = "dataBaseInformation")
	private String dataBaseInformation;
	/**
	 * 说明
	 */
	@TableField(value = "detailedInformation")
	private String detailedInformation;
	/**
	 * 中间件及版本
	 */
	@TableField(value = "middlewareInformation")
	private String middlewareInformation;
	/**
	 * 主机名称
	 */
	@TableField(value = "hostname")
	private String hostname;
	/**
	 * 维护人
	 */
	@TableField(value = "editUserName")
	private String 	editUserName;

	/**
	 * 科室
	 */
	@DataDicTionaryConverter("科室")
	@TableField(value = "department")
	private String department;

	/**
	 * 环境负责人
	 */
	@TableField(value = "envUserName")
	private String envUserName;
	/**
	 * 系统组件
	 */
	@TableField(value = "systemComponents")
	private String systemComponents;

	public String getEnvUserName() {
		return envUserName;
	}

	public void setEnvUserName(String envUserName) {
		this.envUserName = envUserName;
	}

	public String getSystemComponents() {
		return systemComponents;
	}

	public void setSystemComponents(String systemComponents) {
		this.systemComponents = systemComponents;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getSystemName() {
		return systemName;
	}
	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}
	public String getSystemManager() {
		return systemManager;
	}
	public void setSystemManager(String systemManager) {
		this.systemManager = systemManager;
	}
	public String getCpu() {
		return cpu;
	}
	public void setCpu(String cpu) {
		this.cpu = cpu;
	}
	public String getMemoryInformation() {
		return memoryInformation;
	}
	public void setMemoryInformation(String memoryInformation) {
		this.memoryInformation = memoryInformation;
	}
	public String getOperatingSystemInformation() {
		return operatingSystemInformation;
	}
	public void setOperatingSystemInformation(String operatingSystemInformation) {
		this.operatingSystemInformation = operatingSystemInformation;
	}
	public String getDataBaseInformation() {
		return dataBaseInformation;
	}
	public void setDataBaseInformation(String dataBaseInformation) {
		this.dataBaseInformation = dataBaseInformation;
	}
	public String getDetailedInformation() {
		return detailedInformation;
	}
	public void setDetailedInformation(String detailedInformation) {
		this.detailedInformation = detailedInformation;
	}
	public String getMiddlewareInformation() {
		return middlewareInformation;
	}
	public void setMiddlewareInformation(String middlewareInformation) {
		this.middlewareInformation = middlewareInformation;
	}
	public String getHostname() {
		return hostname;
	}
	public void setHostname(String hostname) {
		this.hostname = hostname;
	}
	public String getEditUserName() {
		return editUserName;
	}
	public void setEditUserName(String editUserName) {
		this.editUserName = editUserName;
	}
	
}
