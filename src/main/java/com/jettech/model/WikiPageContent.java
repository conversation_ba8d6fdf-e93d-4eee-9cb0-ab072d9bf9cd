package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Entity;

@Entity
@TableName("wiki_page_content")
public class WikiPageContent extends BaseModel {

    private Long pageResourceId;

    private String content;

    private String textContent;

    public Long getPageResourceId() {
        return pageResourceId;
    }

    public void setPageResourceId(Long pageResourceId) {
        this.pageResourceId = pageResourceId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTextContent() {
        return textContent;
    }

    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }
}
