package com.jettech.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.Version;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: BaseModelHb
 * @projectName jettomanager
 * @description: id和resourceID相同
 * @date 2021/3/2211:05
 */
public class BaseModelHB implements Serializable {
    private static final long serialVersionUID = 6114061703483839280L;
    /**
     * id
     */
    @TableId(value = "id",type = IdType.INPUT)
    private Long id;
    /**
     * 数据版本
     */
    @Version
    @TableField(value="version")
    private Integer version;
    /**
     * 创建时间
     */
    @TableField(value="createTime")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField(value="editTime")
    private Date editTime;
    /**
     * 数据创建者
     */
    @TableField(value="createUser")
    private String createUser;
    /**
     * 数据修改者
     */
    @TableField(value="editUser")
    private String editUser;
    /**
     * 是否读取文本
     */
    @Transient
    @TableField(exist=false)
    protected boolean isLoadContent = false;
    /**
     * 资源ID
     */
    @Column(unique = true)
    @TableField(value="resourceID")
    protected Long resourceID;

    public void setIsLoadContent(boolean isLoadContent) {
        this.isLoadContent = isLoadContent;
    }

    public void onCreate() {
        if(createTime == null)
            createTime = new Date();
        if(editTime == null)
            editTime = new Date();
    }

    public void onUpdate() {
        editTime = new Date();

        if(createTime == null)
            createTime = editTime;
    }

    @Override
    public boolean equals(Object object) {

        if (object == null) {
            return false;
        }
        if (this == object && this.hashCode() == object.hashCode()) {
            return true;
        }

        if (!(object instanceof BaseModel)) {
            return false;
        }

        final BaseModel other = (BaseModel) object;
        Serializable thisId = this.getId();
        Serializable otherId = other.getId();
        if ((thisId == null && otherId != null) || (thisId != null && !thisId.equals(otherId))) {
            return false;
        }
        return true;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getEditTime() {
        return editTime;
    }

    public void setEditTime(Date editTime) {
        this.editTime = editTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getEditUser() {
        return editUser;
    }

    public void setEditUser(String editUser) {
        this.editUser = editUser;
    }

    public Long getResourceID() {
        return resourceID;
    }

    public void setResourceID(Long resourceID) {
        this.resourceID = resourceID;
    }

    public boolean isLoadContent() {
        return isLoadContent;
    }

    public void setLoadContent(boolean isLoadContent) {
        this.isLoadContent = isLoadContent;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

}
