package com.jettech.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Entity;
import java.util.List;

@Entity
@TableName("wiki_page")
public class WikiPage extends BaseModel {

    /*
     * 父页面ID
     */
    private Long parentResourceId;

    /*
     * 空间ID
     */
    private Long spaceResourceId;

    /*
     * 页面标题
     */
    private String title;

    /*
     * 页面类型
     */
    private String pageType;

    @TableField(value = "IS_INRECYCLE")
    private Boolean inRecycle;

    @TableField(exist = false)
    private String content;

    @TableField(exist = false)
    private String textContent;

    @TableField(value = "IS_DRAFT")
    private Boolean draft;

    @TableField(exist = false)
    private String createName;

    @TableField(exist = false)
    private String updateName;

    @TableField(exist = false)
    private List<WikiPage> children;

    @TableField(exist = false)
    private Integer userPageType;

    @TableField(exist = false)
    private Long userPageTypeResourceId;

    @TableField(exist = false)
    private String spaceName;

    @TableField(exist = false)
    private String spaceType;

    public Long getParentResourceId() {
        return parentResourceId;
    }

    public void setParentResourceId(Long parentResourceId) {
        this.parentResourceId = parentResourceId;
    }

    public Long getSpaceResourceId() {
        return spaceResourceId;
    }

    public void setSpaceResourceId(Long spaceResourceId) {
        this.spaceResourceId = spaceResourceId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    public Boolean getInRecycle() {
        return inRecycle;
    }

    public void setInRecycle(Boolean inRecycle) {
        this.inRecycle = inRecycle;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTextContent() {
        return textContent;
    }

    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }

    public Boolean getDraft() {
        return draft;
    }

    public void setDraft(Boolean draft) {
        this.draft = draft;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public List<WikiPage> getChildren() {
        return children;
    }

    public void setChildren(List<WikiPage> children) {
        this.children = children;
    }

    public Integer getUserPageType() {
        return userPageType;
    }

    public void setUserPageType(Integer userPageType) {
        this.userPageType = userPageType;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Long getUserPageTypeResourceId() {
        return userPageTypeResourceId;
    }

    public void setUserPageTypeResourceId(Long userPageTypeResourceId) {
        this.userPageTypeResourceId = userPageTypeResourceId;
    }

    public String getSpaceType() {
        return spaceType;
    }

    public void setSpaceType(String spaceType) {
        this.spaceType = spaceType;
    }
}
