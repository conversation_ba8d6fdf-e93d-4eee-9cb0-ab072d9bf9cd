package com.jettech;

import org.apache.logging.log4j.core.lookup.MainMapLookup;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.jettech.common.util.Utils.getLog4j2Args;

@SpringBootApplication
@MapperScan("com.jettech.mapper")
@ComponentScan(value = "com.jettech")
@EnableFeignClients(basePackages = { "com.jettech.feign" })
@ServletComponentScan("com.jettech.common.config")
public class AssetsManagementApplication {

	public static void main(String[] args) {
		// 设置Log4j2的运行参数
		String[] log4j2Args = getLog4j2Args(AssetsManagementApplication.class, args);
		MainMapLookup.setMainArguments(log4j2Args);
		SpringApplication.run(AssetsManagementApplication.class, args);
	}

//	@Value("${server.port}")
//	private int port;
//	@Value("${server.ip}")
//    private String ip;
//    @Value("${trp.socketPort}")
//    private int socketPort;

//	@RequestMapping("/setSession")
//	public String setSession(HttpServletRequest request) {
//		String value = request.getParameter("value");
//		request.getSession().setAttribute("value", value);
//		return "set success Value is:" + value;
//	}

	@Bean
	public RestTemplate RestTemplate() {
		return new RestTemplate();
	}


	@Bean
	public ExecutorService executorService() {
		return Executors.newFixedThreadPool(5);
//		int ncpus = Runtime.getRuntime().availableProcessors();
//		int maximumPoolSize = (int) Math.ceil(ncpus * 0.8 * 2);
//		return new ThreadPoolExecutor(5,maximumPoolSize,60L, TimeUnit.MILLISECONDS,new LinkedBlockingDeque<>(), Executors.defaultThreadFactory(),new ThreadPoolExecutor.AbortPolicy());
	}
//	@Bean
//    public SocketIOServer socketIOServer() {
//        Configuration config = new Configuration();
////        config.setHostname(ip);
////        config.setPort(socketPort);
//        config.setMaxFramePayloadLength( 1024 * 1024 );
//        config.setMaxHttpContentLength( 1024 * 1024 );
//        final SocketIOServer server = new SocketIOServer(config);
//        ConnectListener connect = new ConnectListener() {
//            public void onConnect(SocketIOClient client) {}
//        };
//        server.addConnectListener( connect );
//        server.start();
//        return server;
//   }
}
