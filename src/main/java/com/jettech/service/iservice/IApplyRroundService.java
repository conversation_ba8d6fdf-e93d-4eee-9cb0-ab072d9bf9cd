package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.ApplyRround;

import java.util.Map;

/**
 * @title: ITestSystemApplyService
 * @description: 应用轮次接口
 * @date 2024/7/15
 */
public interface IApplyRroundService extends BaseHBService<ApplyRround> {


    Result findApplyRround(Map<String, String> params);

    Result<?> addApplyRround(Map<String, String> params, String userNumber);

    Result<?> updateApplyRround(Map<String, String> params, String userNumber);

    Result<?> deleteApplyRround(String resourceID, String userNumber);
}
