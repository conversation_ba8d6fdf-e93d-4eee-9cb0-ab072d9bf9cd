package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.DemandTestSystem;

import java.util.List;
import java.util.Map;

/**
 *
 * @ClassName IDemandTestSystemService
 * @Description 需求与被测系统service
 * <AUTHOR>
 * @date 2019年12月4日
 */
public interface IDemandTestSystemService extends  IBaseService<DemandTestSystem> {

	/**
	 *
	 * @Title: findRelevanceSystem
	 * @Description: 查询所有系统
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	List<Map<String,Object>> findAllSystem(String name,String resourceID);

	/**
	 *
	 * @Title: addDemandSystem
	 * @Description: 给需求关联系统
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	DemandTestSystem findByTestSystemResourceIDAndDemandResourceID(String testSystemResourceID , String demandResourceID);
	/**
	 *
	 * @Title: findNotRelevanceSystem
	 * @Description: 查询已关联系统
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	List<Map<String,Object>> findRelevanceSystem(String resourceID);
	List<DemandTestSystem> findBydemandResourceID(Long ResourceID);

	/**
	 * @Title: findBytestSystemResourceID
	 * @Description:  根据系统查询需求关联的关系
	 * @Param: "[resourceID]"
	 * @Return: "java.util.List<DemandTestSystem>"
	 * @Author: xpp
	 * @Date: 2019/12/9
	 */
    List<DemandTestSystem> findBytestSystemResourceID(Long resourceID);
	/**
	 * @Description 通过需求resourceid查询已关联的被测系统实体类
	 * <AUTHOR>
	 * @date 2019-12-12 20:31
	 * @param demandResourceID
	 * @return com.jettech.dto.Result<?>
	 */
    Result<?> findTestSystemByDemandResourceID(Long demandResourceID);
    /**
	 *
	 * @Title: findNotRelevanceSystem
	 * @Description: 查询未关联系统
	 * @param request
	 * @return
	 */
	List<Map<String, Object>> getNotRelevanceSystem(String name,String resourceID);
	/**
     * 
     * @Title  findRelevanceSystemByDemandResourceID     
     * @Description  查询当前需求是否关联系统
     * @author: slq    
     * @date:   2020年8月17日 上午11:51:24
     */
	boolean findRelevanceSystemByDemandResourceID(String demandResourceID);

	/**
	 * 设置主系统
	 * @param resouceID
	 * @param isPrincipal 0 否  1 是
	 * @return
	 */
	int setPrincipalSystem(String number,String resouceID,String isPrincipal);


	/**
	 * @Method: findBydemandResourceIDIn
	 * @Description: 批量需求rid查询
	 * @Param: " [demandRdis] "
	 * @return: java.util.List<com.jettech.model.DemandTestSystem>
	 * @Author: wws
	 * @Date: 2020/11/10
	 */
    List<DemandTestSystem> findByDemandResourceIDIn(List<String> demandRdis);
}
