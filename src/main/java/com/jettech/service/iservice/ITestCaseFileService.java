package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.TestCaseFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ITestCaseFileService  extends  IBaseService<TestCaseFile>{
    List<TestCaseFile> findTestCaseFileByResourceID(Long testCaseResourceID);

    String downloadCaseFile(Map<String, Object> mapParam) throws IOException;

    Result<?> deleteCaseFile(String resourceIDs, String userNumber);
}
