package com.jettech.service.iservice;

import java.util.List;
import java.util.Map;

public interface IDataBackupService {

    String getCreateTableSql(String tableName);

    List<Map<String, Object>> queryTableData(String tableName, Map<String, Object> params);

    int countTableData(String tableName, Map<String, Object> params);

    List<Map<String, Object>> getAllTable();

    int deleteTableData(String tableName, Map<String, Object> params);

    int dataBackup(List<Long> demandResourceID,String filePath,String fileNam,boolean clear);
}
