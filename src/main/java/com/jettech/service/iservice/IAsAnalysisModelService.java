package com.jettech.service.iservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jettech.common.dto.Result;
import com.jettech.dto.*;
import com.jettech.model.AsAnalysisModel;
import com.jettech.vo.AsAnalysisModelDetailVO;
import com.jettech.vo.AsAnalysisModelToJsonVO;
import com.jettech.vo.AsAnalysisModelVO;
import com.jettech.vo.AsAnalysisModelVersionVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <p>
    * 测试分析模型表 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/
public interface IAsAnalysisModelService extends IService<AsAnalysisModel> {

    /**
    * 支持分页的dto条件查询
    *
    * @param asAnalysisModelQueryDTO
    * @return IPage
    */
    Result<IPage<AsAnalysisModelVO>> selectPageByDto(AsAnalysisModelQueryDTO asAnalysisModelQueryDTO);

    /**
    * 保存
    *
    * @param dto 参数
    * @return 保存结果
    */
    Result<Boolean> saveOne(com.jettech.dto.AsAnalysisModelDTO dto,HttpServletRequest request);

    Result<Boolean> updateOne(AsAnalysisModelUpdateDTO dto, HttpServletRequest request);

    /**
    * 根据主键查询VO
    *
    * @param id 主键
    * @return VO
    */
    Result<AsAnalysisModelVO> getById(String id);

    /**
    * 根据主键批量删除
    *
    * @param ids 主键
    * @return 删除结果
    */
    Result<Boolean> deleteById(List<String> ids);

    Result<String> withdraw(String id,HttpServletRequest request);

    Result<AsAnalysisModelVersionVO> contrast(String id);

    Result<String> submitReview(List<String> idList,String remarks,HttpServletRequest request);

    Result<IPage<AsAnalysisModelVO>> reviewPage(Integer current, Integer size, String name, String updateUser,HttpServletRequest request);

    Result<String> reviewBatch(List<String> idList, String remarks,Integer status,HttpServletRequest request);

    Result<List<AsAnalysisModelDetailVO>> reviewRecords(String id);

    Result<AsAnalysisModelVersionVO> xmindParse(MultipartFile multipartFile,String tradeResourceId);

    Result<AsAnalysisModelToJsonVO> xmindToJson(MultipartFile multipartFile);

    void xmindExport(String id, HttpServletResponse response);

    Result<IPage<AsAnalysisModelVO>> attachmentPage(AsAnalysisModelAttachmentQueryDTO asAnalysisModelAttachmentQueryDTO);

    Result<Boolean> updateRepeatedOrCase(AsModelUpdateRepeatedCaseDTO dto);

    void attachmentXmindExport(String id, HttpServletResponse response);

    public Result generateCase(String userNumber,String id);
}
