package com.jettech.service.iservice;

import com.jettech.common.util.UserVo;
import com.jettech.model.RichTextImage;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface IRichTextImageService extends IBaseService<RichTextImage>{
    void uploadRichTextImageArray(MultipartFile[] file, String fileName);

    String uploadRichTextImage(MultipartFile file, UserVo user);

    void getRichTextImage(String resourceID, HttpServletResponse response);

    void delRichTextImage(String resourceID, UserVo user);
}
