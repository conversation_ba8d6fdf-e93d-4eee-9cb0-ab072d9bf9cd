package com.jettech.service.iservice;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jettech.model.BaseModel;

/**
 * 基础服务层接口
 * <AUTHOR>
 *
 * @param <T>
 */
public interface IBaseService<T extends BaseModel> {

	/**
	 * 根据业务主键查询
	 * @param resourceID 业务主键
	 * @return 返回当前数据
	 */
	public T findByResourceID(Long resourceID);
	/**
	 * 根据业务主键批量查询
	 * @param resourceIDs 业务主键
	 * @return 当前一批数据
	 */
	public List<T> findByResourceIDIn(List<String> resourceIDs);
	/**
	 * 根据ID集合查询
	 * @param idList 一批主键
	 * @return 
	 */
	public List<T> findByIdIn(List<Integer> idList);
	/**
	 * 根据ID集合查询
	 * @param idList 一批主键
	 * @return 
	 */
	public T findById(Integer id);
	/**
	 * 单个保存数据
	 * @param model 单个对象
	 * @return 
	 */
	public T save(T model,String userNumber);
	/**
	 * 批量保存数据
	 * @param modelList 批量对象
	 * @return 
	 */
	public List<T> save(List<T> modelList,String userNumber);
	/**
	 * 更新一批数据
	 * @param modelList 批量对象
	 * @return 
	 */
	public List<T> update(List<T> modelList,String userNumber);
	/**
	 * 更新单个对象
	 * @param model 单个更新对象
	 * @return 
	 */
	public T update(T model,String userNumber); 
	/**
	 * @param id 根据ID查询
	 * @return  
	 */
	public T find(Integer id);
	/**
	 * 查询所有数据
	 * @return 
	 */
	public List<T> findAll();
	/**
	 * 查询总量
	 * @return 当前总量
	 */
	public long count();
	/**
	 * 删除对象
	 * @param model 单个删除对象
	 * @param userVo 操作人
	 */
	public void delete(T model,String userNumber);
	/**
	 * 批量删除
	 * @param modelList 批量删除数据
	 */
	public void deleteInBatch(List<T> modelList,String userNumber); 
	/**
	 * 分页查询所有数据
	 * @return 
	 */
	public PageInfo<T> findAll(Integer pageNum, Integer pageSize);
	
}
