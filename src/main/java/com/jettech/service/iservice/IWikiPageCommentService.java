package com.jettech.service.iservice;

import com.jettech.model.WikiPageComment;

import java.util.List;

public interface IWikiPageCommentService extends IBaseService<WikiPageComment> {

    List<WikiPageComment> listCommentsByPageId(Long pageResourceId);

    void addComment(WikiPageComment comment, String loginUserNumber);

    void editContentById(WikiPageComment comment, String loginUserNumber);

    void clearContentById(Long resourceID, String loginUserNumber);
}
