package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.ProjectGroup;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: IProjectGroupService
 * @projectName jettomanager
 * @description: 项目组接口
 * @date 2020/7/1319:25
 */
public interface IProjectGroupService extends  IBaseService<ProjectGroup>{
    /**
      * @Title: saveProjectGroup
      * @description: 新增项目组
      * @param "[projectGroup, userNumber]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2020/7/13 19:46
      */
    Result saveProjectGroup(ProjectGroup projectGroup, String userNumber);

    /**
     *
     * @Title: findByTestProjectResourceID
     * @Description:查询项目下的项目组
     * @param resourceID
     * @return
     * <AUTHOR>
     * @date 2020年7月14日 下午1:59:59
     */
    List<ProjectGroup> findByTestProjectResourceID(Long resourceID,String groupType);



    /***
     * 修改项目组
     * @Method : updateProjectGroup
     * @Description : 修改项目组
     * @param pg : 修改对象参数
     * @param userNumber : 当前登录用户number
     * @return : com.jettech.dto.Result
     * <AUTHOR> Wws.
     * @param updatePlace
     * @CreateDate : 2020-07-14 星期二 10:50:17
     *
     */
    Result updateProjectGroup(ProjectGroup pg, String userNumber, String updatePlace);

    /***
     * 删除项目组
     * @Method : deleteProjectGroup
     * @Description : 删除项目组
     * @param resourceID : 被删除的rid
     * @param userNumber
     * @return : com.jettech.dto.Result
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 14:19:13
     *
     */
    Result deleteProjectGroup(String resourceID, String type, String userNumber);

    /**
     * @Method: findByProjectRidsAndIsSmallPoint
     * @Description: 项目rid查询末节点
     * @Param: " [resourceIDs] "
     * @return: java.util.List<com.jettech.model.ProjectGroup>
     * @Author: wws
     * @Date: 2020/9/17
     */
    List<String> findByProjectRidsAndIsSmallPoint(List<String> projectResourceIDs,String groupType);
    /***
     * 父节点查询
     * @Method : findByParentResourceID
     * @Description : 父节点查询
     * @param resourceID : parentRid
     * @return : java.util.List<com.jettech.model.ProjectGroup>
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 10:58:03
     *
     */
    List<ProjectGroup> findByParentResourceID(Long resourceID,String groupType);

    /***
     * Excel导入项目组
     * @Method : importProjectGroup
     * @Description : Excel导入项目组
     * @param file : 要导入的Excel文件
     * @param testProjectResourceID : 项目的ResourceID
     * @param groupType
     * @return : com.jettech.dto.Result
     * <AUTHOR> zhangsheng
     * @CreateDate : 2020-09-17 下午 13:51:23
     *
     */
    Result importProjectGroup(MultipartFile file, Long testProjectResourceID, String userNumber, String groupType);
	/**
	 *
	* @Title: findPerformCaseLeadInfo
	* @Description: 获取项目案例的所属项目和项目组的层级结构
	* @param @param projectGroupResourceID
	* @param @return    参数
	* @return Result<?>    返回类型
	* @throws
	* <AUTHOR>
	 */
    String findPerformCaseLeadInfo(String projectGroupResourceID);

    List<Map<String, String>> findDefectByGroupResourceIDs(List<String> projectGroups);

    /**
     * 获取项目下的项目组
     * @param projectResourceIDs
     * @param groupType
     * @return
     */
    List<ProjectGroup> findByProjectRIDsAndGroupType(List<String> projectResourceIDs, String groupType);
}
