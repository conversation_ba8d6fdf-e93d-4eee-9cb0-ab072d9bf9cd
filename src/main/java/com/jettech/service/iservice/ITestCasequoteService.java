package com.jettech.service.iservice;

import com.jettech.model.TestCasequote;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-10-22 13:45
 */
public interface ITestCasequoteService extends IBaseService<TestCasequote> {
    TestCasequote findByTestcaseResourceID(String resourceID);

    List<TestCasequote> findByCaseResourceID(String resourceID);

    /**
     * <AUTHOR>
     * @description 根据案例resourceid查询案例的执行记录
     * @date 2021年01月06日 10:15
     * @param [testcaseResourceIDList]
     * @return java.util.List<com.jettech.model.TestCasequote>
     **/
    List<TestCasequote> findByCaseResourceIDs(List<Long> testcaseResourceIDList);

    List<TestCasequote> findByTaskAndCaseResourceIds(Long testTaskResourceID, List<Long> testCaseResourceIDList);

    List<TestCasequote> findByTaskAndTrade(Long testTaskResourceID, Long tradeResourceID);

    boolean isQuote(String testCaseResourceID);
}
