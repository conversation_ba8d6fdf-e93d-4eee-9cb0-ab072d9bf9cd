package com.jettech.service.iservice;

import com.jettech.common.dto.assets.WikiPageCopyDTO;
import com.jettech.common.dto.assets.WikiPageDTO;
import com.jettech.common.page.PageResult;
import com.jettech.model.WikiPage;
import com.jettech.model.WikiPageUserTemConfig;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

public interface IWikiPageService extends IBaseService<WikiPage> {

    void addPage(WikiPage wikiPage, String loginUserNumber);

    void editPage(WikiPage wikiPage, String loginUserNumber);

    List<WikiPage> getDraftPageList(WikiPageDTO dto);

    PageResult<WikiPage> draftPage(WikiPageDTO dto);

    WikiPage getPage(Long resourceID, String loginUserNumber);

    List<WikiPage> listPageTreeBySpace(Long spaceId, boolean draft);

    void collectPage(WikiPageUserTemConfig wikiPageUserTemConfig, String loginUserNumber);

    void copyPageById(WikiPageCopyDTO wikiPageCopyDTO, String loginUserNumber);

    void editSpaceIdByPageId(WikiPageCopyDTO wikiPageCopyDTO, String loginUserNumber);

    PageResult<WikiPage> listPageUserByParam(WikiPageUserTemConfig config, String loginUserNumber);

    void delPage(Long resourceID, Boolean delChildren, String loginUserNumber);

    List<WikiPage> listRecyclePageBySpace(WikiPageDTO wikiPageDTO);

    void clearRecyclePage(Long pageResourceID);

//    String uploadImg(MultipartFile file);
//
//    InputStream getImg(String filePath);

    void exportWord(Long resourceID, HttpServletResponse response);

    void restore(Long pageResourceID, String loginUserNumber);

    boolean checkPageName(WikiPage wikiPage);
}
