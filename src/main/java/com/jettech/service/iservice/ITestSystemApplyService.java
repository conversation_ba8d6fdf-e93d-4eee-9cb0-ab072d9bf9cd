package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.TestSystemApply;

import java.util.Map;

/**
 * @title: ITestSystemApplyService
 * @description: 系统应用接口
 * @date 2024/7/15
 */
public interface ITestSystemApplyService extends BaseHBService<TestSystemApply> {


    Result findTestSystemApply(Map<String, String> params);

    Result<?> addTestSystemApply(Map<String, String> params, String userNumber);


    Result<?> updateTestSystemApply(Map<String, String> params, String userNumber);

    Result<?> deleteTestSystemApply(String resourceID, String userNumber);

    Result findTestSystemApplyBySystemResourceID(String systemResourceID);

    Result findApplyRoundByApplyResourceID(String applyResourceID);

    Map<String, Object> findRootDirectoryPath();
}
