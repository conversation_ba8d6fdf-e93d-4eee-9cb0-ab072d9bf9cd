package com.jettech.service.iservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jettech.common.dto.Result;
import com.jettech.dto.AsAnalysisModelDetailDTO;
import com.jettech.model.AsAnalysisModelDetail;
import com.jettech.vo.AsAnalysisModelDetailVO;


import java.util.List;

/**
* <p>
    * 测试分析模型审核表 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/
public interface IAsAnalysisModelDetailService extends IService<AsAnalysisModelDetail> {

    /**
    * 保存
    *
    * @param dto 参数
    * @return 保存结果
    */
    Result<Boolean> saveOne(AsAnalysisModelDetailDTO dto);

    /**
    * 根据主键查询VO
    *
    * @param id 主键
    * @return VO
    */
    Result<AsAnalysisModelDetailVO> getById(String id);

    /**
    * 根据主键删除
    *
    * @param id 主键
    * @return 删除结果
    */
    Result<Boolean> deleteById(String id);

    List<AsAnalysisModelDetailVO> reviewRecords(String id);
}
