package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.TestSystem;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ITestSystemService
 * @projectName jettopro
 * @description: 被测系统（管理级）接口
 * @date 2019/11/419:00
 */
public interface ITestSystemService extends BaseHBService<TestSystem> {
    /**
     * @param projectResourceID
     * @return com.jettech.dto.Result<?>
     * @Description 查询当前项目下单点案例中被测系统、模块、交易树
     * <AUTHOR>
     * @date 2019-11-06 10:47
     */
    Result<?> findSinglePointLeftTreeByProjectResourceID(String projectResourceID);

    /**
     * @Title saveorUpdateTestSystem
     * @Description 新增或修改被测系统
     * @Params [request, paramsMap]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    Result saveorUpdateTestSystem(TestSystem testSystem, String userNumber);

    /**
     * @Title deleteTestSystemByResourceID
     * @Description 删除被测系统
     * @Params [resourceID, userNumber]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    Result deleteTestSystemByResourceID(String resourceID, String userNumber);

    /**
     * @Title saveorUpdateTestSystem
     * @Description 查询当前被测系统下是否维护数据
     * @Params [request, paramsMap]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    Result findResourceBySystemResourceID(String resourceID);

    /**
     * @param "[map]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: updateNodePosition
     * @description: 更新左侧树节点位置
     * <AUTHOR>
     * @date 2019/11/7 14:36
     */
    Result updateNodePosition(Map map);

    /**
     * @Title findSystemManagement
     * @Description 查询系统负责人（下拉展示）
     * @Params [request]
     * @Return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @Date 2019/11/11
     */
    Result<?> findSystemManagement();

    /**
     * 查询系统名称是否重复
     *
     * @param paramsMap
     * @Return com.jettech.dto.Result<?>
     */
    Result whetherToRepeat(Map<String, String> paramsMap);

    /**
     * @param projectResourceIDObj
     * @param relationTradeMapObj
     * @param testPlanType
     * @return com.jettech.dto.Result<?>
     * @Description 通过项目resourceid和交易下案例已关联个数查询单点案例左侧树和案例个数(manexecute调用)
     * <AUTHOR>
     * @date 2019-11-27 18:31
     */
    Result<?> SinglePointLeftTree(Object projectResourceIDObj, Object relationTradeMapObj, Object testPlanType);

    Result<?> findUserBySystem(String id);

    /**
     * @Title: initializeTestSystemTable
     * @Description: 系统交易管理初始化被测系统table(分页 + 条件查询)
     * @Param: "[]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    Result initializeTestSystemTable(Map<String, String> params);

    /**
     * @Title: selectTestSystemUser
     * @Description: 返回已选和未选的人员列表
     * @Param: "[resourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    Result selectTestSystemUser(Long resourceID);

    /**
     * @param "[systemResourceID]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getTreeBySystemRid
     * @description: 单个被测系统的左侧树
     * <AUTHOR>
     * @date 2019/12/4 14:28
     */
    Result getTreeBySystemRid(String systemResourceID, String userNumber);

    String SavedemandUser(String userId, String systemModelresourceIDs);

    /**
     * @Title: initializeTradeTable
     * @Description: 根据单个被测系统或模块查询关联的交易
     * @Param: "[map]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    Result initializeTradeTable(Map<String, String> map);

    /**
     * @Title: setSelectTestSystemUser
     * @Description: 被测系统关联人员
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    Result setSelectTestSystemUser(Map<String, Object> params);

    /**
     * @param "[dicName]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getDicValues
     * @description: 获取字典里的值
     * <AUTHOR>
     * @date 2019/12/6 11:13
     */
    Result getDicValues(String dicName);

    /**
     * @Title: simpleNameWhetherToRepeat
     * @Description: 判断系统简称是否重复
     * @Param: "[paramsMap]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/10
     */
    Result simpleNameWhetherToRepeat(Map<String, String> paramsMap);

    /**
     * @Title findDataDictionaryByDicName
     * @Description 数据字典查询
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/12/4
     */
    Result findDataDictionaryByDicName(String dicName);

    /**
     * @param "[]"
     * @param page
     * @param pageSize
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getSITtestUser
     * @description: 返回所有的人员列表，也就是SIT测试人员和SIT测试负责人
     * <AUTHOR>
     * @date 2019/12/12 19:18
     */
    Result getSITtestUser(String name, String page, String pageSize);

    /**
     * @param "[]"
     * @param page
     * @param pageSize
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findTypeAllUser
     * @description: 返回内部机构下所有的人员列表，通过basic查询
     * <AUTHOR>
     * @date 2019/12/12 19:50
     */
    Result findTypeAllUser(String name, String page, String pageSize);

    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findOutDept
     * @description: 查询外部机构下所有的子机构
     * <AUTHOR>
     * @date 2019/12/12 20:11
     */
    Result findOutDept();

    /**
     * @param testSystemResourceID
     * @return com.jettech.dto.Result<?>
     * @Description 查询被测系统下一级的模块和交易MAP
     * <AUTHOR>
     * @date 2019-12-12 20:42
     */
    Result<?> queryNextLowerLevelMap(Long testSystemResourceID);

    /**
     * @Title findAllSystem
     * @Description 查询所有被测系统
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/12/13
     */
    Result findAllSystem();

    /**
     * @param
     * @return
     * @Title: findRelevanceSystemID
     * @Description: 已关联系统ID
     * <AUTHOR>
     * @date 2019年12月04日
     */
    List<String> findRelevanceSystemID(String resourceID);

    /**
     * @Title: findOneTestSystem
     * @Description: 根据id查询单个被测系统
     * @Param: "[systemResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/17
     */
    Result findOneTestSystem(Long systemResourceID);

    /**
     * @param "[agent, response]"
     * @return void
     * @throws
     * @Title: downloadModal
     * @description: 模板下载
     * <AUTHOR>
     * @date 2019/12/27 14:38
     */
    void downloadModal(String agent, HttpServletResponse response);

    /**
     * @param "[file, userNumber]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: importExcel
     * @description: 被测系统导入excel
     * <AUTHOR>
     * @date 2019/12/27 18:40
     */
    Result importExcel(MultipartFile file, String userNumber) throws Exception;

    /**
     * @Title: getBelongTribe
     * @Description: 获取下拉框选项所属部落
     * @Param: "[]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/31
     */
    Result getBelongTribe();

    /**
     * @Title: getSubordinatTeam
     * @Description: 根据选择的所属部落获取下拉框选项-->所属小队
     * @Param: "[id]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/31
     */
    Result getSubordinatTeam(String text);

    /**
     * @Title: findLeftTreeByUAT
     * @Description: 通过UAT按钮初始化左侧树，增加UAT树节点
     * @Param: "[testEnviroment]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    Result findLeftTreeByUAT(String demandResourceID);

    /**
     * @param testSystemResourceID
     * @return Result<?>
     * @Title checkData
     * @Description 根据被测系统查询所有的模块和交易
     * <AUTHOR>
     * @data Jan 11, 20206:05:04 PM
     */
    Result<?> checkData(Long testSystemResourceID, Long resourceID, String type);

    /**
     * @param "[demandResourceID]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findTestSystemByDemandResourceID
     * @description: 需求下被测系统
     * <AUTHOR>
     * @date 2020/2/12 13:29
     */
    Result findTestSystemByDemandResourceID(String demandResourceID);

    /**
     * @Title: customLeftTree
     * @Description: 通过勾选自定义显示案例编写左侧树
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/2/18
     */
    Result customLeftTree(Map<String, String> params, String userNumber);

    /**
     * @Title: findLeftTreeByUAT
     * @Description: 通过SIT按钮初始化左侧树
     * @Param: "[testEnviroment]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    Result findLeftTreeBySIT(String demandResourceID);

    /**
     * @param "[map]"
     * @return void
     * @throws
     * @Title: exportTestSystem
     * @description: 导出被测系统
     * <AUTHOR>
     * @date 2020/2/20 17:03
     */
    void exportTestSystem(Map map);

    /**
     * @Title: findOneLeftTreeByTestSystemResourceID
     * @Description: 案例编写查询单个被测系统的左侧树
     * @Param: "[testSystemResourceID]"
     * @Return: "com.jettech.dto.Result<?>"
     * @Author: xpp
     * @Date: 2020/2/21
     */
    Result<?> findOneLeftTreeByTestSystemResourceID(String testSystemResourceID, String demandResourceID, String userNumber);

    /**
     * @return
     * @Title: searchTestSystem
     * @Description: 查询系统
     * <AUTHOR>
     * @date 2020年2月23日 上午11:20:51
     */
    List<Map<String, Object>> searchTestSystem();


    /**
     * @Title: findCheckedLeftTree
     * @Description: 页面回选已经自定义的左侧树节点
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/2/25
     */
    Result findCheckedLeftTree(Map<String, String> params);

    /**
     * @param userResourceID
     * @return
     * @Title: findTestSystemByUserResourceID
     * @Description: 查询人员所属系统
     * <AUTHOR>
     * @date 2020年3月3日 下午9:02:30
     */
    String findTestSystemByUserResourceID(Long userResourceID);

    /**
     * @Title findAllSubordinateSystemMap
     * @Description 缺陷归属系统初始化查所有
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2020/3/6
     */
    Result findSubordinateSystemMap();

    /**
     * @Title: findTradeByResourceID
     * @Description: 根据交易id查询系统
     * @Param: "[tradeResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/3/7
     */
    Result findTestSystembyTradeByResourceID(Long tradeResourceID);

    /**
     * @Title findTestSystemMap
     * @Description 查询被测系统返回Map
     * @Params [systemRID]
     * @Return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Date 2020/3/7
     */
    Map<String, String> findTestSystemMap(Long systemRID);

    /**
     * @param
     * @return Result
     * @Title:findLeftTreeByTestStage
     * @Description:通过测试阶段初始化左侧树
     * @author: wu_yancheng
     * @date 2020年4月1日下午3:04:10
     */
    Result findLeftTreeByTestStage(String demandResourceID, String testStage);

    /**
     * 当任务类型是案例设计时，任务范围的左侧系统模块树
     *
     * @param @param  systemResourceID
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: getTestTaskTradeTreeBySystemResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Result<?> getTestTaskTradeTreeBySystemResourceID(Map<String, String> params);

    /**
     * 当任务类型是案例设计时，任务范围的左侧系统模块树,当点击系统或者模块时展示右侧的交易列表
     *
     * @param @param  params
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: initTestTaskTradeTable
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Result<?> initTestTaskTradeTable(Map<String, String> params);

    /**
     * 根据任务的rid查询该任务维护的系统模块交易范围，展示为左侧树
     *
     * @param @param  params
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findLeftTreeByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Result<?> findLeftTreeByTestTaskResourceID(Map<String, String> params);

    /**
     * 根据任务的rid查询该任务维护的系统模块交易和手工编写案例，展示为左侧树（手工执行用例范围树结构-来源为任务的勾选保存的案例）
     *
     * @param @param  params
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findManExecuteLeftTreeByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Result<?> findManExecuteLeftTreeByTestTaskResourceID(Map<String, String> params);

    /**
     * 根据任务id获取被测系统
     *
     * @param taskRid
     * @return 测试系统
     * <AUTHOR>
     */
    Result<List<TestSystem>> findTestSystemsByTestTaskResourceId(String taskRid);

    /**
     * 根据交易和任务查询手工执行引用案例列表信息
     *
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findExecuteCaseListInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Result<?> findExecuteCaseListInfo(Map<String, String> params, PageRequest pageRequest);

    /**
     * @param file
     * @param userNumber
     * @return
     * @Title: importTrade
     * @Description:交易导入
     * <AUTHOR>
     * @date 2020年5月26日 下午2:38:24
     */
    Result<?> importTrade(MultipartFile file, String userNumber);
    /**
     * 交易导入(东莞银行定制化)
     * <AUTHOR>
     */
    Result<?> importTrade2(MultipartFile file, String userNumber);

    /**
     * @param name
     * @return
     * @Title: findByName
     * @Description: 根据名称查询系统
     * <AUTHOR>
     * @date 2020年5月27日 上午10:42:57
     */
    TestSystem findByName(String name);

    /**
     * @param projectResourceID : 需求RID(null时查所有)
     * @param keyWord           : 关键字
     * @return : com.jettech.dto.Result<?>
     * @Method : findSinglePointLeftTreeByProjectResourceIDAndKeyWord
     * @Description : 通过需求rid和关键字查询系统模块交易树
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-06-24 周三 11:54:42
     */
    Result<?> findSinglePointLeftTreeByDemandResourceIDAndKeyWord(String demandResourceID, String keyWord);

    /**
     * <AUTHOR>
     * @description 确认删除被测系统
     * @date 2020年11月25日 14:19
     * @param [resourceID, userNumber]
     * @return com.jettech.dto.Result
     **/
    Result confirmDeleteTestSystem(String resourceID, String userNumber);

    List<Map<String, Object>> getSinglePointLeftTree(List<Map<String, Object>> testSystemList);

    List<TestSystem> findAllByDataAuth();

    List<Map<String, Object>> countQuoteRecord(List<Long> quoteResourceIDList, Date timeStart, Date timeEnd);

    List<Map<String, Object>> countCaseResultFile(List<Long> recordResourceIDList);
}
