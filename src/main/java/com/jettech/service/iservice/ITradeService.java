package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.datadesign.DemandToTradeDTO;
import com.jettech.common.dto.datadesign.SystemModuleTradeDTO;
import com.jettech.model.Trade;
import com.jettech.model.TreeNode;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ITradeService
 * @description ITradeService
 * @create 2019-11-04 19:22
 */
public interface ITradeService extends IBaseService<Trade> {
    /**
     * @param strings
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description 通过被测系统resourceID查询交易数据
     * <AUTHOR>
     * @date 2019-11-06 14:11
     */
    List<Map<String, Object>> findByTestSystemResourceIDs(List<String> strings);

    /**
     * @Title saveOrUpdateTrade
     * @Description 新增或修改交易
     * @Params [params，userNumber]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    Result saveOrUpdateTrade(Map<String, String> params, String userNumber);

    /**
     * @Title verifyTradeNameNotRepeatedByModule   /  verifyTradeNameNotRepeatedBySystem
     * @Description 用于校验父节点（被测系统或者模块）下交易名称唯一
     * @Params [name, nodeResourceID, parentResourceID]
     * @Return java.util.List<SystemModule>
     * <AUTHOR>
     * @Date 2019/11/5
     */
    List<Trade> verifyTradeNameNotRepeatedByModule(String name, String nodeResourceID, String moduleResourceID);

    List<Trade> verifyTradeNameNotRepeatedBySystem(String name, String nodeResourceID, String testSystemResourceID);

    List<Trade> verifyTradeNumberNotRepeatedBySystem(String number, String tradeResourceID, String testSystemResourceID);

    /**
     * @param ""
     * @return java.util.List<Trade>
     * @throws
     * @Title: findbyTestSystemResourceID
     * @description: 被测系统resourceID查询交易
     * <AUTHOR>
     * @date 2019/11/6 10:13
     */
    List<Trade> findbyTestSystemResourceID(String testSystemResourceID, String name, String versionNumber, String executeMode);

    /**
     * @param "[resourceID_list]"
     * @return java.util.List<Trade>
     * @throws
     * @Title: getObjectByTestSystemResourceIDs
     * @description: 被测系统查询交易(返回值对象)
     * <AUTHOR>
     * @date 2019/11/7 10:29
     */
    List<Trade> getObjectByTestSystemResourceIDs(List<String> resourceIDs);

    /**
     * @Title findBySystemModule
     * @Description 查询模块下的交易
     * @Params
     * @Return
     * <AUTHOR>
     * @Date 2019/11/7
     */
    List<Trade> findBySystemModule(List<String> collect);

    /**
     * @Title deleteSystemModule
     * @Description 删除交易
     * @Params [request, resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    Result deleteTradeByResourceID(String tradeResourceID, String userNumber);

    /**
     * @Title saveorUpdateTestSystem
     * @Description 校验 删除时交易下是否维护数据
     * @Params [resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    Result findResourceByTrade(String resourceID);

    /**
     * @Title checkResourceByTradeForDelete
     * @Description 校验 删除时交易下是否维护数据
     * @Params [resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    Result whetherToRepeat(@RequestBody Map<String, String> paramsMap);

    List<Map<String, Object>> getTestProjectTree(List<String> tradeResourceIDList);

    /**
     * @Title: findBySystemModuleResourceID
     * @Description: 根据当前模块查询交易
     * @Param: "[resourceID]"
     * @Return: "java.util.List<Trade>"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    List<Trade> findBySystemModuleResourceID(String resourceID, String name, String versionNumber, String executeMode);

    /**
     * 根据系统模块查询交易
     * @param resourceIDs
     * @param name
     * @param versionNumber
     * @param executeMode
     * @return
     */
    List<Trade> findBySystemModuleResourceIDsIn(List<String> resourceIDs, String name, String versionNumber, String executeMode);


    /**
     * @param "[file, moduleResourceID, userNumber]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: importTradeExcel
     * @description: 导入交易
     * <AUTHOR>
     * @date 2019/12/7 17:25
     */
    Result importTradeExcel(MultipartFile file, String moduleResourceID, String testSystemResourceID, String userNumber) throws IOException;

    /**
     * @param "[agent, response]"
     * @return void
     * @throws
     * @Title: downloadModel
     * @description: 下载模板
     * <AUTHOR>
     * @date 2019/12/9 11:19
     */
    void downloadModel(String agent, HttpServletResponse response);

    /**
     * @param testSystemResourceID
     * @return java.util.List<java.util.HashMap < java.lang.String, java.lang.Object>>
     * @Description 查询直接挂在该被测系统下的Map
     * <AUTHOR>
     * @date 2019-12-12 20:51
     */
    List<HashMap<String, Object>> findNextLowerLevelMapByTestSystemResourceID(Long testSystemResourceID);

    /**
     * @param "[resourceID, testSystemResourceID]"
     * @return java.util.List<Trade>
     * @throws
     * @Title: getByTsResourceIDAndModulResourceID
     * @description:
     * <AUTHOR>
     * @date 2019/12/24 17:53
     */
    List<Trade> getByTsResourceIDAndModulResourceID(Long moduleResourceID, Long testSystemResourceID);

    /**
     * @param "[nodeType, nodeResourceID]"
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @throws
     * @Title: getTradeListAndObj
     * @description: 案例资产库服务接口，返回节点对象，交易list
     * <AUTHOR>
     * @date 2019/12/26 12:09
     */
    Map<String, Object> getTradeListAndObj(String nodeType, String nodeResourceID);

    /**
     * @Title: findbyTradeName
     * @Description: 根据交易名称查询交易
     * @Param: "[tradeName]"
     * @Return: "java.util.List<Trade>"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    List<Trade> findbyTradeName(String tradeName);

    /**
     * @param ""
     * @return java.util.List<Trade>
     * @throws
     * @Title: findbyTestSystemResourceID
     * @description: 被测系统resourceID查询交易(补差模块带的交易)
     * <AUTHOR>
     * @date 2019/11/6 10:13
     */
    List<Trade> findOnlyTradebyTestSystemResourceID(String testSystemResourceID, String name);

    /**
     * @param ""
     * @return java.util.List<Trade>
     * @throws
     * @Title: findbyTestSystemResourceID
     * @description: 被测系统resourceID查询交易(补差模块带的交易)
     * <AUTHOR>
     * @date 2019/11/6 10:13
     */
    List<Trade> findOnlyTradebyTestSystemResourceID2(String testSystemResourceID);

    /**
     * 查询任务下选中的交易
     *
     * @param @return 参数
     * @return List<Trade>    返回类型
     * @throws
     * @Title: findSelectedTradeByTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    List<Trade> findSelectedTradeByTaskResourceID(String taskResourceID);

    /**
     * @param testSystemResource
     * @param name
     * @return
     * @Title: findByTestSystemResourceAndName
     * @Description:
     * <AUTHOR>
     * @date 2020年5月27日 上午9:52:37
     */
    Trade findByTestSystemResourceAndName(Long testSystemResource, String name);

    /**
     * @param moduleResourceID
     * @return
     * @Title: findByModuleResourceID
     * @Description: 查询模块下的交易
     * <AUTHOR>
     * @date 2020年5月27日 下午12:50:40
     */
    List<Trade> findByModuleResourceID(Long moduleResourceID);

    /**
     * @param testSystemResourceID
     * @param name
     * @param modeleResourceID
     * @return
     * @Title: findByNameAndModuleResourceID
     * @Description: 查询交易
     * <AUTHOR>
     * @date 2020年5月27日 下午2:53:29
     */
    Trade findByNameAndModuleResourceID(String name, Long modeleResourceID);

    /**
     * 系统交易管理-根据系统导出系统交易excel
     *
     * @param @param  request
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: exportTestSystemTradeExcel
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Result<?> exportTestSystemTradeExcel(Map<String, Object> mapData);
    /**
     * 系统交易管理-根据系统导出系统交易excel（东莞银行定制化：「所属系统」列后展示交易所属系统模块的所有层级）
     * <AUTHOR>
     */
    Result<?> exportTestSystemTradeExcel2(Map<String, Object> mapData);

    /**
     * 查询交易数据字典
     *
     * @param @return 参数
     * @return Map<String, String>    返回类型
     * @throws
     * @Title: findTradeDicsByDicName
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    public List<Map<String, String>> findTradeDicsByDicName(String name);

    /**
     * 交易类型数据字典查询
     *
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: getTradeTypeDictionary
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Result getTradeTypeDictionary();

    /**
     * 根据需求名和被测系统名获取需求到交易的关系
     *
     * @param demandName
     * @param testSystemName
     * @return
     */
    List<DemandToTradeDTO> getDemandToTradeMapperByDemandNameAndTestSystemName(String demandName, String testSystemName);

    /**
     * 案例编写、手工执行系通过任务查找任务下维护的统模块交易树
     *
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: initTestTaskTradeTree
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR> 2020-06-09
     */
    Result<?> initTestTaskTradeTree(String userNumber, String taskResourceID);

    Result<?> initTradeTree();

    /**
     * 根据 任务ID 以及 系统/模块/子模块ID等查询下级及子级所有交易
     *
     * @param testTaskResourceID
     * @param type
     * @param resId
     * @return
     */
    List<Map<String, Object>> findAllTradeByParentResIDAndTaskResID(String testTaskResourceID, String type, String resId);


    /**
     * @Title findTradeBySystemRIDOrModuleRID
     * @Description 添加范围弹框根据系统或者模块查询交易列表
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2020/6/10
     */
    Result findTradeBySystemRIDOrModuleRID(Map<String, String> params);

    /**
     * 根据任务rid查询当前任务
     *
     * @param @param  taskResourceID
     * @param @return 参数
     * @return Map<String, String>    返回类型
     * @throws
     * @Title: findTestTaskByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Map<String, Object> findTestTaskByTestTaskResourceID(String taskResourceID);

    /**
     * @param enableSystemResourceID : 开启状态的系统resourceIDs
     * @param enableModuleResourceID : 开启状态的模块resourceIDs
     * @param keyWord                : 关键字
     * @return : java.util.List<com.jettech.model.TreeNode>
     * @Method : findBySystemResourceIDsOrModuleResourceIDsOrKeyWord
     * @Description : 通过系统resourceIDs,模块resourceIDs,关键字,查询交易树节点
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-06-24 周三 15:33:54
     */
    List<TreeNode> findBySystemResourceIDsOrModuleResourceIDsOrKeyWord(ArrayList<String> enableSystemResourceID, ArrayList<String> enableModuleResourceID, String keyWord);

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findTradeByModuleResourceID
     * <AUTHOR> 2020-07-16
     * @Description: 根据模块查询交易
     */
    Result findTradeByModuleResourceID(String moduleResourceID);

    /**
     * 根据被测系统和交易编号查询交易
     * @param testSystemRid
     * @param number
     * @return
     */
    Trade findByTestSystemResourceAndNumber(Long testSystemRid, String number);

    /**
     * <AUTHOR>
     * @description 确认删除交易
     * @date 2020年11月25日 15:37
     * @param [resourceID, userNumber]
     * @return com.jettech.dto.Result
     **/
    Result confirmDeleteTrade(String resourceID, String userNumber);

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findTradeByModuleResourceIDs
     * <AUTHOR> 2020-07-16
     * @Description: 根据模块查询交易
     */
    Result findTradeByModuleResourceIDs(String moduleResourceID,String taskResourceID);

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findTradeByModuleResourceIDs
     * <AUTHOR> 2020-07-16
     * @Description: 根据模块查询交易
     */
    Result findTradeBySystemAndTask(String systemResourceID,String taskResourceID);

    List<SystemModuleTradeDTO> getTradeSystemModule(List<Long> tradeResourceIDList);
    
    /**
     * 
     *@param systemResourceIDs 系统id
     * @throws
     * @Title: findTradeByModuleResourceIDs
     * @Description: 根据模块查询交易
     */
    List<Map<String, Object>> findTradeByModuleResourceIDs(List<String> moduleResourceIDs);

    Result<?> initTradeTreeByDemand(Long demandResourceID);

	Trade findTradeAndanagerByResourceID(long parseLong);
    /**
     * 
     *@Description 根据系统的rid查询交易
     *@param 
     *@return List<Trade>
     *<AUTHOR>
     *@Date 2023年2月21日
     */
	List<Trade> findBySystesmResourceIDIn(List<Long> sysList);
}
