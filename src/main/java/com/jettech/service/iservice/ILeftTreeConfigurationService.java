package com.jettech.service.iservice;

import com.jettech.model.LeftTreeConfiguration;

/**
 * <AUTHOR>
 * @ClassName ILeftTreeConfiguration
 * @Description 自定义左侧树service
 * @Date 2020-02-21 16:06
 * @Version V1.0
 */
public interface ILeftTreeConfigurationService extends IBaseService<LeftTreeConfiguration> {

    /**
     * @Title: findLeftTreeConfigurationByDemandResrouceIDAndType
     * @Description:  根据当前需求和类型查询自定义左侧树
     * @Param: "[demandResourceID, leftType]"
     * @Return: "java.lang.String"
     * @Author: xpp
     * @Date: 2020/2/21
     */
    LeftTreeConfiguration findLeftTreeConfigurationByDemandResrouceIDAndType(String demandResourceID, String leftType);
}
