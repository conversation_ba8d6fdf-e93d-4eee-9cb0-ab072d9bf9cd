package com.jettech.service.iservice;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.jettech.DTO.TaskTestCaseDTO;
import com.jettech.DTO.TaskTradeCaseDto;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.GenerageCaseDto;
import com.jettech.common.dto.datadesign.TestCaseDTO;
import com.jettech.model.TestCase;
import com.jettech.view.TaskTradeCaseView;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * @ClassName ITestCaseService
 * @description ITestCaseService
 * <AUTHOR>
 * @create 2019-11-04 19:09
 */
public interface ITestCaseService extends BaseHBService<TestCase> {
	/**
	 * @Title: addTestCase
	 * @Description: 新增案例
	 * @Param: "[testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	Result addTestCase(TestCase testCase, MultipartFile[] files, String userNumber);

	/**
	 * @Title: updateTestCase
	 * @Description: 修改案例
	 * @Param: "[testCase, s]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	Result updateTestCase(TestCase testCase, MultipartFile[] files, String userNumber);

	/**
	 * @Title: findByTestCase
	 * @Description: 查询案例详情
	 * @Param: "[currentTestCaseResourceID]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	Result findByTestCase(String currentTestCaseResourceID);

	/**
	 * @Title: deleteTestCase
	 * @Description: 删除案例
	 * @Param: "[iDList]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	Result deleteTestCase(List<String> iDList, String userNumber);

	/**
	 * @Title: findbyTradeResourceID
	 * @Description: 根据当前交易查询其下的所有案例
	 * @Param: "[tradeResourceID]"
	 * @Return: "java.util.List<TestCase>"
	 * @Author: xpp
	 * @Date: 2019/11/6
	 */
	List<TestCase> findbyTradeResourceID(Long tradeResourceID);

	/**
	 * @Title findbyTradeResourceIDList
	 * @Description 根据交易集合查询案例
	 * @Params [collect]
	 * @Return [List<TestCase>]
	 * <AUTHOR>
	 * @Date 2019/11/6
	 */
	List<TestCase> findbyTradeResourceIDList(List<Long> collect);

	/**
	 * @Description 通过交易resourceid查询案例带分页
	 * <AUTHOR>
	 * @date 2019-11-06 17:32
	 * @param paramMap
	 * @return com.jettech.dto.Result
	 */
	Result findByTradeAndOptions(Map paramMap);

	/**
	 * @Title: findByCaseIdsAndTradeResourceID
	 * @description: 通过案例编号和交易ResourceID查询
	 * @param "[caseIds]"
	 * @param
	 * @param demandResourceID
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2019/11/15 17:54
	 */
	List<TestCase> findByCaseIdsAndTradeResourceID(List<String> caseIds, String tradeResourceID,
			String demandResourceID);

	/**
	 * @Title: isNotRepeat
	 * @Description: 判断案列编号是否重复
	 * @Param: "[request, testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/21
	 */
	Result isNotRepeat(TestCase testCase);

	/**
	 * @Title: findTestPlanTreebyTestCaseResourceID
	 * @Description: 根据案例查询组装测试计划左侧树结构
	 * @Param: "[testCaseResouceIDList]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/27
	 */
	Result findTestPlanTreebyTestCaseResourceID(List<Map<Long, String>> maps);

	/**
	 * @Description 通过交易resourceid查询交易下案例数量
	 * <AUTHOR>
	 * @date 2019-11-27 18:44
	 * @param t
	 * @param demandResourceID
	 * @param s
	 * @return java.lang.Integer
	 */
	Integer findCountNumberByTradeResourceID(String t, String demandResourceID, String s);

	/**
	 * @Title findCaseTotalBySelectedCase
	 * @Description 根据选中的案例查询所属交易下的案例总数
	 * @Params [caseResourceID]
	 * @Return com.jettech.dto.Result
	 * <AUTHOR>
	 * @Date 2019/11/28
	 */
	Result findCaseTotalBySelectedCase(String caseResourceID);

	/**
	 * @Title findTestCaseMapByResourceID
	 * @Description 查询单个案例
	 * @Params [caseResourceID]
	 * @Return com.jettech.dto.Result<?>
	 * <AUTHOR>
	 * @Date 2019/11/29
	 */
	Result<?> findTestCaseMapByResourceID(String caseResourceID);

	/**
	 * @Title findTestCaseMapByResourceID
	 * @Description 根据demandresourceid查询案例
	 * @Params [caseResourceID]
	 * @Return List<TestCase>
	 * <AUTHOR>
	 * @Date 2019/12/04
	 */
	List<TestCase> findBydemandResourceID(Long ResourceID);

	/**
	 * @Title: getTestCaseCaseId
	 * @Description: 新增案例时回显的案例编号
	 * @Param: "[map]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/12/5
	 */
	Result getTestCaseCaseId(Map<String, String> map);

	/**
	 * @param testSystemResourceID
	 * @Title: findTestPlanTreebyTestCaseResourceID
	 * @Description: 根据案例查询组装测试计划左侧树结构不带案例节点
	 * @Param: "[testCaseResouceIDList]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/27
	 */
	Result findTradeTreebyTestCaseResourceID(List<Map<Long, String>> maps, String testSystemResourceID);

	/**
	 * @Title: getNewTestCaseCaseId
	 * @Description: 根据最新要求返回案例编号
	 * @Param: "[map]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/12/24
	 */
	String getNewTestCaseCaseId(Long tradeResourceID);
	String getNewTestCaseCaseId(Long tradeResourceID, String maxCaseId, String maxRecycleBinCaseId, String maxAssetCaseId);

	/**
	 * @Title: updateTestCaseByTrade
	 * @Description: 批量更新案例的编号
	 * @Param: "[]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/12/25
	 */
	Result updateTestCaseByTrade() throws Exception;

	/**
	 * @param userNumber
	 *
	 * @Title: findCountNumberByDemandRids @Description:
	 *         根据需求查询需求下手工编写案例数 @param @param demandResourceIDs @param @return
	 *         参数 @return List<Map<String,Object>> 返回类型 @throws
	 */
	List<Map<String, Object>> findCountNumberByDemandRids(List<String> demandResourceIDs, String userNumber);

	/**
	 * QiaoHongju
	 *
	 * @param moduleResourceID
	 * @param testCaseDTOPage
	 * @return
	 */
	IPage<TestCase> findBySystemModule(Long moduleResourceID, Long demandResourceID, IPage<TestCase> testCasePage);

	/**
	 * @Title findTotalNumberByDemandRids
	 * @Description 查询需求下的所有案例总数
	 * @param demandResourceIDs
	 * @param userNumber
	 * @return List<Map<String,Object>>
	 * <AUTHOR>
	 * @data Jan 10, 20205:44:48 PM
	 */
	List<Map<String, Object>> findTotalNumberByDemandRids(List<String> demandResourceIDs);

	/**
	 * @Title: InitAssetsLeftTree
	 * @description: 引用案例资产库左侧树
	 * @param "[]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2020/1/13 10:50
	 */
	Result InitAssetsLeftTree();

	/**
	 * @Title: InitAssetsTestCaseTables
	 * @description: 初始化资产库案例列表
	 * @param "[map]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2020/1/13 10:59
	 */
	Result InitAssetsTestCaseTables(HashMap map);

	/**
	 * @Title: QuoteTestCase
	 * @description: 引用案例
	 * @param "[map]"
	 * @param
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2020/1/13 13:56
	 */
	Result QuoteTestCase(Map map) throws Exception;

	/**
	 * @Title: findByTradeRidAndTestEnviroment
	 * @description: 通过交易rid和测试环境查询案例
	 * @param "[valueOf,       testEnviroment]"
	 * @param demandResourceID
	 * @param userNumber
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2020/2/11 11:15
	 */
	List<TestCase> findByTradeRidAndTestEnviroment(Long tradeResourceID, String demandResourceID, String testEnviroment,
			String userNumber);

	/**
	 * 通过TradeResources查询 QiaoHongju
	 *
	 * @param tradeResourceIDList
	 * @param testCasePage
	 * @return
	 */
	IPage<TestCase> findTestCaseByTradeResourceIDsAndDemandResourceIDPage(List<String> tradeResourceIDList,
			String demandResourceID, String testEnviroment, Page<TestCase> testCasePage);

	/**
	 * @Title: findByTradeResourceIDAndDemadnResourceID
	 * @description: 交易rid和需求rid查询
	 * @param "[tradeResourceID, demandResourceID]"
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2020/2/25 10:47
	 */
	List<TestCase> findByTradeResourceIDAndDemandResourceID(Long tradeResourceID, String demandResourceID);

	/**
	 * @Title: saveImportList
	 * @description: 批量导入案例
	 * @param "[addCase, userNumber]"
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2020/3/4 10:44
	 */
	List<TestCase> saveImportList(ArrayList<TestCase> addCase, String userNumber);

	/**
	 *
	 * @Title: findTestcasequoteExportedWord
	 * @Description: 查询导出引用执行案例到word文档需要数据
	 * @param map
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @date 2020年3月13日下午4:29:51
	 */
	Result<?> findTestcasequoteExportedWord(Map<String, Object> map);

	/**
	 * 查询手工执行任务下选中保存的案例所属交易Rid
	 *
	 * @param userNumber
	 * @param hasQuoteSearchCaseRids
	 * @param testPlanResourceID
	 * @Title: findTradeRidsBySelectedTestCaseByTaskResourceID
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return List<Trade> 返回类型
	 * @throws <AUTHOR>
	 */
	List<String> findTradeRidsBySelectedTestCaseByTaskResourceID(String taskResourceID, String userNumber,
			List<String> hasQuoteSearchCaseRids);

	/**
	 * 查询手工执行任务下选中保存的案例所属交易Rid
	 *
	 * @param userNumber
	 * @param hasQuoteSearchCaseRids
	 * @param testPlanResourceID
	 * @Title: findTradeRidsBySelectedTestCaseByTaskResourceID
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return List<Trade> 返回类型
	 * @throws <AUTHOR>
	 */
	List<String> findTradeRidsBySelectedTestCaseByTaskResourceID(String taskResourceID, String userNumber,
			List<String> hasQuoteSearchCaseRids, String testPlanResourceID);

	/**
	 * 根据交易和任务的rid查询当前任务当前交易下的手工编写案例
	 *
	 * @Title: findTestCaseByTaskAndTradeResourceID
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  map
	 * @param @return 参数
	 * @return List<TestCase> 返回类型
	 * @throws <AUTHOR>
	 */
	Result<?> findTestCaseByTaskAndTradeResourceID(HashMap<String, Object> map);

	/**
	 * 通过交易和任务查询保存的案例
	 *
	 * @param userNumber
	 * @param hasQuoteSearchCaseRids
	 * @param testPlanResourceID
	 * @Title: findTestCasesByTaskAndTrades
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  trades
	 * @param @param  taskResourceID
	 * @param @return 参数
	 * @return List<TestCase> 返回类型
	 * @throws <AUTHOR>
	 */
	List<TestCase> findTestCasesByTaskAndTrades(String tradeResourceID, String taskResourceID, String userNumber,
			List<String> hasQuoteSearchCaseRids);

	/**
	 * 通过字典的name查询案例的字典数据
	 *
	 * @Title: findTestCaseDictionaryData
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  dicName
	 * @param @return 参数
	 * @return List<Map<String,Object>> 返回类型
	 * @throws <AUTHOR>
	 */
	List<Map<String, Object>> findTestCaseDictionaryData(String dicName);

	/**
	 * 查询所有人员的name和number
	 *
	 * @Title: findAllUsersNameAndNumber
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return List<Map<String,String>> 返回类型
	 * @throws <AUTHOR>
	 */
	List<Map<String, String>> findAllUsersNameAndNumber();

	/**
	 * 案例资产库：根据最新要求返回最大的案例编号
	 *
	 * @Title: getAssetMaxTestCaseCaseId
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return String 返回类型
	 * @throws <AUTHOR>
	 */
	String getAssetMaxTestCaseCaseId(Long tradeResourceID);

	/**
	 * 分页查询案例信息
	 *
	 * @Title: findCaseInfoPage
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  pageRequest
	 * @param @param  caseResourceID
	 * @param @return 参数
	 * @return List<Map<String,Object>> 返回类型
	 * @throws <AUTHOR>
	 */
	PageImpl<Map<String, Object>> findCaseInfoPage(PageRequest pageRequest, List<Long> caseResourceID);


	/**
	 * @Title findTotalZXNumberByDemandRids
	 * @Description 查询需求下的所有执行案例
	 * @param demandResourceIDs
	 * @return   List<Map<String,Object>>
	 * <AUTHOR>
	 */
	List<Map<String, Object>> findTotalZXNumberByDemandRids(List<String> demandResourceIDs);
	/**
	 *
	 * 批量保存案例
	 *
     * @return
     */
	Result<?> batchesSaveTestCaseByLimit(List<TestCase> testCaseList, String userNumber);
	/**
	 *
	 * 批量更新案例
	 *
	 */
	void batchesUpdateTestCaseByLimit(List<TestCase> testCaseList, String userNumber);
	/**
	 * @Title: findCaseIDByTradeResourceID
	 * @description: 查询交易下已经存在的全部案例编号
	 * <AUTHOR>
	 * @date 2020/6/16
	 */
	List<String> findCaseIDByTradeResourceID(Long tradeResourceID);

	/**
	 *
	 * @Title: commitTestCase
	 * @Description: 提交案例
	 * @param testTaskResourceID
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @param userNumber2
	 * @date 2020年6月9日 下午6:02:21
	 */
	Result<?> commitTestCase(String testTaskResourceID,String state, String userNumber);
	/**
     * @Title findByTradeRidAndTaskRid
     * @description 查询交易下某一任务的案例
     * @date 20200628
     * <AUTHOR>
     */
	List<TestCase> findByTradeRidAndTaskRid(Long tradeResourceID, Long taskResourceID, String maintainer,
			String isNegative, String caseType, String caseId, String testMode, String testCaseResourceIDs, String leadsource);

	/**
	 *
	 * @Title: findProjectTestCase
	 * @Description: 分页查询项目案例
	 * @return
	 * <AUTHOR>
	 * @param demandFlag
	 * @param tagResourceIDs
	 * @param caseFinalResult
	 * @date 2020年7月14日 下午5:21:51
	 */
	PageImpl<TestCaseDTO> findProjectTestCase(PageRequest pageRequest, String testSystem, String trade, String caseId,
											  String intent, String caseType, String casetLevel, String resourceID,
											  String nodeType, String demandFlag, String tagResourceIDs,String caseFinalResult, String userNumber);


	/***
	 * 通过项目组的rid查询案例
	 * @Method : findByProjectGroupResourceIDs
	 * @Description : 通过项目组的rid查询案例
	 * @param pgResourceIDList : 项目rids
	 * @return : java.util.List<com.jettech.model.TestCase>
	 * <AUTHOR> Wws.
	 * @CreateDate : 2020-07-14 星期二 17:52:28
	 *
	 */
    List<TestCase> findByProjectGroupResourceIDs(List<String> pgResourceIDList,String testSystem,String trade,String caseId,String intent,String caseType,String casetLevel, String demandFlag);
	/**
	 * @Title: addProjectCaseLibTestCase
	 * @Description: 新增项目案例库案例（用的表还是ds_testcase,通过leadsource字段区分，案例来源（默认0，项目案例为1））
	 * @Param: "[testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: cao_jinbao
	 * @Date: 2020/7/15
	 */
	Result addProjectCaseLibTestCase(TestCase testCase, String userNumber);
	/**
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @Title: updateProjectCaseLibTestCase
	 * @Description: 修改项目案例库案例
	 * @Param: "[testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: cao_jinbao
	 * @Date: 2020/7/15
	 */
	Result updateProjectCaseLibTestCase(TestCase testCase, String userNumber) throws IllegalArgumentException, IllegalAccessException;

	/**
	 * @Method: relationDemand
	 * @Description: //案例关联需求
	 * @Param: " [demandResourceID, testCaseResourceID, userNumber] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/7/15
	 */


    Result relationDemand(String demandResourceID, String testCaseResourceID, String userNumber);

    /**
     * @Method: findAllTaskTestCase
     * @Description: 查询所有需求案例
     * @Param: " [] "
     * @return: com.jettech.dto.Result<java.util.List<com.jettech.DTO.TaskTestCaseDTO>>
     * @Author: wws
     * @Date: 2020/7/29
     */
    Result<List<TaskTestCaseDTO>> findAllTaskTestCase();
    /**
     * //查询案例执行库是否有数据
     * @param listS
     * @return
     * caojinbao
     */
	int findPerformCaseByProjectGroupResourceIDs(List<String> listS);
    /**
     *
    * @Title: findInfoByTestCaseResourceID
    * @Description: 根据当前案例的rid查找案例所属的系统模块交易和所属需求所属项目
    * @param @param resourceIDs
    * @param @param request
    * @param @return    参数
    * @return Result    返回类型
    * @throws
    * <AUTHOR>
     */
	Result findInfoByTestCaseResourceID(String resourceID,String taskResourceID,String testProjectResourceID);
    /**
     *
    * @Title: findAnotherInfoByTestCaseResourceID
    * @Description: 根据当前案例的rid查找案例所属的系统模块交易和所属项目(案例执行库页面用)--没有所属需求
    * @param @param resourceIDs
    * @param @param request
    * @param @return    参数
    * @return Result    返回类型
    * @throws
    * <AUTHOR>
     */
	Result findAnotherInfoByTestCaseResourceID(String performCaseResourceID);
	/**
	 *
	* @Title: countAllPerformCaseAddToExecute
	* @Description: 校验当前需求下的项目案例是否都都添加到项目案例执行表
	* @param @param demandResourceID
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	int countAllPerformCaseAddToExecute(Long demandResourceID);
	/**
	 *
	* @Title: countAllTestCaseAddToExecute
	* @Description:校验当前需求下的手工执行案例的数据是否都都添加到手工案例引用表
	* @param @param demandResourceID
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	int countAllTestCaseAddToExecute(Long demandResourceID);
	/**
	 *
	* @Title: findPerformcaseByCaseResourceID
	* @Description: 项目案例表里当前案例非成功取消状态的案例
	* @param @param ceseRids
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	Integer findPerformcaseByCaseResourceID(List<Long> ceseRids,String flag);
	/**
	 *
	* @Title: findTestCaseByCaseResourceID
	* @Description: 手工执行案例引用表里当前案例非成功取消状态的案例
	* @param @param ceseRids
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	Integer findTestCaseByCaseResourceID(List<Long> ceseRids,String flag);
	/**
	 * 根据需求的查询案例表信息
	 * @return List<String,Object>
	 * <AUTHOR>
	 */
	 List<Map<String,Object>> findCaseInfoByDemandResourceID(Long demandResourceID);

	/**
	 * 根据tradeId更新tradeName
	 * @param tradeName
	 * @param tradeResourceID
	 * @return Result
	 * by zhangsheng
	 */
    Result updateTestCaseTradeNameByTradeId(String tradeName, Long tradeResourceID);

	/**
	 * 根据SystemId更新SystemName
	 * @param systemName
	 * @param systemResourceId
	 * @return Result
	 * by zhangsheng
	 */
    Result updateTestCaseSystemNameBySystemId(String systemName, Long systemResourceId);

	/**
	 * 根据testSystemResourceID 将ds_testcase表中的模块根据name替换
	 * @param oldName
	 * @param newName
	 * @param testSystemResourceID
	 * @return
	 */
	Result updateTestCaseSystemModuleNameByNameAndSystemId(String oldName, String newName, Long testSystemResourceID);

    PageImpl<Map<String, Object>> initMyCaseToAssesWithBynumber(PageRequest pageRequest, Map<String, String> params);

    Result updateCaseStatus(Map<String, String> params, String userNumber);
	/**
	 *
	* @Title: findTestCaseDetailsForDefect
	* @Description:点击缺陷所属案例编号查询任务案例的信息
	* @param @param params
	* @param @return    参数
	* @return Result    返回类型
	* @throws
	* <AUTHOR>
	 */
	Result<?> findTestCaseDetailsForDefect(String testCaseResourceID);


	/**
	 * <AUTHOR>
	 * @description 根据caseID批量更新
	 * @date 2020年11月09日 9:44
	 * @param [entityList, batchCount]
	 * @return void
	 **/
	void updateBatchByCaseId(Collection<TestCase> entityList, int batchCount);

	/**
	 * <AUTHOR>
	 * @description 根据caseid和tradeResid查询案例
	 * @date 2020年11月09日 10:04
	 * @param [caseId, resourceID]
	 * @return java.util.List<com.jettech.model.TestCase>
	 **/
	List<TestCase> findByCaseIdAndTradeResId(String caseId, Long tradeRresourceID);

	/**
	 * <AUTHOR>
	 * @description 通过infoname查询字典值
	 * @date 2020年12月15日 14:22
	 * @param [type]
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 **/
	List<Map<String, Object>> findTestCaseDictionaryDataByInfoName(String infoName);

	/**
	 * <AUTHOR>
	 * @description 根据需求resourceid查询需求下的案例
	 * @date 2021年01月06日 10:04
	 * @param [demandsResourceIDList]
	 * @return java.util.List<com.jettech.model.TestCase>
	 **/
	List<TestCase> findBydemandResourceIDs(List<Long> demandsResourceIDList);

	/**
	 * 根据资源id查询标签关联表数据
	 * <AUTHOR>
	 *    10:10
	 * @update
	 * @param [relationResourceIDs]     [relationResourceIDs]说明
	 * @return  java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 * @exception/throws [异常类型] [异常说明]
	 * @see   [类、类#方法、类#成员]
	 * @since [起始版本]
	 */
    List<Map<String, Object>> findRelationResourceTags(List<String> relationResourceIDs,int relationType);

	List<Map<String, Object>> findTaskTree();

	List<Map<String, Object>> findTradeTree(String taskResourceIDs);

	PageInfo<TaskTradeCaseView> findTaskTradeCasePage(TaskTradeCaseDto dto);

	List<String> findCaseQuote(TaskTradeCaseDto dto);

	Object findSceneCaseByTradeID(JSONObject params);

	Object findApiScriptById(String tradeFlowId);

	List<TestCase> findProjectCase(Map<String, Object> params);

	Map<String, Object> findProjectCasePage(Map<String,Object> params);

	List<Map<String, String>> findAllUser();

	List<TestCase> findTaskCase(String taskResourceIDs);

	/**
	 * 根据项目组获取新建类项目组中的案例信息
	 * @param projectGroupResourceID
	 * @return
	 */
	List<TestCase> findByProjectGroupResourceID(Long projectGroupResourceID);

	Result generateCase(GenerageCaseDto generageCaseDto);
	PageInfo<TestCase> InitModelTestCaseTables(Map<String, String> params);

}
