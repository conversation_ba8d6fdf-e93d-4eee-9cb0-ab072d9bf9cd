package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.AtTestCase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ITestCaseService
 * @description ITestCaseService
 * <AUTHOR>
 * @create 2019-11-04 19:09
 */
public interface ITestCaseService extends BaseHBService<AtTestCase> {
    /**
     * @Title: addTestCase
     * @Description: 新增案例
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    Result addTestCase(AtTestCase testCase, String userNumber);

    /**
     * @Title: updateTestCase
     * @Description: 修改案例
     * @Param: "[testCase, s]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    Result updateTestCase(AtTestCase testCase, String userNumber);

    /**
     * @Title: findByTestCase
     * @Description: 查询案例详情
     * @Param: "[currentTestCaseResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    Result findByTestCase(String currentTestCaseResourceID);

    /**
     * @Title: deleteTestCase
     * @Description: 删除案例
     * @Param: "[iDList]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    Result deleteTestCase(List<String> iDList, String userNumber);

    /**
     * @Title: findbyTradeResourceID
     * @Description: 根据当前交易查询其下的所有案例
     * @Param: "[tradeResourceID]"
     * @Return: "java.util.List<com.jettech.model.TestCase>"
     * @Author: xpp
     * @Date: 2019/11/6
     */
    List<AtTestCase> findbyTradeResourceID(Long tradeResourceID);

    /**
     * @Title findbyTradeResourceIDList
     * @Description 根据交易集合查询案例
     * @Params [collect]
     * @Return [List<TestCase>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    List<AtTestCase> findbyTradeResourceIDList(List<Long> collect);

    /**
     * @Description 通过交易resourceid查询案例带分页
     * <AUTHOR>
     * @date 2019-11-06 17:32
     * @param paramMap
     * @return com.jettech.dto.Result
     */
    Result findByTradeAndOptions(HashMap paramMap);

    /**
     * @Title: findByCaseIdsAndTradeResourceID
     * @description: 通过案例编号和交易ResourceID查询
     * @param "[caseIds]"
     * @return java.util.List<com.jettech.model.TestCase>
     * @throws
     * <AUTHOR>
     * @date 2019/11/15 17:54
     */
    List<AtTestCase> findByCaseIdsAndTradeResourceID(List<String> caseIds, String tradeResourceID);

    /**
     * @Title: isNotRepeat
     * @Description: 判断案列编号是否重复
     * @Param: "[request, testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/21
     */
    Result isNotRepeat(AtTestCase testCase);

     /**
     * @Description 通过交易resourceid查询交易下案例数量
     * <AUTHOR>
     * @date 2019-11-27 18:44
      * @param t
     * @return java.lang.Integer
     */
    Integer findCountNumberByTradeResourceID(String t);
    /**
     * @Title findCaseTotalBySelectedCase
     * @Description 根据选中的案例查询所属交易下的案例总数
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/28
     */
    Result findCaseTotalBySelectedCase(String caseResourceID);
    /**
     * @Title findTestCaseMapByResourceID
     * @Description 查询单个案例
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @Date 2019/11/29
     */
    Result<?> findTestCaseMapByResourceID(String caseResourceID);

    /**
     * @Description 查询当前项目下单点案例中被测系统、模块、交易树
     * <AUTHOR>
     * @date 2019-11-06 10:47
     * @param "projectResourceID"
     * @return com.jettech.dto.Result<?>
     */
    Result<?> findSinglePointLeftTreeByProjectResourceID();
    
    /**
     * 
     * @Title:copyTestCase
     * @Description:复制需求案例至案例库
     * @param 
     * @return Result
     * @author: wu_yancheng
     * @date 2019年12月9日下午5:15:49
     */
    Result<?> copyTestCase(Map<String,Object> map);

    /**
     * @Title: getNewTestCaseCaseId
     * @Description: 根据最新要求返回案例编号
     * @Param: "[map]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/24
     */
    String getNewTestCaseCaseId(Long tradeResourceID);

    /** 
      * @Title: updateTestCaseByTrade
      * @description: 洗数据(字段caseId)
      * @param "[]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2019/12/26 12:53 
      */
    Result updateTestCaseByTrade()  throws Exception;

    /**
     * @Title: getNewTestCaseCaseId
     * @Description: 案例资产库：根据最新要求返回最大的案例编号
     * @Param: "[tradeResourceID]"
     * @Return: "java.lang.String"
     * @Author: xpp
     * @Date: 2020/1/13
     */
    Result getMaxTestCaseCaseId(Long tradeResourceID);

    /**
      * @Title: isOperationAuthority
      * @description: 登录用户是否有操作案例库的权限
      * @param "[userNumber]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2020/1/14 11:08
      */
    Result isOperationAuthority();


    void syncTestCase4Demand(String demandResourceID);


    /**
      * @Title: saveImportList
      * @description: 批量导入
      * @param "[addCase, userNumber]"
      * @return java.util.List<junit.framework.TestCase>
      * @throws
      * <AUTHOR>
      * @date 2020/3/5 10:24
      */
    List<AtTestCase> saveImportList(ArrayList<AtTestCase> addCase, String userNumber);
    /**
     * 根据交易和测试环境查询案例
    * @Title: findbyTradeResourceIDAndTestEnviroment
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param valueOf
    * @param @param testEnviroment
    * @param @return    参数
    * @return List<AtTestCase>    返回类型
    * @throws
    * <AUTHOR>
     */
	List<AtTestCase> findbyTradeResourceIDAndTestEnviroment(Long tradeRsourceID, String testEnviroment,String maintainer,
															String isNegative, String caseType);
	/**
	 * 
	 * @Title findTestCaseDictionaryData
	 * @Description 通过字典的name查询案例的字典数据
	 * @return List<Map<String,Object>> 返回类型
	 * <AUTHOR>
	 */
	List<Map<String, Object>> findTestCaseDictionaryData(String nameDescription);
	/**
	 * 
	 * 批量保存案例
	 * 
	 */
	void batchesSaveTestCaseByLimit(List<AtTestCase> testCaseList, String userNumber);
	/**
	 * 
	 * 批量更新案例
	 * 
	 */
	void batchesUpdateTestCaseByLimit(List<AtTestCase> testCaseList, String userNumber);
	/**
	 * 
	 * @Title: findAllUsersNameAndNumber
	 * @Description: 查询所有人员的name和number
	 * <AUTHOR>
	 */
	List<Map<String, String>> findAllUsersNameAndNumber();
	/**
	 * 
	 * @Title findTestSystemByTradeResourceID
	 * @Description 查询交易对应的系统
	 * @return Map<String,Object> 返回类型
	 * <AUTHOR>
	 */
	Map<String, Object> findTestSystemByTradeResourceID(Long tradeResourceID);

	/**
	 * @Title: findCaseIDByTradeResourceID
	 * @description: 查询交易下已经存在的全部案例编号
	 * <AUTHOR>
	 * @date 2020/6/16
	 */
	List<String> findCaseIDByTradeResourceID(Long tradeResourceID);

    Result<?> findTestCaseMapByResourceIDFeginToDataDic(String caseResourceID);

    Page<Map<String, Object>> initMyCaseToAssesWithBynumber(PageRequest pageRequest, Map<String, String> params);

    /**
     * <AUTHOR>
     * @description 根据字典中infoName查询字典值
     * @date 2021年01月21日 9:52
     * @param [nameDescription]
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     **/
	List<Map<String, Object>> findTestCaseDictionaryDataByInfoName(String nameDescription);
}
