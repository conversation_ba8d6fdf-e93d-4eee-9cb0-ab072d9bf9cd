package com.jettech.service.iservice;

import com.jettech.DTO.ProjectUserDTO;
import com.jettech.common.dto.Result;
import com.jettech.model.TestProjectUser;
import org.springframework.data.domain.PageRequest;

import java.util.List;


/**
 * 项目和人员关联
* @ClassName: ITestProjectUserService
* @Description: TODO(这里用一句话描述这个类的作用)
* <AUTHOR>
* @date 2020年4月20日
*
 */
public interface ITestProjectUserService extends IBaseService<TestProjectUser>{
   
	/**
	 * 
	 * @Title: findByTestProjectResourceID
	 * @Description: 查询项目关联的人员
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午3:01:07
	 */
	public List<TestProjectUser>findByTestProjectResourceID(Long resourceID);
	
	
	/**
	 * 
	 * @Title: findNotRelateUser 
	 * @Description: 查询未关联的人员
	 * @param name
	 * @param deptName
     * @param testProjectResourceID
     * @param userGroupReourceID
	 * @param pageRequest
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:05:58
	 */
	public Result<?> findNotRelateUser(String name, String deptName, String testProjectResourceID, String userGroupReourceID, PageRequest pageRequest);
	
	/**
	 * 
	 * @Title: findRelatedUser 
	 * @Description: 查询已关联人员
	 * @param name
	 * @param deptName
	 * @param testProjectResourceID
	 * @param userGroupReourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:28:52
	 */
	public Result<?> findRelatedUser(String name, String deptName, String testProjectResourceID, String userGroupReourceID, PageRequest pageRequest);
	
	/**
	 * 
	 * @Title: relateUser 
	 * @Description: 关联人员
	 * @param userResourceIDs
	 * @param testProjectResourceID
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:38:20
	 */
	public Result<?> relateUser(String userResourceIDs,String testProjectResourceID,String userNumber);
	
	/**
	 * 
	 * @Title: cancelRelatedUser 
	 * @Description: 取消关联人员
	 * @param userResourceIDs
	 * @param testProjectResourceID
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:38:20
	 */
	public Result<?> cancelRelatedUser(String userResourceIDs, String testProjectResourceID, String userNumber);
	
	/**
	 * 
	 * @Title: findByTestProjectResourceIDAndUserResourceIDIn 
	 * @Description: 根据项目和人员查询关联关系
	 * @param testProjectResourceID
	 * @param userResourceIDs
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:52:21
	 */
	public List<TestProjectUser> findByTestProjectResourceIDAndUserResourceIDIn(Long testProjectResourceID,List<Long> userResourceIDs);
	
	/**
	 * 
	 * @Title: findByTestProjectResourceIDIN 
	 * @Description: 查询项目与人员关联关系
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 上午9:53:14
	 */
	public List<TestProjectUser> findByTestProjectResourceIDIN(List<Long> resourceIDList);

	/**
	 * @Method: findALlProjectAndUser
	 * @Description: 项目和用户的关联关系（带用户名称和number）
	 * @Param: " [] "
	 * @return: java.util.List<com.jettech.model.TestProjectUser>
	 * @Author: wws
	 * @Date: 2020/9/23
	 */
    List<ProjectUserDTO> findALlProjectAndUser();
}
