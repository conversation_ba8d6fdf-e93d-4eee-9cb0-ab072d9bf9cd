package com.jettech.service.iservice;

import com.alibaba.fastjson.JSONObject;
import com.jettech.DTO.SceneCaseExecDTO;
import com.jettech.DTO.TestCasequoteDTO;
import com.jettech.common.dto.Result;
import com.jettech.model.SceneCaseContain;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ISceneCaseContainService
 * @projectName jettopro
 * @description: 场景案例
 */
public interface ISceneCaseContainService extends BaseHBService<SceneCaseContain> {
    public Object findBySceneCaseResourceID(JSONObject params);

    public List<TestCasequoteDTO> findTestCaseQuoteDTOList(SceneCaseExecDTO sceneCaseExecDTO);

    public boolean isSceneQuote(String testCaseResourceID);

    Result<?> checkInitParamBySceneCaseList(List<Long> testCaseResourceIDList);
}
