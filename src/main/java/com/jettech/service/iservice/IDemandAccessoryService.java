package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.DemandAccessory;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-09-28 16:22
 */
public interface IDemandAccessoryService extends  IBaseService<DemandAccessory> {
    Result<?> findByDefectresourceID(String resourceID);


    String downloadDemand(Map<String, Object> mapParam) throws IOException;
    byte[] getFileData(String resourceID,String exName, String path);

}
