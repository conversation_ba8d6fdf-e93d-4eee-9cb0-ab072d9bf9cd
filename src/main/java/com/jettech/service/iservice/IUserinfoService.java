/**
 * 
 */
package com.jettech.service.iservice;

import java.util.List;
import java.util.Optional;

import com.jettech.model.User;

/**
 * <AUTHOR>
 */

public interface IUserinfoService extends IBaseService<User> {
	
	/**
	 * @desc 根据用户编码查询用户
	 * @param userName
	 * <AUTHOR>
     * @date: 2019年6月5日 上午10:41:37
	 */
	public Optional<User> findByNumber(String number);
	/**
	 * @desc 根据用户名称查询用户
	 * @param userName
	 * <AUTHOR>
     * @date: 2019年6月5日 上午10:41:37
	 */
	public Optional<User> findByUserName(String userName);

	List<String> findByUserResourceIDs(String valueOf);

	List<String> findByUserNumberResourceIDs(String userResourceIDs);
}
