package com.jettech.service.iservice;

import com.jettech.common.dto.assets.WikiSpacePageDTO;
import com.jettech.common.page.PageResult;
import com.jettech.model.WikiSpace;

import java.util.List;

public interface IWikiSpaceService extends IBaseService<WikiSpace> {

    PageResult getSpacePage(WikiSpacePageDTO dto);

    List<WikiSpace> getSpaceList(WikiSpacePageDTO dto);

    String addOrUpdateSpace(WikiSpace wikiSpace, String loginUserNumber);
}
