package com.jettech.service.iservice;

import com.jettech.DTO.SceneTaskInfoDTO;
import com.jettech.model.SceneTaskInfo;

import java.util.List;

public interface ISceneTaskInfoService {
    List<SceneTaskInfo> findByTestTaskResourceID(Long testTaskResourceID);

    SceneTaskInfo saveSceneTask(SceneTaskInfoDTO sceneTaskInfoDTO);

    SceneTaskInfo updateSceneTask(SceneTaskInfo sceneTaskInfo);

    void deleteById(Long id);
}
