package com.jettech.service.iservice;

import com.jettech.DTO.jettoApi.env.ApiEnvPage;
import com.jettech.DTO.jettoApi.env.ApiEnvQueryDto;
import com.jettech.DTO.jettoApi.result.base.ApiResultRoot;
import com.jettech.model.SystemModule;
import com.jettech.model.TestProject;
import com.jettech.model.TestSystem;
/**
 * <AUTHOR>
 * @title: JettoApiService
 * @projectName jettomanager
 * @description: jettoApi
 * @date 2021/3/3114:40
 */
public interface JettoApiService {

    /**
     * @Method: checkProjectDelete
     * @Description: 判断jettoApi的项目是否可以删除
     * @Param: " [projectResourceID] "
     * @return: boolean
     * @Author: wws
     * @Date: 2021/3/31
     */
    boolean checkProjectDelete(String projectResourceID);

    /**
     * @Method: saveProjectFolder
     * @Description: 添加jettoApi的项目关联关系
     * @Param: " [toString] "
     * @return: void
     * @Author: wws
     * @Date: 2021/4/1
     */
    void saveOrUpdateProjectFolder(TestProject testProject);

    /**
     * @Method: saveOrUpdateSystemFolder
     * @Description: 关联jettoApi的tradeFolder表
     * @Param: " [testSystem] "
     * @return: void
     * @Author: wws
     * @Date: 2021/4/2
     */
    void saveOrUpdateSystemFolder(TestSystem testSystem);

    /**
     * @Method: saveOrUpdateSystemFolder
     * @Description: 关联jettoApi的tradeFolder表
     * @Param: " [systemModule] "
     * @return: void
     * @Author: zhangnb
     * @Date: 2021/4/2
     */
    void saveOrUpdateSystemFolder(SystemModule systemModule);

    /**
     * @Method: saveOrUpdateTradeFlowCaseFolder
     * @Description: trade_flow_case_folder关联这个表
     * @Param: " [resultObject] "
     * @return: void
     * @Author: wws
     * @Date: 2021/4/22
     */
    void saveOrUpdateTradeFlowCaseFolder(TestProject resultObject);

    ApiResultRoot<ApiEnvPage> findEnvByPage(ApiEnvQueryDto apiEnvQueryDto);
}
