package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.EnvironmentPageDTO;
import com.jettech.model.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;

/**
 * @ClassName IEnvironmentService
 * @description 被测系统环境信息记录Service层
 * <AUTHOR>
 * @create 20200630
 */
public interface IEnvironmentService extends IBaseService<Environment>{
	/**
	 * @Title findEnvironmentPage
	 * @Description 分页查询环境信息
	 * @author: slq
	 * @date: 2020年6月30日 上午11:40:30
	 */
	Page<Environment> findEnvironmentPage(PageRequest pageRequest, EnvironmentPageDTO environment);
	/**
	 * @Title  addOrUpdateEnvironment     
	 * @Description  新建或修改环境   
	 * @author: slq    
	 * @date:   2020年6月30日 下午4:43:39
	 */
	String addOrUpdateEnvironment(Environment environment, String userNumber);
	/**
	 * @Title  deleteEnvironments     
	 * @Description 删除环境   
	 * @author: slq    
	 * @date:   2020年6月30日 下午5:11:02
	 */
	String deleteEnvironments(List<String> environmentResID, String userNumber);
	/**
	 * @Title  findByEnvironment     
	 * @Description  查询环境信息
	 * @author: slq    
	 * @date:   2020年6月30日 下午5:34:14
	 */
	List<Map<String, String>> findByEnvironment(Environment environment);
	/**
	 * @Title  findUpdateEnvironmentByResID     
	 * @Description 查询要修改的环境信息   
	 * @author: slq    
	 * @date:   2020年7月1日 上午11:51:04
	 */
	EnvironmentPageDTO findUpdateEnvironmentByResID(String resourceID);

	/**
	 * @Title: importExcel
	 * @description: 导出excel（支持批量）
	 * @param  [Map]
	 * @return com.jettech.dto.Result<?>
	 * @throws
	 * <AUTHOR>
	 * @date 2019/11/4 15:19
	 */
	void exportExcel(Map map);
}
