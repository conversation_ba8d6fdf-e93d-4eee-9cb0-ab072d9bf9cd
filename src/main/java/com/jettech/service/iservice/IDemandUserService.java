package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.DemandUser;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName IDemandUserService
 * @Description 需求与人员service
 * @date 2019年12月4日
 */
public interface IDemandUserService extends IBaseService<DemandUser> {
    /**
     * @param demandResourceID
     * @return
     * @Title: findUserNotINDemandUser
     * @Description: 根据项目查询用户
     * <AUTHOR>
     * @date 2019年12月04日
     */
    List<Map<String, Object>> findUserNotINDemandUser(String demandResourceID);

    /**
     * @param demandResourceID
     * @return
     * @Title: findUserByDemandResourceID
     * @Description: 根据需求查询用户
     * <AUTHOR>
     * @date 2019年12月04日
     */
    List<Map<String, Object>> findUserByDemandResourceID(String demandResourceID);

    /**
     * @param request
     * @return
     * @Title: addDemandUser
     * @Description: 给需求关联人员
     * <AUTHOR>
     * @date 2019年12月04日
     */
    List<DemandUser> findByUserResourceIDAndDemandResourceID(String userResourceID, String demandResourceID);

    List<DemandUser> findBydemandResourceID(Long ResourceID);


    /**
     * @param demandResourceID
     * @return List<Map   <   Object   ,   Object>>
     * @Title getUserByDemandResourceID
     * @Description 根据需求rid查询出用户
     * <AUTHOR>
     * @data Dec 5, 20193:24:46 PM
     */
    List<Map<String, String>> getUserByDemandResourceID(Long demandResourceID);

    /**
     * @param
     * @return void
     * @Title:joinDemand
     * @Description:用户加入需求
     * @author: wu_yancheng
     * @date 2019年12月9日上午10:55:57
     */
    Result<?> joinDemand(List<String> demandRids, String userResourceID, String userNumber);

    /**
     * @param userResID
     * @param demandResIDList
     * @Title findUserResIDAnddemandResIDList
     * @Description 根据人员和需求查询
     * <AUTHOR>
     */
    List<DemandUser> findUserResIDAnddemandResIDList(String userResID, List<String> demandResIDList);

    /**
     * @param userResID
     * @param demandResIDList
     * @param typeName
     * @param type
     * @Title findUserResIDAndDemandResIDListAndDemandType
     * @Description 根据人员，需求ResourceID和需求类型查询
     * <AUTHOR>
     */
    List<DemandUser> findUserResIDAndDemandResIDListAndDemandType(String userResID, List<String> demandResIDList, String type, String typeName);

    /**
     * @param "[userResourceID]"
     * @return java.util.List<Demand>
     * @throws
     * @Title: findByUserResourceID
     * @description: 用户rid查询需求
     * <AUTHOR>
     * @date 2019/12/25 11:52
     */
    List<DemandUser> findByUserResourceID(String userResourceID);

    /**
     * @param
     * @return List<Map   <   String   ,   Object>>
     * @Title:findUserByDemandRIDAndRole
     * @Description:根据需求id和角色查询人员
     * @author: wu_yancheng
     * @date 2020年2月12日下午2:39:50
     */
    List<String> findUserByDemandRIDAndRole(String demandResourceID, String role);

    /**
     * @param demandResourceID
     * @param userResourceID
     * @return com.jettech.dto.Result
     * @Description 通过需求rid和人员rid查询人员与需求关联表MAP
     * <AUTHOR>
     * @date 2020-02-18 10:59
     */
    Result findDemandUserMapByDemandResourceIDAndUserResourceID(String demandResourceID, String userResourceID);

    /**
     * @Title: getUserByTestTaskResourceID
     * @Description: 根据任务查询需求关联人员, 消除重复值
     * @Param: "[demandResourceID]"
     * @Return: "com.jettech.dto.Result<?>"
     * @Author: xpp
     * @Date: 2020/2/25
     */
    List<Map<String, String>> getUserByTestTaskResourceID(String demandResourceID);

    /**
     * @param testTaskResourceID
     * @return
     * @Title: findByTestTaskResourceID
     * @Description: 根据任务查询需求关联的人员
     * <AUTHOR>
     * @date 2020年6月24日 下午6:03:33
     */
    List<DemandUser> findByTestTaskResourceID(Long testTaskResourceID);

    /***
     *
     * @Method : getUserByDemandResourceIDAndUserGroupResourceIDs
     * @Description : 通过需求resourceID和角色resourceIDs查询人员
     * @param demandResourceID : 需求resourceID
     * @param userGroupResourceIDs : 角色resourceIDs
     * @return : com.jettech.dto.Result<java.util.Map<java.lang.String,java.lang.String>>
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-07-13 周一 17:53:25
     *
     */
    List<Map<String, String>> getUserByDemandResourceIDAndUserGroupResourceIDs(String demandResourceID, List<String> userGroupResourceIDs);

    /**
     * 根据testManagerResourceID,demandResourceID,role查询关联表中的数据
     *
     * @param oldTestManagerResourceID
     * @param demandResourceID
     * @param roleName
     * @return DemandUser
     */
    DemandUser findByTestManagerResourceIDAndDemandResourceIDAndRole(Long oldTestManagerResourceID, Long demandResourceID, String roleName);
}
