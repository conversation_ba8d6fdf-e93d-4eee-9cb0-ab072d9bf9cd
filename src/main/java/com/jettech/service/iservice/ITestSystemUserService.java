package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.TestSystemUser;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ITestSystemUserService
 * @projectName jettopro
 * @description: 系统和用户关联关系
 * @date 2019/12/515:09
 */
public interface ITestSystemUserService extends IBaseService<TestSystemUser>{
    /**
     * @Title: findbyTestSystemResourceID
     * @Description: 根据被测系统查询关联关系
     * @Param: "[testSystemResourceID]"
     * @Return: "java.util.List<TestSystemUser>"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    List<TestSystemUser> findbyTestSystemResourceID(String testSystemResourceID);
    
    
    /**
     * @Title: findbyTestSystemResourceID
     * @Description: 根据被测系统查询关联关系
     * @Param: "[testSystemResourceID]"
     * @Return: "java.util.List<TestSystemUser>"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    Boolean addTestSystemModul(String testSystemResourceIDs, Long userresourceID, String userNumber);


    Boolean deleteTestSystemModul(String resourceID,String userNumber);
    /**
     * @Title addTestsystemAndUsers
     * @Description 添加被测系统与人员
     * @Params []
     * @Return boolean
     * <AUTHOR>
     * @Date 2020/1/3
     */
	public boolean addTestsystemAndUsers(List<TestSystemUser> TestSystemUsers,String userNumber);

	/**
	  * @Title: isOperationAuthority
	  * @description: 登录用户是否有操作案例库的权限
	  * @param "[userNumber]"
	  * @return com.jettech.dto.Result
	  * @throws
	  * <AUTHOR>
	  * @date 2020/1/14 11:18
	  */
    Result isOperationAuthority(String userNumber);

    /**
     * 通过系统查询系统下面人员表所有系统人员
     * findbyTestSystemResourceIDsIn
     * @param testSystemResourceIDs
     * @return
     * cao_jinbao
     */
	List<TestSystemUser> findbyTestSystemResourceIDsIn(List<String> testSystemResourceIDs);

	/**
	  * @Title: findByUserNumber
	  * @description: 查询当前登录用户是否和被测系统关联
	  * @param "[nodeType, nodeResourceID, userNumber]"
	  * @return com.jettech.dto.Result
	  * @throws
	  * <AUTHOR>
	  * @date 2020/2/23 14:30
	  */
    Result findByUserNumber(String nodeType, String nodeResourceID, String userNumber);


	/**
	 *
	 * @Title: findNotRelateUser
	 * @Description: 查询未关联的人员
	 * @param name
	 * @param deptName
     * @param testSystemResourceID
     * @param userGroupReourceID
     * @param pageRequest
     * @return
	 * <AUTHOR>
	 * @date 2020年9月9日 下午5:05:58
	 */
	Result<?> findNotRelateUser(String name, String deptName, String testSystemResourceID, String userGroupReourceID, PageRequest pageRequest);

	/**
	 *
	 * @Title: findRelatedUser
	 * @Description: 查询已关联人员
	 * @param name
	 * @param deptName
	 * @param testSystemResourceID
	 * @param userGroupReourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年9月4日 下午5:28:52
	 */
	Result<?> findRelatedUser(String name, String deptName, String testSystemResourceID, String userGroupReourceID, PageRequest pageRequest);

	/**
	 *
	 * @Title: relateUser
	 * @Description: 关联人员
	 * @param userResourceIDs
	 * @param testSystemResourceID
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2020年9月4日 下午5:28:52
	 */
	Result<?> relateUser(String userResourceIDs,String testSystemResourceID,String userNumber);

	/**
	 *
	 * @Title: cancelRelatedUser
	 * @Description: 取消关联人员
	 * @param userResourceIDs
	 * @param testSystemResourceID
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2020年9月4日 下午5:28:52
	 */
	 Result<?> cancelRelatedUser(String userResourceIDs, String testSystemResourceID, String userNumber);

    /**
     *  
     *@Description 多系统同时关联的人员
     *@param 
     *@return Result<?>
     *<AUTHOR>
     *@Date 2022年12月21日
     */
	Result<?> findRelatedUserOfMultipleSystem(String name, String deptName, String testSystemResourceID,
			String userGroupReourceID, PageRequest pageRequest);
   
    /**
     * 
     *@Description  多系统关联人员
     *@param 
     *@return Result<?>
     *<AUTHOR>
     *@Date 2022年12月21日
     */
	//Result<?> multipleSystemRelateUser(String userResourceIDs, String testSystemResourceID, String userNumber);

    /**
     * 
     *@Description 多系统未关联的人员
     *@param 
     *@return Result<?>
     *<AUTHOR>
     *@Date 2022年12月21日
     */
	Result<?> findNotRelatedUserOfMultipleSystem(String name, String deptName, String testSystemResourceID,
			String userGroupReourceID, PageRequest pageRequest);
}
