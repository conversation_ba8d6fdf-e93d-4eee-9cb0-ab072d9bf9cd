package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.SystemModule;
import com.jettech.model.TreeNode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @title: ISystemModuleService
 * @projectName jettopro
 * @description: 系统模块(被测资产管理) 接口
 * @date 2019/11/419:33
 */
public interface ISystemModuleService extends IBaseService<SystemModule>{

    /**
     * @Description 通过被测系统resourceID查询模块
     * <AUTHOR>
     * @date 2019-11-06 14:02
      * @param strings
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    List<Map<String, Object>> findByTestSystemResourceIDs(List<String> strings);
    /**
     * @Title saveorUpdateSystemModule
     * @Description 新增或修改系统模块
     * @Params [request, params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    Result saveorUpdateSystemModule(Map<String, String> params, String userNumber);
    /**
     * @Title verifyModuleNameNotRepeatedOfSystem   /  verifyModuleNameNotRepeatedOfModule
     * @Description 校验父节点（被测系统或者模块）下模块名称唯一
     * @Params [name, nodeResourceID, parentResourceID]
     * @Return String
     * <AUTHOR>
     * @Date 2019/11/5
     */
    Result whetherToRepeat(Map<String, String> params);

    Result verifyModuleSimpleNameNotRepeated(Map<String, String> params);

    /**
      * @Title: findByParentResourceID
      * @description:   父节点查询
      * @param "ParentResourceID"
      * @return java.util.List<SystemModule>
      * @throws
      * <AUTHOR>
      * @date 2019/11/5 19:11
      */
    List<SystemModule> findByParentResourceID(String ParentResourceID);
    /**
     * @Title findbyTestSystemResourceID
     * @Description 查询当前被测系统下所有的模块（包含子模块）
     * @Params [testSystemResourceID]
     * @Return [ List<SystemModule>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    List<SystemModule> findbyTestSystemResourceID(String testSystemResourceID);
    /**
     * @Title deleteSystemModule
     * @Description 删除系统模块
     * @Params [request, resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    Result deleteSystemModuleByResourceID(String resourceID, String userNumber);
    /**
     * @Title checkForAddSonModuleORTrade
     * @Description 校验 模块下新增模块或者交易时校验子节点
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/7
     */
    Result checkSonModuleORTradeForAdd(String type ,String resourceID);
    /**
     * @Title saveorUpdateTestSystem
     * @Description 校验删除时模块下是否维护数据
     * @Params [resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    Result findResourceBySystemModule(String resourceID);
    /**
     * @Description 查询该被测系统下一级的数据
     * <AUTHOR>
     * @date 2019-12-12 20:45
      * @param testSystemResourceID
     * @return java.util.List<java.util.HashMap<java.lang.String,java.lang.Object>>
     */
    List<HashMap<String, Object>> findNextLowerLevelMapByTestSystemResourceID(Long testSystemResourceID);

    /**
     * @Title: findbySystemModuleName
     * @Description:  根据名称查询模块
     * @Param: "[systemModuleName]"
     * @Return: "java.util.List<SystemModule>"
     * @Author: xpp
     * @Date: 2020/2/17
     */
    List<SystemModule> findbySystemModuleName(String systemModuleName);
    /**
     *
    * @Title: findUserUnderModuleTaskTradeCount
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @return    参数
    * @return Map<Long,String>    返回类型
    * @throws
    * <AUTHOR>
     */
	List<Map<Long, String>> findUserUnderModuleTaskTradeCount(String userNumber, String taskResourceID,String Flag);
    /**
     * 查询交易列表系统或者模块下面的交易总数（flag-system系统/flag-module-模块）
    * @Title: findUnderSystemORModuleTradeCount
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param flag
    * @param @return    参数
    * @return List<Map<Long,String>>    返回类型
    * @throws
    * <AUTHOR>
     */
	List<Map<Long, String>> findUnderSystemORModuleTradeCount(String flag);

	/**
	 *
	 * @Title: findByNameAndTestSystemResourceIDAndParentResourceID
	 * @Description: 根据系统和模块名称查询模块
	 * @param name
	 * @param testSystemResourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年5月27日 下午12:00:07
	 */
	SystemModule findByNameAndTestSystemResourceIDAndParentResourceID(String name,Long testSystemResourceID,Long parentResourceID);
    /**
     *
     * @Method : findByDemandResourceID
     * @Description : 通过需求resourceID查询模块树节点数据
     * @param demandResourceID : 需求resourceID
     * @return : java.util.List<com.jettech.model.TreeNode>
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-06-24 周三 14:28:45
     *
     */
    List<TreeNode> findByDemandResourceID(String demandResourceID);
    /**
     * 根据被测系统查询个各层级模块，平拼写成List返回(如：模块1/模块11/模块111)
     * @param request
     * <AUTHOR>
     * @Date 2020/7/14
     * @return
     */
	Result findModuleLevelBySystemRid(String systemResourceID);

    /**
     * 根据ID查询当前模块以及子模块
     * @param moduleResID
     * @return
     */
	List<SystemModule> findModuleAndSubModelByResId(String moduleResID);
    /**
     * 根据模块的rid获取上级和上级所有直接模块的层级关系
     * @param moduleResourceID
     * @return
     */
    String findParentModuleNamesByModuleRid(Long moduleResourceID);

	/**
	 * <AUTHOR>
	 * @description 确认删除系统模块
	 * @date 2020年11月25日 15:03
	 * @param [resourceID, userNumber]
	 * @return com.jettech.dto.Result
	 **/
    Result confirmDeleteSystemModule(String resourceID, String userNumber);

    /**
     * 根据所属系统查询交易
     * @param request
     * @Date 2022/05/13
     * @return
     */
	Result findTradeBysystemResourceID(String systemResourceID);

	List<SystemModule> findByTestSystemResIdIn(List<String> systemResIdList);
	/**
	 * 
	 *@Description 批量删除模块
	 *@param 
	 *@return Result
	 *<AUTHOR>
	 *@Date 2023年2月8日
	 */
	Result<?> moreModuleDelete(String moduleResourceID, String testSytemResourceID,String userNumber);
}
