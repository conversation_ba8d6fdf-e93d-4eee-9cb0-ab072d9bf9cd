package com.jettech.service.iservice;

import com.jettech.common.util.UserVo;
import com.jettech.model.DataBackupHistory;

import java.util.List;
import java.util.Map;

public interface IDataBackupHistoryService extends IBaseService<DataBackupHistory> {

    List<DataBackupHistory> findUnBackup();

    List<DataBackupHistory> findByParams(Map<String,Object> params);

    int del(String ids, UserVo user);
}
