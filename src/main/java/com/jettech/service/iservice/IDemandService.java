package com.jettech.service.iservice;

import com.jettech.DTO.DemandItemDto;
import com.jettech.common.dto.Result;
import com.jettech.common.util.UserVo;
import com.jettech.model.Demand;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @ClassName IDemandService
 * @Description 需求service
 * <AUTHOR>
 * @date 2019年12月3日下午8:21:33
 */
public interface IDemandService extends IBaseService<Demand> {

	// 查询需求
	public Page<Map<String, Object>> findDemandPage(PageRequest pageRequest, String name, String number,
                                                     String typeName, String testProjectResourceID, String testSystemResourceID, String projectManagerName, String testManagerName,String level);

	List<Map<String, Object>> findNotRelevanceUser(String id);

	/**
	 *
	 * @Title: findRelevanceUser
	 * @Description: 查询已关联用户
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日 下午9:28:38
	 */
	List<Map<String, Object>> findRelevanceUser(String id);

	/**
	 *
	 * @Title: addDemandUser
	 * @Description: 给需求关联人员
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	String addDemandUser(String demandId, String ids, String role, String userNumber) throws Exception;

	/**
	 *
	 * @Title: addDemandSystem
	 * @Description: 给需求关联系统
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	String addDemandSystem(List<String> testSystemResourceIDs, String demandId, String ids, String userNumber);

	/**
	 *
	 * @Title: removeDemandUser
	 * @Description: 删除关联人员
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @param duResourceIDs
	 * @date 2019年12月04日
	 */
	Result removeDemandUser(String demandId, String rIds, String userNumber, String duResourceIDs);

	/**
	 *
	 * @Title: findNotRelevanceSystem
	 * @Description: 查询已关联系统
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	List<Map<String, Object>> findRelevanceSystem(String resourceID);

	/**
	 *
	 * @Title: findRelevanceSystem
	 * @Description: 查询所有系统
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	List<Map<String, Object>> findAllSystem(String name, String resourceID);

	Result<?> addDemand(Map<String, Object> param,MultipartFile[] files) throws IOException;

	Result<?> updateDemand(Map<String, Object> param, MultipartFile[] files) throws IOException;

	Result delectDemand(String delectDemandResourceID, UserVo userVo);

	/**
	 *
	 * @Title: initWorkBenchMyDemand
	 * @Description: 初始化工作空间我的需求
	 * @return : Page<Map<String,Object>> 返回类型
	 * <AUTHOR>
	 * @date 2019年12月5日下午4:15:06
	 */
	Page<Map<String, Object>> initWorkBenchMyDemand(PageRequest pageRequest, Map<String, String> params);

	/**
	 * 
	 * @Title: initWorkbenchAlreadyMyDemand
	 * @Description: 查询我的已办需求列表
	 * @param pageRequest
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月23日 下午3:41:26
	 */
	Page<Map<String, Object>> initWorkbenchAlreadyMyDemand(PageRequest pageRequest, Map<String, String> params);

	/**
	 * 
	 * @Title: initWorkbenchCreateMyDemand
	 * @Description: 查询我创建的需求列表
	 * @param pageRequest
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月23日 下午4:12:13
	 */
	Page<Map<String, Object>> initWorkbenchCreateMyDemand(PageRequest pageRequest, Map<String, String> params);

	List<String> importExcel(MultipartFile files, UserVo userVo);

	/**
	 * @Title removeUserFromDemand
	 * @Description 从需求中批量移除人员
	 * @param demandResIDs
	 * @param user
	 * <AUTHOR>
	 *
	 */
	String removeUserFromDemand(String demandResIDs, UserVo user);

	/**
	 * @Description 查询未上线的需求,缺陷管理新增缺陷使用
	 * <AUTHOR>
	 * @date 2019-12-12 20:09
	 * @param
	 * @return com.jettech.dto.Result<?>
	 */
	Result<?> queryNotOnLineDemand();

	/**
	 * @Title: createFives
	 * @description: 5000条缺陷
	 * @param "[userResourceID]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2019/12/25 11:51
	 */
	Result createFives(String userResourceID);

	/**
	 * @Title findDemandRelevanceUser
	 * @Description 根据需求主键查询已关联的人员
	 * @param demandResourceID
	 * @return List<Map<String,Object>>
	 * <AUTHOR>
	 * @data Jan 8, 20203:50:33 PM
	 */
	List<Map<String, Object>> findDemandRelevanceUser(String demandResourceID);

	/**
	 *
	 * @Title: findNotRelevanceSystem
	 * @Description: 查询未关联系统
	 * @param request
	 * @return
	 */
	List<Map<String, Object>> getNotRelevanceSystem(String name,String resourceID);

	/**
	 *
	 * @Title: removeDemandSystem
	 * @Description: 移除需求关联系统
	 * @param request
	 * @return
	 */
	String removeDemandSystem(String demandResourceID, String ids, String user);

	/**
	 * QiaoHongju
	 * 
	 * @param demand
	 * @param userNumber
	 */
	void updateByResourceID(Demand demand, String userNumber);

	/**
	 * 更新带空值
	 */
	void updateDemand(Demand demand);

	/**
	 * 查找所有用户
	 * 
	 * @return cao_jinbao
	 */
	Result<?> findAllUsersForDemand(String demandResourceID);

	/**
	 * @Title: testPlanRotationPie
	 * @description: 报表需求中测试计划轮次占比图(sit,uat)
	 * @param "[demandResourceID, testEnviroment]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2020/2/10 17:28
	 */
	Result testPlanRotationPie(String demandResourceID, String testEnviroment);

	/**
	 * @Description:查询测试风险
	 * <AUTHOR>
	 * @date 2020/2/11
	 * @param demandResourceID:
	 * @return com.jettech.dto.Result
	 */
	Result testVenture(String demandResourceID);

	List<Map<String, String>> findDemandIDs(String ids);

	/**
	 * 需求导入
	 * 
	 * @Method importExcel
	 * @Descripation 导入需求Excel
	 * @param request
	 * <AUTHOR>
	 * @return
	 */
	Result<?> importDemandExcel(MultipartFile files, UserVo createUser);

	/**
	 * 更新需求的状态（需求状态：未开始、准备中、测试中、已完成、特殊状态（部分上线、已上线、已暂停、已取消））
	 * 
	 * @Title: changeDemandType
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param demandResourceID 参数
	 * @return void 返回类型
	 * @throws <AUTHOR>
	 */
	String changeDemandType(Long demandResourceID, String userNumber);

	/**
	 * @Title findDemandsNotOnLine
	 * @Description 查询非已上线的需求
	 * @Params []
	 * @Return java.util.List<Demand>
	 * <AUTHOR>
	 * @Date 2020/3/6
	 */
	List<Demand> findDemandsNotOnLine();

	/**
	 * 
	 * @Title: findByTestProjectResourceID
	 * @Description: 根据所属项目查询需求
	 * @param testProjectResourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午10:03:32
	 */
	List<Demand> findByTestProjectResourceID(Long testProjectResourceID);

	/**
	 * 根据项目ResourceIDs查询符合需求
	 * bao_qiuxu
	 */
	List<Demand>  findByTestProjectResourceIDs(List<String> ids);
	/**
	 * 
	 * @Title: initMyDemandToDealWith
	 * @Description: 工作台待我处理需求列表（当前用户加入当前需求就显示）
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年6月15日
	 */
	Page<Map<String, Object>> initMyDemandToDealWith(PageRequest pageRequest, Map<String, String> params);

	/**
	 * @Title: initTaskScopeLeftTree
	 * @Description: //根据需求，初始化添加任务范围弹框左侧树
	 * @Param: " [demandResourceID] "
	 * @return: " com.jettech.dto.Result "
	 * @throws:
	 * @Author: xpp
	 * @Date: 14:24 2020/6/10
	 */
	Result initTaskScopeLeftTree(String demandResourceID);


    /**
      * @Title: findAllDemandToTestTask
      * @description: 查询所有需求
      * @param "[name]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2020/6/23 15:10
      */
	Result findAllDemandToTestTask(String name);
	/**
	 * 
	 * @Title: initMyDemandToDealWith
	 * @Description: 工作台待我处理需求列表（当前用户加入当前需求就显示）的需求个数
	 * <AUTHOR>
	 * @date 2020年6月15日
	 */
	int initMyDemandNumberToDealWith(Map<String, String> params);
	/**
	 * @Title: initWorkbenchCreateMyDemandNumber 
	 * @Description: 查询我创建的需求数
	 * @date 2020年4月23日 下午4:11:37
	 */
	int initWorkbenchCreateMyDemandNumber(Map<String, String> params);

	/**
	 * 
	 * @Title: findByTestProjectResourceIDIn 
	 * @Description: 根据所属项目查询需求
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 上午9:58:54
	 */
	public List<Demand> findByTestProjectResourceIDIn(List<Long> resourceIDList);


	/***
	 * 弹窗中需求列表
	 * @Method : initDemandTable
	 * @Description : 弹窗中需求列表
	 * @param resourceID : 项目rid
	 * @return : com.jettech.dto.Result
	 * <AUTHOR> Wws.
	 * @CreateDate : 2020-07-15 星期三 10:40:05
	 *
	 */
	Result initDemandTable(HashMap<String, Object> map);
	/**
	 * 需求状态刷新（只更新---未开始、准备中、测试中、已完成---四种状态的需求）
	 * @param userNumber
	 * @return
	 * <AUTHOR> && bao_qiuxu
	 */
	String refreshDemandType(String userNumber,String resourceID);

    Result<?> uploadDemandAccessory(Map<String, Object> param, MultipartFile[] files) throws IOException;

    Result<?> deleteDemandAccessory(String resourceIDs, String userNumbe);


	/**
	 * 根据需求编号或需求名称 以及项目id模糊查询需求
	 * @param condition
	 * @param projectIDs
	 * @param projectTypeStr
	 * @param demandTypes
	 * @return
	 */
    List<DemandItemDto> findDemandItemByCondition(String condition, List<Long> projectIDs, String projectTypeStr, String demandTypes);

	/**
	 * 查找需求管理下未关联的所有用户
	 * @param name
	 * @param testSystemResourceID
	 * @param demandResourceID
	 * @param userGroupReourceID
	 * @param pageRequest
	 * @return
	 * by zhangsheng
	 */
	Result<?> findDemandNotRelevanceUser(String name, String testSystemResourceID, String demandResourceID, String userGroupReourceID,String deptResourceID, PageRequest pageRequest);

	/**
	 * 查找需求管理下已关联的所有用户
	 * @param name
	 * @param testSystemResourceID
	 * @param demandResourceID
	 * @param userGroupReourceID
	 * @param pageRequest
	 * @return
	 * by zhangsheng
	 */
	Result<?> findDemandRelevanceUser(String name, String testSystemResourceID, String demandResourceID, String userGroupReourceID,String deptResourceID, PageRequest pageRequest);

	/**
	 * @Method: findByDemandNames
	 * @Description: 通过需求名称查询
	 * @Param: " [demandNames] "
	 * @return: java.util.List<com.jettech.model.Demand>
	 * @Author: wws
	 * @Date: 2020/11/9
	 */
    List<Demand> findByDemandNames(ArrayList<String> demandNames);

    /**
     * @Method: demandProjectDataDeal
     * @Description:  通过excel中的数据更新需求和项目的关联关系，更新缺陷数据
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/11/11
     */
    Result demandProjectDataDeal() throws IOException;

    /**
     * <AUTHOR>
     * @description 确认删除需求
     * @date 2020年11月25日 10:59
     * @param [demandResourceIDs, createUser]
     * @return com.jettech.dto.Result<?>
     **/
    Result<?> confirmDelectDemand(String demandResourceIDs, UserVo createUser);
	/**
	 * 根据项目的业务主键查询项目关联的所有需求
	 * <AUTHOR>
	 *    10:18
	 * @update
	 * @param [request]     [request]说明
	 * @return  com.jettech.dto.Result
	 * @exception/throws [异常类型] [异常说明]
	 * @see   [类、类#方法、类#成员]
	 * @since [起始版本]
	 */
    List<Map<String, Object>> findDemandsByTestProjectResourceID(String projectResourceID);
	/**
	 * 根据需求的业务主键查询需求的所属项目
	 * <AUTHOR>
	 *    10:18
	 * @update
	 * @param [request]     [request]说明
	 * @return  com.jettech.dto.Result
	 * @exception/throws [异常类型] [异常说明]
	 * @see   [类、类#方法、类#成员]
	 * @since [起始版本]
	 */
	List<Map<String, Object>> findProjectBydemandResourceID(String demandResourceID,String userNumber);

	/**
	  * 富滇新增需求
	  *
	  * <AUTHOR>
	  *    上午11:56
	  * @update
	  * @param paramList
	  * @param userVo
	  * @return  com.jettech.dto.Result<?>
	  * @exception/throws [异常类型] [异常说明]
	  * @see   [类、类#方法、类#成员]
	  * @since [起始版本]
	  */
	Result<?> createOrUpdateDemand(List<Map<String,String>> paramList, UserVo userVo);

	String moreDemandAddDemandUser(String demandId, String ids, String role, String userNumber) throws Exception;
}
