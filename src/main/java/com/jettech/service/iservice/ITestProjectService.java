package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.TestProject;
import com.jettech.view.TestProjectView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

public interface ITestProjectService extends BaseHBService<TestProject>{

	/**
	 *
	 * @Title: findTestProject
	 * @Description: 分页查询项目
	 * @param pageRequest
	 * @param name
	 * @param status
	 * @param number
     * @param projectType
     * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 上午11:52:01
	 */
	public Page<TestProject> findTestProject(PageRequest pageRequest, String name, String status, String number, String projectType,String testMode);

	/**
	 *
	 * @Title: validateNumber
	 * @Description:验证项目编号是否重复
	 * @param number
	 * @param resourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午12:44:27
	 */
	public Result<?> validateNumber(String number,String resourceID);

	/**
	 *
	 * @Title: findByNumber
	 * @Description: 根据项目编号查询项目
	 * @param number
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午12:56:03
	 */
	public TestProject findByNumber(String number);

	/**
	 *
	 * @Title: addAndUpdateTestProject
	 * @Description: 新增项目
	 * @param resourceID
	 * @param name
	 * @param number
	 * @param status
	 * @param statusName
	 * @param startDate
	 * @param endDate
	 * @param managerResourceID
	 * @param managerName
	 * @param describeInfo
	 * @param fileMap
	 * @param userNumber
	 * @param projectType
     * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午1:12:15
	 */
	public Result<?> addAndUpdateTestProject(String parentResourceID, String resourceID, String name, String number,
											 String status, String statusName, String startDate, String endDate,
											 String managerResourceID, String managerName, String describeInfo,
											 Map<String, InputStream> fileMap, String userNumber, String projectType,
											 String testMode, MultipartFile[] files);

	/**
	 *
	 * @Title: deleteTestProject
	 * @Description: 批量删除项目
	 * @param resourceIDs
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午1:50:34
	 */
	public Result<?> deleteTestProject(String resourceIDs,String userNumber);

	/**
	 *
	 * @Title: validateDeleteTestProject
	 * @Description: 删除项目前验证下是否关联用户和需求
	 * @param resourceIDs
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午2:08:46
	 */
	public Result<?> validateDeleteTestProject(String resourceIDs);

	/**
	 *
	 * @Title: findAllUser
	 * @Description: 查询所有人员
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午4:23:34
	 */
	public Result<?> findAllUser();

	/**
	 *
	 * @Title: findAttachmentList
	 * @Description: 获取附件列表
	 * @param resourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午7:19:39
	 */
	public Result<?> findAttachmentList(String resourceID);

	/**
	 *
	 * @Title: deleteAttachmentList
	 * @Description: 删除附件
	 * @param resourceID
	 * @param fileName
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午8:04:45
	 */
	public Result<?> deleteAttachmentList(String resourceID,String fileName);

	/**
	 *
	 * @Title: getFileData
	 * @Description: 获取附件
	 * @param resourceID
	 * @param fileName
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午8:18:35
	 */
	public byte[] getFileData(String resourceID,String fileName);

	/**
	 *
	 * @Title: previewAttachment
	 * @Description: 预览图片
	 * @param resourceID
	 * @param fileName
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午8:41:29
	 */
	public Result<?> previewAttachment(String resourceID,String fileName);

	/**
	 *
	 * @Title: findAllTestProjectNameAndResourceID
	 * @Description: 查询所有项目name和resourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月22日 下午3:38:12
	 */
	public Result<?> findAllTestProjectNameAndResourceID();

	/**
	 *
	 * @Title: findNoCloseTestProjectNameAndResourceID
	 * @Description: 查询非关闭项目name和resourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年12月29日 下午3:38:12
	 */
	public Result<?> findNoCloseTestProjectNameAndResourceID();

	/**
	 *
	 * @Title: findByName
	 * @Description: 根据项目名称查询项目
	 * @param name
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月22日 下午8:17:52
	 */
	public List<TestProject> findByName(String name);

	/**
	 * 根据分页数据查询项目下需求的进度
	 * bao_qiuxu
	 */
	Map<String, Integer> selectProjectProgress(List<TestProject> tpList);
	/**
	 * findDemandByTestProjectResourceID
	 * 根据ResourceID查询所有需求及需求的案例和缺陷
	 * bao_qiuxu
	 */
	Result<?> findDemandByTestProjectResourceID(String resourceID);
	/**
	 *
	 * @Title: findAllTestProjectByUser
	 * @Description: 查询工作台当前用户所有项目name和resourceID
	 * @param request
	 * @return List<Map<String, Object>>
	 * <AUTHOR>
	 * @date 2020年6月19日
	 */
	public Result<List<Map<String, Object>>> findAllTestProjectByUser(String userNumber);

	/**
	 *
	 * @Title: findAllParent
	 * @Description: 查询所有父项目
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月13日 下午5:07:55
	 */
	public  List<TestProject> findAllParent();

	/**
	 *
	 * @Title: findChildTestProjectByResourceID
	 * @Description: 查询子项目
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月13日 下午5:35:09
	 */
	public List<TestProject> findChildTestProjectByResourceID(String resourceID);


	/**
	  * @param name
	 * @Title: loadParentDemand
	  * @description: 加载所有父级节点的项目
	  * @param "[]"
	  * @return com.jettech.dto.Result
	  * @throws
	  * <AUTHOR>
	  * @date 2020/7/13 17:06
	  */
    Result loadParentTestProject(String name);

    /**
      * @Title: getChildrenTestProject
      * @description: 父节点查询所属项目
      * @param "[resourceID]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2020/7/13 17:37
      */
    Result getChildrenTestProject(String resourceID);

    /**
     *
     * @Title: findProjectAndGroupTree
     * @Description: 查询项目和项目组树
     * @param userNumber
     * @return
     * <AUTHOR>
     * @date 2020年7月14日 上午11:04:38
     */
    Result findProjectAndGroupTree(String userNumber,String groupType);

	/**
	 *  查询项目是否有子项目
	 * @param resourceIDs
	 * @return
	 */
	List<TestProject> findParentbyResourceIDIn(List<String> resourceIDs);

	/**
	 *
	 * @Title: addTestProjectValidate
	 * @Description: 新增项目前校验
	 * @param resourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月20日 上午10:44:32
	 */
	public Result<String> addTestProjectValidate(Long resourceID);

	/**
	 * @Method: initTestProjectTree
	 * @Description: 加载项目树
	 * @Param: " [] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/7/20
	 */
    Result initTestProjectTree();

	/**
	 * @Method: initProjectTree
	 * @Description: 缺陷流程配置左侧树初始化
	 * @Param: " [name] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/8/17
	 */
    Result initProjectTree(String name);
    /**
	 *
	 * @Title: findProjectTree
	 * @Description: 查询项目树
	 * <AUTHOR>
	 * @date 2020年8月21日
	 */
    List<Map<String, Object>> findProjectTree(String userNumber, String groupType);

	/**
	 * 获取要查询的子级节点项目组
	 * @param nodeType
	 * @param resourceID
	 * @param groupType
	 */
	List<String> getNodeList(String nodeType,String resourceID,String groupType,String userNumber);

	Result findTestProjectAndChildren(String userNumber, String name, String status, PageRequest pageRequest, String number, String projectType,
									  String testMode);

	/**
	 * 根据条件查询所有项目
	 *
	 * @param name
	 * @param status
	 * @param number
	 * @param projectType
	 * @return
	 */
	List<TestProject> findTestProjectByCondition( String name, String status, String number, String projectType, String testMode);

	/**
	 * @Method: findParentProjectByProjectResourceID
	 * @Description: 查询父节点项目
	 * @Param: " [projectResourceID] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/10/26
	 */
    Result findParentProjectByProjectResourceID(String projectResourceID);

    /**
     * @Method: findChildByParentRid
     * @Description: 根节点查询子项目
     * @Param: " [parentProjectResourceID] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/10/27
     */
    Result findChildByParentRid(String parentProjectResourceID);

    List<String> findEndStateBytestProjectResourceID(String testProjectResourceID);

    /**
     * <AUTHOR>
     * @description 确认删除项目
     * @date 2020年11月25日 9:51
     * @param [resourceIDs, zhangsheng]
     * @return com.jettech.dto.Result<?>
     **/
	Result<?> confirmDeleteTestProject(String resourceIDs, String userNumber);


	/**
	 *
	 * @Title: createTestProject
	 * @Description: 富滇新增项目
	 * @param testProjectView
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2021年1月15日 下午1:12:15
	 */
	public Result<?> createTestProject(TestProjectView testProjectView, String userNumber);

	/**
	 *
	 * @Title: updateTestProject
	 * @Description: 富滇修改项目
	 * @param testProjectView
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2021年1月15日 下午1:12:15
	 */
	public Result<?> updateTestProject(TestProjectView testProjectView, String userNumber);

	List<TestProject> findByUserNumber(String userNumber);

	List<TestProject> findAllByDataAuth();

	Map<String, Object> findProjectSelectDataByDataAuth(Map<String, String> params);

	Result findAllProjectTree();

	List<Map<String,Object>> findTradeFlowCaseFolder(Long testProjectResourceId);

	public List<Map<String, Object>> findProjectTreeOfreport(String userNumber, String groupType);
	
	public Result<?> validateName(String name,String resourceID);
}
