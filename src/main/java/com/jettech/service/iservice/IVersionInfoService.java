package com.jettech.service.iservice;

import com.github.pagehelper.PageInfo;
import com.jettech.DTO.VersionInfoDto;
import com.jettech.common.util.UserVo;
import com.jettech.model.VersionInfo;
import com.jettech.view.VersionInfoView;

/**
 * <AUTHOR>
 * @Date 2021/5/20
 **/
public interface IVersionInfoService extends BaseHBService<VersionInfo> {

    PageInfo<VersionInfoView> page(VersionInfoDto dto);

    VersionInfo submit(VersionInfo versionInfo, UserVo user);

    int del(String ids, UserVo user);
}
