package com.jettech.service.iservice;

import com.jettech.DTO.TaskTradeCaseDto;
import com.jettech.common.dto.Result;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: IExcelService
 * @projectName jettopro
 * @description: excel导入导出
 * @date 2019/11/411:52
 */
public interface IExcelService {

    /**
      * @Title: importExcel
      * @description: 导出excel（支持批量）
      * @param  [Map]
      * @return com.jettech.dto.Result<?>
      * @throws
      * <AUTHOR>
      * @date 2019/11/4 15:19
      */
    Result<?> exportExcel(Map map);

    /**
      * @Title: downloadModel
      * @description: 下载模板
      * @param "[response]"
      * @return void
      * @throws
      * <AUTHOR>
      * @date 2019/11/7 14:55
      */
    void downloadModel(String agent,HttpServletResponse response);

    Result showExportFields();


    /**
     * @Title: getUrlMap
     * @description: 导出数据处理
     * @param "[map]"
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @throws
     * <AUTHOR>
     * @date 2019/12/3 20:02
     */
    Map<String,Object> getUrlMap(Map map);


    /**
     * @Title importExcelNew
     * @description 导入案例（需求变更）
     * @date 20200525
     * <AUTHOR>
     */
	Result<?> importExcel(MultipartFile file, String nodeType, String nodeResourceID,
                          String userNumber, String taskResourceID);

	/**
	 * @Title  projectCaseExportExcel
	 * @Description 项目案例库导出
	 * @author: slq
	 * @date:   2020年7月15日 上午11:23:23
	 */
	Result<?> projectCaseExport(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response);
	/**
     * @Title  querySheetNames
     * @Description 查询导入文件的sheet页名称
     * @author: slq
     * @date:   2020年7月15日 下午4:16:29
     */
	Result<?> querySheetNames(MultipartFile file);
	/**
	 * @Title  projectCaseImport
	 * @Description 项目案例库导入
	 * @author: slq
	 * @date:   2020年7月15日 上午11:16:22
	 */
	Result<?> projectCaseImport(MultipartFile file, String projectgroupResourceID, String sheetName, String userNumber);

	/**
	 * <AUTHOR>
	 * @description 导入案例库
	 * @date 2020年11月03日 17:26
	 * @param [file, projectgroupResourceID, sheetNames, userNumber]
	 * @return com.jettech.dto.Result<?>
	 **/
	Result<?> importProjectCase(MultipartFile file, String projectgroupResourceID, String projectResourceID,String isBuildGroup,String sheetNames, String userNumber);

	/**
	 * @Title: importIdentifyCaseID
	 * @description: excel导入(当有案例编号重复时提示)
	 * @param  "[file, request]"
	 * @return com.jettech.dto.Result<?>
	 * @throws
	 * <AUTHOR>
	 * @date 2021-06-11
	 */
	Result<?> importIdentifyCaseID(MultipartFile file, String nodeType, String nodeResourceID,
								   String userNumber, String taskResourceID, Boolean committed);


	Result<?> projectAllCaseExport(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response);

	Result<?> importShouzi(MultipartFile file, String nodeType, String nodeResourceID, String userNumber,
			String taskResourceID);

	Result<?> exportExcelByDemandCase(TaskTradeCaseDto taskTradeCaseDto, HttpServletRequest request, HttpServletResponse response);

	Result downProjectCaseTemp(@RequestBody Map<String, Object> map, HttpServletRequest request, HttpServletResponse response);
}
