package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: IExcelService
 * @projectName jettopro
 * @description: excel导入导出
 * @date 2019/11/411:52
 */
public interface IExcelService {
    /** 
      * @Title: importExcel
      * @description: 导入excel（模板文件，不支持批量操作）
      * @param  [file, userNumber]
      * @param nodeType
     * @param nodeResourceID
     * @return com.jettech.dto.Result<?>
      * @throws
      * <AUTHOR>
      * @date 2019/11/4 11:56 
      */
    Result<?> importExcel(MultipartFile file, String nodeType, String nodeResourceID,  String userNumber,String testEnviroment) throws Exception;

    /** 
      * @Title: importExcel
      * @description: 导出excel（支持批量）
      * @param  [Map]
      * @return com.jettech.dto.Result<?>
      * @throws
      * <AUTHOR>
      * @date 2019/11/4 15:19 
      */
    Result<?> exportExcel(Map map);

    /** 
      * @Title: downloadModel
      * @description: 下载模板
      * @param "[response]"
      * @return void
      * @throws
      * <AUTHOR>
      * @date 2019/11/7 14:55 
      */
    void downloadModel(String agent,HttpServletResponse response);
    
    /** 
     * @Title importExcelNew
     * @description 导入案例（需求变更）
     * @date 20200525
     * <AUTHOR>
     */
	Result<?> importExcelNew(MultipartFile file, String nodeType, String nodeResourceID, String userNumber,
                             String testEnviroment);
}
