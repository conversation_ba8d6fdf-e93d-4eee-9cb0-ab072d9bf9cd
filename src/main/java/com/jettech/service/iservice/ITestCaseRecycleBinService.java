package com.jettech.service.iservice;

import com.jettech.common.dto.Result;
import com.jettech.model.TestCaseRecycleBin;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ITestCaseRecycleBinService
 * @description 案例编写（回收站表）Service层
 * <AUTHOR>
 * @create 2020-05-19
 */
public interface ITestCaseRecycleBinService extends BaseHBService<TestCaseRecycleBin> {
	/**
	 * 
	 * @Title: findLeftTreeByTestTaskResourceID
	 * @Description: 根据任务的rid查询该任务维护的系统模块交易范围，展示为左侧树
	 * @param params
	 * @param 参数
	 * @return Result<?> 返回类型
	 * @throws <AUTHOR>
	 * 
	 */
	Result<?> findLeftTreeByTestTaskResourceID(Map<String, String> params);

	/**
	 * @param userNumber
	 * @Title: findTestCaseRecycleBinPage
	 * @Description: 根据左侧树展示右侧回收站案例列表
	 * @Return: Page<TestCaseRecycleBin>
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	Page<TestCaseRecycleBin> findTestCaseRecycleBinPage(PageRequest pageRequest, String taskResourceID,
			String testsystemResourceID,String systemmoduleResourceID,String tradeResourceID, String userNumber);

	/**
	 * 
	 * @Title: restoreTestCaseData
	 * @Description: 还原回收站案例数据
	 * @param restoreTestCaseResIDs
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @param userNumber
	 * @param userVo
	 * @Date: 20200520
	 */
	Result<?> restoreTestCaseData(List<String> restoreTestCaseResIDs, String userNumber);

	/**
	 * 
	 * @Title: deleteTestCaseRecycleBins
	 * @Description: 删除回收站案例数据
	 * @param restoreTestCaseResIDs
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @Date: 20200520
	 */
	Result<?> deleteTestCaseRecycleBins(List<String> restoreTestCaseResIDs, String userNumber);

	/**
	 * 获取早于某创建时间的数据
	 * 
	 * @param createTime
	 */
	List<TestCaseRecycleBin> findByLECreateTime(Date createTime);
	
	/**
	 * @Title: findMaxTestCasebyTradeResourceID
	 * @Description: 根据交易查询当前交易下案例编号的最大值
	 * @Param: "[resourceID]"
	 * @Return: "TestCase"
	 * @Author: xpp
	 * @Date: 2019/12/24
	 */
	TestCaseRecycleBin findMaxTestCasebyTradeResourceID(Long resourceID);
	/**
	 * @Title: findByTradeResourceID
	 * @Description: 根据TradeResourceID查询回收站案例
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	List<TestCaseRecycleBin> findByTradeResourceID(Long tradeResourceID);
	/**
	 * @Title: findByTradeResourceID
	 * @Description: 根据TradeResourceID查询回收站案例CaseID
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	List<String> findCaseIDByTradeResourceID(Long tradeResourceID);
 
	List<TestCaseRecycleBin> findCaseByTradeResourceIDList(List<Long> mlist);
}
