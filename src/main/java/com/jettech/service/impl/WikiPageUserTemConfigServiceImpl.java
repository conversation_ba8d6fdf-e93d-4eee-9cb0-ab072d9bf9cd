package com.jettech.service.impl;

import com.jettech.dao.idao.IWikiPageUserTemConfigDao;
import com.jettech.model.WikiPage;
import com.jettech.model.WikiPageUserTemConfig;
import com.jettech.service.iservice.IWikiPageUserTemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

@Service
public class WikiPageUserTemConfigServiceImpl extends BaseServiceImpl<WikiPageUserTemConfig> implements IWikiPageUserTemConfigService {

    @Autowired
    private IWikiPageUserTemConfigDao wikiPageUserTemConfigDao;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = wikiPageUserTemConfigDao;
    }

    @Override
    public int deleteByParam(Map<String, Object> map) {
        return wikiPageUserTemConfigDao.deleteByParam(map);
    }

    @Override
    public List<WikiPage> listPageUserByParam(WikiPageUserTemConfig config) {
        return wikiPageUserTemConfigDao.listPageUserByParam(config);
    }
}
