package com.jettech.service.impl;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.jettech.common.dto.basic.FieldConfigurationDTO;
import com.jettech.common.dto.trp.DataDictionaryDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 导出案例 Excel单元格写入拦截器
 * 用于设置单元格样式及数格式
 * Created by WZJ
 * Time       2020/9/16.
 */
public class TradeExcelWriteCellHandler implements CellWriteHandler {

    private List<String> selectedFields;
    private List<FieldConfigurationDTO> fieldDefinitionList;

    private CellStyle requiredStyle;
    private CellStyle style;
    private List<String> dicKeys;

    public TradeExcelWriteCellHandler(List<String> selectedFields, List<FieldConfigurationDTO> fieldDefinitionList, List<String> dicKeys) {
        this.selectedFields = selectedFields;
        this.fieldDefinitionList = fieldDefinitionList;
        this.dicKeys = dicKeys;
    }


    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer integer, Integer integer1, Boolean aBoolean) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

    }

    /**
     * 获取必填项表头单元格样式
     *
     * @param cell
     * @return
     */
    private CellStyle getRequiredStyle(Cell cell) {
        if (requiredStyle == null) {
            Workbook wb = cell.getSheet().getWorkbook();
            requiredStyle = wb.createCellStyle();
            requiredStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            requiredStyle.setFillForegroundColor(IndexedColors.RED.getIndex());// 设置背景色
            requiredStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            requiredStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            requiredStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            requiredStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            requiredStyle.setAlignment(HorizontalAlignment.CENTER);
        }
        return requiredStyle;
    }

    /**
     * 获取非必填项表头单元格样式
     *
     * @param cell
     * @return
     */
    private CellStyle getNormalStyle(Cell cell) {
        if (style == null) {
            Workbook wb = cell.getSheet().getWorkbook();
            style = wb.createCellStyle();
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());// 设置背景色
            style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            style.setTopBorderColor(IndexedColors.BLACK.getIndex());
            style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            style.setRightBorderColor(IndexedColors.BLACK.getIndex());
            style.setAlignment(HorizontalAlignment.CENTER);
        }
        return style;
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        String name = selectedFields.get(cell.getColumnIndex());
        FieldConfigurationDTO field = fieldDefinitionList.stream()
                .filter(x -> Objects.equals(x.getAliasName(), name)).findFirst().orElse(null);

        if (isHead && cell.getColumnIndex() < selectedFields.size()) {
            cell.getSheet().setColumnWidth(cell.getColumnIndex(), 100 * 35);
            //设置表头样式
            boolean required = field != null && field.isWriteRequired();

            if (required || "所属系统".equals(name) || "所属交易".equals(name)) {
                cell.setCellStyle(getRequiredStyle(cell));
            } else {
                cell.setCellStyle(getNormalStyle(cell));
            }
            boolean isDateFormat = field != null && "5".equals(field.getFieldType());
            if (isDateFormat) {
                //TODO 设置单元格日期格式
            }

        }
        if (field == null) {
            return;
        }
        //设置单元格格式 以及替换单元格内容
        if ("2".equals(field.getFieldType()) || "3".equals(field.getFieldType())) {
            if (isHead) {
                int keyIndex = dicKeys.indexOf(name);
                String colNum = String.valueOf((char) (keyIndex + 65));
                int rowCount = field.getDictionary() == null ? 0 : field.getDictionary().size();
                if (rowCount > 0) {
                    CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 1000, cell.getColumnIndex(), cell.getColumnIndex());
                    DataValidationHelper helper = writeSheetHolder.getSheet().getDataValidationHelper();
                    String dicValueFormula = String.format("=Dictionary!$%s$2:$%s$%s", colNum, colNum, rowCount + 1);
                    DataValidationConstraint constraint = helper.createFormulaListConstraint(dicValueFormula);
                    DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
                    dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                    if (dataValidation instanceof XSSFDataValidation) {
                        dataValidation.setSuppressDropDownArrow(true);
                        dataValidation.setShowErrorBox(true);
                    } else {
                        dataValidation.setSuppressDropDownArrow(false);
                    }
                    writeSheetHolder.getSheet().addValidationData(dataValidation);
                }
            } else {
                String value = cell.getStringCellValue();
                if (StringUtils.isNotBlank(value)) {
                    List<String> valueList = Arrays.asList(value.split(","));
                    List<String> dicValueList = new ArrayList<>();
                    valueList.forEach(v -> {
                        DataDictionaryDTO dic = field.getDictionary().stream().filter(x -> Objects.equals(x.getValue(), v)).findFirst().orElse(null);
                        if (dic != null) {
                            dicValueList.add(dic.getTextName()) ;
                        }
                    });
                    cell.setCellValue(CollectionUtils.isEmpty(dicValueList) ? value : dicValueList.stream().collect(Collectors.joining(",")));
                }
            }
        }
        if ("测试步骤".equals(name) || "预期结果".equals(name)) {
            String value = cell.getStringCellValue();
            String text = getText(value);
            cell.setCellValue(text);
        }
    }

    private String getText(String richText) {
        String regx = "(<.+?>)|(</.+?>)";
        Matcher matcher = Pattern.compile(regx).matcher(richText);
        while (matcher.find()) {
            richText = matcher.replaceAll("").replace("&nbsp;", " ");
        }
        return richText;
    }
}
