package com.jettech.service.impl;

import com.jettech.DTO.jettoApi.env.ApiEnvPage;
import com.jettech.DTO.jettoApi.env.ApiEnvQueryDto;
import com.jettech.DTO.jettoApi.request.ApiRequest;
import com.jettech.DTO.jettoApi.result.base.ApiResultRoot;
import com.jettech.dao.idao.JettoApiDao;
import com.jettech.feign.IFeignJettoApiResource;
import com.jettech.model.SystemModule;
import com.jettech.model.TestProject;
import com.jettech.model.TestSystem;
import com.jettech.service.iservice.JettoApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: JettoApiServiceImpl
 * @projectName jettomanager
 * @description: jettoApiService
 * @date 2021/3/3114:40
 */
@Service
@Transactional
public class JettoApiServiceImpl implements JettoApiService {
    @Autowired
    private JettoApiDao jettoApiDao;
    @Autowired
    private IFeignJettoApiResource feignJettoApiResource;

    /**
     * @Method: checkProjectDelete
     * @Description: 判断jettoApi的项目是否可以删除
     * @Param: " [projectResourceID] "
     * @return: boolean
     * @Author: wws
     * @Date: 2021/3/31
     */
    @Override
    public boolean checkProjectDelete(String projectResourceID) {
        /**
         * 判断当前项目是否在jettoApi使用
         * 判断逻辑：
         删除项目时查询  脚本目录表 script_folder 再查脚本trade_flow表，案例目录表trade_flow_case_folder，再查案例，
         删除系统时，查询1、通讯环境配置表  system_env_arg  2、交易目录表 trade_folder，再查交易表
         */
        List<Map<String,Object>> scriptFolderList = jettoApiDao.findScriptFolderAndTradeFlowByProjectResourceID(projectResourceID);
        if(scriptFolderList == null || scriptFolderList.size()  == 0){
            return false;
        }else{
            return true;
        }
    }

    /**
     * @Method: saveProjectFolder
     * @Description: 添加jettoApi的项目关联关系
     * @Param: " [toString] "
     * @return: void
     * @Author: wws
     * @Date: 2021/4/1
     */
    @Override
    public void saveOrUpdateProjectFolder(TestProject testProject) {
        Map<String,Object> scriptFolder = jettoApiDao.findScriptFolderByProjectResourceID(testProject.getResourceID().toString());
        if(scriptFolder == null){
            //新增
            scriptFolder = new HashMap<>();
            SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
            scriptFolder.put("id",worker.genNextId());
            scriptFolder.put("name",testProject.getName());
            scriptFolder.put("level",0);
            scriptFolder.put("parent_menu",0);
            scriptFolder.put("script_test_project_id",testProject.getResourceID());
            scriptFolder.put("create_user",testProject.getCreateUser());
            scriptFolder.put("update_user",testProject.getEditUser());
            scriptFolder.put("create_date",testProject.getCreateTime());
            scriptFolder.put("update_date",testProject.getEditTime());

            jettoApiDao.saveScriptFolder(scriptFolder);
        }else{
            //修改
            scriptFolder.put("name",testProject.getName());
            jettoApiDao.updateScriptFolder(scriptFolder);
        }

    }

    /**
     * @Method: saveOrUpdateSystemFolder
     * @Description: 关联jettoApi的tradeFolder表
     * @Param: " [testSystem] "
     * @return: void
     * @Author: wws
     * @Date: 2021/4/2
     */
    @Override
    public void saveOrUpdateSystemFolder(TestSystem testSystem) {
        Map<String,Object> tradeFolder = jettoApiDao.findTradeFolderBySystemResourceID(testSystem.getResourceID().toString());
        if(tradeFolder == null){
            tradeFolder = new HashMap<>();
            SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
            tradeFolder.put("id",worker.genNextId());
            tradeFolder.put("name",testSystem.getName());
            tradeFolder.put("level",0);
            tradeFolder.put("parent_menu",0);
            tradeFolder.put("status",0);//状态 0 正常，1 删除
            tradeFolder.put("test_system_id",testSystem.getResourceID());
            tradeFolder.put("create_user",testSystem.getCreateUser());
            tradeFolder.put("update_user",testSystem.getEditUser());
            tradeFolder.put("create_date",testSystem.getCreateTime());
            tradeFolder.put("update_date",testSystem.getEditTime());

            jettoApiDao.saveTradeFolder(tradeFolder);
        }else{
            tradeFolder.put("name",testSystem.getName());
            jettoApiDao.updateTradeFolder(tradeFolder);
        }
    }

    /**
     * @Method: saveOrUpdateSystemFolder
     * @Description: 关联jettoApi的tradeFolder表
     * @Param: " [systemModule] "
     * @return: void
     * @Author: zhangnb
     * @Date: 2021/09/29
     */
    @Override
    public void saveOrUpdateSystemFolder(SystemModule systemModule) {
        Map<String,Object> tradeFolder = jettoApiDao.findTradeFolderBySystemResourceID(systemModule.getResourceID().toString());
        if(tradeFolder == null){
            tradeFolder = new HashMap<>();
            SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
            tradeFolder.put("id",worker.genNextId());
            tradeFolder.put("name",systemModule.getName());
            tradeFolder.put("level",0);
            tradeFolder.put("parent_menu",0);
            tradeFolder.put("status",0);//状态 0 正常，1 删除
            tradeFolder.put("test_system_id",systemModule.getResourceID());
            tradeFolder.put("create_user",systemModule.getCreateUser());
            tradeFolder.put("update_user",systemModule.getEditUser());
            tradeFolder.put("create_date",systemModule.getCreateTime());
            tradeFolder.put("update_date",systemModule.getEditTime());

            jettoApiDao.saveTradeFolder(tradeFolder);
        }else{
            tradeFolder.put("name",systemModule.getName());
            jettoApiDao.updateTradeFolder(tradeFolder);
        }
    }

    /**
     * @Method: saveOrUpdateTradeFlowCaseFolder
     * @Description: trade_flow_case_folder关联这个表
     * @Param: " [resultObject] "
     * @return: void
     * @Author: wws
     * @Date: 2021/4/22
     */
    @Override
    public void saveOrUpdateTradeFlowCaseFolder(TestProject testProject) {
        Map<String,Object> tradeFlowCaseFolder = jettoApiDao.findTradeFlowCaseFolderByProjectResourceID(testProject.getResourceID().toString());
        if(tradeFlowCaseFolder == null){
            //新增
            tradeFlowCaseFolder = new HashMap<>();
            SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
            tradeFlowCaseFolder.put("id",worker.genNextId());
            tradeFlowCaseFolder.put("name",testProject.getName());
            tradeFlowCaseFolder.put("level",0);
            tradeFlowCaseFolder.put("parent_menu",0);
            tradeFlowCaseFolder.put("test_project_id",testProject.getResourceID());
            tradeFlowCaseFolder.put("create_user",testProject.getCreateUser());
            tradeFlowCaseFolder.put("update_user",testProject.getEditUser());
            tradeFlowCaseFolder.put("create_date",testProject.getCreateTime());
            tradeFlowCaseFolder.put("update_date",testProject.getEditTime());

            jettoApiDao.saveTradeFlowCaseFolder(tradeFlowCaseFolder);
        }else{
            //修改
            tradeFlowCaseFolder.put("name",testProject.getName());
            jettoApiDao.updateTradeFlowCaseFolder(tradeFlowCaseFolder);
        }
    }

    public ApiResultRoot<ApiEnvPage> findEnvByPage(ApiEnvQueryDto apiEnvQueryDto){
        ApiRequest apiRequest = ApiRequest.newRequest().setBody(apiEnvQueryDto);
        return feignJettoApiResource.findEnvByPage(apiRequest);
    }
}
