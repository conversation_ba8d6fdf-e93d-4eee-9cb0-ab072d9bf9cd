package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.dao.idao.ISystemmoduleDao;
import com.jettech.feign.IFeignDataDesignToAssets;
import com.jettech.feign.IFeignDataDesignToBugService;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.SystemModule;
import com.jettech.model.TestCase;
import com.jettech.model.TestCaseRecycleBin;
import com.jettech.model.Trade;
import com.jettech.model.TradeAndTestManager;
import com.jettech.model.TreeNode;
import com.jettech.service.iservice.*;
import com.jettech.util.LongUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SystemModuleServiceImpl
 * @projectName jettopro
 * @description: 系统模块(被测资产管理) serice实现层
 * @date 2019/11/419:33
 */
@Service
@Transactional
public class SystemModuleServiceImpl extends BaseServiceImpl<SystemModule> implements ISystemModuleService {

	@SuppressWarnings("unused")
	private static final Logger logger = LoggerFactory.getLogger(SystemModuleServiceImpl.class);
    @Autowired
    private ISystemmoduleDao systemmoduleDao;
    @Autowired
    private ITradeService tradeService;
    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private IFeignDataDesignToBugService feignDataDesignToBugService;
    @Autowired
    private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;

    @Autowired
    private ITestSystemService testSystemService;

    @Autowired
    private JettoApiService jettoApiService;
    @Autowired
    private IFeignDataDesignToAssets iFeignDataDesignToAssets;
    @Autowired
    private ITestCaseRecycleBinService iTestCaseRecycleBinService;
    @Autowired
    private ITradeTestManagerService iTradeTestManagerService;
    
    
    @PostConstruct
    public void postConstruct() {
        this.baseDao = systemmoduleDao;
    }

    /**
     * @param strings
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description 通过被测系统resourceID查询模块
     * <AUTHOR>
     * @date 2019-11-06 14:02
     */
    @Override
    public List<Map<String, Object>> findByTestSystemResourceIDs(List<String> strings) {
        return systemmoduleDao.findByTestSystemResourceIDs(strings);
    }

    @Override
    /**
     * @Title saveorUpdateSystemModule
     * @Description 新增或修改系统模块
     * @Params [params, userNumber]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result saveorUpdateSystemModule(Map<String, String> params, String userNumber) {

        try {
            String name = params.get("name");
            String resourceID = params.get("resourceID");
            String parentResourceID = params.get("parentResourceID");
            String testSystemResourceID = params.get("testSystemResourceID");
            String isSystemChild = params.get("isSystemChild");
            String simpleName = params.get("simpleName");
            if (name == null || "".equals(name)) {
                return new Result(false, 20001, "名称为必输项不能为空");
            }
            String result = verifyModuleNameNotRepeated(params);
            if (result != null && !"".equals(result)) {
                return new Result(false, 20001, "模块名称已存在");
            }
            SystemModule systemModule = this.findByResourceID(LongUtil.parseLong(resourceID));
            if (null == systemModule) {
                systemModule = new SystemModule();
            } else {
                // 修改模块的时候，同步更新defect表中的数据名称
                if (!systemModule.getName().equals(name)) {
                    // 当名称不一致，调用同步方法
                	String token = HttpRequestUtils.getCurrentRequestToken();
                	// 根据resourceId拼接改条记录的整个路径结构
                    Map<String,String> map = new HashMap<>();
                    map.put("oldName", systemModule.getName());
                    map.put("newName", name);
                    Map<String, String> moduleString = this.getModuleString(systemModule, map);

                    Result r = feignDataDesignToBugService.updateDefectSystemModuleNameByNameAndSystemId(moduleString.get("oldName"),moduleString.get("newName"),
                            systemModule.getTestSystemResourceID(), token);
                    if(!r.isSuccess()){
                        return Result.renderError("同步更新defect表中的数据名称失败");
                    }
                    // 同步更改案例编写库的交易名称
                    Result testCaseResult = testCaseService.updateTestCaseSystemModuleNameByNameAndSystemId(moduleString.get("oldName"),moduleString.get("newName"),
                            systemModule.getTestSystemResourceID());
                    if (!testCaseResult.isSuccess()) {
                        return Result.renderError("同步更新ds_testcase表中的数据名称失败");
                    }
                    // 同步更改案例执行中的交易名称
                    Result performcaseResult = feignDataDesignToManexecuteService.updatePerformcaseSystemModuleNameByNameAndSystemId(moduleString.get("oldName"),moduleString.get("newName"),
                            systemModule.getTestSystemResourceID(), token);
                    if (!performcaseResult.isSuccess()) {
                        return Result.renderError("同步更新performcase表中的数据名称失败");
                    }
                }
            }
            systemModule.setName(name);
            systemModule.setParentResourceID(LongUtil.parseLong(parentResourceID));
            systemModule.setTestSystemResourceID(LongUtil.parseLong(testSystemResourceID));
            systemModule.setSimpleName(simpleName);
            systemModule.setSystemChild("1".equals(isSystemChild));
            if (null == systemModule.getResourceID()) {
                this.save(systemModule, userNumber);
                if (systemModule.isSystemChild()) {
                    jettoApiService.saveOrUpdateSystemFolder(systemModule);
                }
            } else {
                this.update(systemModule, userNumber);
            }
            return Result.renderSuccess(systemModule);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("保存系统模块数据异常！");
        }
    }

    /**
     * 获取拼接好的模块整个路径，用/隔开
     * @param resourceID
     * @param oldName
     * @param newName
     */
    private Map<String,String> getModuleString (SystemModule systemModule, Map<String, String> map) {
        SystemModule parentModule = this.findByResourceID(systemModule.getParentResourceID());
        if (null != parentModule) {
            map.put("oldName", parentModule.getName() + "/" + map.get("oldName"));
            map.put("newName", parentModule.getName() + "/" + map.get("newName"));
            map = getModuleString(parentModule, map);
        }
        return map;
    }


    /**
     * @Title verifyModuleNameNotRepeatedOfSystem   /  verifyModuleNameNotRepeatedOfModule
     * @Description 校验父节点（被测系统或者模块）下模块名称唯一
     * @Params [name, nodeResourceID, parentResourceID]
     * @Return String
     * <AUTHOR>
     * @Date 2019/11/5
     */
    private String verifyModuleNameNotRepeated(Map<String, String> params) {
        String name = params.get("name");
        String resourceID = params.get("resourceID");
        String parentResourceID = params.get("parentResourceID");
        String testSystemResourceID = params.get("testSystemResourceID");
        String type = params.get("type");
        List<SystemModule> repeated1 = null;
        if ("system".equals(type)) {
            repeated1 = systemmoduleDao.verifyModuleNameNotRepeatedOfSystem(name, resourceID, testSystemResourceID);
        } else {
            repeated1 = systemmoduleDao.verifyModuleNameNotRepeatedOfModule(name, resourceID, parentResourceID);
        }
        if (!repeated1.isEmpty()) {
            return "名称已存在";
        }
        return "";
    }


    /**
     * @param "ParentResourceID"
     * @return java.util.List<SystemModule>
     * @throws
     * @Title: findByParentResourceID
     * @description: 父节点查询
     * <AUTHOR>
     * @date 2019/11/5 19:11
     */
    @Override
    public List<SystemModule> findByParentResourceID(String ParentResourceID) {
        return systemmoduleDao.findByParentResourceID(ParentResourceID);
    }

    @Override
    /**
     * @Title findbyTestSystemResourceID
     * @Description 查询当前被测系统下所有的模块（包含子模块）
     * @Params [testSystemResourceID]
     * @Return [ List<SystemModule>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public List<SystemModule> findbyTestSystemResourceID(String testSystemResourceID) {
        return systemmoduleDao.findbyTestSystemResourceID(testSystemResourceID);
    }

    @Override
    /**
     * @Title deleteSystemModuleByResourceID
     * @Description 删除模块
     * @Params [moduleResourceID, userNumber]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result deleteSystemModuleByResourceID(String moduleResourceID, String userNumber) {
        return this.checkDeleteSystemModule(moduleResourceID, userNumber);
    }


    /**
     * <AUTHOR>
     * @description 确认删除系统模块
     * @date 2020年11月25日 15:03
     * @param [resourceID, userNumber]
     * @return com.jettech.dto.Result
     **/
    @Override
    public Result confirmDeleteSystemModule(String moduleResourceID, String userNumber) {
        Result result = this.checkDeleteSystemModule(moduleResourceID, userNumber);
        if (!result.isSuccess()) {
            return result;
        }
        SystemModule systemModule = this.findByResourceID(LongUtil.parseLong(moduleResourceID));
        this.delete(systemModule, userNumber);
        return Result.renderSuccess("删除成功");
    }

    /**
     * <AUTHOR>
     * @description 校验是否能删除系统模块
     * @date 2020年11月25日 15:13
     * @param [moduleResourceID, userNumber]
     * @return com.jettech.dto.Result
     **/
    private Result checkDeleteSystemModule(String moduleResourceID, String userNumber) {
        List<String> collect = new ArrayList<String>();
        collect.add(moduleResourceID);
        List<SystemModule> byModuleResouceID = findByParentResourceID(moduleResourceID);  // 找当前模块的子模块
        //当前模块下已有子模块不允许删除
        if(!byModuleResouceID.isEmpty()){
            return Result.renderError("当前模块下已经维护子模块/交易不允许删除！");
        }
        //当前模块下有交易禁止删除
        List<Trade> trades = tradeService.findBySystemModule(collect);
        if(trades!=null&&trades.size()>0){
            return Result.renderError("当前模块下已经维护子模块/交易不允许删除！");
        }
        return Result.renderSuccess();
    }

    /**
     * 
     *@Description 新增批量删除模块功能，并增加权限控制, 校验模块下的交易不关联案例资产即可删除-- 
     *@param 
     *@return Result
     *<AUTHOR>
     *@Date 2023年2月6日
     */
    @Override
    public Result<?> moreModuleDelete(String moduleResourceID, String testSystemResourceID,String userNumber) {
    	//j校验
        if(StringUtils.isBlank(testSystemResourceID)) {
        	return Result.renderError("系统的resourceID不能为空");
        }
        if(StringUtils.isBlank(moduleResourceID)) {
        	return Result.renderError("模块的resourceID不能为空");
        }
        List<String> collect = new ArrayList<String>();
        List<SystemModule> allmoduleList=new ArrayList<>(); //存储全部模块
        List<Trade> alltradeList=new ArrayList<>(); //存储全部交易
        Map<String,Object> returnMap=new HashMap<>(); //返回Map
//        if(StringUtils.isBlank(moduleResourceID)) {
//        	//查询系统下的第一层级模块
//        	List<SystemModule> smFirst = this.findByParentResourceID(testSystemResourceID);
//        	asList= smFirst.stream().map(x->x.getResourceID().toString()).collect(Collectors.toList());
//        }else {
//             String[] split = moduleResourceID.split(",");
//             asList = Arrays.asList(split);
//        }
        String[] split = moduleResourceID.split(",");
        List<String> asList = Arrays.asList(split);
        //查询系统下的全部模块
        List<SystemModule> allSystemModule = this.findbyTestSystemResourceID(testSystemResourceID);
        Map<Long, List<SystemModule>> collect2 = allSystemModule.stream().collect(Collectors.groupingBy(s->s.getParentResourceID()));
       
        for(String s :asList) {
        	List<SystemModule> list = collect2.get(Long.valueOf(s));
        	SystemModule sm = this.findByResourceID(Long.valueOf(s));
        	if(sm==null) {
        		return Result.renderError("当前模块不存在");
        	}
        	if(list!=null&&!list.isEmpty()) { 
        		List<SystemModule> moduleList=new ArrayList<>();
                List<Trade> tradeList=new ArrayList<>();
        		//是有子模块的
        		 Map reMap = getlastModuleResurceID(list,collect2,moduleList,tradeList,returnMap);
        		 List<SystemModule> list2=(List<SystemModule>)reMap.get("moduleList");
 			     List<Trade> list3=(List<Trade>)reMap.get("tradeList");
 				 if(list2!=null&&!list2.isEmpty()) {
 					list2.add(sm);
 					allmoduleList.addAll(list2);
 				 }
 				 if(list3!=null&&!list3.isEmpty()) {
 					alltradeList.addAll(list3);
 				 }
        		 String String = (java.lang.String) reMap.get("messageString");
        		 if(String!=null) {
        			  return Result.renderError(sm.getName()+"下含有案例，不能删除");
        		 }
        	}else {
		        collect.add(s);
			   //说明是没有子模块的，查询交易
		     //   findTradeAndCase(sm, collect, allmoduleList, alltradeList , new HashMap <String,Object>());
			    List<Trade> trades = tradeService.findBySystemModule(collect);
			    if(trades!=null &&!trades.isEmpty()) {
				   List<Long> collect3 = trades.stream().map(x->x.getResourceID()).collect(Collectors.toList());
   			       List<TestCase> caseList = testCaseService.findbyTradeResourceIDList(collect3);
    			   String token = HttpRequestUtils.getCurrentRequestToken();
    		       Result<?> assetsCaseMap= iFeignDataDesignToAssets.findbyTradeResourceIDList(collect3,token);
    		       List<Map<String,Object>> obj = (List<Map<String, Object>>) assetsCaseMap.getObj();
    		       //查询回收站ds_testcaseRecycleBin
    		       List<TestCaseRecycleBin>  recyLst=iTestCaseRecycleBinService.findCaseByTradeResourceIDList(collect3);
    		       if(recyLst.isEmpty()&&caseList.isEmpty()&&obj.isEmpty()) {
    		    	   allmoduleList.add(sm);
    		    	   alltradeList.addAll(trades);
    			   }else {
    				   return Result.renderError(sm.getName()+"下含有案例，不能删除");
    			   }
			   }else {
				   allmoduleList.add(sm);
			   }
			   
        	}
        }
        if(alltradeList!=null&&!alltradeList.isEmpty()) {
        	List<Long> ridList = alltradeList.stream().map(x->x.getResourceID()).collect(Collectors.toList());
            if(ridList!=null&&ridList.size()>0) {
            	List<TradeAndTestManager> maList=iTradeTestManagerService.findByTradeResourceIDIn(ridList);
                if(maList!=null&& !maList.isEmpty()) {
                	iTradeTestManagerService.deleteInBatch(maList, userNumber);
                }
            }
        	 tradeService.deleteInBatch(alltradeList, userNumber);
        }
        if(allmoduleList!=null&&!allmoduleList.isEmpty()) {
        	this.deleteInBatch(allmoduleList, userNumber);
        }
 
        return Result.renderSuccess();
    }
    
    public Map<String,Object> getlastModuleResurceID(List<SystemModule> list,Map<Long, List<SystemModule>> collect2,
    		List<SystemModule> moduleList,List<Trade> tradeList ,Map<String,Object> map) {
    	
    		for(SystemModule ss:list) {
    			List<SystemModule> list1 = collect2.get(Long.valueOf(ss.getResourceID()));
    			if(list1!=null&&!list.isEmpty()) { //是有子模块的
    				Map<String,Object>  childmap = getlastModuleResurceID(list1,collect2,moduleList,tradeList,map);
    			    List<SystemModule> list2=(List<SystemModule>)childmap.get("moduleList");
    			    List<Trade> list3= (List<Trade>)childmap.get("tradeList");
    			    String String = (java.lang.String) childmap.get("messageString");
           		    if(String== null && list2!=null&&!list2.isEmpty() ) {
           		    	list2.add(ss);
           		    }else {
           		    	list2.remove(list2.size()-1);
           		    }
    		   }else {
    			   List<String> collect = new ArrayList<String>();
    		        collect.add(ss.getResourceID().toString());
    		        findTradeAndCase(ss, collect, moduleList, tradeList , map);
    			   //说明是没有子模块的，查询交易
//    			   List<Trade> trades = tradeService.findBySystemModule(collect);
//    			   if(trades!=null &&!trades.isEmpty()) {
//    				   List<Long> collect3 = trades.stream().map(x->x.getResourceID()).collect(Collectors.toList());
//    				   //
//        			   List<TestCase> caseList = testCaseService.findbyTradeResourceIDList(collect3);
//        			   String token = HttpRequestUtils.getCurrentRequestToken();
//        			   //系统案例库
//        		       Result<?> assetsCaseMap= iFeignDataDesignToAssets.findbyTradeResourceIDList(collect3,token);
//        		       List<Map<String,Object>> obj = (List<Map<String, Object>>) assetsCaseMap.getObj();
//        		       //查询回收站ds_testcaseRecycleBin
//        		       List<TestCaseRecycleBin>  recyLst=iTestCaseRecycleBinService.findCaseByTradeResourceIDList(collect3);
//        			   if(recyLst.isEmpty()&&caseList.isEmpty()&&obj.isEmpty()) {
//        				   moduleList.add(ss);
//        				   tradeList.addAll(trades);
//        				   map.put("moduleList", moduleList);
//        				   map.put("tradeList", tradeList);
//        			   }else {
//        				   map.put("messageString", "不能删除");
//        				   return map;
//        			   }
//    			   }else {
//    				   moduleList.add(ss);
//    				   map.put("moduleList", moduleList);
//    				   map.put("tradeList", tradeList);
//    			   }
//    			   
    		   }
    			
    		}
    		return map;
    	
    }
    //查询交易以及交易下的案例
    private  Map<String,Object>  findTradeAndCase(SystemModule module,List<String> moduleRidList,List<SystemModule> moduleList,List<Trade> tradeList ,Map<String,Object> map) {
    	   //说明是没有子模块的，查询交易
		   List<Trade> trades = tradeService.findBySystemModule(moduleRidList);
		   if(trades!=null &&!trades.isEmpty()) {
			   List<Long> collect3 = trades.stream().map(x->x.getResourceID()).collect(Collectors.toList());
			   List<TestCase> caseList = testCaseService.findbyTradeResourceIDList(collect3);
			   String token = HttpRequestUtils.getCurrentRequestToken();
			   //系统案例库
		       Result<?> assetsCaseMap= iFeignDataDesignToAssets.findbyTradeResourceIDList(collect3,token);
		       List<Map<String,Object>> obj = (List<Map<String, Object>>) assetsCaseMap.getObj();
		       //查询回收站ds_testcaseRecycleBin
		       List<TestCaseRecycleBin>  recyLst=iTestCaseRecycleBinService.findCaseByTradeResourceIDList(collect3);
			   if(recyLst.isEmpty()&&caseList.isEmpty()&&obj.isEmpty()) {
				   moduleList.add(module);
				   tradeList.addAll(trades);
				   map.put("moduleList", moduleList);
				   map.put("tradeList", tradeList);
			   }else {
				   map.put("messageString", "不能删除");
				   return map;
			   }
		   }else {
			   moduleList.add(module);
			   map.put("moduleList", moduleList);
			   map.put("tradeList", tradeList);
		   }
		   
    	
		return map;
    	
    }
    
    
    

    /**
     * @Title findChildRid
     * @Description 递归查询模块节点
     * @Params
     * @Return
     * <AUTHOR>
     * @Date 2019/11/6
     */
    private List<String> findChildRid(String moduleResourceID, List<String> collect) {
        List<SystemModule> byModuleResouceID = findByParentResourceID(moduleResourceID);
        List<String> collect1 = byModuleResouceID.stream().map(s -> String.valueOf(s.getResourceID())).collect(Collectors.toList());
        collect.addAll(collect1);
        for (String rid : collect1) {
            findChildRid(rid, collect);
        }
        return collect;
    }

    @Override
    /**
     * @Title checkForAddSonModuleORTrade
     * @Description 校验 模块下新增模块或者交易时校验子节点
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/7
     */
    public Result checkSonModuleORTradeForAdd(String type, String resourceID) {
        String result = "当前模块下";
        if (type.equals("trade")) {
            List<SystemModule> sonSystemModule = this.findByParentResourceID(resourceID);
            if (!sonSystemModule.isEmpty() && null != sonSystemModule) {
                result += "已维护子模块，不能再建交易！";
            } else {
                result = "";
            }
        } else if (type.equals("module")) {
            List<String> collect = new ArrayList<String>();
            collect.add(resourceID);
            List<Trade> sontradeList = tradeService.findBySystemModule(collect);
            if (!sontradeList.isEmpty() && null != sontradeList) {
                result += "已维护交易，不能再建子模块！";
            } else {
                result = "";
            }
        } else {
            result = "";
        }
        if (result.equals("")) {
            return Result.renderSuccess(result);
        } else {
            return Result.renderError(result);
        }
    }

    @Override
    /**
     * @Title saveorUpdateTestSystem
     * @Description 校验删除时模块下是否维护数据
     * @Params [resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result findResourceBySystemModule(String resourceID) {
        List<SystemModule> sonSystemModule = this.findByParentResourceID(resourceID);
        List<String> collect = new ArrayList<String>();
        collect.add(resourceID);
        List<Trade> sontradeList = tradeService.findBySystemModule(collect);
        if (sonSystemModule.isEmpty() && sontradeList.isEmpty()) {
            return Result.renderSuccess("当前模块下没有维护数据，请确认删除");
        }
        return Result.renderSuccess("当前模块下已维护数据，请确认删除");
    }

    /**
     * @param testSystemResourceID
     * @return java.util.List<java.util.HashMap < java.lang.String, java.lang.Object>>
     * @Description 查询该被测系统下一级的数据
     * <AUTHOR>
     * @date 2019-12-12 20:45
     */
    @Override
    public List<HashMap<String, Object>> findNextLowerLevelMapByTestSystemResourceID(Long testSystemResourceID) {
        return systemmoduleDao.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);
    }

    /**
     * @Title: findbySystemModuleName
     * @Description: 根据名称查询模块
     * @Param: "[systemModuleName]"
     * @Return: "java.util.List<SystemModule>"
     * @Author: xpp
     * @Date: 2020/2/17
     */
    @Override
    public List<SystemModule> findbySystemModuleName(String systemModuleName) {
        return systemmoduleDao.findbySystemModuleName(systemModuleName);
    }

    /**
     * @Title whetherToRepeat
     * @Description 校验父节点（被测系统或者模块）下模块名称唯一
     * @Params [name, nodeResourceID, parentResourceID]
     * @Return String
     * <AUTHOR>
     * @Date 2019/11/5
     */
    @Override
    public Result whetherToRepeat(Map<String, String> params) {
        String result = verifyModuleNameNotRepeated(params);
        if (result != null && !"".equals(result)) {
            return new Result(false, 20001, "名称已存在");
        }
        return Result.renderSuccess();
    }

    @Override
    public Result verifyModuleSimpleNameNotRepeated(Map<String, String> params) {
        List<SystemModule> list = systemmoduleDao.verifyModuleSimpleNameNotRepeatedOfModule(params.get("name"), params.get("resourceID"), params.get("parentResourceID"));
        if (list.isEmpty()) {
            return Result.renderSuccess();
        } else {
            return Result.renderError("简称已存在！");
        }
    }

    /**
     * @param @return 参数
     * @return Map<Long, String>    返回类型
     * @throws
     * @Title: findUserUnderModuleTaskTradeCount
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public List<Map<Long, String>> findUserUnderModuleTaskTradeCount(String userNumber, String taskResourceID, String Flag) {

        return systemmoduleDao.findUserUnderModuleTaskTradeCount(userNumber, taskResourceID, Flag);
    }

    /**
     * 查询交易列表系统或者模块下面的交易总数（flag-system系统/flag-module-模块）
     *
     * @param @param  flag
     * @param @return 参数
     * @return List<Map < Long, String>>    返回类型
     * @throws
     * @Title: findUnderSystemORModuleTradeCount
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public List<Map<Long, String>> findUnderSystemORModuleTradeCount(String flag) {

        return systemmoduleDao.findUnderSystemORModuleTradeCount(flag);
    }

    /**
     * @param name
     * @param testSystemResourceID
     * @return
     * @Title: findByNameAndTestSystemResourceIDAndParentResourceID
     * @Description: 根据系统和模块名称查询模块
     * <AUTHOR>
     * @date 2020年5月27日 下午12:00:07
     */
    @Override
    public SystemModule findByNameAndTestSystemResourceIDAndParentResourceID(String name, Long testSystemResourceID, Long parentResourceID) {
        return systemmoduleDao.findByNameAndTestSystemResourceIDAndParentResourceID(name, testSystemResourceID, parentResourceID);
    }

    /**
     * @param demandResourceID : 需求resourceID
     * @return : java.util.List<com.jettech.model.TreeNode>
     * @Method : findByDemandResourceID
     * @Description : 通过需求resourceID查询模块树节点数据
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-06-24 周三 14:28:45
     */
    @Override
    public List<TreeNode> findByDemandResourceID(String demandResourceID) {
        return systemmoduleDao.findByDemandResourceID(demandResourceID);
    }

    /**
     * 根据被测系统查询个各层级模块，平拼写成List返回(如：模块1/模块11/模块111)
     *
     * @param request
     * @return
     * <AUTHOR>
     * @Date 2020/7/14
     */
    @Override
    public Result findModuleLevelBySystemRid(String systemResourceID) {
        Map<String, List<Map<String, String>>> mapResult = new HashMap<>();
        //用来存模块的层级关系，key最末级模块的rid，value为：模块1/模块11/模块111
        List<Map<String, String>> listModuleLevel = new ArrayList<>();
        //用来存被测系统下的直属交易
        List<Map<String, String>> listTrade = new ArrayList<>();
        //查询当前被测系统下所有的模块（包含子模块）
        //List<SystemModule> modules = this.findbyTestSystemResourceID(systemResourceID);
        if(StringUtils.isEmpty(systemResourceID)){
            return Result.renderError("systemResourceID不能为空！");

        }
        List<SystemModule> modules = this.systemmoduleDao.findbyTestSystemResourceID2(systemResourceID);
        if (!modules.isEmpty()) {
            //所有和被测系统直接关联的
            List<SystemModule> rootModules = modules.stream().filter(x -> systemResourceID.equals(String.valueOf(x.getParentResourceID()))).collect(Collectors.toList());

            for (SystemModule systemModule : rootModules) {
                String parentName = systemModule.getName();
//				List<SystemModule> childs = systemmoduleDao.findByParentResourceID(String.valueOf(systemModule.getResourceID()));
                List<SystemModule> childs = modules.stream().filter(x -> String.valueOf(systemModule.getResourceID()).equals(String.valueOf(x.getParentResourceID()))).collect(Collectors.toList());
                if (!childs.isEmpty()) {
                    for (SystemModule child : childs) {

                        this.buildChildMudule(listModuleLevel, child, parentName, modules);
                    }
                } else {
                    Map<String, String> map = new HashMap<>();
                    map.put("bottomModuleResourceID", String.valueOf(systemModule.getResourceID()));
                    map.put("moduleName", parentName);
                    map.put("id", String.valueOf(systemModule.getId()));
//					map.put(String.valueOf(systemModule.getResourceID()), parentName);
                    listModuleLevel.add(map);
                }
            }
        }
        //List<Trade> tradeUnderTestSystem = tradeService.findOnlyTradebyTestSystemResourceID(systemResourceID, null);
        List<Trade> tradeUnderTestSystem = tradeService.findOnlyTradebyTestSystemResourceID2(systemResourceID);
        for (Trade trade : tradeUnderTestSystem) {
            Map<String, String> map = new HashMap<>();
            map.put("tradeResourceID", String.valueOf(trade.getResourceID()));
            map.put("tradeName", trade.getName());
            map.put("id", String.valueOf(trade.getId()));
            listTrade.add(map);
        }
        mapResult.put("module", listModuleLevel);
        mapResult.put("trade", listTrade);

        return Result.renderSuccess(mapResult);
    }

    /**
     * 根据moduleid 获取当前module以及所有子module
     *
     * @param moduleResID
     * @return
     * <AUTHOR>
     */
    @Override
    public List<SystemModule> findModuleAndSubModelByResId(String moduleResID) {
        List<SystemModule> modules = new ArrayList<>();
        SystemModule module = findByResourceID(Long.valueOf(moduleResID));
        Deque<SystemModule> deque = new ArrayDeque<>();
        deque.add(module);
        while (!deque.isEmpty()) {
            SystemModule pModule = deque.poll();
            modules.add(pModule);
            List<SystemModule> subModules = findByParentResourceID(pModule.getResourceID().toString());
            if (subModules != null && !subModules.isEmpty()) {
                deque.addAll(subModules);
            }
        }
        return modules;
    }

    /**
     * 递归查找拼接模块
     *
     * @param listResult
     * @param child
     */
    private void buildChildMudule(List<Map<String, String>> listResult, SystemModule current, String parentName, List<SystemModule> modules) {

        List<SystemModule> childs = modules.stream().filter(x -> String.valueOf(current.getResourceID()).equals(String.valueOf(x.getParentResourceID()))).collect(Collectors.toList());
//		List<SystemModule> childs = systemmoduleDao.findByParentResourceID(String.valueOf(current.getResourceID()));
        if (!childs.isEmpty()) {
            for (SystemModule child : childs) {
                this.buildChildMudule(listResult, child, parentName + "/" + current.getName(), modules);
            }
        } else {
            Map<String, String> map = new HashMap<>();
            map.put("bottomModuleResourceID", String.valueOf(current.getResourceID()));
            map.put("moduleName", parentName + "/" + current.getName());
            map.put("id", String.valueOf(current.getId()));
//			map.put(String.valueOf(current.getResourceID()), parentName);
            listResult.add(map);
        }

    }

    /**
     * 根据模块的rid获取上级和上级所有直接模块的层级关系
     *
     * @param moduleResourceID
     * @return
     */
    @Override
    public String findParentModuleNamesByModuleRid(Long moduleResourceID) {
        Map<String,String> resultMap = new HashMap<>();
        SystemModule current  = systemmoduleDao.findParentModuleByRid(moduleResourceID);
        resultMap.put("name",current.getName());
        this.findParentModuleByRid(current.getParentResourceID(),resultMap);
        return resultMap.get("name");
    }

    private SystemModule findParentModuleByRid(Long parentModuleResourceID,Map<String,String> resultMap){

        SystemModule parent  = systemmoduleDao.findParentModuleByRid(parentModuleResourceID);
        if(parent != null){
            resultMap.put("name",parent.getName()+"/"+resultMap.get("name"));
            parent = this.findParentModuleByRid(parent.getParentResourceID(),resultMap);
        }
        return parent;
    }

    /**
     * 根据所属系统查询交易
     * @param systemResourceID
     * @return
     */
	@Override
	public Result findTradeBysystemResourceID(String systemResourceID) {

		logger.info("根据所属系统查询交易--开始查询，所属系统ID:"+systemResourceID);
		List<Map<String, Object>> returnList = new ArrayList<Map<String,Object>>();
		if(StringUtils.isBlank(systemResourceID)) {
			logger.error("所属系统ID为空");
			return Result.renderSuccess(returnList);
		}
		//用来存模块的层级关系，key最末级模块的rid，value为：模块1/模块11/模块111
        List<Map<String, String>> listModuleLevel = new ArrayList<>();
		//根据系统id获取所属模块集合
		List<SystemModule> modules = this.systemmoduleDao.findbyTestSystemResourceID2(systemResourceID);
        if (!modules.isEmpty()) {
            //所有和被测系统直接关联的
            List<SystemModule> rootModules = modules.stream().filter(x -> systemResourceID.equals(String.valueOf(x.getParentResourceID()))).collect(Collectors.toList());
            for (SystemModule systemModule : rootModules) {
                String parentName = systemModule.getName();
                List<SystemModule> childs = modules.stream().filter(x -> String.valueOf(systemModule.getResourceID()).equals(String.valueOf(x.getParentResourceID()))).collect(Collectors.toList());
                if (!childs.isEmpty()) {
                    for (SystemModule child : childs) {
                        this.buildChildMudule(listModuleLevel, child, parentName, modules);
                    }
                } else {
                    Map<String, String> map = new HashMap<>();
                    map.put("bottomModuleResourceID", String.valueOf(systemModule.getResourceID()));
                    listModuleLevel.add(map);
                }
            }
        }
   	    //获取所属模块的id集合
   	    List<Object> nameList = listModuleLevel.stream().filter(p->p.get("bottomModuleResourceID") !=null).map(p->p.get("bottomModuleResourceID")).collect(Collectors.toList());
   	    if(nameList == null || nameList.isEmpty()) {
   	    	logger.info("根据所属系统查询交易--获取模板为空");
   	    	return Result.renderSuccess(returnList);
   	    }
   	    //根据模块集合查询对应的系统集合
   	    List<String> moduleResourceIDs = (List<String>) (List) nameList;
   	    logger.info("根据所属系统查询交易--获取模板成功："+moduleResourceIDs);
   	    logger.info("根据所属系统查询交易--根据模板集合查询所属系统集合");
        return Result.renderSuccess(tradeService.findTradeByModuleResourceIDs(moduleResourceIDs));
	}

    @Override
    public List<SystemModule> findByTestSystemResIdIn(List<String> systemResIdList) {
        return systemmoduleDao.findByTestSystemResIdIn(systemResIdList);
    }

}
