package com.jettech.service.impl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jettech.common.util.BinaryDecimalUtil;
import com.jettech.model.TestCase;
import com.jettech.model.TestCaseRecycleBin;
import com.jettech.model.TestSystem;
import com.jettech.model.Trade;
import com.jettech.util.FieldNote;
import com.jettech.util.FieldType;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 导入用例处理类
 * Created by WZJ
 * Time       2020/9/18.
 */
public class TradeExcelReadHandler extends AnalysisEventListener<Map<String, String>> {


    //必须表头
    private final List<String> requiredHead = new ArrayList<>(Arrays.asList("所属系统", "所属交易"));

    //字段
    private List<Map<String, Object>> fields;
    //被测系统
    private List<TestSystem> testSystems;
    //交易
    private List<Trade>     tradeList;
    //用户
    private String userNumber;
    private String testEnvironment;
    private String demandResourceID;
    //字典
    private Map<String, Map<String, String>> fieldDic;

    private String testTaskRid;

    //回收站已有案例
    Map<Long, List<TestCaseRecycleBin>> testCaseRecycleBinsOfTrade;
    //交易下所有的caseId
    Map<Long, List<String>> caseIdsOfTrade;
    //此任务下已有的testCase
    Map<Long, List<TestCase>> caseOfTaskTrade;

    Map<Long, String> baseCaseEditIdOfTaskTrade;

    private int currentSheetIndex;
    private int currentRowIndex;
    private Map<Integer, String> currentHead;
    //案例编号名称
    private String caseIdName;


    private List<TestCase> casesToAdd = new ArrayList<>();
    private List<TestCase> caseToUpdate = new ArrayList<>();
    private List<TestCaseRecycleBin> caseRecycleBinsToDelete = new ArrayList<>();

    public List<TestCase> getCasesToAdd() {
        return casesToAdd;
    }

    public List<TestCase> getCaseToUpdate() {
        return caseToUpdate;
    }

    public List<TestCaseRecycleBin> getCaseRecycleBinsToDelete() {
        return caseRecycleBinsToDelete;
    }


    //当前excel 交易与案例编号对应map
    private Map<String, List<String>> excelTradeCaseIdMap = new HashMap<>();

    private String errorMsg = "";


    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public TradeExcelReadHandler(String taskResId, List<Map<String, Object>> fields, Map<String, Map<String, String>> fieldDic,
                                 List<TestSystem> testSystems, List<Trade> tradeList, String userNumber, Map<String, String> testPlan,
                                 Map<Long, List<TestCaseRecycleBin>> testCaseRecycleBinsOfTrade, Map<Long, List<String>> caseIdsOfTrade,
                                 Map<Long, List<TestCase>> caseOfTaskTrade, Map<Long, String> baseCaseEditIdOfTaskTrade) {

        this.testTaskRid = taskResId;
        this.fields = fields;
        fields.forEach(x -> {
            if(x.get("name").equals("caseId")){
                this.caseIdName = x.get("aliasName").toString();
            }
            if ((Boolean) x.get("writeRequired")) {
                requiredHead.add(x.get("aliasName").toString());
            }
        });

        this.testSystems = testSystems;
        this.tradeList = tradeList;
        this.userNumber = userNumber;
        this.testEnvironment = testPlan != null ? testPlan.get("testStage") : "";
        this.demandResourceID = testPlan != null ? testPlan.get("demandResourceID") : null;
        this.fieldDic = fieldDic;
        this.testCaseRecycleBinsOfTrade = testCaseRecycleBinsOfTrade;
        this.caseIdsOfTrade = caseIdsOfTrade;
        this.caseOfTaskTrade = caseOfTaskTrade;
        this.baseCaseEditIdOfTaskTrade = baseCaseEditIdOfTaskTrade;

    }

    @Override
    public void invoke(Map<String, String> stringStringMap, AnalysisContext analysisContext) {
        currentRowIndex++;
        try {
            checkRowValue(stringStringMap);
        } catch (IllegalAccessException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        super.invokeHeadMap(headMap, context);
        currentSheetIndex++;
        currentRowIndex = 0;
        currentHead = headMap;
        checkSheetHead(headMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (this.errorMsg.isEmpty()) {

        }
    }

    private int getColIndexByName(String name) {
        for (Map.Entry<Integer, String> integerStringEntry : currentHead.entrySet()) {
            if (integerStringEntry.getValue().equals(name)) {
                return integerStringEntry.getKey();
            }
        }
        return -1;
    }

    private void checkRowValue(Map<String, String> stringStringMap) throws IllegalAccessException {
        String msg = String.format("第【%s】个Sheet的第【%s】行", currentSheetIndex, currentRowIndex);

        int key = getColIndexByName("所属系统");

        String testSystemStr = stringStringMap.get(key);
        if (StringUtils.isEmpty(testSystemStr)) {
            errorMsg += (msg + "字段【所属系统】不能为空");
            return;
        }
        TestSystem testSystem = testSystems.stream().filter(x -> x.getName().equals(testSystemStr)).findFirst().orElse(null);
        if (testSystem == null) {
            errorMsg += String.format("%s，所属系统【%s】不存在。", msg, testSystemStr);
            return;
        }
        key = getColIndexByName("所属交易");
        String tradeName = stringStringMap.get(key);
        if (StringUtils.isEmpty(tradeName)) {
            errorMsg += (msg + "字段【所属交易】不能为空");
            return;
        }
        Trade trade = tradeList.stream().filter(x -> x.getTestSystemResourceID().equals(testSystem.getResourceID())
                && x.getName().equals(tradeName)).findFirst().orElse(null);
        if (trade == null) {
            errorMsg += String.format("%s，所属交易【%s】不存在。", msg, tradeName);
            return;
        }
        TestCase testCaseOrigin = null;
        TestCaseRecycleBin recycleBin = null;
        StringBuilder sb = new StringBuilder();
        Object[] keys = stringStringMap.keySet().toArray();
        for (Object indexKey : keys) {
            Integer index = (Integer) indexKey;
            String s = currentHead.get(index);
            if (requiredHead.contains(s)) {
                String value = stringStringMap.get(index);
                if (StringUtils.isEmpty(value)) {
                    sb.append(String.format("%s，字段【%s】不能为空。", msg, s));
                }
//                else if (fieldDic.containsKey(s)) {
//                    if (!fieldDic.get(s).containsValue(value)) {
//                        sb.append(String.format("%s，字段【%s】的取值不在范围【%s】内。", msg, s, StringUtils.joinWith(",", fieldDic.get(s).values())));
//                    }
//                }
            }
            if (s.equals(caseIdName)) {
                String caseId = stringStringMap.get(index);
                if (StringUtils.isNotEmpty(caseId)){
                    if (!excelTradeCaseIdMap.containsKey(tradeName)) {
                        excelTradeCaseIdMap.put(tradeName, new ArrayList<>());
                    }
                    if (excelTradeCaseIdMap.containsKey(tradeName) && excelTradeCaseIdMap.get(tradeName).contains(caseId)) {
                        sb.append(String.format("%s，【案例编号】在当前表格内重复。", msg));
                    }
                    excelTradeCaseIdMap.get(tradeName).add(caseId);
                    //案例是否存在于其他任务中  增加测试案例管理后 案例编号不能是任务下唯一  调整为交易下唯一
                    List<TestCase> caseOptional = caseOfTaskTrade.get(trade.getResourceID()).stream()
                            .filter(x -> caseId.equals(x.getCaseId())).collect(Collectors.toList());
                    //案例id是否存在于回收站中
                    Optional<TestCaseRecycleBin> testCaseRecycleBinOptional = testCaseRecycleBinsOfTrade
                            .get(trade.getResourceID()).stream().filter(x -> caseId.equals(x.getCaseId())).findFirst();

                    if (!caseOptional.isEmpty()) {
                        if (!StringUtils.isEmpty(testTaskRid)) {
                            if (caseOptional.stream().anyMatch(x -> x.getTesttaskResourceID() != null && x.getTesttaskResourceID() != Long.parseLong(testTaskRid))) {
                                sb.append(String.format("%s，【案例编号】在其他任务下已存在。", msg));
                                break;
                            }
                        }
                        testCaseOrigin = caseOptional.get(0);
                    } else if (caseIdsOfTrade.get(trade.getResourceID()).contains(caseId)) {
                        sb.append(String.format("%s，【案例编号】在系统案例下已经存在。", msg));
                    } else if (testCaseRecycleBinOptional.isPresent()) {
//                        if (Long.parseLong(testTaskRid) == testCaseRecycleBinOptional.get().getTesttaskResourceID()) {
//                            caseRecycleBinsToDelete.add(testCaseRecycleBinOptional.get());
//                            recycleBin = testCaseRecycleBinOptional.get();
//                        } else {
//                            sb.append(String.format("%s，【案例编号】在其他任务下已存在。", msg));
//                        }
                        caseRecycleBinsToDelete.add(testCaseRecycleBinOptional.get());
                        recycleBin = testCaseRecycleBinOptional.get();
                    }
                }
            }
        }
        if (sb.length() > 0) {
            errorMsg += sb.toString();
            return;
        }
        TestCase testCase = fillTestCase(stringStringMap, testCaseOrigin, recycleBin, testSystem, trade);
        if (testCaseOrigin != null) {
            caseToUpdate.add(testCase);
        } else {
            casesToAdd.add(testCase);
        }
    }

    private void setFieldValue(TestCase testCase, String name, Object value) throws IllegalAccessException {

        Class clazz = TestCase.class;
        Field[] fields = clazz.getDeclaredFields();
        Field[] declaredFields = clazz.getSuperclass().getDeclaredFields();
        List<Field> fieldList = new ArrayList<>();
        fieldList.addAll(new ArrayList<>(Arrays.asList(fields)));
        fieldList.addAll(new ArrayList<>(Arrays.asList(declaredFields)));
        //String[] pattern = new String[]{"yyyy-mm-dd", "yyyy/mm/dd"};
        String[] pattern = new String[]{"yyyy-MM-dd", "yyyy/MM/dd"};
        String finalName = name;
        Optional<Map<String, Object>> fieldDef = this.fields.stream().filter(x -> Objects.equals(x.get("aliasName"), finalName)).findFirst();
        if (fieldDef.isPresent()) {
            name = fieldDef.get().get("aliasName") == null ? "" : fieldDef.get().get("aliasName").toString();
        }
        Map<String,String> mapSelect = fieldDic.get("selectConfig");
        Map<String,String> dateConfig = fieldDic.get("dateConfig");
        //get dic value
        if (fieldDic.containsKey(name)) {
            if(mapSelect!= null){
                String type = mapSelect.get(name);
                if("2".equals(type)){//单选下拉框
                    for (Map.Entry<String, String> stringStringEntry : fieldDic.get(name).entrySet()) {
                        if (Objects.equals(stringStringEntry.getValue(), value == null ? null : value.toString())) {
                            value = stringStringEntry.getKey();
                            break;
                        }
                    }
                }
                if("3".equals(type)){//多选下拉框
                    Map<String,String> keyValue = fieldDic.get(name);
                   String[] values =  value.toString().split(",");
                   String finalValue = "";
                   for (String valueOne :values){
                       for (Map.Entry<String,String> entry:keyValue.entrySet()){
                           if(entry.getValue().equals(valueOne)){
                               finalValue += entry.getKey() +",";
                           }
                       }
                   }
                   if(!"".equals(finalValue)){
                       value = finalValue.substring(0,finalValue.length()-1);
                   }
                }

                if ("测试方式".equals(name) && !StringUtils.isEmpty(value.toString())) {
                    String testMode = value.toString().replaceAll(",", "");
                    String x = BinaryDecimalUtil.DicValToBin(testMode);
                    int y = BinaryDecimalUtil.BinToTen(x);
                    testCase.setTestMode(y);
                }

            }

        }
        if (fieldDef.isPresent()) {
            name = fieldDef.get().get("nameDescription") == null ? "" : fieldDef.get().get("nameDescription").toString();
        }
        for (Field f : fieldList) {
            f.setAccessible(true);
            FieldNote fieldNote = f.getAnnotation(FieldNote.class);
            if (fieldNote == null)
                continue;
            String fieldNoteValue = fieldNote.value();

            if (fieldNoteValue.equals(name)) {
                if (fieldNote.type() == FieldType.Date) {
                    Date date = new Date();
                    try {
                        date = DateUtils.parseDate(value.toString(), pattern);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    f.set(testCase, date);
                } else if (fieldNote.type() == FieldType.Long) {
                    f.set(testCase, (Long) value);
                } else {
                    if(dateConfig != null  && dateConfig.containsKey(name)){
                        Date date = new Date();
                        try {
                            date = DateUtils.parseDate(value.toString(), pattern);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        f.set(testCase, String.valueOf(date.getTime()));
                    }else{
                        f.set(testCase, value.toString());
                    }
                }
                break;
            }

        }
    }

    private TestCase fillTestCase(Map<String, String> cellMap, TestCase origin, TestCaseRecycleBin recycleBinObj, TestSystem testSystem, Trade trade) throws IllegalAccessException {
        TestCase testCase;
        if (origin != null) {
            testCase = origin;
        } else {
            testCase = new TestCase();
        }
        Object[] keys = cellMap.keySet().toArray();
        for (Object key : keys) {
            String name = currentHead.get((Integer) key);
            if (cellMap.get(key) == null) {
                continue;
            }
            setFieldValue(testCase, name, cellMap.get(key));
        }
        String newTestEditCaseId = null;
        if (origin == null) {
            newTestEditCaseId = baseCaseEditIdOfTaskTrade.get(trade.getResourceID());
            newTestEditCaseId = String.format("%07d", Integer.parseInt(newTestEditCaseId) + 1);
            baseCaseEditIdOfTaskTrade.put(trade.getResourceID(), newTestEditCaseId);
        } else if (recycleBinObj != null) {
            testCase.setCaseEditId(recycleBinObj.getCaseEditId());
        } else {
            newTestEditCaseId = origin.getCaseEditId();
        }
        testCase.setTestsystemResourceID(testSystem.getResourceID());
        testCase.setTesttaskResourceID(StringUtils.isEmpty(testTaskRid) ? null : Long.parseLong(testTaskRid));
        testCase.setTestEnviroment(testEnvironment);
        testCase.setDemandResourceID(StringUtils.isEmpty(demandResourceID) ? null : Long.parseLong(demandResourceID));
        testCase.setCaseEditId(newTestEditCaseId);
        testCase.setTradeResourceID(trade.getResourceID());


        if(testCase.getTestMode() == null){
            testCase.setTestMode(1);
        }
        return testCase;
    }


    private void checkSheetHead(Map<Integer, String> headMap) {
        boolean flag = headMap.values().containsAll(requiredHead);
        if (!flag) {
            if (!errorMsg.isEmpty()) {
                errorMsg += "\n";
            }
            errorMsg += String.format("第【%s】个Sheet的表头格式有误，缺少必要字段,请重新下载模板。", currentSheetIndex);
            throw new RuntimeException("表格缺少必要字段，请重新下载模板");
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
        if (errorMsg.isEmpty()) {
            errorMsg = "导入时发生未知错误";
        }
        throw exception;
    }
}
