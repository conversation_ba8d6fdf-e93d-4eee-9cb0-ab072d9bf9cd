package com.jettech.service.impl;

import java.util.List;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jettech.dao.idao.ITradeTestManagerDao;
import com.jettech.feign.IFeignDataDesignToAssets;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToBugService;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.TradeAndTestManager;
import com.jettech.service.iservice.ISystemModuleService;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.service.iservice.ITestSystemService;
import com.jettech.service.iservice.ITradeTestManagerService;
import com.jettech.util.DataDicUtil;

/**
 * <AUTHOR>
 * @ClassName TradeTestManagerServiceImpl
 * @description TradeTestManagerServiceImpl
 * @create 20123-02-02 
 */
@Service
@Transactional
public class TradeTestManagerServiceImpl extends BaseServiceImpl<TradeAndTestManager> implements ITradeTestManagerService {

    // 日志对象
    private final static Logger logger = LoggerFactory.getLogger(TradeTestManagerServiceImpl.class);

    @Autowired
    private ITradeTestManagerDao iTradeTestManagerDao;
    @Autowired
    private ITestCaseService testCaseService;

    @Autowired
    private ITestSystemService testSystemService;

    @Autowired
    private ISystemModuleService systemModuleService;

    @Autowired
    private IFeignDataDesignToBugService feignDataDesignToBugService;
    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasic;

    @Autowired
    private IFeignDataDesignToAssets feignDataDesignToAssets;

    @Autowired
    private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;

    @Autowired
    private DataDicUtil dataDicUtil;

    private final static String TEST_SYSTEM = "testSystem";
    private final static String SYSTEM_MODULE = "systemModule";
    private final static String TRADE = "trade";

    @PostConstruct
    private void postConstruct() {
        this.baseDao = iTradeTestManagerDao;
    }
    /**
     * 
     * @Description
     * @param tradeResourceID
     * @return
     * tianliping
     * 2023年2月2日
     */
	@Override
	public List<TradeAndTestManager> findByTradeResourceID(Long tradeResourceID) {
		return iTradeTestManagerDao.findByTradeResourceID(tradeResourceID) ;
	}
	@Override
	public List<TradeAndTestManager> findByTradeResourceIDIn(List<Long> ridList) {
		return iTradeTestManagerDao.findByTradeResourceIDIn(ridList);
	}

    


}
