package com.jettech.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.common.dto.FileInfo;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.util.*;
import com.jettech.dao.idao.IDemandDao;
import com.jettech.dao.idao.ITestProjectDao;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToBugService;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.mapper.ITestProjectMapper;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import com.jettech.util.PictureFormatEnum;
import com.jettech.view.TestProjectView;

import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class TestProjectServiceImpl extends BaseHBServiceImpl<TestProject> implements ITestProjectService {
    private static final Logger logger = LoggerFactory.getLogger(TestProjectServiceImpl.class);
    @Autowired
    private ITestProjectDao testProjectDao;
    @Autowired
    private ITestProjectUserService testProjectUserService;
    @Autowired
    private IDemandService demandService;
    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasicService;
    @Autowired
    private ParamConfig paramConfig;
    @Autowired
    private FtpUtil ftpUtil;
    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private IFeignDataDesignToBugService feignDataDesignToBugService;
    @Autowired
    private IProjectGroupService projectGroupService;
    @Autowired
    private ITestProjectService testProjectService;
    @Autowired
    private ITestProjectMapper testProjectMapper;
    @Autowired
    private IDemandUserService demandUserService;
    @Autowired
    private IDemandDao demandDao;
    @Autowired
    private IPerFormCaseService perFormCaseService;
    @Autowired
    private ITestCasequoteService testCasequoteService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private UserinfoService userinfoService;
    @Autowired
    private JettoApiService jettoApiService;
    @Value("${use_file_service}")
    private boolean useFileService;
    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = testProjectDao;
    }

    /**
     * @param pageRequest
     * @param name
     * @param status
     * @param number
     * @param projectType
     * @return
     * @Title: findTestProject
     * @Description: 分页查询项目
     * <AUTHOR>
     * @date 2020年4月21日 上午11:52:01
     */
    @Override
    public Page<TestProject> findTestProject(PageRequest pageRequest, String name, String status, String number, String projectType,
                                             String testMode) {
        List<Integer> projectTypeList = null;
        if (StringUtils.isNotBlank(projectType)) {
            projectTypeList = Arrays.asList(projectType.split(",")).stream().map(s -> (Integer.valueOf(s.trim()))).collect(Collectors.toList());
        }

        //测试方式数据处理
        testMode = BinaryDecimalUtil.DicValToBin(testMode);

        return testProjectDao.findTestProject(pageRequest, name, status, number, projectTypeList, testMode);
    }

    /**
     * @param number
     * @param resourceID
     * @return
     * @Title: validateNumber
     * @Description:验证项目编号是否重复
     * <AUTHOR>
     * @date 2020年4月21日 下午12:44:27
     */
    @Override
    public Result<?> validateNumber(String number, String resourceID) {

        if (number == null || "".equals(number.trim())) {
            return Result.renderError("项目编号为空！");
        }
        number = number.trim();
        if (resourceID != null && !"".equals(resourceID)) {
            //修改信息
            TestProject testProject = findByResourceID(Long.valueOf(resourceID));
            if (testProject != null) {
                TestProject newTestProject = findByNumber(number);
                if (newTestProject == null) {
                    return Result.renderSuccess();
                }
                if (newTestProject.getResourceID() != testProject.getResourceID()) {
                    return Result.renderError("项目编号已存在！");
                }
            }
        } else {
            //新增信息
            TestProject newTestProject = findByNumber(number);
            if (newTestProject != null) {
                return Result.renderError("项目编号已存在！");
            }
        }
        return Result.renderSuccess();
    }

    /**
     * @param number
     * @return
     * @Title: findByNumber
     * @Description: 根据项目编号查询项目
     * <AUTHOR>
     * @date 2020年4月21日 下午12:56:03
     */
    @Override
    public TestProject findByNumber(String number) {
        return testProjectDao.findByNumber(number);
    }

    /**
     * @param resourceID
     * @param name
     * @param number
     * @param status
     * @param statusName
     * @param startDate
     * @param endDate
     * @param managerResourceID
     * @param managerName
     * @param describeInfo
     * @param fileMap
     * @param userNumber
     * @param projectType
     * @return
     * @Title: addAndUpdateTestProject
     * @Description: 新增项目
     * <AUTHOR>
     * @date 2020年4月21日 下午1:12:15
     */
    @Override
    public Result<?> addAndUpdateTestProject(String parentResourceID, String resourceID, String name, String number, String status,
                                             String statusName, String startDate, String endDate, String managerResourceID, String managerName,
                                             String describeInfo, Map<String, InputStream> fileMap, String userNumber, String projectType,
                                             String testMode, MultipartFile[] files) {

        if (name == null || "".equals(name)) {
            return Result.renderError("项目名称为空！");
        }
        if (number == null || "".equals(number)) {
            return Result.renderError("项目编号为空！");
        }
        if (status == null || "".equals(status)) {
            return Result.renderError("项目状态为空！");
        }
        if (statusName == null || "".equals(statusName)) {
            return Result.renderError("项目状态名称为空！");
        }
        if (StringUtils.isBlank(projectType)) {
            return Result.renderError("项目类型为空！");
        }
        TestProject parent = null;
        if (parentResourceID != null && !"".equals(parentResourceID)) {
            parent = findByResourceID(Long.valueOf(parentResourceID));
            if (parent == null) {
                return Result.renderError("父项目不存在！");
            }
            //检查项目是否关联类需求
            List<Demand> demands = demandService.findByTestProjectResourceID(parent.getResourceID());
            if (!demands.isEmpty()) {
                return Result.renderError("项目已经关联需求，不允许新建子项目！");
            }
            //检查项目下是否有项目组
            List<ProjectGroup> groups = projectGroupService.findByTestProjectResourceID(parent.getResourceID(), "");
            if (!groups.isEmpty()) {
                return Result.renderError("项目下已维护项目组，不允许新建子项目！");
            }
        }
        //测试方式数据处理
        testMode = BinaryDecimalUtil.DicValToBin(testMode);
        int val = BinaryDecimalUtil.BinToTen(testMode);

        TestProject resultObject;
        if (resourceID == null || "".equals(resourceID)) {
            //新增项目
            TestProject testProject = new TestProject();
            testProject.setName(name);
            testProject.setNumber(number);
            testProject.setStatus(Integer.valueOf(status));
            testProject.setStatusName(statusName);
            testProject.setStartDate(startDate == null || "".equals(startDate) ? null : DateUtil.getDateFromStr(startDate));
            testProject.setEndDate(endDate == null || "".equals(endDate) ? null : DateUtil.getDateFromStr(endDate));
            testProject.setManagerResourceID(managerResourceID == null || "".equals(managerResourceID) ? null : Long.valueOf(managerResourceID));
            testProject.setManagerName(managerName);
            testProject.setDescribeInfo(describeInfo);
            testProject.setProjectType(Integer.valueOf(projectType));
            testProject.setTestMode(val);

            if (parent != null) {
                testProject.setParentResourceID(parent.getResourceID());
                Long rootResourceID = parent.getRootResourceID();
                testProject.setRootResourceID(rootResourceID == null ? parent.getResourceID() : parent.getRootResourceID());
            }
            resultObject = save(testProject, userNumber);
            resourceID = testProject.getResourceID().toString();
            //项目关联缺陷配置流程
            Result res = findParentProjectByProjectResourceID(resourceID);
            TestProject project = (TestProject) res.getObj();
            feignDataDesignToBugService.saveDefectProcessQuote(resourceID, project.getResourceID().toString(), userNumber);
          /*  if (!fileMap.isEmpty()){
            if(useFileService){
                feignDataDesignToFileService.upload(files,Long.parseLong(resourceID), ObjectTypeEnum.PROJECT.getValue(),true);
            }}*/
        } else {
            //修改项目
            TestProject testProject = findByResourceID(Long.valueOf(resourceID));
            if (testProject == null) {
                return Result.renderError("项目异常！");
            }

            // 已关闭校验逻辑
            if ("已关闭".equals(statusName)) {
                Result result = verifyProjectStatus(resourceID);
                if (!result.isSuccess()) {
                    return result;
                }
            }

            if (!testProject.getName().equals(name)) {
                //修改需求中所属项目名称
                List<Demand> demands = demandService.findByTestProjectResourceID(testProject.getResourceID());
                if (!demands.isEmpty()) {
                    demands.stream().forEach(x -> {
                        x.setTestProjectName(name);
                    });
                    demandService.update(demands, userNumber);
                }
            }
            testProject.setName(name);
            testProject.setNumber(number);
            testProject.setStatus(Integer.valueOf(status));
            testProject.setStatusName(statusName);
            testProject.setStartDate(startDate == null || "".equals(startDate) ? null : DateUtil.getDateFromStr(startDate));
            testProject.setEndDate(endDate == null || "".equals(endDate) ? null : DateUtil.getDateFromStr(endDate));
            testProject.setManagerResourceID(managerResourceID == null || "".equals(managerResourceID) ? null : Long.valueOf(managerResourceID));
            testProject.setManagerName(managerName);
            testProject.setDescribeInfo(describeInfo);
            testProject.setProjectType(Integer.valueOf(projectType));
            testProject.setTestMode(val);
            resultObject = update(testProject, userNumber);
        }
        //新增文件的时候，只传递新增文件的参数
        if (useFileService) {
            if (!fileMap.isEmpty()) {
                feignDataDesignToFileService.upload(files, Long.parseLong(resourceID), ObjectTypeEnum.PROJECT.getValue(), true);
            }
        } else {
            if (!fileMap.isEmpty()) {
                saveAndUpdateAttachment(resourceID, fileMap);
            }
        }
        //添加jettoApi的关联关系
        jettoApiService.saveOrUpdateProjectFolder(resultObject);
        //添加jettoApi的trade_flow_case_folder
        jettoApiService.saveOrUpdateTradeFlowCaseFolder(resultObject);
        // 给前端返回新增或者修改的这个对象
        List<TestProject> testProjectList = new ArrayList<>();
        testProjectList.add(resultObject);
        // 返回项目类型字典值名称
        Map<String, String> testProjectTypeMap = new HashMap<>();
        Result result = feignDataDesignToBasicService.findByName("TESTPROJECTTYPE", HttpRequestUtils.getCurrentRequestToken());//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list = (List<Map<String, String>>) result.getObj();
        list.stream().forEach(x -> {
            testProjectTypeMap.put(x.get("value"), x.get("textName"));
        });
        List<Map<String, Object>> resultList = translateData(testProjectList, testProjectTypeMap);
        Map<String, Object> resultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(resultList) && resultList.size() == 1) {
            resultMap = resultList.get(0);
        }
        return Result.renderSuccess(resultMap);
    }

    private Result verifyProjectStatus(String resourceID) {
        // 查询项目下的所有子项目
        List<TestProject> allChildTestProjectList = new ArrayList<>();
        getAllChildTestProject(resourceID, allChildTestProjectList);
        if (!CollectionUtils.isEmpty(allChildTestProjectList)) {
            boolean hasNotColseProject = allChildTestProjectList.stream().anyMatch(x -> !"已关闭".equals(x.getStatusName()));
            if (hasNotColseProject) {
                return Result.renderError("项目下存在未关闭的项目，不允许关闭");
            }
        }
        // 校验项目关联的需求中若存在未开始、准备中和测试中状态的需求 存在则弹框提示：“项目下存在未开始、进行中或测试中的需求，不允许关闭”
        List<Demand> demands = demandService.findByTestProjectResourceID(Long.valueOf(resourceID));
        List<String> demandStatus = Arrays.asList("未开始", "准备中", "测试中");
        boolean match = demands.stream().anyMatch(x -> demandStatus.contains(x.getTypename()));
        if (match) {
            return Result.renderError("项目下存在未开始、进行中或测试中的需求，不允许关闭");
        }
        if (!CollectionUtils.isEmpty(demands)) {
            List<Long> demandsResourceIDList = demands.stream().map(x -> x.getResourceID()).collect(Collectors.toList());
            List<TestCase> testcaseList = testCaseService.findBydemandResourceIDs(demandsResourceIDList);
            if (!CollectionUtils.isEmpty(testcaseList)) {
                List<Long> testcaseResourceIDList = testcaseList.stream().map(x -> x.getResourceID()).collect(Collectors.toList());
                List<TestCasequote> testCasequoteList = testCasequoteService.findByCaseResourceIDs(testcaseResourceIDList);
                List<String> defectStatus = Arrays.asList("成功", "取消");
                boolean flag = testCasequoteList.stream().allMatch(x -> defectStatus.contains(x.getCaseResult()));
                if (!flag) {
                    return Result.renderError("项目下存在执行结果不为成功或取消的案例，不允许关闭");
                }
            }
        }

        List<ProjectGroup> projectGroupList = projectGroupService.findByTestProjectResourceID(Long.valueOf(resourceID), "1");
        List<String> projectGroupResourceIDList = projectGroupList.stream().map(x -> x.getResourceID().toString()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(projectGroupResourceIDList)) {
            // 校验项目下执行范围中的用例的执行结果是否均为成功或取消状态，若否，则弹框提示：“项目下存在执行结果不为成功或取消的案例，不允许关闭”
            List<PerFormCase> perFormCaseList = perFormCaseService.findByGroupResourceIDs(projectGroupResourceIDList);
            List<String> defectStatus = Arrays.asList("成功", "取消");
            boolean flag = perFormCaseList.stream().allMatch(x -> defectStatus.contains(x.getCaseResult()));
            if (!flag) {
                return Result.renderError("项目下存在执行结果不为成功或取消的案例，不允许关闭");
            }
        }
        // 判断项目下的缺陷是否都为闭环状态，若否则弹框提示：“项目下存在未闭环的缺陷，不允许关闭”
        Result result = feignDataDesignToBugService.findBugsByTestProjectResourceID(resourceID, null);
        List<Map<String, Object>> bugList = (List<Map<String, Object>>) result.getObj();

        //查找多个流程配置闭环状态（目前取多个配置的所有闭环状态，暂时不区分哪个流程）
        List<String> closeStates = demandDao.findBugsClosedStsate(resourceID);
        boolean closeBugFlag = bugList.stream().anyMatch(x -> !closeStates.contains(String.valueOf(x.get("defectState"))));
        if (closeBugFlag) {
            return Result.renderError("项目下存在未闭环的缺陷，不允许关闭");
        }
        return Result.renderSuccess();
    }


    private void getAllChildTestProject(String resourceID, List<TestProject> allChildTestProjectList) {
        List<TestProject> childrenTestProject = testProjectDao.getChildrenTestProject(resourceID);
        if (!CollectionUtils.isEmpty(childrenTestProject)) {
            allChildTestProjectList.addAll(childrenTestProject);
            for (TestProject testProject : childrenTestProject) {
                getAllChildTestProject(testProject.getResourceID().toString(), allChildTestProjectList);
            }
        }
    }

    /**
     * @param resourceID
     * @param fileMap
     * @Title: saveAndUpdateAttachment
     * @Description: 保存修改附件
     * <AUTHOR>
     * @date 2020年4月21日 下午6:48:13
     */
    private void saveAndUpdateAttachment(String resourceID, Map<String, InputStream> fileMap) {

        //代码检查防止路径操作
        resourceID = CleanPathUtil.pathManipulation(resourceID);

        for (Map.Entry<String, InputStream> entry : fileMap.entrySet()) {

            String fileName = entry.getKey();
            InputStream inputStream = entry.getValue();

            String folderPath = "";

            ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
            byte[] buff = new byte[100]; //buff用于存放循环读取的临时数据
            int rc = 0;
            FileOutputStream outPutStream = null;
            try {
                int m = CheckUtil.checkLoop(inputStream.available() / 1000);
                if (m == CheckUtil.MAX_LOOPS) {
                    return;
                }
                while ((rc = inputStream.read(buff, 0, 100)) > 0) {
                    byteStram.write(buff, 0, rc);
                }
                byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果

                if (paramConfig.getIsFtpOn()) {//判断ftp是否打开

                    folderPath = paramConfig.getFtpPath() + "/" + resourceID;

                    //上传FTP
                    ftpUtil.upload(folderPath, fileName, bytes);

                } else {
                    folderPath = paramConfig.getAttachmentPath() + "/" + resourceID;

                    File dir = new File(folderPath);
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }
                    File file = new File(folderPath + "/" + fileName);

                    outPutStream = new FileOutputStream(file);

                    outPutStream.write(bytes);

                    outPutStream.flush();

                    outPutStream.close();

                }
            } catch (Exception e1) {
            } finally {
                try {
                    if (outPutStream != null) {
                        outPutStream.close();
                    }
                } catch (Exception e) {

                }
            }
        }
    }

    /**
     * @param resourceIDs
     * @param userNumber
     * @return
     * @Title: deleteTestProject
     * @Description: 批量删除项目
     * <AUTHOR>
     * @date 2020年4月21日 下午1:50:34
     */
    @Override
    public Result<?> deleteTestProject(String resourceIDs, String userNumber) {

        List<Long> resourceIDList = new ArrayList<>();
        List<Demand> demandList = new ArrayList<>();
        List<TestProject> projects = new ArrayList<>();

        return this.checkDeleteTestProject(resourceIDs, resourceIDList, projects);
    }

    /**
     * @param [resourceIDs, userNumber]
     * @return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @description 确认删除项目
     * @date 2020年11月24日 17:04
     **/
    @Override
    public Result<?> confirmDeleteTestProject(String resourceIDs, String userNumber) {

        List<Long> resourceIDList = new ArrayList<>();
        List<TestProject> projects = new ArrayList<>();

        Result result = this.checkDeleteTestProject(resourceIDs, resourceIDList, projects);
        if (!result.isSuccess()) {
            return result;
        }

        //删除项目
        deleteInBatch(projects, userNumber);

        //删除附件
        if (useFileService) {


            feignDataDesignToFileService.deleteFileByResourceIds(resourceIDs, ObjectTypeEnum.PROJECT.getValue());

        } else {
            if (paramConfig.getIsFtpOn()) {
                //删除ftp文件
                String path = paramConfig.getFtpPath();
                ftpUtil.removeFiles(path, Arrays.asList(resourceIDs.split(",")));
            } else {
                //删除本地文件
                String localPath = paramConfig.getAttachmentPath();
                for (Long rid : resourceIDList) {
                    deleteLocalFile(localPath + File.separator + rid);
                }
            }
        }

        return Result.renderSuccess("删除成功！");
    }

    /**
     * @param [resourceIDs, resourceIDList, projects]
     * @return com.jettech.dto.Result
     * <AUTHOR>
     * @description 校验删除逻辑
     * @date 2020年11月25日 10:20
     **/
    private Result checkDeleteTestProject(String resourceIDs, List<Long> resourceIDList, List<TestProject> projects) {
        if (resourceIDs == null || "".equals(resourceIDs)) {
            return Result.renderError("请勾选项目删除！");
        }
        String[] arr = resourceIDs.split(",");
        List<String> parents = new ArrayList<>();
        for (String str : arr) {
            TestProject tp = findByResourceID(Long.valueOf(str));
            Result childProject = findChildByParentRid(str);
            parents.add(str);
            List<TestProject> child = (List<TestProject>) childProject.getObj();
            if (child != null && child.size() > 0) {
                return Result.renderError("当前项目已经维护子项目不允许删除");
            } else {
                List<Demand> demands = demandService.findByTestProjectResourceID(Long.valueOf(str));
                List<TestProjectUser> testProjectUsers = testProjectUserService.findByTestProjectResourceID(Long.valueOf(str));
                if (demands != null && demands.size() > 0 || testProjectUsers != null && testProjectUsers.size() > 0) {
                    return Result.renderError("当前项目已经维护人员/关联需求，不允许删除");
                }
            }
            List<TestProject> parentbyResourceIDIn = testProjectService.findParentbyResourceIDIn(parents);
            if (parentbyResourceIDIn != null && parentbyResourceIDIn.size() > 0) {
                return Result.renderError("当前项目已经维护子项目不允许删除");
            }
            if (tp != null) {
                String msg = findAllChildProject(tp, projects);
                if (msg != null) {
                    return Result.renderError(msg);
                }
            }
        }
        List<TestProject> parentbyResourceIDIn = testProjectService.findParentbyResourceIDIn(parents);
        if (parentbyResourceIDIn != null && parentbyResourceIDIn.size() > 0) {
            return Result.renderError("当前项目已经维护子项目不允许删除");
        }

        if (!projects.isEmpty()) {
            resourceIDList = projects.stream().map(TestProject::getResourceID).collect(Collectors.toList());
        }
        if (resourceIDList.isEmpty()) {
            return Result.renderSuccess();
        }
        /**
         * 判断当前项目是否在jettoApi使用
         * 判断逻辑：
         删除项目时查询  脚本目录表 script_folder 再查脚本trade_flow表，案例目录表trade_flow_case_folder，再查案例，
         删除系统时，查询1、通讯环境配置表  system_env_arg  2、交易目录表 trade_folder，再查交易表
         */
        boolean flag = jettoApiService.checkProjectDelete(arr[0]);
        if (flag) return Result.renderError("当前项目在已JettoApi使用。");

        return Result.renderSuccess();
    }

    /**
     * @param path
     * @Title: deleteLocalFile
     * @Description: 删除附件
     * <AUTHOR>
     * @date 2020年7月14日 上午10:18:11
     */
    public void deleteLocalFile(String path) {

        File file = new File(path);
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                if (f.exists()) {
                    f.delete();
                }
            }
        }
        file.delete();
    }

    /**
     * @param testProject
     * @param tps
     * @Title: findAllChildProject
     * @Description: 递归查询项目及其子项目
     * <AUTHOR>
     * @date 2020年7月13日 下午8:02:43
     */
    private String findAllChildProject(TestProject testProject, List<TestProject> tps) {

        if (testProject != null) {
            List<ProjectGroup> groups = projectGroupService.findByTestProjectResourceID(testProject.getResourceID(), "");
            if (!groups.isEmpty()) {
                return testProject.getName() + "项目下已维护项目组，不允许删除！";
            }
            tps.add(testProject);
            List<TestProject> childs = testProjectDao.getChildrenTestProject(String.valueOf(testProject.getResourceID()));
            for (TestProject child : childs) {
                String msg = findAllChildProject(child, tps);
                if (msg != null) {
                    return msg;
                }
            }
        }
        return null;

    }

    /**
     * @param resourceIDs
     * @return
     * @Title: validateDeleteTestProject
     * @Description: 删除项目前验证下是否关联用户和需求
     * <AUTHOR>
     * @date 2020年4月21日 下午2:08:46
     */
    @Override
    public Result<?> validateDeleteTestProject(String resourceIDs) {

        if (resourceIDs == null || "".equals(resourceIDs)) {
            return Result.renderError("请勾选项目删除！");
        }
        String[] arr = resourceIDs.split(",");
        for (String str : arr) {
            List<TestProjectUser> users = testProjectUserService.findByTestProjectResourceID(Long.valueOf(str));
            if (!users.isEmpty()) {
                return Result.renderError("删除会同步删除该项目下所有数据，删除操作无法恢复是否确认删除?");
            }
        }
        return Result.renderSuccess();
    }

    /**
     * @return
     * @Title: findAllUser
     * @Description: 查询所有人员
     * <AUTHOR>
     * @date 2020年4月21日 下午4:23:34
     */
    @Override
    public Result<?> findAllUser() {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<JettechUserDTO> list = feignDataDesignToBasicService.findAllPerson(token);
        List<Map<String, Object>> users = new ArrayList<>();
        for (JettechUserDTO user : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("resourceID", user.getResourceID());
            map.put("name", user.getUserName());
            users.add(map);
        }
        return Result.renderSuccess(users);
    }

    /**
     * @param resourceID
     * @return
     * @Title: findAttachmentList
     * @Description: 获取附件列表
     * <AUTHOR>
     * @date 2020年4月21日 下午7:19:39
     */
    @Override
    public Result<?> findAttachmentList(String resourceID) {
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("项目的resourceID为空！");
        }
        //代码检查，防止路径操纵
        resourceID = CleanPathUtil.pathManipulation(resourceID);

        List<String> fileNames = new ArrayList<String>();
        if (paramConfig.getIsFtpOn()) {//判断ftp是否打开

            fileNames = ftpUtil.getFileNames(paramConfig.getFtpPath() + "/" + resourceID);

        } else {

            File dir = new File(paramConfig.getAttachmentPath() + "/" + resourceID);
            if (dir.isDirectory()) {
                File[] fiels = dir.listFiles();
                for (File file : fiels) {
                    String name = file.getName();
                    fileNames.add(name);
                }
            }

        }
        return Result.renderSuccess(fileNames);
    }

    /**
     * @param resourceID
     * @param fileName
     * @return
     * @Title: deleteAttachmentList
     * @Description: 删除附件
     * <AUTHOR>
     * @date 2020年4月21日 下午8:04:45
     */
    @Override
    public Result<?> deleteAttachmentList(String resourceID, String fileName) {

        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("项目的resourceID为空！");
        }
        if (fileName == null || "".equals(fileName)) {
            return Result.renderError("请选择附件删除！");
        }
        TestProject findByResourceID = testProjectService.findByResourceID(Long.valueOf(resourceID));
        if (findByResourceID == null) {
            return Result.renderError("项目不存在");
        }
        if (paramConfig.getIsFtpOn()) {//判断ftp是否打开

            ftpUtil.removeFile(paramConfig.getFtpPath() + "/" + resourceID, fileName);

        } else {

            File dir = new File(paramConfig.getAttachmentPath() + "/" + resourceID);
            if (dir.isDirectory()) {
                File[] fiels = dir.listFiles();
                for (File file : fiels) {
                    String name = file.getName();
                    if (name.equals(fileName)) {
                        file.delete();
                    }
                }
            }
        }
        return Result.renderSuccess();
    }

    /**
     * @param resourceID
     * @param fileName
     * @return
     * @Title: getFileData
     * @Description: 获取附件
     * <AUTHOR>
     * @date 2020年4月21日 下午8:18:35
     */
    @Override
    public byte[] getFileData(String resourceID, String fileName) {
        //代码检查，防止路径操作
        resourceID = CleanPathUtil.pathManipulation(resourceID);
        //fileName = CleanPathUtil.pathManipulation(fileName);
        InputStream in = null;
        if (paramConfig.getIsFtpOn()) {//判断ftp是否打开
            in = ftpUtil.startDown(paramConfig.getFtpPath() + "/" + resourceID + "/" + fileName);
            ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
            byte[] buff = new byte[1024]; //buff用于存放循环读取的临时数据
            int rc = 0;
            try {
                while ((rc = in.read(buff)) != -1) {
                    byteStram.write(buff, 0, rc);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果

            return bytes;
        } else {
            File file = new File(paramConfig.getAttachmentPath() + "/" + resourceID + "/" + fileName);
            InputStream inputStream = null;
            if (file.exists()) {
                try {
                    inputStream = new FileInputStream(file);
                    byte[] data = new byte[(int) file.length()];
                    inputStream.read(data);
                    inputStream.close();
                    return data;
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        if (inputStream != null) {
                            inputStream.close();
                        }
                    } catch (Exception e) {

                    }
                }
            }
        }
        return null;
    }

    /**
     * @param resourceID
     * @param fileName
     * @return
     * @Title: previewAttachment
     * @Description: 预览图片
     * <AUTHOR>
     * @date 2020年4月21日 下午8:41:29
     */
    @Override
    public Result<?> previewAttachment(String resourceID, String fileName) {

        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("项目的resourceID为空！");
        }
        if (fileName == null || "".equals(fileName)) {
            return Result.renderError("附件名称为空！");
        }
        String[] arr = fileName.split("\\.");
        byte[] data = null;
        if (EnumUtils.isValidEnum(PictureFormatEnum.class, arr[arr.length - 1].toUpperCase())) {
            try {
                if (useFileService) {
                    Result<List<FileInfo>> record = feignDataDesignToFileService.getFileInfoList(Long.parseLong(resourceID), ObjectTypeEnum.PROJECT.getValue());
                    List<FileInfo> fileInfos = record.getObj();
                    if (!CollectionUtils.isEmpty(fileInfos)) {
                        for (FileInfo item : fileInfos) {
                            if (item.getOriginalFilename().equals(fileName)) {
                                byte[] data1 = feignDataDesignToFileService.getContent(item.getResourceId());
                                //接口得到的文件数组需要转码。原因没有弄清楚
                                data = Base64.getMimeDecoder().decode(data1);
                            }
                        }
                    }
                } else {
                    data = getFileData(resourceID, fileName);
                }
                Map<String, Object> map = new HashMap<>();
                map.put("extname", "data:image/" + arr[arr.length - 1] + ";base64,");
                map.put("data", data);
                return Result.renderSuccess(map);
            } catch (Exception e) {
                e.printStackTrace();
                return Result.renderError("获取附件失败！");
            }
        } else {
            return Result.renderError("该格式附件不支持预览！");
        }
    }

    /**
     * @return
     * @Title: findAllTestProjectNameAndResourceID
     * @Description: 查询所有项目name和resourceID
     * <AUTHOR>
     * @date 2020年4月22日 下午3:38:12
     */
    @Override
    public Result<?> findAllTestProjectNameAndResourceID() {

        List<Map<String, Object>> result = new ArrayList<>();

        List<TestProject> list = findAll();
        for (TestProject testProject : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", testProject.getName());
            map.put("resourceID", testProject.getResourceID());
            result.add(map);
        }
        return Result.renderSuccess(result);
    }

    /**
     * @return
     * @Title: findNoCloseTestProjectNameAndResourceID
     * @Description: 查询非关闭项目name和resourceID
     * <AUTHOR>
     * @date 2020年12月29日 下午3:38:12
     */
    @Override
    public Result<?> findNoCloseTestProjectNameAndResourceID() {

        List<Map<String, Object>> result = new ArrayList<>();

        List<TestProject> list = findAll();
        for (TestProject testProject : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", testProject.getName());
            map.put("resourceID", testProject.getResourceID());
            //不返回关闭状态项目名
            if (testProject.getStatusName().equals("已关闭")) {
                continue;
            }
            result.add(map);
        }
        return Result.renderSuccess(result);
    }

    /**
     * @param name
     * @return
     * @Title: findByName
     * @Description: 根据项目名称查询项目
     * <AUTHOR>
     * @date 2020年4月22日 下午8:17:52
     */
    @Override
    public List<TestProject> findByName(String name) {
        return testProjectDao.findByName(name);
    }

    /**
     * 根据分页数据查询项目下需求的进度
     * bao_qiuxu
     */
    @Override
    public Map<String, Integer> selectProjectProgress(List<TestProject> tpList) {
        if (tpList == null || tpList.isEmpty()) {
            return null;
        }
        List<String> list = tpList.stream().map(tp -> {
            return tp.getResourceID().toString();
        }).collect(Collectors.toList());
        List<Demand> listD = demandService.findByTestProjectResourceIDs(list);
        Map<String, Integer> map = new HashMap<String, Integer>();
        for (String rid : list) {
            map.put(rid, 0);
            int i = 0;
            for (Demand dd : listD) {
                if (rid.equals(dd.getTestProjectResourceID().toString())) {
                    if ("2".equals(dd.getType()) || "3".equals(dd.getType())) {
                        map.put(rid, 50 + map.get(dd.getTestProjectResourceID().toString()));
                    } else if (!"1".equals(dd.getType())) {
                        map.put(rid, 100 + map.get(dd.getTestProjectResourceID().toString()));
                    }
                    i++;
                }
            }
            double jd = 0.0;
            if (i > 0) {
                jd = map.get(rid) / i;
            }
            map.put(rid, (int) Math.rint(jd));
        }
        return map;
    }

    /**
     * findDemandByTestProjectResourceID
     * 根据ResourceID查询所有需求及需求的案例和缺陷
     * bao_qiuxu
     *
     * @param resourceID
     */
    @Override
    public Result<?> findDemandByTestProjectResourceID(String resourceID) {
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("项目的resourceID为空！");
        }
        List<Demand> listD = demandService.findByTestProjectResourceID(Long.valueOf(resourceID));
        if (listD == null || listD.isEmpty()) {
            return Result.renderSuccess();
        }
        List<Map<String, Object>> result = new ArrayList<>();
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<String> list = listD.stream().map(demand -> {
            return demand.getResourceID().toString();
        }).collect(Collectors.toList());
        // 查询需求下的案例总数
        List<Map<String, Object>> totalCount = testCaseService.findTotalNumberByDemandRids(list);
        //查询需求下的案例的执行数
        List<Map<String, Object>> totalZXCount = testCaseService.findTotalZXNumberByDemandRids(list);
        // 查看当前需求下是否还存在缺陷
        List<Map<String, Object>> bugList = feignDataDesignToBugService.findBugsByDemandResourceIDs(list, token);

        Map<String, Object> totalMap = totalCount.stream().collect(Collectors.toMap(e -> e.get("demandResourceID").toString(), e -> e.get("totalCount")));
        Map<String, Object> totalZXMap = totalZXCount.stream().collect(Collectors.toMap(e -> e.get("resourceID").toString(), e -> e.get("num")));
        Map<String, Object> bugMap = bugList.stream().collect(Collectors.toMap(e -> e.get("demandResourceID").toString(), e -> e.get("num")));
        for (Demand de : listD) {
            Map<String, Object> dmap = new HashMap<String, Object>();
            dmap.put("resourceID", de.getResourceID());
            dmap.put("name", de.getName());
            dmap.put("typeName", de.getTypename());
            dmap.put("total", totalMap.get(de.getResourceID().toString()) == null ? 0 : totalMap.get(de.getResourceID().toString()));
            dmap.put("totalZX", totalZXMap.get(de.getResourceID().toString()) == null ? 0 : totalZXMap.get(de.getResourceID().toString()));
            dmap.put("bugCount", bugMap.get(de.getResourceID().toString()) == null ? 0 : bugMap.get(de.getResourceID().toString()));
            result.add(dmap);
        }
        return Result.renderSuccess(result);
    }

    /**
     * @param userNumber
     * @return
     * @Title: findAllTestProjectByUser
     * @Description: 查询工作台当前用户所有项目name和resourceID
     * <AUTHOR>
     * @date 2020年6月19日
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<List<Map<String, Object>>> findAllTestProjectByUser(String userNumber) {
        List<Map<String, Object>> result = new ArrayList<>();

        List<TestProject> list = testProjectDao.findAllTestProjectByUser(userNumber);
        for (TestProject testProject : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", testProject.getName());
            map.put("resourceID", testProject.getResourceID());
            result.add(map);
        }
        return Result.renderSuccess(result);
    }

    /**
     * @return
     * @Title: findAllParent
     * @Description: 查询所有父项目
     * <AUTHOR>
     * @date 2020年7月13日 下午5:07:55
     */
    @Override
    public List<TestProject> findAllParent() {
        return testProjectDao.findAllParent();
    }

    /**
     * @return
     * @Title: findChildTestProjectByResourceID
     * @Description: 查询子项目
     * <AUTHOR>
     * @date 2020年7月13日 下午5:35:09
     */
    @Override
    public List<TestProject> findChildTestProjectByResourceID(String resourceID) {
        return testProjectDao.findChildTestProjectByResourceID(resourceID);
    }

    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: loadParentDemand
     * @description: 加载所有父级节点的项目
     * <AUTHOR>
     * @date 2020/7/13 17:06
     */
    @Override
    public Result loadParentTestProject(String name) {
        List<TestProject> list = testProjectDao.loadParentTestProject(name);
        if (list != null && !list.isEmpty()) {
            List<String> lists = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                TestProject testProject = list.get(i);
                lists.add(testProject.getResourceID().toString());
            }
            List<TestProject> pTestproject = this.findParentbyResourceIDIn(lists);
            List<HashMap<String, Object>> valList = list.stream().map(e -> {
                HashMap<String, Object> map = new HashMap<>();
                map.put("label", e.getName());
                map.put("value", e.getResourceID());
                for (TestProject tp : pTestproject) {
                    if (tp.getParentResourceID().equals(e.getResourceID())) {
                        map.put("haschild", true);
                        break;
                    }
                    map.put("haschild", false);
                }
                return map;
            }).collect(Collectors.toList());
            return Result.renderSuccess(valList);
        }
        return Result.renderSuccess(new ArrayList<HashMap<String, Object>>());
    }


    /**
     * @param "[resourceID]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getChildrenTestProject
     * @description: 父节点查询所属项目
     * <AUTHOR>
     * @date 2020/7/13 17:37
     */
    @Override
    public Result getChildrenTestProject(String resourceID) {
        List<TestProject> list = testProjectDao.getChildrenTestProject(resourceID);
        if (list != null && !list.isEmpty()) {
            List<String> lists = list.stream().map(e -> e.getResourceID().toString()).collect(Collectors.toList());
            List<TestProject> pTestproject = this.findParentbyResourceIDIn(lists);
            Map<Long, List<TestProject>> childrenList = pTestproject.stream().collect(Collectors.groupingBy(TestProject::getParentResourceID));
            List<HashMap<String, Object>> valList = list.stream().map(e -> {
                HashMap<String, Object> map = new HashMap<>();
                map.put("label", e.getName());
                map.put("value", e.getResourceID());
                if (childrenList.get(e.getResourceID()) != null) {
                    map.put("haschild", true);
                } else {
                    map.put("haschild", false);
                }
                return map;
            }).collect(Collectors.toList());
            return Result.renderSuccess(valList);
        }
        return Result.renderSuccess(new ArrayList<HashMap<String, Object>>());
    }

    /**
     * @param userNumber
     * @return
     * @Title: findProjectAndGroupTree
     * @Description: 查询项目和项目组树
     * <AUTHOR>
     * @date 2020年7月14日 上午11:04:38
     */
    @Override
    public Result findProjectAndGroupTree(String userNumber, String groupType) {

        Map<Long, String> tpMap = new HashMap<>();//用户关联的项目
        Set<String> rootSet = new HashSet<>();//根结点
        //查询用户关联的项目
        List<TestProject> testProjects = testProjectDao.findByUserNumber(userNumber, "已关闭");
        if (testProjects.isEmpty()) {
            return Result.renderSuccess(null);
        }
        /*
        // 过滤掉已关闭的项目
        List<TestProject> testProjectsNotEnd = testProjects.stream().filter(x -> !"已关闭".equals(x.getStatusName())).collect(Collectors.toList());
         */
        for (TestProject tp : testProjects) {
            tpMap.put(tp.getResourceID(), "");
            rootSet.add(tp.getRootResourceID() == null ? String.valueOf(tp.getResourceID()) : String.valueOf(tp.getRootResourceID()));
        }
        //查询所有根结点
        List<TestProject> rootProjects = findByResourceIDIn(new ArrayList<>(rootSet));

        Map<Long, List<TestProject>> parent_projectMap = new HashMap<>();//父-子项目集合
        Map<Long, TestProject> allProjectMap = new HashMap<>();
        // 包含根项目和所有子项目  用于查询所有项目的关联项目组
        List<String> allProjectRIDs = new ArrayList<>(rootSet);
        //查询所有根结点下的所有项目
        List<TestProject> allList = testProjectDao.findByRootResourceIDIn(new ArrayList<>(rootSet), "已关闭");
        /*
        if(!allList.isEmpty()){
            allList = allList.stream().filter(x ->!"已关闭".equals(x.getStatusName())).collect(Collectors.toList());
        }*/
        for (TestProject project : allList) {
            allProjectMap.put(project.getResourceID(), project);
            allProjectRIDs.add(project.getResourceID().toString());
            List<TestProject> childList = new ArrayList<>();
            if (!parent_projectMap.isEmpty() && parent_projectMap.containsKey(project.getParentResourceID())) {
                childList = parent_projectMap.get(project.getParentResourceID());
            }
            childList.add(project);
            parent_projectMap.put(project.getParentResourceID(), childList);
        }

        // 查询所有项目的项目群
        List<ProjectGroup> allProjectGroupList = this.projectGroupService.findByProjectRIDsAndGroupType(allProjectRIDs, groupType);

        //构建树
        List<Map<String, Object>> tree = new ArrayList<>();
        for (TestProject rootProject : rootProjects) {
            Map<String, Object> node = new HashMap<>();
            buildChildProjectNode(rootProject, parent_projectMap, tpMap, node, groupType, allProjectGroupList);
            tree.add(node);
        }

        return Result.renderSuccess(tree);
    }


    /**
     * @return
     * @Title: buildChildProjectNode
     * @Description: 递归查询子项目
     * <AUTHOR>
     * @date 2020年7月14日 下午1:57:16
     */
    private Boolean buildChildProjectNode(TestProject testProject, Map<Long, List<TestProject>> parent_childMap, Map<Long, String> tpMap, Map<String, Object> node, String groupType, List<ProjectGroup> allProjectGroupList) {

        List<Map<String, Object>> childNodes = new ArrayList<>();
        if (!tpMap.isEmpty() && tpMap.containsKey(testProject.getResourceID())) {
            findAllChilds(testProject, parent_childMap, node, groupType, allProjectGroupList);
            return true;
        }
        List<TestProject> childList = parent_childMap.get(testProject.getResourceID());
        if (childList == null || childList.isEmpty()) {
            return false;
        }
        //继续查找子项目
        Boolean flag = false;
        Boolean isSmallpoints = false;
        for (TestProject child : childList) {
            Map<String, Object> childNode = new HashMap<>();
            Boolean b = buildChildProjectNode(child, parent_childMap, tpMap, childNode, groupType, allProjectGroupList);
            if (b && !childNode.isEmpty()) {
                childNodes.add(childNode);
            }
            if (b) {
                flag = true;
            }
        }
        if (flag) {
            node.put("resourceID", testProject.getResourceID());
            node.put("name", testProject.getName());
            node.put("parentResourceID", testProject.getParentResourceID());
            node.put("nodeType", "project");
            node.put("childList", childNodes);
            node.put("isSmallpoints", isSmallpoints);
            node.put("number", testProject.getNumber());

        }
        return flag;

    }

    private void findAllChilds(TestProject testProject, Map<Long, List<TestProject>> parent_childMap, Map<String, Object> node, String groupType, List<ProjectGroup> allProjectGroupList) {

        Boolean isSmallpoints = false;
        List<Map<String, Object>> childNodes = new ArrayList<>();
        List<TestProject> childList = parent_childMap.get(testProject.getResourceID());
        if (childList == null || childList.isEmpty()) {
            isSmallpoints = true;
            //查找项目组
            childNodes = buildProjectGroupTree(testProject.getResourceID(), groupType, allProjectGroupList);
        } else {
            //继续查找子项目
            for (TestProject child : childList) {
                Map<String, Object> childNode = new HashMap<>();
                findAllChilds(child, parent_childMap, childNode, groupType, allProjectGroupList);
                if (childNode != null && !childNode.isEmpty()) {
                    childNodes.add(childNode);
                }
            }
        }
        node.put("resourceID", testProject.getResourceID());
        node.put("name", testProject.getName());
        node.put("parentResourceID", testProject.getParentResourceID());
        node.put("nodeType", "project");
        node.put("childList", childNodes);
        node.put("isSmallpoints", isSmallpoints);
    }

    /**
     * @return
     * @Title: buildProjectGroupTree
     * @Description: 构建项目组树
     * <AUTHOR>
     * @date 2020年7月14日 下午1:57:40
     */
    private List<Map<String, Object>> buildProjectGroupTree(Long resourceID, String groupType, List<ProjectGroup> allProjectGroupList) {

        List<Map<String, Object>> tree = new ArrayList<>();

        Map<Long, List<ProjectGroup>> parent_childMap = new HashMap<>();//父-（子）项目组集合
        //查询子项目下的所有项目组
//        List<ProjectGroup> projectGroups = projectGroupService.findByTestProjectResourceID(resourceID, groupType);
        List<ProjectGroup> projectGroups = allProjectGroupList.stream().filter(item -> resourceID.equals(item.getTestProjectResourceID())).collect(Collectors.toList());
        for (ProjectGroup projectGroup : projectGroups) {
            List<ProjectGroup> childList = new ArrayList<>();
            Long parentResourceID = projectGroup.getParentResourceID() == null ? resourceID : projectGroup.getParentResourceID();
            if (!parent_childMap.isEmpty() && parent_childMap.containsKey(parentResourceID)) {
                childList = parent_childMap.get(parentResourceID);
            }
            childList.add(projectGroup);
            parent_childMap.put(parentResourceID, childList);
        }

        List<ProjectGroup> roots = parent_childMap.get(resourceID);
        if (roots != null) {
            for (ProjectGroup root : roots) {
                Map<String, Object> node = buildChildProjectGroupNode(root, parent_childMap);
                if (node != null && !node.isEmpty()) {
                    tree.add(node);
                }
            }
        }
        return tree;
    }

    /**
     * @return
     * @Title: buildChildProjectGroupNode
     * @Description: 递归构建项目组子节点
     * <AUTHOR>
     * @date 2020年7月14日 下午1:57:50
     */
    private Map<String, Object> buildChildProjectGroupNode(ProjectGroup projectGroup, Map<Long, List<ProjectGroup>> parent_childMap) {

        List<ProjectGroup> childList = parent_childMap.get(projectGroup.getResourceID());
        List<Map<String, Object>> childNodes = new ArrayList<>();
        if (childList != null) {
            for (ProjectGroup child : childList) {
                Map<String, Object> childNode = buildChildProjectGroupNode(child, parent_childMap);
                if (childNode != null && !childNode.isEmpty()) {
                    childNodes.add(childNode);
                }
            }
        }
        Map<String, Object> node = new HashMap<>();
        node.put("resourceID", projectGroup.getResourceID());
        node.put("name", projectGroup.getName());
        node.put("parentResourceID", projectGroup.getParentResourceID());
        node.put("testProjectResourceID", projectGroup.getTestProjectResourceID());
        node.put("nodeType", "group");
        node.put("childList", childNodes);
        node.put("isSmallpoints", projectGroup.isSmallPoints());
        return node;
    }

    /**
     * 查询项目是否有子项目
     *
     * @param resourceIDs
     * @return
     */
    @Override
    public List<TestProject> findParentbyResourceIDIn(List<String> resourceIDs) {
        return testProjectDao.findParentbyResourceIDIn(resourceIDs);
    }

    /**
     * @param resourceID
     * @return
     * @Title: addTestProjectValidate
     * @Description: 新增项目前校验
     * <AUTHOR>
     * @date 2020年7月20日 上午10:44:32
     */
    @Override
    public Result<String> addTestProjectValidate(Long resourceID) {

        TestProject testProject = findByResourceID(resourceID);
        if (testProject == null) {
            return Result.renderError("项目不存在！");
        }
        //检查项目是否关联类需求
        List<Demand> demands = demandService.findByTestProjectResourceID(testProject.getResourceID());
        if (!demands.isEmpty()) {
            return Result.renderError("项目已经关联需求，不允许新建子项目！");
        }
        //检查项目下是否有项目组
        List<ProjectGroup> groups = projectGroupService.findByTestProjectResourceID(testProject.getResourceID(), "");
        if (!groups.isEmpty()) {
            return Result.renderError("项目下已维护项目组，不允许新建子项目！");
        }
        return Result.renderSuccess();
    }

    /**
     * @Method: initTestProjectTree
     * @Description: 加载项目树  2021/1/5 只展示未关闭的项目
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/7/20
     */
    @Override
    public Result initTestProjectTree() {
        //parent
        List<TestProject> list = testProjectDao.loadParentTestProject(null);
        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (TestProject tp : list) {
            //不返回关闭状态项目名
            if (tp.getStatusName().equals("已关闭")) {
                continue;
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("label", tp.getName());
            map.put("value", tp.getResourceID());
            List<Map<String, Object>> childrenList = getChildrenList(tp.getResourceID());
            if (!childrenList.isEmpty() && childrenList.size() > 0) {
                map.put("children", childrenList);
            }
            returnList.add(map);
        }
        return Result.renderSuccess(returnList);
    }

    private List<Map<String, Object>> getChildrenList(Long resourceID) {
        List<Map<String, Object>> returnList = new ArrayList<Map<String, Object>>();
        List<TestProject> list = testProjectDao.findChildTestProjectByResourceID(resourceID.toString());
        for (TestProject tp : list) {
            //不返回关闭状态项目名
            if (tp.getStatusName().equals("已关闭")) {
                continue;
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("label", tp.getName());
            map.put("value", tp.getResourceID());
            List<Map<String, Object>> childrenList = getChildrenList(tp.getResourceID());
            if (!childrenList.isEmpty() && childrenList.size() > 0) {
                map.put("children", childrenList);
            }
            returnList.add(map);
        }
        return returnList;
    }


    /**
     * @Method: initProjectTree
     * @Description: 缺陷流程配置左侧树初始化
     * @Param: " [name] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/8/17
     */
    @Override
    public Result initProjectTree(String name) {
        //查询所有
        if (StringUtils.isBlank(name)) {
            return this.initTestProjectTree();
        } else {
            List<TestProject> list = testProjectDao.loadParentTestProject(name);
            if (list == null || list.isEmpty()) return Result.renderSuccess();
            List<String> parentRids = new ArrayList<>();
            for (TestProject tp : list) {
                parentRids.add(getParentResourceID(tp));
            }

            List<TestProject> parentList = this.findByResourceIDIn(parentRids);
            ArrayList<Map<String, Object>> returnList = new ArrayList<>();
            for (TestProject tp : parentList) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("label", tp.getName());
                map.put("value", tp.getResourceID());
                List<Map<String, Object>> childrenList = getChildrenList(tp.getResourceID());
                if (!childrenList.isEmpty() && childrenList.size() > 0) {
                    map.put("children", childrenList);
                }
                returnList.add(map);
            }
            return Result.renderSuccess(returnList);
        }
    }

    /**
     * @Method: getParentResourceID
     * @Description: 递归查询父节点rid
     * @Param: " [list] "
     * @return: java.util.List<java.lang.String>
     * @Author: wws
     * @Date: 2020/8/17
     */
    private String getParentResourceID(TestProject tp) {
        if (tp.getParentResourceID() == null && tp.getRootResourceID() == null) {
            return tp.getResourceID().toString();
        } else {
            TestProject parent = this.findByResourceID(tp.getParentResourceID());
            return getParentResourceID(parent);
        }
    }

    /**
     * @Title: findProjectTree
     * @Description: 查询项目树
     * <AUTHOR>
     * @date 2020年8月21日
     */
    @Override
    public List<Map<String, Object>> findProjectTree(String userNumber, String groupType) {
        Map<Long, String> tpMap = new HashMap<>();//用户关联的项目
        Set<String> rootSet = new HashSet<>();//根结点
        //查询用户关联的项目
        List<TestProject> testProjects = testProjectDao.findByUserNumber(userNumber, "已关闭");
        /*
        //筛选所有非已关闭的项目
        if(!testProjects.isEmpty()){
            testProjects = testProjects.stream().filter(x ->!"已关闭".equals(x.getStatusName())).collect(Collectors.toList());
        }*/
        if (null == testProjects || testProjects.isEmpty()) {
            return new ArrayList<>();
        }
        for (TestProject tp : testProjects) {
            tpMap.put(tp.getResourceID(), "");
            rootSet.add(tp.getRootResourceID() == null ? String.valueOf(tp.getResourceID()) : String.valueOf(tp.getRootResourceID()));
        }
        //查询所有根结点
        List<TestProject> rootProjects = findByResourceIDIn(new ArrayList<>(rootSet));

        Map<Long, List<TestProject>> parent_projectMap = new HashMap<>();//父-子项目集合
        Map<Long, TestProject> allProjectMap = new HashMap<>();
        //查询所有根结点下的所有项目
        List<TestProject> allList = testProjectDao.findByRootResourceIDIn(new ArrayList<>(rootSet), "已关闭");
        /*
        //筛选所有非已关闭的项目
        if(!testProjects.isEmpty()){
            allList = allList.stream().filter(x ->!"已关闭".equals(x.getStatusName())).collect(Collectors.toList());
        }*/
        for (TestProject project : allList) {
            allProjectMap.put(project.getResourceID(), project);

            List<TestProject> childList = new ArrayList<>();
            if (!parent_projectMap.isEmpty() && parent_projectMap.containsKey(project.getParentResourceID())) {
                childList = parent_projectMap.get(project.getParentResourceID());
            }
            childList.add(project);
            parent_projectMap.put(project.getParentResourceID(), childList);
        }

        //构建树
        List<Map<String, Object>> tree = new ArrayList<>();
        for (TestProject rootProject : rootProjects) {
            Map<String, Object> node = new HashMap<>();
            buildChildProjectNodeTree(rootProject, parent_projectMap, tpMap, node, groupType);
            tree.add(node);
        }

        return tree;
    }

    /**
     * @return
     * @Title: buildChildProjectNode
     * @Description: 递归查询子项目
     * <AUTHOR>
     * @date 2020年7月14日 下午1:57:16
     */
    private Boolean buildChildProjectNodeTree(TestProject testProject, Map<Long, List<TestProject>> parent_childMap, Map<Long, String> tpMap, Map<String, Object> node, String groupType) {

        List<Map<String, Object>> childNodes = new ArrayList<>();
        if (!tpMap.isEmpty() && tpMap.containsKey(testProject.getResourceID())) {
            findAllChildsTree(testProject, parent_childMap, node, groupType);
            return true;
        }
        List<TestProject> childList = parent_childMap.get(testProject.getResourceID());
        if (childList == null || childList.isEmpty()) {
            return false;
        }
        //继续查找子项目
        Boolean flag = false;
        Boolean isSmallpoints = true;
        for (TestProject child : childList) {
            Map<String, Object> childNode = new HashMap<>();
            Boolean b = buildChildProjectNodeTree(child, parent_childMap, tpMap, childNode, groupType);
            if (b && !childNode.isEmpty()) {
                childNodes.add(childNode);
            }
            if (b) {
                flag = true;
            }
        }
        if (flag) {
            node.put("value", testProject.getResourceID());
            node.put("label", testProject.getName());
            if (!childNodes.isEmpty()) {
                node.put("children", childNodes);
            }
            node.put("isSmallpoints", isSmallpoints);
        }
        return flag;

    }

    private void findAllChildsTree(TestProject testProject, Map<Long, List<TestProject>> parent_childMap, Map<String, Object> node, String groupType) {

        Boolean isSmallpoints = true;
        List<Map<String, Object>> childNodes = new ArrayList<>();
        List<TestProject> childList = parent_childMap.get(testProject.getResourceID());
        if (childList == null || childList.isEmpty()) {
            isSmallpoints = false;
            //查找项目组
        } else {
            //继续查找子项目
            for (TestProject child : childList) {
                Map<String, Object> childNode = new HashMap<>();
                findAllChildsTree(child, parent_childMap, childNode, groupType);
                if (childNode != null && !childNode.isEmpty()) {
                    childNodes.add(childNode);
                }
            }
        }
        node.put("value", testProject.getResourceID());
        node.put("label", testProject.getName());
        if (!childNodes.isEmpty()) {
            node.put("children", childNodes);
        }
        node.put("isSmallpoints", isSmallpoints);
    }

    /**
     * 获取要查询的子级节点项目组
     *
     * @param nodeType
     * @param resourceID
     */
    @Override
    public List<String> getNodeList(String nodeType, String resourceID, String groupType, String userNumber) {
        List<String> result = new ArrayList<>();
        List<String> resourceIDs = new ArrayList<>();
//        List<String> resourceIdBynumberList=new ArrayList<>();
        //查询用户关联的项目
//        List<TestProject> testProjects = testProjectService.findByUserNumber(userNumber);
//        if (testProjects.isEmpty()) {
//            return result;
//        }else{
//            testProjects.stream().forEach(e->{resourceIdBynumberList.add(e.getResourceID().toString());});
//        }
        if ("project".equals(nodeType)) {
            List<TestProject> allProjectList = new ArrayList<>();
            TestProject tp = this.findByResourceID(Long.valueOf(resourceID));
            if (tp != null) {
                allProjectList.add(tp);
                if (tp.getParentResourceID() != null) {
                    //查询的是子项目的所有子项目
                    // 通过子项目的顶级项目获取所有的项目数据
                    allProjectList.addAll(this.findByRootResourceID(tp.getRootResourceID().toString()));
                } else {
                    // 查询的是顶层项目的所有子项目
                    allProjectList.addAll(this.findByRootResourceID(resourceID));
                }
                findAllchildProject(tp, allProjectList, resourceIDs);
//            resourceIDs.retainAll(resourceIdBynumberList);
                result = getChildGroup(resourceIDs, groupType);
            }
        } else if ("group".equals(nodeType)) {
            ProjectGroup pg = projectGroupService.findByResourceID(Long.valueOf(resourceID));
            //根据当前项目组所属项目获取项目下所有的项目组
            List<ProjectGroup> projectAllGroupList = projectGroupService.findByTestProjectResourceID(pg.getTestProjectResourceID(), groupType);
            findAllchildGroup(pg, result, groupType, projectAllGroupList);
        }
        return result;
    }

    /**
     * 获取父和子节点的项目数据
     *
     * @param userNumber
     * @param name
     * @param status
     * @param number
     * @param projectType
     */
    @Override
    public Result findTestProjectAndChildren(String userNumber, String name, String status, PageRequest pageRequest, String number, String projectType,
                                             String testMode) {
        List<Integer> projectTypeList = null;
        if (StringUtils.isNotBlank(projectType)) {
            projectTypeList = Arrays.asList(projectType.split(",")).stream().map(s -> (Integer.valueOf(s.trim()))).collect(Collectors.toList());
        }
        Map<Long, String> tpMap = new HashMap<>();//用户关联的项目
        Set<String> rootSet = new HashSet<>();//根结点
        //查询用户关联的项目
//        List<TestProject> testProjectList = testProjectDao.findByUserNumber(userNumber);
//        if (testProjectList.isEmpty()) {
//            return Result.renderSuccess(null);
//        }
        // 返回项目类型字典值名称
        Map<String, String> testProjectTypeMap = new HashMap<>();
        Result result = feignDataDesignToBasicService.findByName("TESTPROJECTTYPE", HttpRequestUtils.getCurrentRequestToken());//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list = (List<Map<String, String>>) result.getObj();
        list.stream().forEach(x -> {
            testProjectTypeMap.put(x.get("value"), x.get("textName"));
        });

        // 返回测试方式字典名称
        Result result1 = feignDataDesignToBasicService.findByName("TESTMODE", HttpRequestUtils.getCurrentRequestToken());
        List<Map<String, Object>> testModeList = (List<Map<String, Object>>) result1.getObj();
        Map<String, String> testModeMap = testModeList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));

        //测试方式数据处理
        testMode = BinaryDecimalUtil.DicValToBin(testMode);
        if (StringUtils.isBlank(name) && StringUtils.isBlank(status) && StringUtils.isBlank(number) && StringUtils.isBlank(projectType)) {
            Page<TestProject> pages = testProjectService.findTestProject(pageRequest, name, status, number, projectType, testMode);
            List<Map<String, Object>> resultList = translateData(pages.getContent(), testProjectTypeMap);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("content", resultList);
            resultMap.put("pageable", pages.getPageable());
            resultMap.put("sort", pages.getSort());
            resultMap.put("total", pages.getTotalElements());
            return Result.renderSuccess(resultMap);
        }

        List<TestProject> testProjects = testProjectMapper.findTestProjectByCondition(name, status, number, projectTypeList, testMode);
        for (TestProject tp : testProjects) {
            tpMap.put(tp.getResourceID(), "");
            rootSet.add(tp.getRootResourceID() == null ? String.valueOf(tp.getResourceID()) : String.valueOf(tp.getRootResourceID()));
        }
        if (testProjects == null || testProjects.size() == 0) {
            Map<String, Object> resultMapOne = new HashMap<>();
            resultMapOne.put("content", null);
            resultMapOne.put("sort", pageRequest.getSort());
            resultMapOne.put("total", testProjects.size());
            return Result.renderSuccess(resultMapOne);
        }
        //查询所有根结点
        List<TestProject> rootProjects = findByResourceIDIn(new ArrayList<>(rootSet));
         Collections.sort(rootProjects, (obj1, obj2) -> obj1.getCreateTime().compareTo(obj2.getCreateTime()));

        Map<Long, List<TestProject>> parent_projectMap = new HashMap<>();//父-子项目集合
        Map<Long, TestProject> allProjectMap = new HashMap<>();
        //查询所有根结点下的所有项目
        List<TestProject> allList = testProjectDao.findByRootResourceIDIn(new ArrayList<>(rootSet), null);
        Map<String, Object> node1 = new HashMap<>();
        for (TestProject testProject : testProjects) {
            findAllChildsResourseID(testProject.getResourceID(), allList, node1);
        }

        for (TestProject project : allList) {
            allProjectMap.put(project.getResourceID(), project);
            List<TestProject> childList = new ArrayList<>();
            if (!parent_projectMap.isEmpty() && parent_projectMap.containsKey(project.getParentResourceID())) {
                childList = parent_projectMap.get(project.getParentResourceID());
            }
            if (node1.containsKey(project.getResourceID().toString())) {
                childList.add(project);
            }
            parent_projectMap.put(project.getParentResourceID(), childList);
        }
        Map<String, Integer> stringIntegerMap = testProjectService.selectProjectProgress(testProjectDao.findAll());
        //构建树
        List<Map<String, Object>> tree = new ArrayList<>();
        for (TestProject rootProject : rootProjects) {
            Map<String, Object> node = new HashMap<>();
            buildChildProjectNodeNew(rootProject, parent_projectMap, tpMap, node, null, stringIntegerMap, testProjectTypeMap, testModeMap);
            tree.add(node);
        }
        Collections.reverse(tree);
        int pageNumber = pageRequest.getPageNumber();
        int pageSize = pageRequest.getPageSize();
        List<Map<String, Object>> treeNode = new ArrayList<>();
        int m = CheckUtil.checkLoop(pageSize * pageNumber + pageSize);
        for (int i = pageSize * pageNumber; i < m; i++) {
            if (i < tree.size()) {
                treeNode.add(tree.get(i));
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("content", treeNode);
        resultMap.put("sort", pageRequest.getSort());
        resultMap.put("total", tree.size());
        return Result.renderSuccess(resultMap);
    }

    private void findAllChildsResourseID(Long resourceID, List<TestProject> allList, Map<String, Object> node) {
        TestProject testProject = new TestProject();
        for (TestProject testProject1 : allList) {
            if (testProject1 != null && testProject1.getResourceID().equals(resourceID)) {
                testProject = testProject1;
                node.put(testProject.getResourceID().toString(), "");
            }
        }
        if (testProject.getParentResourceID() != null) {
            findAllChildsResourseID(testProject.getParentResourceID(), allList, node);
        }
    }


    /**
     * 递归查询最子级项目
     *
     * @param testProject
     * @param resourceIDs
     */
    public void findAllchildProject(TestProject testProject, List<TestProject> allProjectList, List<String> resourceIDs) {
        String resourceID = String.valueOf(testProject.getResourceID());
        //递归算法中不要加查询
//        List<TestProject> childs = testProjectDao.getChildrenTestProject(String.valueOf(resourceID));
        List<TestProject> childs = allProjectList.stream().filter(item -> testProject.getResourceID().equals(item.getParentResourceID())).collect(Collectors.toList());
        if (childs.isEmpty()) {
            resourceIDs.add(resourceID);
        } else {
            for (TestProject child : childs) {
                findAllchildProject(child, allProjectList, resourceIDs);
            }
        }
    }


    /**
     * 根据子级项目查询末级节点项目组
     *
     * @param resourceIDs
     */
    public List<String> getChildGroup(List<String> resourceIDs, String groupType) {
        return projectGroupService.findByProjectRidsAndIsSmallPoint(resourceIDs, groupType);
    }

    /**
     * 递归查询最子级项目组
     *
     * @param projectGroup
     * @param resourceIDs
     */
    public void findAllchildGroup(ProjectGroup projectGroup, List<String> resourceIDs, String groupType, List<ProjectGroup> projectAllGroupList) {
        Long resourceID = projectGroup.getResourceID();
        // 递归中不要使用查询
//        List<ProjectGroup> childs= projectGroupService.findByParentResourceID(resourceID,groupType);
        List<ProjectGroup> childs = projectAllGroupList.stream().filter(item -> projectGroup.getResourceID().equals(item.getParentResourceID())).collect(Collectors.toList());
        if (childs.isEmpty()) {
            if (projectGroup.isSmallPoints()) {
                resourceIDs.add(resourceID.toString());
            }
        } else {
            for (ProjectGroup child : childs) {
                findAllchildGroup(child, resourceIDs, groupType, projectAllGroupList);
            }
        }
    }

    private Boolean buildChildProjectNodeNew(TestProject testProject, Map<Long, List<TestProject>> parent_childMap, Map<Long, String> tpMap, Map<String, Object> node, String groupType, Map<String, Integer> tpmap, Map<String, String> testProjectTypeMap, Map<String, String> testModeMap) {

        List<Map<String, Object>> childNodes = new ArrayList<>();
        if (!tpMap.isEmpty() && tpMap.containsKey(testProject.getResourceID())) {
            findAllChildsNew(testProject, parent_childMap, node, groupType, tpmap, testProjectTypeMap, testModeMap);
            return true;
        }
        List<TestProject> childList = parent_childMap.get(testProject.getResourceID());
        if (childList == null || childList.isEmpty()) {
            return false;
        }
        //继续查找子项目
        Boolean flag = false;
        Boolean isSmallpoints = false;
        for (TestProject child : childList) {
            Map<String, Object> childNode = new HashMap<>();
            Boolean b = buildChildProjectNodeNew(child, parent_childMap, tpMap, childNode, groupType, tpmap, testProjectTypeMap, testModeMap);
            if (b && !childNode.isEmpty()) {
                childNodes.add(childNode);
            }
            if (b) {
                flag = true;
            }
        }
        if (flag) {
            node.put("resourceID", testProject.getResourceID());
            node.put("name", testProject.getName());
//            node.put("parentResourceID", testProject.getParentResourceID());
//            node.put("nodeType", "project");
            node.put("childList", childNodes);
            node.put("number", testProject.getNumber());
            node.put("ManagerName", testProject.getManagerName());
            node.put("describeInfo", testProject.getDescribeInfo());
            node.put("endDate", testProject.getEndDate());
            node.put("managerResourceID", testProject.getManagerResourceID());
            node.put("startDate", testProject.getStartDate());
            node.put("status", testProject.getStatus());
            node.put("statusName", testProject.getStatusName());
            node.put("parentResourceID", testProject.getParentResourceID());
            node.put("projectType", testProject.getProjectType());
            node.put("projectTypeName", testProject.getProjectType() == null ? "" : testProjectTypeMap.get(testProject.getProjectType().toString()));
            //创建人，创建时间，修改人，修改时间
            node.put("createUser",testProject.getCreateUser());
            node.put("createTime",testProject.getCreateTime());
            node.put("editUser",testProject.getEditUser());
            node.put("editTime",testProject.getEditTime());
            /* 处理进度数据 */
            if (tpmap != null && !tpmap.isEmpty()) {
                String progress = testProject.getResourceID().toString();
                node.put("progress", tpmap.get(progress) == 0 ? 0 : tpmap.get(progress) + "%");
            } else {
                node.put("progress", 0);
            }
        }
        return flag;

    }

    private void findAllChildsNew(TestProject testProject, Map<Long, List<TestProject>> parent_childMap, Map<String, Object> node, String groupType, Map<String, Integer> tpmap, Map<String, String> testProjectTypeMap, Map<String, String> testModeMap) {

        Boolean isSmallpoints = false;
        List<Map<String, Object>> childNodes = new ArrayList<>();
        List<TestProject> childList = parent_childMap.get(testProject.getResourceID());
        if (childList != null && childList.size() > 0) {
            //继续查找子项目
            for (TestProject child : childList) {
                Map<String, Object> childNode = new HashMap<>();
                findAllChildsNew(child, parent_childMap, childNode, groupType, tpmap, testProjectTypeMap, testModeMap);
                if (childNode != null && !childNode.isEmpty()) {
                    childNodes.add(childNode);
                }
            }
        }
        node.put("resourceID", testProject.getResourceID());
        node.put("name", testProject.getName());
//            node.put("parentResourceID", testProject.getParentResourceID());
//            node.put("nodeType", "project");
        node.put("childList", childNodes);
        node.put("number", testProject.getNumber());
        node.put("ManagerName", testProject.getManagerName());
        node.put("describeInfo", testProject.getDescribeInfo());
        node.put("endDate", testProject.getEndDate());
        node.put("managerResourceID", testProject.getManagerResourceID());
        node.put("startDate", testProject.getStartDate());
        node.put("status", testProject.getStatus());
        node.put("statusName", testProject.getStatusName());
        node.put("parentResourceID", testProject.getParentResourceID());
        node.put("projectType", testProject.getProjectType());
        node.put("projectTypeName", testProject.getProjectType() == null ? "" : testProjectTypeMap.get(testProject.getProjectType().toString()));
        //创建人，创建时间，修改人，修改时间
        node.put("createUser",testProject.getCreateUser());
        node.put("createTime",testProject.getCreateTime());
        node.put("editUser",testProject.getEditUser());
        node.put("editTime",testProject.getEditTime());
        /* 处理进度数据 */
        if (tpmap != null && !tpmap.isEmpty()) {
            String progress = testProject.getResourceID().toString();
            node.put("progress", tpmap.get(progress) == 0 ? 0 : tpmap.get(progress) + "%");
        } else {
            node.put("progress", 0);
        }
        //测试方式
        List<String> testModeList = BinaryDecimalUtil.TenToDicVal(testProject.getTestMode());
        String testModeValue = "";
        for (String s : testModeList) {
            testModeValue = testModeValue + (testModeMap.get(s) == null ? "" : testModeMap.get(s).toString()) + ",";
        }

        node.put("testModeKey", testModeList);
        node.put("testModeValue", "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
    }

    private List<Map<String, Object>> translateData(List<TestProject> list, Map<String, String> testProjectTypeMap) {

        Map<Long, String> parentMap = new HashMap<>();
        List<TestProject> parentList = testProjectService.findAllParent();
        for (TestProject testProject : parentList) {
            parentMap.put(testProject.getResourceID(), "");
        }
        Result r6 = feignDataDesignToBasicService.findByName("TESTMODE", null);
        List<Map<String, String>> tmList = (List<Map<String, String>>) r6.getObj();
        Map<String, String> tmMap = tmList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));

        List<Map<String, Object>> resultList = new ArrayList<>();
        //添加进度数据
        Map<String, Integer> tpmap = new HashMap<String, Integer>();
        tpmap = testProjectService.selectProjectProgress(list);
        for (TestProject tp : list) {
            Boolean hasChildren = false;//是否有子项目
            if (!parentMap.isEmpty() && parentMap.containsKey(tp.getResourceID())) {
                hasChildren = true;
            }
            String startDate = tp.getStartDate() != null ? DateUtil.getDateStr(tp.getStartDate(), DateUtil.FMT_YYYY_MM_DD) : "";
            String endDate = tp.getEndDate() != null ? DateUtil.getDateStr(tp.getEndDate(), DateUtil.FMT_YYYY_MM_DD) : "";
            Map<String, Object> map = new HashMap<>();
            map.put("resourceID", tp.getResourceID());
            map.put("name", tp.getName());
            map.put("number", tp.getNumber());
            map.put("status", tp.getStatus());
            map.put("statusName", tp.getStatusName());
            map.put("startDate", startDate);
            map.put("endDate", endDate);
            map.put("managerResourceID", tp.getManagerResourceID());
            map.put("ManagerName", tp.getManagerName());
            map.put("describeInfo", tp.getDescribeInfo());
            map.put("hasChildren", hasChildren);
            map.put("parentResourceID", tp.getParentResourceID());
            map.put("projectType", tp.getProjectType());
            map.put("projectTypeName", tp.getProjectType() == null ? "" : testProjectTypeMap.get(tp.getProjectType().toString()));

            //创建人，创建时间，修改人，修改时间
            map.put("createUser",tp.getCreateUser());
            map.put("createTime",tp.getCreateTime());
            map.put("editUser",tp.getEditUser());
            map.put("editTime",tp.getEditTime());
            //测试方式
            List<String> testModeKey = BinaryDecimalUtil.TenToDicVal(tp.getTestMode());
            String testModeValue = "";
            for (String s : testModeKey) {
                testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) + ",";
            }
            map.put("testModeKey", testModeKey);
            map.put("testModeValue", "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));

            /* 处理进度数据 */
            if (tpmap != null && !tpmap.isEmpty()) {
                String progress = tp.getResourceID().toString();
                map.put("progress", tpmap.get(progress) == 0 ? 0 : tpmap.get(progress) + "%");
            } else {
                map.put("progress", 0);
            }
            resultList.add(map);
        }
        return resultList;
    }


    /**
     * 根据条件查询项目
     *
     * @param name
     * @param status
     * @param number
     * @param projectType
     * @return
     */
    @Override
    public List<TestProject> findTestProjectByCondition(String name, String status, String number, String projectType, String testMode) {
        List<Integer> projectTypeList = null;
        if (StringUtils.isNotBlank(projectType)) {
            projectTypeList = Arrays.asList(projectType.split(","))
                    .stream().map(s -> (Integer.valueOf(s.trim()))).collect(Collectors.toList());
        }
        return testProjectDao.findTestProjectByCondition(name, status, number, projectTypeList, testMode);
    }


    /**
     * @Method: findParentProjectByProjectResourceID
     * @Description: 查询父节点项目
     * @Param: " [projectResourceID] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/10/26
     */
    @Override
    public Result findParentProjectByProjectResourceID(String projectResourceID) {
        while (true) {
            TestProject testproject = this.findByResourceID(Long.valueOf(projectResourceID));
            if (testproject.getRootResourceID() == null && testproject.getParentResourceID() == null) {
                return Result.renderSuccess(testproject);
            } else {
                projectResourceID = testproject.getParentResourceID().toString();
            }
        }
    }

    /**
     * @Method: findChildByParentRid
     * @Description: 根节点查询子项目
     * @Param: " [parentProjectResourceID] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/10/27
     */
    @Override
    public Result findChildByParentRid(String parentProjectResourceID) {
        List<TestProject> list = this.findByRootResourceID(parentProjectResourceID);
        return Result.renderSuccess(list);
    }

    @Override
    public List<String> findEndStateBytestProjectResourceID(String testProjectResourceID) {
        return testProjectDao.findEndStateBytestProjectResourceID(testProjectResourceID);
    }


    private List<TestProject> findByRootResourceID(String parentProjectResourceID) {
        return testProjectDao.findByRootResourceID(parentProjectResourceID);
    }

    /**
     * @param testProjectView
     * @param userNumber
     * @return
     * @Title: createTestProject
     * @Description: 富滇新增项目
     * <AUTHOR>
     * @date 2021年1月15日 下午1:12:15
     */
    @Override
    public Result<?> createTestProject(TestProjectView testProjectView, String userNumber) {
        if (StringUtils.isBlank(testProjectView.getName())) {
            return Result.renderError("项目名称为空！");
        }
        if (StringUtils.isBlank(testProjectView.getNumber())) {
            return Result.renderError("项目编号为空！");
        }
        if (StringUtils.isBlank(testProjectView.getStatus())) {
            return Result.renderError("项目状态为空！");
        }
        if (StringUtils.isBlank(testProjectView.getProjectType())) {
            return Result.renderError("项目类型为空！");
        }
        TestProject resultObject;
        User user = userinfoService.findByNumber(testProjectView.getNumber()).get();

        //新增项目
        TestProject testProject = new TestProject();
        testProject.setName(testProjectView.getName());
        testProject.setNumber(testProjectView.getNumber());
        testProject.setStatus(Integer.valueOf(testProjectView.getStatus()));
        String statusName = ((Map) redisUtils.get("项目状态")).get(testProjectView.getStatus()).toString();
        testProject.setStatusName(statusName);
        testProject.setStartDate(StringUtils.isBlank(testProjectView.getStartDate()) ? null : DateUtil.getDateFromStr(testProjectView.getStartDate()));
        testProject.setEndDate(StringUtils.isBlank(testProjectView.getEndDate()) ? null : DateUtil.getDateFromStr(testProjectView.getEndDate()));
        testProject.setManagerResourceID(user.getResourceID());
        testProject.setManagerName(testProjectView.getManagerName());
        testProject.setDescribeInfo(testProjectView.getDescribeInfo());
        testProject.setProjectType(Integer.valueOf(testProjectView.getProjectType()));

        resultObject = save(testProject, userNumber);

        //项目关联缺陷配置流程
        feignDataDesignToBugService.saveDefectProcessQuote(resultObject.getResourceID().toString(), resultObject.getResourceID().toString(), userNumber);
        return Result.renderSuccess();
    }

    /**
     * @param testProjectView
     * @param userNumber
     * @return
     * @Title: updateTestProject
     * @Description: 富滇修改项目
     * <AUTHOR>
     * @date 2021年1月15日 下午1:12:15
     */
    @Override
    public Result<?> updateTestProject(TestProjectView testProjectView, String userNumber) {
        if (StringUtils.isBlank(testProjectView.getName())) {
            return Result.renderError("项目名称为空！");
        }
        if (StringUtils.isBlank(testProjectView.getNumber())) {
            return Result.renderError("项目编号为空！");
        }
        if (StringUtils.isBlank(testProjectView.getStatus())) {
            return Result.renderError("项目状态为空！");
        }
        if (StringUtils.isBlank(testProjectView.getProjectType())) {
            return Result.renderError("项目类型为空！");
        }

        //查询原项目信息
        TestProject testProject = findByNumber(testProjectView.getNumber());
        if (testProject == null) {
            return Result.renderError("项目异常！");
        }

        String statusName = ((Map) redisUtils.get("项目状态")).get(testProjectView.getStatus()).toString();
        // 已关闭校验逻辑
        if ("已关闭".equals(statusName)) {
            Result result = verifyProjectStatus(testProject.getResourceID().toString());
            if (!result.isSuccess()) {
                return result;
            }
        }

        if (!testProject.getName().equals(testProjectView.getName())) {
            //修改需求中所属项目名称
            List<Demand> demands = demandService.findByTestProjectResourceID(testProject.getResourceID());
            if (!demands.isEmpty()) {
                demands.stream().forEach(x -> {
                    x.setTestProjectName(testProjectView.getName());
                });
                demandService.update(demands, userNumber);
            }
        }
        User user = userinfoService.findByNumber(testProjectView.getNumber()).get();

        testProject.setName(testProjectView.getName());
        testProject.setNumber(testProjectView.getNumber());
        testProject.setStatus(Integer.valueOf(testProjectView.getStatus()));
        testProject.setStatusName(statusName);
        testProject.setStartDate(StringUtils.isBlank(testProjectView.getStartDate()) ? null : DateUtil.getDateFromStr(testProjectView.getStartDate()));
        testProject.setEndDate(StringUtils.isBlank(testProjectView.getEndDate()) ? null : DateUtil.getDateFromStr(testProjectView.getEndDate()));
        testProject.setManagerResourceID(user.getResourceID());
        testProject.setManagerName(testProjectView.getManagerName());
        testProject.setDescribeInfo(testProjectView.getDescribeInfo());
        testProject.setProjectType(Integer.valueOf(testProjectView.getProjectType()));
        //更新项目
        update(testProject, userNumber);

        return Result.renderSuccess();
    }

    @Override
    public List<TestProject> findByUserNumber(String userNumber) {
        return testProjectDao.findByUserNumber(userNumber, null);
    }

    @Override
    public List<TestProject> findAllByDataAuth() {


        return testProjectDao.findAllByDataAuth();
    }

    @Override
    public Map<String, Object> findProjectSelectDataByDataAuth(Map<String, String> params) {

        Integer pageNumber = params.get("pageNumber") == null ? 1 : Integer.valueOf(params.get("pageNumber"));
        Integer pageSize = params.get("pageSize") == null ? 10 : Integer.valueOf(params.get("pageSize"));
        String projectName = params.get("projectName");
        String disable = params.get("disable");

        PageHelper.startPage(pageNumber, pageSize);
        List<Map<String, Object>> resultList = new ArrayList<>();
        long total = 0L;
        if(Boolean.parseBoolean(disable)){
            QueryWrapper<TestProject> queryWrapper = new QueryWrapper<>();
            queryWrapper.like(StringUtils.isNotBlank(projectName), "name", projectName).orderByAsc("createTime");
            List<TestProject> allList = testProjectMapper.selectList(queryWrapper);

            resultList.addAll(allList.stream().map(item -> {
                Map<String, Object> map = new HashMap<>();
                map.put("resourceID", item.getResourceID());
                map.put("name", item.getName());
                map.put("disabled", true);
                return map;
            }).collect(Collectors.toList()));

            PageInfo<TestProject> info = new PageInfo<>(allList);
            total = info.getTotal();
        }else{
            List<TestProject> authList = testProjectMapper.findAllByDataAuthByCondition(projectName);
            resultList.addAll(authList.stream().map(item -> {
                Map<String, Object> map = new HashMap<>();
                map.put("resourceID", item.getResourceID());
                map.put("name", item.getName());
                map.put("disabled", false);
                return map;
            }).collect(Collectors.toList()));

            PageInfo<TestProject> info = new PageInfo<>(authList);
            total = info.getTotal();
        }

        Map<String, Object> map = new HashMap<>();
        map.put("content", resultList);
        map.put("total", total);

        return map;
    }

    @Override
    public Result findAllProjectTree() {
        //parent
        List<TestProject> list = testProjectDao.loadParentTestProject(null);
        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (TestProject tp : list) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("label", tp.getName());
            map.put("value", tp.getResourceID());
            List<Map<String, Object>> childrenList = getChildrenList(tp.getResourceID());
            if (!childrenList.isEmpty() && childrenList.size() > 0) {
                map.put("children", childrenList);
            }
            returnList.add(map);
        }
        return Result.renderSuccess(returnList);
    }

    @Override
    public List<Map<String, Object>> findTradeFlowCaseFolder(Long testProjectResourceId) {
        return testProjectDao.findTradeFlowCaseFolder(testProjectResourceId);
    }

    @Override
    public List<Map<String, Object>> findProjectTreeOfreport(String userNumber, String groupType) {
        Map<Long, String> tpMap = new HashMap<>();//用户关联的项目
        Set<String> rootSet = new HashSet<>();//根结点
        //查询用户关联的项目
        List<TestProject> testProjects = testProjectMapper.findTestProject(null, null, null, null, null);
        if (null == testProjects || testProjects.isEmpty()) {
            return new ArrayList<>();
        }
        for (TestProject tp : testProjects) {
            tpMap.put(tp.getResourceID(), "");
            rootSet.add(tp.getRootResourceID() == null ? String.valueOf(tp.getResourceID()) : String.valueOf(tp.getRootResourceID()));
        }
        //查询所有根结点
        List<TestProject> rootProjects = findByResourceIDIn(new ArrayList<>(rootSet));

        Map<Long, List<TestProject>> parent_projectMap = new HashMap<>();//父-子项目集合
        Map<Long, TestProject> allProjectMap = new HashMap<>();
        //查询所有根结点下的所有项目
        // List<TestProject> allList = testProjectDao.findByRootResourceIDIn(new ArrayList<>(rootSet),"已关闭");
        List<TestProject> allList = testProjectDao.findByRootResourceIDIn(new ArrayList<>(rootSet), null);
        for (TestProject project : allList) {
            allProjectMap.put(project.getResourceID(), project);

            List<TestProject> childList = new ArrayList<>();
            if (!parent_projectMap.isEmpty() && parent_projectMap.containsKey(project.getParentResourceID())) {
                childList = parent_projectMap.get(project.getParentResourceID());
            }
            childList.add(project);
            parent_projectMap.put(project.getParentResourceID(), childList);
        }
        //构建树
        List<Map<String, Object>> tree = new ArrayList<>();
        for (TestProject rootProject : rootProjects) {
            Map<String, Object> node = new HashMap<>();
            buildChildProjectNodeTree(rootProject, parent_projectMap, tpMap, node, groupType);
            tree.add(node);
        }
        return tree;
    }

    @Override
    public Result<?> validateName(String name, String resourceID) {

        if (name == null || "".equals(name.trim())) {
            return Result.renderError("项目名称为空！");
        }
        name = name.trim();
        if (resourceID != null && !"".equals(resourceID)) {
            //修改信息
            TestProject testProject = findByResourceID(Long.valueOf(resourceID));
            if (testProject != null) {
                List<TestProject> testProjectList = findByName(name);
                if (testProjectList.isEmpty()) {
                    return Result.renderSuccess();
                }
                List<Long> testProjectRid = testProjectList.stream().map(TestProject::getResourceID).collect(Collectors.toList());
                if (!testProjectRid.contains(Long.valueOf(resourceID))) {
                    return Result.renderError("项目名称已存在！");
                }
            }
        } else {
            //新增信息
            List<TestProject> testProjectList = findByName(name);
            if (!testProjectList.isEmpty()) {
                return Result.renderError("项目名称已存在！");
            }
        }
        return Result.renderSuccess();
    }

}
