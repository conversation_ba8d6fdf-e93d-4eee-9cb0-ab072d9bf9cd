package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.util.ParamConfig;
import com.jettech.common.util.UserVo;
import com.jettech.dao.idao.IRichTextImageDao;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.model.RichTextImage;
import com.jettech.service.iservice.IRichTextImageService;
import com.jettech.service.iservice.JettoApiService;
import com.jettech.util.CreateAUniqueIDUtil;
import com.jettech.util.RichTextImageUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Base64;
import java.util.Map;

@Service
public class RichTextImageServiceImpl extends BaseServiceImpl<RichTextImage> implements IRichTextImageService {
    private final static Logger logger = LoggerFactory.getLogger(RichTextImageServiceImpl.class);
    @Autowired
    private RichTextImageUtils fileUtils;

    @Autowired
    private ParamConfig paramConfig;

    @Autowired
    private IRichTextImageDao richTextImageDao;
    private JettoApiService jettoApiService;
    @Value("${use_file_service}")
    private boolean useFileService;
    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;
    @PostConstruct
    private void postConstruct() {
        baseDao = this.richTextImageDao;
    }
    @Override
    public void uploadRichTextImageArray(MultipartFile[] file, String fileName) {
        Map<String, Object> map = fileUtils.uploadFile(file[0], fileName);
    }

    @Override
    public String uploadRichTextImage(MultipartFile file, UserVo user) {
        if(useFileService){
            MultipartFile[] files=new MultipartFile[]{file};

            Result result=feignDataDesignToFileService.uploadSFile(file,null, ObjectTypeEnum.RICHIMG.getValue(),true);
            if(result!=null){
                Long resourceId=(Long)result.getObj();
                return String.valueOf(resourceId);
            }
        }else {
            String path = System.currentTimeMillis() + "";
            String filepath = "";
            String fileName = "";
            String fileType = "";
            if (file != null) {
                fileName = file.getOriginalFilename();
                if (StringUtils.isNotBlank(fileName)) {
                    fileName = fileName.trim();
                    String[] str = fileName.split("\\.");
                    fileType = str[(str.length - 1)];
                }
                String expandedName = CreateAUniqueIDUtil.ImageID() + "." + fileType;
                filepath = fileUtils.saveImage(file, paramConfig.getAttachmentPath(), path, expandedName);
            }
            if (!"".equals(filepath)){
                RichTextImage defectImage = new RichTextImage();
                defectImage.setImagePath(filepath);
                RichTextImage save = save(defectImage, user.getUserNumber());
                return String.valueOf(save.getResourceID());
            }

            return "";

        }
        return "";
    }

    @Override
    public void getRichTextImage(String resourceID, HttpServletResponse response) {
        try {

            if(useFileService){
                byte[] data=feignDataDesignToFileService.getContent(Long.parseLong(resourceID));
                data = Base64.getMimeDecoder().decode(data);
                response.getOutputStream().write(data);
            }else {
                RichTextImage defectImage = findByResourceID(Long.parseLong(resourceID));
                String filePath = defectImage.getImagePath();
                File file1 = new File(filePath);
                InputStream is = new FileInputStream(file1);
                if (is != null) {
                    long length = file1.length();
                    OutputStream os = response.getOutputStream();
                    int tempLength;
                    for (byte[] buffer = new byte[1024]; (tempLength = is.read(buffer)) > -1; length += (long) tempLength) {
                        os.write(buffer, 0, tempLength);
                    }
                    os.flush();
                }
            }
            }catch (Exception e) {
            logger.error("获取图片失败:{}" , e.getMessage());
        }
            //RichTextImage byImagePath = richTextImageDao.findByImagePath(resourceID);

    }

    @Override
    public void delRichTextImage(String resourceID, UserVo user) {

        if(useFileService){
            feignDataDesignToFileService.deleteFile(resourceID);
        }else{
            RichTextImage defectImage = findByResourceID(Long.parseLong(resourceID));
            try {
                if ("".equals(resourceID)) {

                }

                if (defectImage.getImagePath() != null) {
                    File dir = new File(paramConfig.getAttachmentPath() + defectImage.getImagePath());
                    if (dir.isDirectory()) {
                        File[] fiels = dir.listFiles();
                        for (File file : fiels) {
                            file.delete();
                        }
                    }
                    delete(defectImage,user.getUserNumber());

                }
            }catch (Exception e){
                logger.error("图片删除失败：{}",e.getMessage());
            }
        }

    }
}
