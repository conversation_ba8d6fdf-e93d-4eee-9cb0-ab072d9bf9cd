package com.jettech.service.impl;

import com.jettech.dao.idao.IWikiPageCommentDao;
import com.jettech.model.WikiPageComment;
import com.jettech.service.iservice.IWikiPageCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WikiPageCommentServiceImpl extends BaseServiceImpl<WikiPageComment> implements IWikiPageCommentService {

    @Autowired
    private IWikiPageCommentDao wikiPageCommentDao;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = wikiPageCommentDao;
    }

    @Override
    public List<WikiPageComment> listCommentsByPageId(Long pageResourceId) {
        List<WikiPageComment> wikiPageCommentList = wikiPageCommentDao.listCommentsByPageId(pageResourceId);
        List<WikiPageComment> commentList = wikiPageCommentList.stream().filter(comment -> comment.getParentResourceId() == null || comment.getParentResourceId() == 0).collect(Collectors.toList());
        for (WikiPageComment comment : commentList) {
            List<WikiPageComment> children = this.listCommentsByParentId(comment.getResourceID(), wikiPageCommentList);
            comment.setChildren(children);
        }
        return commentList;
    }

    private List<WikiPageComment> listCommentsByParentId(Long commentResourceID, List<WikiPageComment> allCommentList) {
        List<WikiPageComment> commentChildren = allCommentList.stream().filter(comment -> commentResourceID.equals(comment.getParentResourceId())).collect(Collectors.toList());
        if (commentChildren.isEmpty()) {
            return new ArrayList<>();
        }
        for (WikiPageComment child : commentChildren) {
            List<WikiPageComment> comments = this.listCommentsByParentId(child.getResourceID(), allCommentList);
            child.setChildren(comments);
        }
        return commentChildren;
    }


    @Override
    public void addComment(WikiPageComment comment, String loginUserNumber) {
        this.save(comment, loginUserNumber);
    }

    @Override
    public void editContentById(WikiPageComment comment, String loginUserNumber) {
        WikiPageComment pageComment = this.findByResourceID(comment.getResourceID());
        if (pageComment != null) {
            pageComment.setComment(comment.getComment());
            this.update(pageComment, loginUserNumber);
        }
    }

    @Override
    public void clearContentById(Long resourceID, String loginUserNumber) {
        WikiPageComment pageComment = this.findByResourceID(resourceID);
        if (pageComment != null) {
            List<WikiPageComment> children = this.wikiPageCommentDao.listCommentsByParentResourceId(pageComment.getResourceID());
            if (children.isEmpty()) {
                this.delete(pageComment, loginUserNumber);
            } else {
                pageComment.setComment("");
                this.update(pageComment, loginUserNumber);
            }
        }
    }
}
