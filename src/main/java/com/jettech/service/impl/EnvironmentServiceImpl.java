package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.EnvironmentPageDTO;
import com.jettech.common.util.ExcelUtils;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.dao.idao.IEnvironmentDao;
import com.jettech.feign.IFeignAssetsToBasic;
import com.jettech.model.Environment;
import com.jettech.model.User;
import com.jettech.service.iservice.IEnvironmentService;
import com.jettech.service.iservice.IUserinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName EnvironmentServiceImpl
 * @description 被测系统环境信息记录Service层
 * <AUTHOR>
 * @create 20200630
 */
@Service
public class EnvironmentServiceImpl extends BaseServiceImpl<Environment> implements IEnvironmentService {
	@SuppressWarnings("unused")
	private static final Logger logger = LoggerFactory.getLogger(EnvironmentServiceImpl.class);
	@Autowired
	private IEnvironmentDao environmentDao;
	@Autowired
	private IUserinfoService userinfoService;
	@Autowired
	private IFeignAssetsToBasic feignAssetsToBasic;
	@PostConstruct
	public void postConstruct() {
		this.baseDao = environmentDao;
	}
	/**
	 * @Title findEnvironmentPage
	 * @Description 分页查询环境信息
	 * @author: slq
	 * @date: 2020年6月30日 上午11:40:30
	 */
	@Override
	public Page<Environment> findEnvironmentPage(PageRequest pageRequest, EnvironmentPageDTO environment) {
		return environmentDao.findEnvironmentPage(pageRequest, environment);
	}
	/**
	 * @Title  addEnvironment     
	 * @Description  新建或修改环境   
	 * @author: slq    
	 * @date:   2020年6月30日 下午4:43:39
	 */
	@Transactional
	@Override
	public String addOrUpdateEnvironment(Environment environment, String userNumber) {
		Optional<User> userOptional = userinfoService.findByNumber(userNumber);
		if(!userOptional.isPresent()) {
			return "数据异常！";
		}
		Environment existEnv = environmentDao.findByNameAndType(environment.getName(),environment.getType());
		if(StringUtils.isEmpty(environment.getResourceID())) {
			if(existEnv != null) {
				return "已经存在相同名称和类型的环境！";
			}
			environment.setEditUserName(userOptional.get().getUserName());
			//environment.setEditTime(new Date());
			this.save(environment, userNumber);
			return "新建成功！";
		}else {
			if(existEnv != null && !existEnv.getResourceID().equals(environment.getResourceID())) {
				return "已经存在相同名称和类型的环境！";
			}
			Environment update = findByResourceID(environment.getResourceID());
			update.setCpu(environment.getCpu());
			update.setDataBaseInformation(environment.getDataBaseInformation());
			update.setDetailedInformation(environment.getDetailedInformation());
			update.setHostname(environment.getHostname());
			update.setIp(environment.getIp());
			update.setMemoryInformation(environment.getMemoryInformation());
			update.setMiddlewareInformation(environment.getMiddlewareInformation());
			update.setName(environment.getName());
			update.setOperatingSystemInformation(environment.getOperatingSystemInformation());
			update.setState(environment.getState());
			update.setSystemManager(environment.getSystemManager());
			update.setSystemName(environment.getSystemName());
			update.setType(environment.getType());
			update.setDepartment(environment.getDepartment());
			update.setEnvUserName(environment.getEnvUserName());
			update.setSystemComponents(environment.getSystemComponents());
			update.setEditUserName(userOptional.get().getUserName());
			this.update(update, userNumber);
			return "修改成功！";
		}
	}
	/**
	 * @Title  deleteEnvironments     
	 * @Description 删除环境   
	 * @author: slq    
	 * @date:   2020年6月30日 下午5:11:02
	 */
	@Transactional
	@Override
	public String deleteEnvironments(List<String> environmentResID, String userNumber) {
		List<Environment> delete = findByResourceIDIn(environmentResID);
		if(!delete.isEmpty()) {
			this.deleteInBatch(delete, userNumber);
		}
		return "删除成功！";
	}
	/**
	 * @Title  findByEnvironment     
	 * @Description  查询环境信息
	 * @author: slq    
	 * @date:   2020年6月30日 下午5:34:14
	 */
	@Override
	public List<Map<String, String>> findByEnvironment(Environment environment) {
		return environmentDao.findByEnvironment(environment);
	}
	/**
	 * @Title  findUpdateEnvironmentByResID     
	 * @Description 查询要修改的环境信息   
	 * @author: slq    
	 * @date:   2020年7月1日 上午11:51:04
	 */
	@Override
	public EnvironmentPageDTO findUpdateEnvironmentByResID(String resourceID) {
		return environmentDao.findUpdateEnvironmentByResID(resourceID);
	}

	/**
	 * @Title  findUpdateEnvironmentByResID
	 * @Description 查询要修改的环境信息
	 * @author: slq
	 * @date:   2020年7月1日 上午11:51:04
	 */
	@Override
	public void exportExcel(Map data) {
		String token = HttpRequestUtils.getCurrentRequestToken();
		HttpServletResponse response = (HttpServletResponse) data.get("response");
		HttpServletRequest request = (HttpServletRequest) data.get("request");
		Map<String, String> envTypeMap = new HashMap<>();
		Map<String, String> envStatusMap = new HashMap<>();
		Map<String, String> departmentMap = new HashMap<>();
		//获取勾选环境id
		String environmentRids = data.get("environmentRids") == null ? "" : String.valueOf(data.get("environmentRids")).trim();
		// 查询字典对应的值
		Result envTypeResult = feignAssetsToBasic.findByName("environmentType", token);
		Result envStatusResult = feignAssetsToBasic.findByName("environmentState", token);
		Result departmentResult = feignAssetsToBasic.findByName("department", token);

		List<Map<String, String>> list1 = (List<Map<String, String>>) envTypeResult.getObj();
		for (Map<String, String> map : list1) {
			envTypeMap.put(map.get("value"), map.get("textName"));
		}
		List<Map<String, String>> list2 = (List<Map<String, String>>) envStatusResult.getObj();
		for (Map<String, String> map : list2) {
			envStatusMap.put(map.get("value"), map.get("textName"));
		}
		List<Map<String, String>> list3 = (List<Map<String, String>>) departmentResult.getObj();
		for (Map<String, String> map : list3) {
			departmentMap.put(map.get("value"), map.get("textName"));
		}
		Environment environment = new Environment();
		environment.setName(data.get("name") == null ? "" : String.valueOf(data.get("name")).trim());
		environment.setDepartment(data.get("department") == null ? "" : String.valueOf(data.get("department")).trim());
		environment.setEnvUserName(data.get("envUserName") == null ? "" : String.valueOf(data.get("envUserName")).trim());
		environment.setIp(data.get("ip") == null ? "" : String.valueOf(data.get("ip")).trim());
		environment.setState(data.get("state") == null ? "" : String.valueOf(data.get("state")).trim());
		environment.setSystemComponents(data.get("systemComponents") == null ? "" : String.valueOf(data.get("systemComponents")).trim());
		environment.setSystemManager(data.get("systemManager") == null ? "" : String.valueOf(data.get("systemManager")).trim());
		environment.setSystemName(data.get("systemName") == null ? "" : String.valueOf(data.get("systemName")).trim());
		environment.setType(data.get("type") == null ? "" : String.valueOf(data.get("type")).trim());
		List<Map<String, String>> environmentList;
		if (environmentRids != null && environmentRids != "") {
			environmentList = this.environmentDao.findByEnvironmentByResourceIds(Arrays.asList(environmentRids.split(",")));//this.findByEnvironment(environment).stream().filter(item -> environmentRids.contains(item.get("resourceID"))).collect(Collectors.toList());
		} else {
			environmentList = this.findByEnvironment(environment);
		}

		for (Map<String, String> map : environmentList) {
			if (map.containsKey("type")) {
				map.put("type", null == envTypeMap.get(map.get("type")) ? "" : envTypeMap.get(map.get("type")));
			}
			if (map.containsKey("state")) {
				map.put("state", null == envStatusMap.get(map.get("state")) ? "" : envStatusMap.get(map.get("state")));
			}
			if (map.containsKey("department")) {
				map.put("department", null == departmentMap.get(map.get("department")) ? "" : departmentMap.get(map.get("department")));
			}
			if (map.containsKey("editTime") && null != map.get("editTime") && !"".equals(map.get("editTime"))) {
				map.put("editTime", map.get("editTime").substring(0, 10));
			}
		}

		//封装集合里的key
		ExcelUtils excelUtils = new ExcelUtils();
		// 显示的导出表的标题
		String title = "测试环境";
		//导出的文件名
		String fileName = "测试环境";
		//导出表内所需要的字段
		String[] rowName = {"环境名称", "环境类型", "环境状态", "IP地址", "系统名称", "系统负责人", "主机名称", "CPU", "内存信息", "操作系统及版本",
				"数据库及版本", "中间件及版本", "说明", "维护人", "维护时间", "科室", "环境负责人", "系统组件"};
		String[] dataColumnList = {"name", "type", "state", "ip", "systemName", "systemManager", "hostname", "cpu", "memoryInformation",
				"operatingSystemInformation", "dataBaseInformation", "middlewareInformation", "detailedInformation", "editUserName",
				"editTime", "department", "envUserName", "systemComponents"};
		try {
			excelUtils.exportExcel(title, title, rowName, dataColumnList, environmentList, fileName, response, request);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("批量测试环境失败");
			Result.renderError();
		}
	}
}
