package com.jettech.service.impl;

import com.jettech.dao.idao.IDataBackupDao;
import com.jettech.service.iservice.IDataBackupService;
import com.jettech.util.DataBackupUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class DataBackupServiceImpl implements IDataBackupService {

    @Autowired
    private IDataBackupDao dataBackupDao;

    @Autowired
    private DataSourceTransactionManager transactionManager;

    @Value("${databackup.page-size}")
    private int dataBackupPageSize;

    @Override
    public String getCreateTableSql(String tableName) {
        try {
            Map<String, String> createSqlMap = dataBackupDao.getCreateTableSql(tableName);
            if (createSqlMap != null) {
                return createSqlMap.get("Create Table") + ";\n";
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return null;
    }

    public List<Map<String, Object>> queryTableData(String tableName, Map<String, Object> params) {
        return dataBackupDao.queryTableData(tableName, params);
    }

    @Override
    public int countTableData(String tableName, Map<String, Object> params) {
        return dataBackupDao.countTableData(tableName, params);
    }

    public List<Map<String, Object>> getAllTable() {
        return dataBackupDao.getAllTable();
    }

    public int deleteTableData(String tableName, Map<String, Object> params) {
        return dataBackupDao.deleteTableData(tableName, params);
    }

    @Override
    public int dataBackup(List<Long> demandResourceID, String filePath, String fileNam, boolean clear) {
        try {
            DataBackupUtil dataBackupUtil = new DataBackupUtil(this, demandResourceID, filePath, fileNam);
            dataBackupUtil.setTransactionManager(transactionManager);
            dataBackupUtil.setPageSize(this.dataBackupPageSize);
            dataBackupUtil.backupDemandTestData();
            if (clear) {
                dataBackupUtil.clearDemandData();
            }
            return 1;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }
}
