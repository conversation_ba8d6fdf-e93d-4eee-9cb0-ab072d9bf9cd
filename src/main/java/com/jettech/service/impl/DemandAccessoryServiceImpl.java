package com.jettech.service.impl;

import com.jettech.common.dto.FileInfo;
import com.jettech.common.dto.Result;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.util.FtpUtil;
import com.jettech.common.util.ParamConfig;
import com.jettech.dao.idao.IDemandAccessoryDao;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.model.DemandAccessory;
import com.jettech.service.iservice.IDemandAccessoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Base64;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-09-28 16:25
 */
@Service
public class DemandAccessoryServiceImpl extends BaseServiceImpl<DemandAccessory> implements IDemandAccessoryService {
    @Autowired
    private IDemandAccessoryDao iDemandAccessoryDao;

    @Autowired
    private IDemandAccessoryService demandAccessoryService;

    @Autowired
    private ParamConfig paramConfig;
    @Autowired
    private FtpUtil ftpUtil;
    @Value("${use_file_service}")
    private boolean useFileService;
    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;
    @PostConstruct
    private void postConstruct() {
        baseDao = this.iDemandAccessoryDao;
    }

    /**
     *
     * @Title: findByDefectresourceID
     * @Description: 查询附件信息
     * @param resourceID
     * @return
     * <AUTHOR>
     */
    @Override
    public Result<?> findByDefectresourceID(String resourceID) {
        if(useFileService){
            Result<List<FileInfo>> data=feignDataDesignToFileService.getFileInfoList(Long.parseLong(resourceID), ObjectTypeEnum.DEMAND.getValue());
            return data;
        }else{
            List<DemandAccessory> listDemandAccessory = iDemandAccessoryDao.findByDefectresourceID(resourceID);
            return Result.renderSuccess(listDemandAccessory);
        }

    }

    /**
     *
     * @Title: downloadDemand
     * @Description: 下载附件
     * @param mapParam
     * @return
     * <AUTHOR>
     * @throws IOException
     */
    @Override
    public String downloadDemand(Map<String, Object> mapParam) throws IOException {
        HttpServletResponse response = (HttpServletResponse) mapParam.get("response");
        String resourceID = (String) mapParam.get("resourceID");
        byte[] data=null;
        if(useFileService){
            byte[] data1=feignDataDesignToFileService.getContent(Long.parseLong(resourceID));
             data = Base64.getMimeDecoder().decode(data1);

        }else{
            DemandAccessory demandFile = demandAccessoryService.findByResourceID(Long.valueOf(resourceID));
            //时间戳。。
            String extname = demandFile.getExtname();
            String path = demandFile.getPath();
            data = this.getFileData(resourceID, extname,path);
        }


        response.setHeader("Content-Disposition", "attachment;filename=" + "XXXX"+"的附件下载.zip");
        response.getOutputStream().write(data);
        return null;
    }
    /**
     *
     * @Title: getFileData
     * @Description: 获取附件
     * @param resourceID
     * @param exName
     * @return
     * <AUTHOR>
     * @param path
     */
    @Override
    public byte[] getFileData(String resourceID,String exName, String path) {

        InputStream in = null;
        InputStream inputStream=null;
        if(paramConfig.getIsFtpOn()) {//判断ftp是否打开
            in = ftpUtil.startDown(paramConfig.getFtpPath()+path+ File.separator+resourceID+exName);
            ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
            byte[] buff = new byte[1024]; //buff用于存放循环读取的临时数据
            int rc = 0;
            try {
                while ((rc = in.read(buff)) != -1) {
                    byteStram.write(buff, 0, rc);
                }
            } catch (IOException e) {
                e.printStackTrace();;
            }
            byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果

            return bytes;
        }else {
            File file = new File(paramConfig.getAttachmentPath() + File.separator+resourceID+exName);
            if(file.exists()){
                try {
                     inputStream = new FileInputStream(file);
                    byte[] data=new byte[(int) file.length()];
                    inputStream.read(data);
                    inputStream.close();
                    return data;
                } catch (Exception e) {
                    e.printStackTrace();;
                }
                finally {
                    try {
                        if(inputStream!=null){
                            inputStream.close();
                        }
                    }catch (Exception e){

                    }
                }
            }

        }
        return null;
    }
}
