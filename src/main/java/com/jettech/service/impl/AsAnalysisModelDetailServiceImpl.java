package com.jettech.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jettech.common.dto.Result;
import com.jettech.dto.AsAnalysisModelDetailDTO;
import com.jettech.enums.ReviewStatusEnums;
import com.jettech.mapper.AsAnalysisModelDetailMapper;
import com.jettech.model.AsAnalysisModelDetail;
import com.jettech.service.iservice.IAsAnalysisModelDetailService;
import com.jettech.vo.AsAnalysisModelDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    * 测试分析模型审核表 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/

@Service
public class AsAnalysisModelDetailServiceImpl  extends ServiceImpl<AsAnalysisModelDetailMapper, AsAnalysisModelDetail> implements IAsAnalysisModelDetailService {

    /**
    * 保存
    *
    * @param dto 参数
    * @return 保存结果
    */
    @Override
    public Result<Boolean> saveOne(AsAnalysisModelDetailDTO dto) {
        AsAnalysisModelDetail entity = new AsAnalysisModelDetail();
        BeanUtils.copyProperties(dto,entity);
        return Result.renderSuccess(this.save(entity));
    }

    /**
    * 根据主键查询VO
    *
    * @param id 主键
    * @return VO
    */
    @Override
    public Result<AsAnalysisModelDetailVO> getById(String id) {
        AsAnalysisModelDetail entity = this.getById(Long.valueOf(id));
        if(null == entity){
            return Result.renderError("数据不存在");
        }
        AsAnalysisModelDetailVO entityVO = new AsAnalysisModelDetailVO();
        BeanUtils.copyProperties(entity,entityVO);
        return Result.renderSuccess(entityVO);
    }

    /**
    * 根据主键删除
    *
    * @param id 主键
    * @return 删除结果
    */
    @Override
    public Result<Boolean> deleteById(String id) {
        return Result.renderSuccess(this.removeById(id));
    }

    /**
     * 评审/修改记录
     * @param id 主键id
     * @return
     */
    @Override
    public List<AsAnalysisModelDetailVO> reviewRecords(String id) {
        List<AsAnalysisModelDetailVO> asAnalysisModelDetailVOList =  this.baseMapper.reviewRecords(id);
        asAnalysisModelDetailVOList.forEach(e->{
            // 状态
            e.setStatusDesc(ReviewStatusEnums.getDesc(e.getStatus()));
        });
        return asAnalysisModelDetailVOList;
    }

}
