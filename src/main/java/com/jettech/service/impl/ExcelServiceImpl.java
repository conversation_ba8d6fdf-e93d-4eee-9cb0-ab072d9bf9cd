package com.jettech.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.Iterators;
import com.jettech.DTO.TaskTradeCaseDto;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.StatusCode;
import com.jettech.common.dto.basic.FieldConfigurationDTO;
import com.jettech.common.dto.datadesign.SystemModuleTradeDTO;
import com.jettech.common.dto.datadesign.TestCaseDTO;
import com.jettech.common.dto.trp.DataDictionaryDTO;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.DateUtil;
import com.jettech.common.util.ExcelUtils;
import com.jettech.common.util.*;
import com.jettech.common.util.constants.testcase.TestCaseConstant;
import com.jettech.dao.idao.ITestCaseDao;
import com.jettech.feign.IFeignDataDesignToAssets;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import com.jettech.util.*;
import com.jettech.view.TaskTradeCaseView;
import com.jettech.view.TestCaseExcelView;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ImportExcelServiceImpl
 * @projectName jettopro
 * @description: excel导入导出
 * @date 2019/11/411:53
 */
@Service
//@Transactional
public class ExcelServiceImpl implements IExcelService {

    @Autowired
    private ITestProjectService testProjectService;
    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private ITestSystemService testSystemService;
    @Autowired
    private ISystemModuleService systemModuleService;
    @Autowired
    private ITradeService tradeService;
    @Autowired
    private DataDicUtil dataDicUtil;
    @Autowired
    private IDemandService demandService;
    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasicService;
    @Autowired
    private ITestCaseRecycleBinService iTestCaseRecycleBinService;
    @Autowired
    private IProjectGroupService projectGroupService;
    @Autowired
    private ITestProjectService projectService;
    @Autowired
    private IFeignDataDesignToAssets feignDataDesignToAssets;
    @Autowired
    private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;
    @Autowired
    private ParamConfig paramConfig;
    private SimpleDateFormat sfEnd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat sfStart = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", java.util.Locale.ENGLISH);
    private DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final Logger logger = LoggerFactory.getLogger(ExcelServiceImpl.class);


    /**
     * 获取系统字典
     *
     * @param type
     * @param token
     * @return
     */
    private Map<String, String> getSysDictionary(String type, String token) {

        Result res = feignDataDesignToBasicService.findByName(type, token);//findByNamesFromDataDesign("CASETYPE");
        if (!res.isSuccess() || res.getObj() == null) {
            return null;
        }
        List<Map<String, String>> list = (List<Map<String, String>>) res.getObj();
        return list.stream().reduce(new HashMap<>(), (a, b) -> {
            if (b.get("value") != null && !StringUtils.isEmpty(b.get("value"))
                    && b.get("textName") != null && !StringUtils.isEmpty(b.get("textName"))) {
                a.put(b.get("value"), b.get("textName"));
            }
            return a;
        }, (a, b) -> null);
    }

    /**
     * 获取自定义字典
     *
     * @param type
     * @return
     */
    private Map<String, String> getCustomDictionary(String type) {
        List<Map<String, Object>> list = testCaseService.findTestCaseDictionaryDataByInfoName(type);
        Map<String, String> commentsDicMap = new HashMap<>();
        if (list == null || list.size() == 0) {
            return null;
        }
        for (Map<String, Object> map1 : list) {
            if (map1.get("value") != null && map1.get("textName") != null && !StringUtils.isEmpty(map1.get("value"))
                    && !StringUtils.isEmpty(map1.get("textName"))) {
                commentsDicMap.put(String.valueOf(map1.get("value")), String.valueOf(map1.get("textName")));
            }
        }
        return commentsDicMap;
    }


    /**
     * 获取所有字典
     *
     * @param fieldDefinitionList
     * @return 字典   key: aliseName  value : dictionary value and textName  map
     */
    private Map<String, Map<String, String>> getDic(List<Map<String, Object>> fieldDefinitionList) {
        Map<String, Map<String, String>> fieldDic = new HashMap<>();
        String token = HttpRequestUtils.getCurrentRequestToken();
        Map<String, String> caseTypeMap = getSysDictionary("CASETYPE", token);
        if (caseTypeMap != null) {
            fieldDic.put("案例类型", caseTypeMap);
        }
        Map<String, String> caseLevelMap = getSysDictionary("CASETLEVEL", token);
        if (caseLevelMap != null) {
            fieldDic.put("案例级别", caseLevelMap);
        }
        Map<String, String> caseReviewStatusMap = getSysDictionary("REVIEWSTATUS", token);
        if (caseReviewStatusMap != null) {
            fieldDic.put("评审状态", caseReviewStatusMap);
        }
        Map<String, String> testModeMap = getSysDictionary("testMode", token);
        if (testModeMap != null) {
            fieldDic.put("测试方式", testModeMap);
        }
        Map<String,String> mapSelect = new HashMap<>();
        mapSelect.put("测试方式", "3");
        Map<String,String> mapDate = new HashMap<>();
        for (Map<String, Object> obj : fieldDefinitionList) {
            if ("2".equals(obj.get("fieldType")) || "3".equals(obj.get("fieldType"))) {
                if (StringUtils.isEmpty(obj.get("aliasName"))) {
                    continue;
                }
                String name = obj.get("aliasName").toString().trim();
                Map<String, String> dic = null;
                if (name.equals("正反例")) {
                    dic = new HashMap<>();
                    dic.put("1", "正例");
                    dic.put("0", "反例");
                } else {
                    if (StringUtils.isEmpty(obj.get("aliasName"))) {
                        continue;
                    }
                    dic = getCustomDictionary(obj.get("aliasName").toString());
                }
                if (dic != null && !dic.isEmpty()) {
                    fieldDic.put(obj.get("aliasName").toString(), dic);
                }
                mapSelect.put(obj.get("aliasName").toString(),obj.get("fieldType").toString());
            }
            //日期类型
            if ("5".equals(obj.get("fieldType"))) {
                mapDate.put(obj.get("aliasName").toString(),obj.get("fieldType").toString());
            }
        }
        fieldDic.put("selectConfig",mapSelect);
        fieldDic.put("dateConfig",mapDate);
        if(mapSelect.isEmpty()){
            fieldDic.put("selectConfig",null);
        }
        if(mapDate.isEmpty()){
            fieldDic.put("dateConfig",null);
        }
        return fieldDic;
    }


    /**
     * 案例编写导出案例
     *
     * @param map
     * @return
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> exportExcel(Map map) {
        HttpServletResponse response = (HttpServletResponse) map.get("response");
        HttpServletRequest request = (HttpServletRequest) map.get("request");
        if (map.get("taskResourceID") == null) {
            return Result.renderError("入参[taskResourceID]为空！");
        }
        String taskResourceID = (String) map.get("taskResourceID");
        //筛选勾选的案例
//        String testSystemRids = map.get("testCaseRids") ==null ||"".equals(map.get("testCaseRids")) ? "":String.valueOf(map.get("testCaseRids")).trim();
//        if(map.get("testCaseResourceIDs") ==null ||"".equals(map.get("testCaseResourceIDs"))){
//            map.put("testCaseResourceIDs", testSystemRids);
//        }
        //获取所有trade
        List<Trade> tradesList = null;
        String trades = (String) map.get("trade");
        boolean flag=false; //用来区分案例编写页面
        if (trades == null) {
        	flag=true;
            tradesList = tradeService.findSelectedTradeByTaskResourceID(taskResourceID);
        } else {
            String[] tradeRids = trades.split(",");
            tradesList = tradeService.findByResourceIDIn(Arrays.asList(tradeRids));
        }

        if (tradesList == null || tradesList.size() == 0) {
            return Result.renderError("没有可导出的交易");
        }

        if (map.get("selectedFields") == null) {
            return Result.renderError("入参[selectedFields]为空！");
        }

        //测试方式字段-20220516dwl
		String testMode = (map.get("testMode") == null || "".equals(map.get("testMode"))) ? "0"
				: String.valueOf(map.get("testMode")).trim();// 测试方式
		testMode = BinaryDecimalUtil.DicValToBin(testMode);
		map.put("testMode", testMode);

        // 导出字段
        List selectedFields = (List) map.get("selectedFields");
        List<String> head = new ArrayList<>();
        head.addAll(selectedFields);

        String token = HttpRequestUtils.getCurrentRequestToken();

        //查询案例字段配置启用字段
        Result<List<FieldConfigurationDTO>> fields = feignDataDesignToBasicService.findFieldAndDicValuesByConfigType(token);
        List<FieldConfigurationDTO> fieldDefinitionList = fields.getObj();
        if (fieldDefinitionList == null || fieldDefinitionList.size() == 0) {
            return Result.renderError("未能获取到字段配置");
        }

        String tmpFileDir = paramConfig.getAttachmentPath() + File.separator + paramConfig.getTemporaryFolderName() + File.separator + System.currentTimeMillis() + "/";
        logger.info("case export tmp dir" + tmpFileDir);

        try {
        	//当前无任务时，导出案例数据会把平台所有需求案例全量导出（需仅导出案例模板) pro-2952
        	if("".equals(map.get("taskResourceID"))&&flag==true) {
        		String  Agent = (String) map.get("Agent");
        		downloadCaseModel(fieldDefinitionList,head,Agent,token,tmpFileDir);
        	}else {
        		Result<List<TestSystem>> res = testSystemService.findTestSystemsByTestTaskResourceId(taskResourceID);
                if (res.getObj() == null || res.getObj().size() == 0) {
                    return Result.renderError("未能获取到被测系统");
                }
                List<TestSystem> testSystems = res.getObj();
                for (TestSystem testSystem : testSystems) {
	                List<Trade> tradesOfSys = new ArrayList<>();
	                if (tradesList != null) {
	                    tradesOfSys = tradesList.stream().filter(x ->
	                            x.getTestSystemResourceID().toString().equals(testSystem.getResourceID().toString())).collect(Collectors.toList());
	                }
	                if (tradesOfSys.size() > 0) {
	                    //案例系统下所选的案例数据
	                    exportSystemTrade(tmpFileDir, !org.apache.commons.lang.StringUtils.isEmpty(taskResourceID) ? Long.valueOf(taskResourceID) : null, testSystem, tradesOfSys, head, fieldDefinitionList, map);
	                }
                 }
        	}

            logger.info("本地文件夹生成成功。。");
            byte[] data =ExportUtil.createZip(tmpFileDir);// 服务器存储地址

            OutputStream out = response.getOutputStream();

            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("a.zip", "utf-8"));
            response.addHeader("Content-Length", "" + data.length);
            response.addHeader("X-Frame-Options", "DENY");
            response.setContentType("application/octet-stream;charset=UTF-8");

            IOUtils.write(data, out);
            // out.write(data.toString().getBytes("utf-8"));
            // 删除本地文件夹
            ExportUtil.delFile(new File(tmpFileDir));
            logger.info("本地文件夹删除成功。。");
            out.close();
            if (out != null) {
                out.flush();
                out.close();
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Autowired
    private ITestCaseDao testCaseDao;
    /**
     * 导出系统下所选交易案例
     *
     * @param tmpFileDir          临时文件夹
     * @param taskRid             taskRid
     * @param testSystem          被测系统
     * @param tradesOfSys         所选交易
     * @param head                表头
     * @param fieldDefinitionList 字段定义
     * @param
     * <AUTHOR>
     */
    public void exportSystemTrade(String tmpFileDir, Long taskRid, TestSystem testSystem, List<Trade> tradesOfSys,
                                  List<String> head, List<FieldConfigurationDTO> fieldDefinitionList, Map paramMap) {
        String createUser = paramMap.get("createUser") == null ? "" : String.valueOf(paramMap.get("createUser")).trim();
        String isNegative = paramMap.get("isNegative") == null ? "" : String.valueOf(paramMap.get("isNegative")).trim();
        String caseType = paramMap.get("caseType") == null ? "" : String.valueOf(paramMap.get("caseType")).trim();
        String caseId = paramMap.get("caseId") == null ? "" : String.valueOf(paramMap.get("caseId")).trim();// 案例编号、导入案例编号或测试意图
        String coverage = paramMap.get("coverage") == null ? "" : String.valueOf(paramMap.get("coverage")).trim();//测试覆盖情况
        String leadsource = paramMap.get("leadsource") == null ? "" : String.valueOf(paramMap.get("leadsource")).trim();//测试覆盖情况
        //测试案例resourceId和测试方式-查询过滤20220516dwl
        String testCaseResourceIDs = paramMap.get("testCaseResourceIDs") == null ? "" : String.valueOf(paramMap.get("testCaseResourceIDs"));
        String testMode = String.valueOf(paramMap.get("testMode"));

        String targetFilename = tmpFileDir + testSystem.getName() + ".xlsx";
        File targetFile = new File(targetFilename);
        targetFile.getParentFile().mkdirs();
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(targetFile).useDefaultStyle(false).build();
            ExportUtil<TestCase> exportUtil = new ExportUtil<>();

            List<String> dicKeys = exportUtil.writeDicSheet(excelWriter, fieldDefinitionList); //写字典表 并返回有序的字典类型list

            dataDicUtil.getDicMap("testSystem");
            List<Map<String, Object>> testModeList = dataDicUtil.getList("testMode");
            Map<String, String> tmMap = testModeList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));
            int m= CheckUtil.checkLoop(tradesOfSys.size());
            for (int i = 0; i <m ; i++) {
                Trade trade = tradesOfSys.get(i);
                String sheetName = trade.getName();
                sheetName = sheetName.replace('/', '-')
                        .replace('\\', '-')
                        .replace('?', '？')
                        .replace('[', '【')
                        .replace(']', '】');
                List<TestCase> testCaseList = new ArrayList<>();
                //查询案例数据
                if(coverage!=""){
                    List<TestCase> res = testCaseService.findByTradeRidAndTaskRid(trade.getResourceID(), taskRid,createUser,
                            isNegative, caseType, caseId, testMode, testCaseResourceIDs, leadsource);
                    List<Map<String, Object>> testCaselist = testCaseDao.findByTradeResIdsAndTaskResIdPage(Arrays.asList(trade.getResourceID()), taskRid==null?"":taskRid.toString(),
                            1, 1000, false, createUser, isNegative, caseType, caseId, null, null, testMode,
                            "", null, coverage);
                    //案例的rid集合
                    List<String> relationResourceIDs = testCaselist.stream().map(e -> String.valueOf(e.get("resourceID"))).collect(Collectors.toList());
                    testCaseList = res.stream().filter(new Predicate<TestCase>() {
                        public boolean test(TestCase scaleDO) {
                            for (String children : relationResourceIDs) {
                                if(scaleDO.getResourceID().toString().equals(children)){
                                    return true;
                                }
                            }
                            return false;
                        }
                    }).collect(Collectors.toList());
                } else{
                    testCaseList = testCaseService.findByTradeRidAndTaskRid(trade.getResourceID(), taskRid,createUser,
                            isNegative, caseType, caseId, testMode, testCaseResourceIDs, leadsource);
                }

                // 根据交易查询模块
                SystemModule systemModule = systemModuleService.findByResourceID(trade.getModuleResourceID());
                testCaseList.forEach(x -> {
                    if (x.getTestMode() != null) {
                        List<String> strs = BinaryDecimalUtil.TenToDicVal(x.getTestMode());
                        String testModeValue = "";
                        for (String s : strs) {
                            testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) +",";
                        }
                        x.setTestModeName("".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
                    }
                    x.setTestsystem(testSystem.getName());
                    x.setTrade(trade.getName());
                    if (systemModule != null) {
                        x.setSystemmodule(systemModule.getName());
                    }
                });
                List<List<String>> excelHead = head.stream().reduce(new ArrayList<>(), (a, b) -> {
                    List<String> arrays = new ArrayList<>();
                    arrays.add(b);
                    a.add(arrays);
                    return a;
                }, (a, b) -> null);
                //行前补充系统名称 交易名
                List<String> preCells = new ArrayList<>();
//                preCells.add(testSystem.getName());
//                preCells.add(trade.getName());
                List<List<String>> data = exportUtil.getExcelData(preCells, head, fieldDefinitionList, testCaseList, null);
//                head.add("所属系统");
//                head.add("所属交易");
                TradeExcelWriteCellHandler cellHandler = new TradeExcelWriteCellHandler(head, fieldDefinitionList, dicKeys);
                WriteSheet writeSheet = EasyExcel.writerSheet(i + 1, sheetName).useDefaultStyle(false)
                        .registerWriteHandler(cellHandler).head(excelHead).build();
                excelWriter.write(data, writeSheet);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("写入excel错误", e);
        } finally {
            if (excelWriter != null) {
                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
                Sheet sheet = workbook.getSheet("Dictionary");
                if (sheet != null && workbook.getNumberOfSheets() > 1) {
                    workbook.setActiveSheet(1);
                    workbook.setSheetHidden(0, true);
                }
                excelWriter.finish();
            }
        }
    }

/**
 *
 *@Description 案例编写 无任务时导出案例模板
 *@param fieldDefinitionList 案例字段信息
 *@param head    案例所选导出字段信息（表头）
 *@param tmpFileDir 临时文件夹目录
 *@return void
 *<AUTHOR>
 *@Date 2022年12月5日
 */
    public void downloadCaseModel( List<FieldConfigurationDTO> fieldDefinitionList ,List<String> head,String agent,String token,String tmpFileDir) throws Exception {
      String targetFilename = tmpFileDir + "案例模板.xlsx";
      File targetFile = new File(targetFilename);
      targetFile.getParentFile().mkdirs();
      ExcelWriter excelWriter = null;
      try {
    	  excelWriter = EasyExcel.write(targetFile).useDefaultStyle(false).build();
          ExportUtil<TestCase> exportUtil = new ExportUtil<>();
          List<String> dicKeys = exportUtil.writeDicSheet(excelWriter, fieldDefinitionList); //写字典表 并返回有序的字典类型list
          List<List<String>> excelHead = head.stream().reduce(new ArrayList<>(), (a, b) -> {
              List<String> arrays = new ArrayList<>();
              arrays.add(b);
              a.add(arrays);
              return a;
          }, (a, b) -> null);
          TradeExcelWriteCellHandler cellHandler = new TradeExcelWriteCellHandler(head, fieldDefinitionList, dicKeys);
          WriteSheet writeSheet = EasyExcel.writerSheet(1, null).useDefaultStyle(false)
              .registerWriteHandler(cellHandler).head(excelHead).build();
          excelWriter.write(new ArrayList<>(), writeSheet);
	   } catch (Exception e) {
		  e.printStackTrace();
          logger.error("模板下载错误", e);
	   }finally {
		  if (excelWriter != null) {
	          Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
	          Sheet sheet1 = workbook.getSheet("Dictionary");
	          if (sheet1 != null && workbook.getNumberOfSheets() > 1) {
	              workbook.setActiveSheet(1);
	              workbook.setSheetHidden(0, true);
	          }
	          excelWriter.finish();
	      }
	  }
  }


    public String encodeDownloadFilename(String filename, String agent)
            throws IOException {
        if (agent.contains("Firefox")) { // 火狐浏览器
            filename = "=?UTF-8?B?"
                    + Base64Utils.encodeToString(filename.getBytes(StandardCharsets.UTF_8))
                    + "?=";
            filename = filename.replaceAll("\r\n", "");
        } else { // IE及其他浏览器
            filename = URLEncoder.encode(filename, "utf-8");
            filename = filename.replace("+", "");
        }
        filename = new String(filename.getBytes("UTF-8"), "iso-8859-1");
        return filename;
    }

    public static boolean linuxZip(String zipfile, String folder) {
        try {
            File file = new File(folder);
            if (!file.exists()) {
                return false;
            }
            if (file.isDirectory() && file.listFiles().length == 0) {
                return false;
            }
            System.out.println("folder:" + folder);
            Process proc = Runtime.getRuntime().exec(new String[]{"zip -q -r " + zipfile + " " + folder + ""});
            if (proc.waitFor() != 0) {
                if (proc.exitValue() == 0)
                    return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 递归判断module是否在集合中
     *
     * @param moduleResourceID
     * @param resourceID_list
     * @return
     */
    private boolean findParent(Long moduleResourceID, List<String> resourceID_list) {
        SystemModule sm = systemModuleService.findByResourceID(moduleResourceID);
        if (sm == null) {
            return false;
        }
        if (resourceID_list.contains(String.valueOf(sm.getResourceID()))
                || resourceID_list.contains(String.valueOf(sm.getParentResourceID()))) {
            return true;
        } else {
            findParent(sm.getParentResourceID(), resourceID_list);
        }
        return false;
    }


    public HSSFWorkbook exportExcel(String[][] data, List<Integer> cellNums) throws ParseException {
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet("案例数据");
        sheet.setDefaultColumnWidth(16);
        HSSFCellStyle cellStyle = wb.createCellStyle();
        HSSFDataFormat format = wb.createDataFormat();
        cellStyle.setDataFormat(format.getFormat("yyyy-MM-dd"));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 0; i < data.length; i++) {
            HSSFRow row = sheet.createRow(i);
            // sheet.autoSizeColumn(i);
            int m=CheckUtil.checkLoop(data[i].length);
            for (int j = 0; j < m; j++) {
                HSSFCell cell = row.createCell(j);
                if (i > 0 && cellNums.contains(j)) {
                    cell.setCellStyle(cellStyle);
                    if (!org.springframework.util.StringUtils.isEmpty(data[i][j])) {
                        //判断是否为时间戳
                        if(data[i][j].matches("\\d+")){
                            cell.setCellValue(new Date(Long.valueOf(data[i][j])));
                        }else{
                            cell.setCellValue(simpleDateFormat.parse(data[i][j]));
                        }
                    }
                } else {
                    cell.setCellValue(data[i][j]);
                }
            }
        }
        return wb;
    }

    public List<List<String>> getExcelData(TestSystem testSystem, Trade trade, List<String> selectedFields, List<TestCase> testCases) throws ParseException, IllegalAccessException {
        List<List<String>> str = new ArrayList<>();
        for (int row = 0; row < testCases.size(); row++) {
            List<String> cellList = new ArrayList<>();
            int m=CheckUtil.checkLoop(selectedFields.size());
            for (int cell = 0; cell < m; cell++) {
                TestCase testCase = testCases.get(row);
                testCase.setTrade(trade.getName());
                testCase.setTestsystem(testSystem.getName());
                cellList.add(getStringValue(selectedFields.get(cell), testCase));
            }
            str.add(cellList);
        }
        return str;
    }

    public String getStringValue(String selectField, TestCase defect) throws IllegalAccessException, ParseException {
        String val = "";
        Class clazz = defect.getClass();
        Field[] fields = clazz.getDeclaredFields();
        Field[] declaredFields = clazz.getSuperclass().getDeclaredFields();
        for (Field f : fields) {
            f.setAccessible(true);
            FieldNote fieldNote = f.getAnnotation(FieldNote.class);
            if (fieldNote == null)
                continue;
            String value = fieldNote.value();
            String name = f.getName();
            if (selectField.equals(value)) {
                if (fieldNote.type() == FieldType.Date) {
                    if (null == f.get(defect)) {
                        val = "";
                    } else {
                        val = sfEnd.format(sfStart.parse(f.get(defect).toString()));
                    }
                } else {
                    val = String.valueOf(f.get(defect) == null ? "" : f.get(defect));
                }
                break;
            }

        }
        for (Field f : declaredFields) {
            f.setAccessible(true);
            FieldNote fieldNote = f.getAnnotation(FieldNote.class);
            if (fieldNote == null || "创建时间".equals(fieldNote.value()))
                continue;
            String value = fieldNote.value();
            if (selectField.equals(value)) {
                if (fieldNote.type() == FieldType.Date) {
                    if (null == f.get(defect)) {
                        val = "";
                    } else {
                        val = sfEnd.format(sfStart.parse(f.get(defect).toString()));
                    }
                } else {
                    val = String.valueOf(f.get(defect) == null ? "" : f.get(defect));
                }
                break;
            }
        }
        return val;
    }


    /**
     * @param "[map]"
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @throws <AUTHOR>
     * @Title: getUrlMap
     * @description: 导出数据处理
     * @date 2019/12/3 20:02
     */
    @Override
    public Map<String, Object> getUrlMap(Map map) {
        String parent_url = map.get("parent_url").toString();
        HashMap<String, Object> url_map = new HashMap<String, Object>();
        StringBuffer sb = new StringBuffer();
        // 处理前台传递的参数,获取需要导出的交易
        List<Trade> tradeList = getTradeList(map);
        logger.info(tradeList.toString());
        /**
         * 1、倒叙查询所有交易 2、通过交易moduleResourceID == null判断，交易是否直接挂在被测系统下
         * 3、递归查询模块，到被测系统向上查询（跳出条件为testsystemrResourceID == parentResourceID） 4、拼接文件路径
         * 5、导出excel（案例）
         */
        tradeList.forEach(e -> {
            sb.setLength(0);
            if (e.getModuleResourceID() == null) {
                // 为空，交易直接挂靠在被测系统下
                TestSystem ts = testSystemService.findByResourceID(e.getTestSystemResourceID());
                String url = sb.append(parent_url).append(ts.getName()).append(File.separator).toString();
                url_map.put(e.getName() + "," + e.getResourceID(), url);
                logger.info("系统下" + url);
            } else {
                // 交易挂靠在模块下
                String url = findToTestSystem(e.getModuleResourceID(), sb);
                sb.setLength(0);
                url = sb.append(parent_url).append(url).toString();
                url_map.put(e.getName() + "," + e.getResourceID(), url);
                logger.info("模块下" + url);
            }
        });
        return url_map;
    }

    /**
     * 递归拼接文件路径
     *
     * @param resourceID
     * @param sb
     * @return
     */
    private String findToTestSystem(Long resourceID, StringBuffer sb) {
        SystemModule sm = systemModuleService.findByResourceID(resourceID);
        if (sm.getParentResourceID().longValue() == sm.getTestSystemResourceID().longValue()) {
            TestSystem ts = testSystemService.findByResourceID(sm.getParentResourceID());
            // return new
            // StringBuffer(HanyuPinyinUtil.getPinyinString(ts.getName())).append("\\").append(HanyuPinyinUtil.getPinyinString(sm.getName())).append("\\").append(sb).toString();
            return new StringBuffer(ts.getName()).append("\\").append(sm.getName()).append("\\").append(sb).toString();
        }
        return findToTestSystem(sm.getParentResourceID(), new StringBuffer(sm.getName()).append("\\").append(sb));
    }

    private List<Trade> getTradeList(Map map) {
        Set set = map.keySet();
        Iterator iterator = set.iterator();
        List<Trade> tradeList = new ArrayList<Trade>();
        int m=CheckUtil.checkLoop(Iterators.size(iterator));
        if(m==CheckUtil.MAX_LOOPS){
            return null;
        }
        iterator = set.iterator();
        while (iterator.hasNext()) {
            String key = String.valueOf(iterator.next());
            String value = String.valueOf(map.get(key));
            if (org.apache.commons.lang3.StringUtils.isBlank(value)) {
                continue;
            }
            String[] resourceIDs = value.split(",");

            List<String> resourceID_list = Arrays.asList(resourceIDs);
            if ("system".equals(key)) {
                List<Trade> list = tradeService.getObjectByTestSystemResourceIDs(resourceID_list);
                tradeList.addAll(list);
            }
            if ("module".equals(key)) {
                List<SystemModule> sm_list = systemModuleService.findByResourceIDIn(resourceID_list);
                // 被测系统RIDs
                List<String> collect = sm_list.stream().map(e -> String.valueOf(e.getTestSystemResourceID())).distinct()
                        .collect(Collectors.toList());
                List<Trade> list = tradeService.getObjectByTestSystemResourceIDs(collect);
                list = list.stream().filter(e -> e.getModuleResourceID() != null).collect(Collectors.toList());

                for (Trade t : list) {
                    if (findParent(t.getModuleResourceID(), resourceID_list)) {
                        tradeList.add(t);
                    }
                }
            }
            if ("trade".equals(key)) {
                List<Trade> list = tradeService.findByResourceIDIn(resourceID_list);
                tradeList.addAll(list);
            }
        }
        return tradeList;
    }

    /**
     * @param "[response]"
     * @return void
     * @throws <AUTHOR>
     * @Title: downloadModel
     * @description: 下载模板
     * @date 2019/11/7 14:55
     */
    @Override
    public void downloadModel(String agent, HttpServletResponse response) {
        InputStream is=null;
        try {
            createAutoModal(agent, response);
        } catch (Exception e) {
            e.printStackTrace();

             is = ExcelServiceImpl.class.getResourceAsStream("/modal.xlsx");
            try {
                OutputStream os = response.getOutputStream();
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + resFileUtils.encodeDownloadFilename("modal.xlsx", agent));
                // response.setContentType("multipart/form-data;charset=UTF-8");
                response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                response.setCharacterEncoding("utf-8");
                response.addHeader("X-Frame-Options", "DENY");
                byte[] b = new byte[1024];
                int len;
                while ((len = is.read(b)) > 0) {
                    os.write(b, 0, len);
                }
                os.close();
                is.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }finally {
                try {
                    if(is!=null){
                        is.close();
                    }
                }catch (Exception ee){

                }
            }
        }

    }

    private void createAutoModal(String agent, HttpServletResponse response) throws Exception {
        dataDicUtil.getDicMap("testCase");
        List<Map<String, Object>> caseType_list = dataDicUtil.getList("caseType");
        List<Map<String, Object>> caseLevel_list = dataDicUtil.getList("caseLevel");
        List<Map<String, Object>> reviewStatus_list = dataDicUtil.getList("reviewStatus");
        List<Map<String, Object>> isNegative_list = dataDicUtil.getList("isNegative");

        String[] caseType_arr = caseType_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);
        String[] caseLevel_arr = caseLevel_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);
        String[] reviewStatus_arr = reviewStatus_list.stream().map(e -> e.get("textName").toString())
                .toArray(String[]::new);
        String[] isNegative_arr = isNegative_list.stream().map(e -> e.get("textName").toString())
                .toArray(String[]::new);

//        String[] caseType_arr = caseType_list.toArray(new String[caseType_list.size()]);
//        String[] caseLevel_arr = caseLevel_list.toArray(new String[caseLevel_list.size()]);
//        String[] reviewStatus_arr = reviewStatus_list.toArray(new String[reviewStatus_list.size()]);
//        String[] isNegative_arr = isNegative_list.toArray(new String[isNegative_list.size()]);

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 100 * 35);
        sheet.setColumnWidth(1, 100 * 35);
        sheet.setColumnWidth(2, 100 * 35);
        sheet.setColumnWidth(3, 100 * 35);
        sheet.setColumnWidth(4, 100 * 35);
        sheet.setColumnWidth(5, 100 * 35);
        sheet.setColumnWidth(6, 100 * 35);
        sheet.setColumnWidth(7, 100 * 35);
        sheet.setColumnWidth(8, 100 * 35);
        sheet.setColumnWidth(9, 100 * 35);
        sheet.setColumnWidth(10, 100 * 35);
        sheet.setColumnWidth(11, 100 * 35);
        sheet.setColumnWidth(12, 100 * 35);

        sheet.setDefaultRowHeight((short) (22 * 20));

        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);

        // 批注
        XSSFDrawing p = sheet.createDrawingPatriarch();
        XSSFComment isNegative_Com = p.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 2, (short) 4, 5));
        isNegative_Com.setString(new XSSFRichTextString("输入正例或者反例，如果输入正或者反系统系统导入转换为正例/反例"));

        XSSFComment caseType_com = p.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 6, 6));
        caseType_com.setString(new XSSFRichTextString("功能案例/流程案例"));

        XSSFComment maintenanceTime_com = p
                .createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 12, 2, (short) 13, 6));
        maintenanceTime_com.setString(new XSSFRichTextString("建议时间格式为：yyyy/MM/dd"));

        XSSFComment caseId_com = p.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 1, 2, (short) 2.3, 6));
        caseId_com.setString(new XSSFRichTextString("建议案例编号格式：编号000001"));

        // 模板列
        XSSFRow row = sheet.createRow(0);
        row.setHeight((short) (22 * 20));

        XSSFCell caseId = row.createCell(0);
        caseId.setCellValue("案例编号");
        caseId.setCellStyle(cellStyle);
        caseId.setCellComment(caseId_com);

        XSSFCell intent = row.createCell(1);
        intent.setCellValue("测试意图");
        intent.setCellStyle(cellStyle);

        XSSFCell isNegative = row.createCell(2);
        isNegative.setCellValue("正反例");
        isNegative.setCellStyle(cellStyle);
        isNegative.setCellComment(isNegative_Com);

        XSSFCell caseType = row.createCell(3);
        caseType.setCellValue("案例类型");
        caseType.setCellStyle(cellStyle);
        caseType.setCellComment(caseType_com);

        XSSFCell casetLevel = row.createCell(4);
        casetLevel.setCellValue("案例级别");
        casetLevel.setCellStyle(cellStyle);

        XSSFCell preconditions = row.createCell(5);
        preconditions.setCellValue("前置条件");
        preconditions.setCellStyle(cellStyle);

        XSSFCell testStep = row.createCell(6);
        testStep.setCellValue("测试步骤");
        testStep.setCellStyle(cellStyle);

        XSSFCell expectedResult = row.createCell(7);
        expectedResult.setCellValue("预期结果");
        expectedResult.setCellStyle(cellStyle);

        XSSFCell timingName = row.createCell(8);
        timingName.setCellValue("运行条件");
        timingName.setCellStyle(cellStyle);

        XSSFCell comment = row.createCell(9);
        comment.setCellValue("备注");
        comment.setCellStyle(cellStyle);

        XSSFCell maintainer = row.createCell(10);
        maintainer.setCellValue("创建人");
        maintainer.setCellStyle(cellStyle);

        XSSFCell maintenanceTime = row.createCell(11);
        maintenanceTime.setCellValue("创建时间");
        maintenanceTime.setCellStyle(cellStyle);
        maintenanceTime.setCellComment(maintenanceTime_com);

        XSSFCell reviewStatus = row.createCell(12);
        reviewStatus.setCellValue("评审状态");
        reviewStatus.setCellStyle(cellStyle);

        this.setValidationData(sheet, 1, 9999, 3, 3, caseType_arr);
        this.setValidationData(sheet, 1, 9999, 2, 2, isNegative_arr);
        this.setValidationData(sheet, 1, 9999, 4, 4, caseLevel_arr);
        this.setValidationData(sheet, 1, 9999, 12, 12, reviewStatus_arr);

        ServletOutputStream out = response.getOutputStream();
        response.setHeader("Content-Disposition",
                "attachment;filename=" + resFileUtils.encodeDownloadFilename("modal.xlsx", agent));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
        response.addHeader("X-Frame-Options", "DENY");

        wb.write(out);
        out.close();
    }

    /**
     * 添加数据有效性检查.
     *
     * @param sheet              要添加此检查的Sheet
     * @param firstRow           开始行
     * @param lastRow            结束行
     * @param firstCol           开始列
     * @param lastCol            结束列
     * @param explicitListValues 有效性检查的下拉列表
     * @throws IllegalArgumentException 如果传入的行或者列小于0(< 0)或者结束行/列比开始行/列小
     */
    public static void setValidationData(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol,
                                         String[] explicitListValues) throws IllegalArgumentException {
        if (firstRow < 0 || lastRow < 0 || firstCol < 0 || lastCol < 0 || lastRow < firstRow || lastCol < firstCol) {
            throw new IllegalArgumentException(
                    "Wrong Row or Column index : " + firstRow + ":" + lastRow + ":" + firstCol + ":" + lastCol);
        }
        if (sheet instanceof XSSFSheet) {
            XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) sheet);
            XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
                    .createExplicitListConstraint(explicitListValues);
            CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
            XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        } else if (sheet instanceof HSSFSheet) {
            CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
            DVConstraint dvConstraint = DVConstraint.createExplicitListConstraint(explicitListValues);
            DataValidation validation = new HSSFDataValidation(addressList, dvConstraint);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        }
    }


    @SuppressWarnings("unchecked")
    public Result showExportFields() {
        HashMap<String, Object> result = new HashMap<>();
        ArrayList<Map<String, String>> selected = new ArrayList<>();
        ArrayList<Map<String, String>> unSelected = new ArrayList<>();
    /*TestCase testCase = new TestCase();
//      List<String> selected_list = Arrays.asList(selected_str);
    Field[] fields = testCase.getClass().getDeclaredFields();
    Field[] declaredFields = testCase.getClass().getSuperclass().getDeclaredFields();
    for (Field f : fields) {
      f.setAccessible(true);
      String name = f.getName();
      FieldNote fieldNote = f.getAnnotation(FieldNote.class);
      if (fieldNote == null)
        continue;
      HashMap<String, String> maps = new HashMap<>();
      String value = fieldNote.value();
      maps.put("key", name);
      maps.put("label", value);
      selected.add(maps);
    }
    for (Field f : declaredFields) {
      f.setAccessible(true);
      String name = f.getName();
      FieldNote fieldNote = f.getAnnotation(FieldNote.class);
      if (fieldNote == null)
        continue;
      HashMap<String, String> maps = new HashMap<>();
      String value = fieldNote.value();
      if (value.equals("修改时间")) {
        continue;
      }
      maps.put("key", name);
      maps.put("label", value);
      selected.add(maps);
    }*/
        //查询案例字段配置启用字段
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result<?> data = feignDataDesignToBasicService.findByTestCaseExportFields(token);
        List<Map<String, String>> list = (List<Map<String, String>>) data.getObj();
        for (Map<String, String> obj : list) {
            Map<String, String> map = new HashMap<>();
            map.put("key", obj.get("name"));
            map.put("label", obj.get("aliasName"));
            if (obj.get("writeRequired") != null && Boolean.valueOf(String.valueOf(obj.get("writeRequired")))) {
                //必录字段
                selected.add(map);
            } else {
                //非必录字段
                unSelected.add(map);
            }
        }
        Map<String, String> map3 = new HashMap<>();
        map3.put("key","testsystem");
        map3.put("label","所属系统");
        selected.add(map3);
        Map<String, String> map5 = new HashMap<>();
        map5.put("key","trade");
        map5.put("label","所属交易");
        selected.add(map5);

        Map<String, String> map4 = new HashMap<>();
        map4.put("key","systemmodule");
        map4.put("label","所属模块");
        unSelected.add(map4);

        result.put("selected", selected);
        result.put("unSelected", unSelected);

        return Result.renderSuccess(result);
    }

    /**
     * @Title importExcelNew
     * @description 导入案例
     * @date 20200918
     * <AUTHOR>
     */
    @Override
    public Result<?> importExcel(MultipartFile file, String nodeType, String nodeResourceID,
                                 String userNumber, String taskResourceID) {
        if (file == null) {
            return Result.renderError("导入文件为空！");
        }
        String fileName = file.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!fileType.toLowerCase().equals("xls") && !fileType.toLowerCase().equals("xlsx")) {
            return Result.renderError("导入的文件格式不正确，必须为excel文件！");
        }
//        if (StringUtils.isEmpty(taskResourceID)) {
//            return Result.renderError("参数【taskResourceID】为空！");
//        }

        List<TestSystem> testSystems = null;
        if ("system".equals(nodeType)) {
            TestSystem ts = testSystemService.findByResourceID(Long.valueOf(nodeResourceID));
            testSystems = new ArrayList<>();
            testSystems.add(ts);
        } else {
            testSystems = testSystemService.findTestSystemsByTestTaskResourceId(taskResourceID).getObj();
        }
        if (testSystems == null || testSystems.size() == 0) {
            return Result.renderError("未能获取到被测系统！");
        }
        List<Trade> tradeList = tradeService.findSelectedTradeByTaskResourceID(taskResourceID);
        if (tradeList == null || tradeList.size() == 0) {
            return Result.renderError("未能获取到交易！");
        }
        return readExcel(file, testSystems, tradeList, userNumber, taskResourceID);
    }

    /**
     * 案例编写读取excel并导入
     *
     * @param file
     * @param testSystems
     * @param tradeList
     * @param userNumber
     * @return
     * <AUTHOR>
     */
    private Result<?> readExcel(MultipartFile file, List<TestSystem> testSystems, List<Trade> tradeList, String userNumber
            , String taskResourceID) {

        String token = HttpRequestUtils.getCurrentRequestToken();
        Map<String, String> testPlan = null;
        if (!org.apache.commons.lang3.StringUtils.isEmpty(taskResourceID)) {
            testPlan = feignDataDesignToManexecuteService.findTestPlanByTestTaskResourceID(Long.parseLong(taskResourceID), token);
        }
//        if (testPlan == null || testPlan.isEmpty()) {
//            return Result.renderError("测试任务或测试计划异常！");
//        }

        //回收站案例
        Map<Long, List<TestCaseRecycleBin>> testCaseRecycleBinsOfTrade = new HashMap<>();
        //交易下所有的caseId
        Map<Long, List<String>> caseIdsOfTrade = new HashMap<>();
        //此任务下已有的testCase
        Map<Long, List<TestCase>> caseOfTaskTrade = new HashMap<>();

        //？？？作用不明
        Map<Long, String> baseCaseEditIdOfTaskTrade = new HashMap<>();
        //查询回收站案例数据
        List<TestCaseRecycleBin> findAll = iTestCaseRecycleBinService.findAll();
        Map<Long, List<TestCaseRecycleBin>> tradeMap = findAll.stream()
                .collect(Collectors.groupingBy(s -> s.getTradeResourceID()));
        long c3 = System.currentTimeMillis();
       List<TestCase> findAll2 = testCaseService.findAll();
       
       //查询资产库数据
       Result<?> AssetResult = feignDataDesignToAssets.findAll(token);
       Map<Long,List<String>> AssetMap = (Map<Long, List<String>>) AssetResult.getObj();
       long c1 = System.currentTimeMillis();
       System.out.println(" 查询案例和资产库案例用时：-----"+(c3-c1));
       for (Trade trade : tradeList) {
        	List<TestCaseRecycleBin> testCaseRecycleBins = tradeMap.get(trade.getResourceID());
        	testCaseRecycleBinsOfTrade.put(trade.getResourceID(), testCaseRecycleBins==null?new ArrayList():testCaseRecycleBins);

//            List<TestCaseRecycleBin> testCaseRecycleBins = iTestCaseRecycleBinService.findByTradeResourceID(trade.getResourceID());
//            testCaseRecycleBinsOfTrade.put(trade.getResourceID(), testCaseRecycleBins);

        	 //项目案例库的案例
            List<String> list = AssetMap.get(trade.getResourceID());
            caseIdsOfTrade.put(trade.getResourceID(), list==null?new ArrayList():list);
//            Result<List<String>> result = feignDataDesignToAssets.findCaseIDByTradeResourceID(trade.getResourceID(), token);
//            caseIdsOfTrade.put(trade.getResourceID(), result.getObj());

            List<TestCase> testCases = findAll2.stream().filter(x->trade.getResourceID().equals(x.getTradeResourceID())).collect(Collectors.toList());
 //           List<TestCase> testCases = testCaseService.findbyTradeResourceID(trade.getResourceID());

            //遍历修改TestCase中创建时间属性
//            for(TestCase testCase : testCases){
//                testCase.setMaintenanceTime(new Date());
//            }
            caseOfTaskTrade.put(trade.getResourceID(), testCases);

           String maxCaseId = trade.getMaxCaseId();
           String maxRecycleBinCaseId = trade.getMaxRecycleBinCaseId();
           String maxAssetCaseId = trade.getMaxAssetCaseId();
           String newTestCaseCaseId = testCaseService.getNewTestCaseCaseId(trade.getResourceID(), maxCaseId, maxRecycleBinCaseId, maxAssetCaseId);
//           String newTestCaseCaseId = testCaseService.getNewTestCaseCaseId(trade.getResourceID());
            baseCaseEditIdOfTaskTrade.put(trade.getResourceID(), newTestCaseCaseId);
        }
       long c2 = System.currentTimeMillis();
       System.out.println(" 循环处理数据：-----"+(c2-c1)/1000+"秒");
        //获取字典
        Result<?> resultData = feignDataDesignToBasicService.findByTestCaseExportFields(token);
        List<Map<String, Object>> fieldDefinitionList = (List<Map<String, Object>>) resultData.getObj();
        Map<String, Map<String, String>> fieldDictionary = getDic(fieldDefinitionList);

        TradeExcelReadHandler handler = new TradeExcelReadHandler(taskResourceID, fieldDefinitionList, fieldDictionary,
                testSystems, tradeList, userNumber, testPlan, testCaseRecycleBinsOfTrade, caseIdsOfTrade, caseOfTaskTrade,
                baseCaseEditIdOfTaskTrade);
        ExcelReader excelReader = null;
        InputStream is=null;
        try {
            is=file.getInputStream();
            excelReader = EasyExcel.read(is, handler).build();

            List<ReadSheet> readSheets = new ArrayList<>();
            int m=CheckUtil.checkLoop(excelReader.excelExecutor().sheetList().size());
            for (int i = 0; i <m ; i++) {
                if (excelReader.excelExecutor().sheetList().get(i).getSheetName().equals("Dictionary")) {
                    continue;
                }
                readSheets.add(EasyExcel.readSheet(i).build());
            }
            excelReader.read(readSheets);

        } catch (Exception e) {
            logger.error("导入错误", e);
            if (handler.getErrorMsg().isEmpty()) {
                handler.setErrorMsg("导入时发生未知错误");
            }
        }
         try {
             if (!handler.getErrorMsg().isEmpty()) {
                 return Result.renderError(handler.getErrorMsg());
             }

             if (!handler.getCasesToAdd().isEmpty()) {
                 Result<?> result=testCaseService.batchesSaveTestCaseByLimit(handler.getCasesToAdd(), userNumber);
                 if (!result.isSuccess()) {
                     return Result.renderError("案例名称超出长度限制，导入失败。");
                 }
             }
             if (!handler.getCaseToUpdate().isEmpty()) {
                 testCaseService.batchesUpdateTestCaseByLimit(handler.getCaseToUpdate(), userNumber);
             }
             if (!handler.getCaseRecycleBinsToDelete().isEmpty()) {
                 iTestCaseRecycleBinService.deleteInBatch(handler.getCaseRecycleBinsToDelete(), userNumber);
             }
             if (!org.apache.commons.lang3.StringUtils.isEmpty(taskResourceID)) {
                 Result<?> result = feignDataDesignToManexecuteService.updateTestTaskStatus(taskResourceID, "2", userNumber);
                 if (!result.isSuccess()) {
                     return Result.renderError("更新任务状态失败！");
                 }
             }
             return Result.renderSuccess();
         }finally {
             try {
                 if(is!=null){
                     is.close();
                 }
             }catch (Exception e){

             }
         }
    }


    private Map<String, String> createErrorList() {
        Map<String, String> mapError = new HashMap<>();
        mapError.put("caseEditIdBlank", "");
        mapError.put("intentBlank", "");
        mapError.put("isNegativeBlank", "");
        mapError.put("isNegativeError", "");
        mapError.put("caseTypeBlank", "");
        mapError.put("caseTypeError", "");
        mapError.put("casetLevelBlank", "");
        mapError.put("casetLevelError", "");
        mapError.put("preconditionsBlank", "");
        mapError.put("testStepBlank", "");
        mapError.put("expectedResultBlank", "");
        mapError.put("timingNameBlank", "");
        mapError.put("reviewStatusBlank", "");
        mapError.put("reviewStatusError", "");
        mapError.put("commentBlank", "");
        mapError.put("maintainerBlank", "");
        mapError.put("maintainerError", "");
        mapError.put("maintenanceTimeBlank", "");
        mapError.put("maintenanceTimeError", "");
        mapError.put("comments1Blank", "");
        mapError.put("comments2Blank", "");
        mapError.put("comments3Blank", "");
        mapError.put("comments4Blank", "");
        mapError.put("comments5Blank", "");
        mapError.put("comments6Blank", "");
        mapError.put("comments7Blank", "");
        mapError.put("comments8Blank", "");
        mapError.put("comments9Blank", "");
        mapError.put("comments10Blank", "");
        mapError.put("sameCaseEditIdInSheet", "");
        mapError.put("sameCaseEditIdInTask", "");
        mapError.put("comments1Error", "");
        mapError.put("comments2Error", "");
        mapError.put("comments3Error", "");
        mapError.put("comments4Error", "");
        mapError.put("comments5Error", "");
        mapError.put("comments6Error", "");
        mapError.put("comments7Error", "");
        mapError.put("comments8Error", "");
        mapError.put("comments9Error", "");
        mapError.put("comments10Error", "");
        mapError.put("comments1TimeFormatError", "");
        mapError.put("comments2TimeFormatError", "");
        mapError.put("comments3TimeFormatError", "");
        mapError.put("comments4TimeFormatError", "");
        mapError.put("comments5TimeFormatError", "");
        mapError.put("comments6TimeFormatError", "");
        mapError.put("comments7TimeFormatError", "");
        mapError.put("comments8TimeFormatError", "");
        mapError.put("comments9TimeFormatError", "");
        mapError.put("comments10TimeFormatError", "");
        mapError.put("sameCaseEditIdInDataDesign", "");
        mapError.put("testsystemBlank", "");
        mapError.put("testsystemError", "");
        mapError.put("tradeBlank", "");
        mapError.put("tradeModuleError", "");
        mapError.put("systemmoduleError", "");
        mapError.put("systemtradeError", "");
        mapError.put("sameCaseIdInSheetOfTrade", "");
        mapError.put("projectgroupError", "");
        mapError.put("projectgroupName", "");
        return mapError;

    }

    /**
     * 处理错误信息
     *
     * @param mapError
     * @return
     */
    private String dealErrorLog(Map<String, String> mapError, Map<String, String> aliasNameCellMap) {
        String ErrorLog = "";
        if (!StringUtils.isEmpty(mapError.get("caseEditIdBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("caseEditIdBlank").substring(0, mapError.get("caseEditIdBlank").length() - 1) + "】行【" + aliasNameCellMap.get("caseId") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("sameCaseEditIdInSheet"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("sameCaseEditIdInSheet").substring(0, mapError.get("sameCaseEditIdInSheet").length() - 1) + "】行【" + aliasNameCellMap.get("caseId") + "】在导入文件内重复；";
        }
        if (!StringUtils.isEmpty(mapError.get("sameCaseEditIdInTask"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("sameCaseEditIdInTask").substring(0, mapError.get("sameCaseEditIdInTask").length() - 1) + "】行【" + aliasNameCellMap.get("caseId") + "】在其他任务下已经存在；";
        }
        if (!StringUtils.isEmpty(mapError.get("sameCaseEditIdInDataDesign"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("sameCaseEditIdInDataDesign").substring(0, mapError.get("sameCaseEditIdInDataDesign").length() - 1) + "】行【" + aliasNameCellMap.get("caseId") + "】在系统案例库下已经存在；";
        }
        if (!StringUtils.isEmpty(mapError.get("sameCaseIdInSheetOfTrade"))) {
            ErrorLog = ErrorLog + "在当前sheet页内的交易【" + mapError.get("sameCaseIdInSheetOfTrade").substring(0, mapError.get("sameCaseIdInSheetOfTrade").length() - 1) + "】下存在相同的案例编号；";
        }
        if (!StringUtils.isEmpty(mapError.get("intentBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("intentBlank").substring(0, mapError.get("intentBlank").length() - 1) + "】行【" + aliasNameCellMap.get("intent") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("isNegativeBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("isNegativeBlank").substring(0, mapError.get("isNegativeBlank").length() - 1) + "】行【" + aliasNameCellMap.get("isNegative") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("caseTypeBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("caseTypeBlank").substring(0, mapError.get("caseTypeBlank").length() - 1) + "】行【" + aliasNameCellMap.get("caseType") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("casetLevelBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("casetLevelBlank").substring(0, mapError.get("casetLevelBlank").length() - 1) + "】行【" + aliasNameCellMap.get("casetLevel") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("preconditionsBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("preconditionsBlank").substring(0, mapError.get("preconditionsBlank").length() - 1) + "】行【" + aliasNameCellMap.get("preconditions") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("testStepBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("testStepBlank").substring(0, mapError.get("testStepBlank").length() - 1) + "】行【" + aliasNameCellMap.get("testStep") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("expectedResultBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("expectedResultBlank").substring(0, mapError.get("expectedResultBlank").length() - 1) + "】行【" + aliasNameCellMap.get("expectedResult") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("timingNameBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("timingNameBlank").substring(0, mapError.get("timingNameBlank").length() - 1) + "】行【" + aliasNameCellMap.get("timingName") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("reviewStatusBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("reviewStatusBlank").substring(0, mapError.get("reviewStatusBlank").length() - 1) + "】行【" + aliasNameCellMap.get("reviewStatus") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("commentBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("commentBlank").substring(0, mapError.get("commentBlank").length() - 1) + "】行【" + aliasNameCellMap.get("comment") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("maintainerBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("maintainerBlank").substring(0, mapError.get("maintainerBlank").length() - 1) + "】行【" + aliasNameCellMap.get("maintainer") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("maintenanceTimeBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("maintenanceTimeBlank").substring(0, mapError.get("maintenanceTimeBlank").length() - 1) + "】行【" + aliasNameCellMap.get("maintenanceTime") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments1Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments1Blank").substring(0, mapError.get("comments1Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments1") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments2Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments2Blank").substring(0, mapError.get("comments2Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments2") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments3Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments3Blank").substring(0, mapError.get("comments3Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments3") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments4Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments4Blank").substring(0, mapError.get("comments4Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments4") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments5Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments5Blank").substring(0, mapError.get("comments5Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments5") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments6Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments6Blank").substring(0, mapError.get("comments6Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments6") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments7Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments7Blank").substring(0, mapError.get("comments7Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments7") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments8Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments8Blank").substring(0, mapError.get("comments8Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments8") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments9Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments9Blank").substring(0, mapError.get("comments9Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments9") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments10Blank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments10Blank").substring(0, mapError.get("comments10Blank").length() - 1) + "】行【" + aliasNameCellMap.get("comments10") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("testsystemBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("testsystemBlank").substring(0, mapError.get("testsystemBlank").length() - 1) + "】行【" + aliasNameCellMap.get("testsystem") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("tradeBlank"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("tradeBlank").substring(0, mapError.get("tradeBlank").length() - 1) + "】行【" + aliasNameCellMap.get("trade") + "】为空；";
        }
        if (!StringUtils.isEmpty(mapError.get("isNegativeError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("isNegativeError").substring(0, mapError.get("isNegativeError").length() - 1) + "】行【" + aliasNameCellMap.get("isNegative") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("caseTypeError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("caseTypeError").substring(0, mapError.get("caseTypeError").length() - 1) + "】行【" + aliasNameCellMap.get("caseType") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("casetLevelError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("casetLevelError").substring(0, mapError.get("casetLevelError").length() - 1) + "】行【" + aliasNameCellMap.get("casetLevel") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("reviewStatusError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("reviewStatusError").substring(0, mapError.get("reviewStatusError").length() - 1) + "】行【" + aliasNameCellMap.get("reviewStatus") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("maintainerError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("maintainerError").substring(0, mapError.get("maintainerError").length() - 1) + "】行【" + aliasNameCellMap.get("maintainer") + "】用户名称不存在；";
        }
        if (!StringUtils.isEmpty(mapError.get("maintenanceTimeError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("maintenanceTimeError").substring(0, mapError.get("maintenanceTimeError").length() - 1) + "】行【" + aliasNameCellMap.get("maintenanceTime") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments1Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments1Error").substring(0, mapError.get("comments1Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments1") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments2Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments2Error").substring(0, mapError.get("comments2Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments2") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments3Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments3Error").substring(0, mapError.get("comments3Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments3") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments4Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments4Error").substring(0, mapError.get("comments4Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments4") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments5Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments5Error").substring(0, mapError.get("comments5Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments5") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments6Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments6Error").substring(0, mapError.get("comments6Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments6") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments7Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments7Error").substring(0, mapError.get("comments7Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments7") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments8Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments8Error").substring(0, mapError.get("comments8Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments8") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments9Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments9Error").substring(0, mapError.get("comments9Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments9") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments10Error"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments10Error").substring(0, mapError.get("comments10Error").length() - 1) + "】行【" + aliasNameCellMap.get("comments10") + "】填写错误，必须为数据字典内的值；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments1TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments1TimeFormatError").substring(0, mapError.get("comments1TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments1") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments2TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments2TimeFormatError").substring(0, mapError.get("comments2TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments2") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments3TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments3TimeFormatError").substring(0, mapError.get("comments3TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments3") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments4TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments4TimeFormatError").substring(0, mapError.get("comments4TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments4") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments5TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments5TimeFormatError").substring(0, mapError.get("comments5TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments5") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments6TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments6TimeFormatError").substring(0, mapError.get("comments6TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments6") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments7TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments7TimeFormatError").substring(0, mapError.get("comments7TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments7") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments8TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments8TimeFormatError").substring(0, mapError.get("comments8TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments8") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments9TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments9TimeFormatError").substring(0, mapError.get("comments9TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments9") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("comments10TimeFormatError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("comments10TimeFormatError").substring(0, mapError.get("comments10TimeFormatError").length() - 1) + "】行【" + aliasNameCellMap.get("comments10") + "】时间格式填写错误；";
        }
        if (!StringUtils.isEmpty(mapError.get("testsystemError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("testsystemError").substring(0, mapError.get("testsystemError").length() - 1) + "】行所属系统不存在；";
        }
        if (!StringUtils.isEmpty(mapError.get("systemmoduleError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("systemmoduleError").substring(0, mapError.get("systemmoduleError").length() - 1) + "】行所属模块在当前系统不存在；";
        }
        if (!StringUtils.isEmpty(mapError.get("tradeModuleError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("tradeModuleError").substring(0, mapError.get("tradeModuleError").length() - 1) + "】行所属交易在当前模块不存在；";
        }
        if (!StringUtils.isEmpty(mapError.get("systemtradeError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("systemtradeError").substring(0, mapError.get("systemtradeError").length() - 1) + "】行所属交易在当前系统不存在或与当前系统不是直接关联；";
        }
        if (!StringUtils.isEmpty(mapError.get("projectgroupError"))) {
            ErrorLog = ErrorLog + "第【" + mapError.get("projectgroupError").substring(0, mapError.get("projectgroupError").length() - 1) + "】行案例已经在项目【" + mapError.get("projectgroupName").substring(0, mapError.get("projectgroupName").length() - 1) + "】下存在，不允许再次导入；";
        }
        return ErrorLog;
    }

    /**
     * @Title projectCaseExportExcel
     * @Description 项目案例库导出
     * @author: slq
     * @date: 2020年7月15日 上午11:23:23
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> projectCaseExport(Map<String, Object> map, HttpServletRequest request,
                                       HttpServletResponse response) {
        if (map.get("projectgroupResourceID") == null) {
            return Result.renderError("入参[projectgroupResourceID]为空！");
        }
        String projectgroupResourceID = (String) map.get("projectgroupResourceID");
        if (map.get("selectedFields") == null) {
            return Result.renderError("入参[selectedFields]为空！");
        }
        String testSystem=(String)map.get("testSystem");
        String trade=(String)map.get("trade");
        String caseId=(String)map.get("caseId");
        String intent=(String)map.get("intent");
        String caseType=(String)map.get("caseType");
        String casetLevel=(String)map.get("casetLevel");
        String resourceID=(String) map.get("resourceID");
        String nodeType=(String) map.get("nodeType");
        String userNumber = LoginUserUtil.getUserNumber(request);
        String testCaseRids=(String)map.get("testCaseRids");//勾选案例id
        String caseFinalResult = request.getParameter("caseFinalResult");
        String tagResourceIDs=map.get("tagResourceIDs").toString().replace("[","").replace("]","");
        //demandFlag是否关联需求，0-未关联需求的，1-关联需求的
        String demandFlag=(String)map.get("demandFlag");
        if (resourceID == null|| "".equals(resourceID)) {
            return Result.renderError("节点参数resourceID为空");
        }
        String rows = map.get("rows").toString();// 默认为10
        if (rows == null|| "".equals(rows)) {
            rows = "10";
        }
        PageRequest pageRequest = PageRequest.of(0, Integer.parseInt(rows));
        // 导出字段
        final List<String> selectedFields = (List<String>) map.get("selectedFields");
        map.put("parent_url", paramConfig.getAttachmentPath() + File.separator + paramConfig.getTemporaryFolderName() + File.separator);
        Map<String, String> caseTypeMap = new HashMap<>();
        Map<String, String> caseLevelMap = new HashMap<>();
        Map<String, String> caseReviewStatusMap = new HashMap<>();
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result result1 = feignDataDesignToBasicService.findByName("CASETYPE", token);//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list1 = (List<Map<String, String>>) result1.getObj();
        list1.stream().forEach(x -> {
            caseTypeMap.put(x.get("value"), x.get("textName"));
        });
//    List<DataDictionaryDTO>  caseLevel = feignDataDesignToBasicService.findByNamesFromDataDesign("CASETLEVEL");
        Result result2 = feignDataDesignToBasicService.findByName("CASETLEVEL", token);//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list2 = (List<Map<String, String>>) result2.getObj();
        list2.stream().forEach(x -> {
            caseLevelMap.put(x.get("value"), x.get("textName"));
        });
//    List<DataDictionaryDTO>  caseReviewStatus = feignDataDesignToBasicService.findByNamesFromDataDesign("REVIEWSTATUS");
        Result result3 = feignDataDesignToBasicService.findByName("REVIEWSTATUS", token);//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list3 = (List<Map<String, String>>) result3.getObj();
        list3.stream().forEach(x -> {
            caseReviewStatusMap.put(x.get("value"), x.get("textName"));
        });

        //查询案例字段配置启用字段
        Result<?> resultData = feignDataDesignToBasicService.findAllTestCaseFields(token);
        List<Map<String, String>> resultDataList = (List<Map<String, String>>) resultData.getObj();
        Map<String, String> cellNameMap = new HashMap<>();
        Map<String, String> dictionaryCellMap = new HashMap<>();
        Map<String, String> dateFormdataCellMap = new HashMap<>();
        List<String> dateFormdataCellList = new ArrayList<>();
        Map<String, String> excelCellMap = new HashMap<>();
        for (Map<String, String> obj : resultDataList) {
            if ("2".equals(obj.get("fieldType")) || "3".equals(obj.get("fieldType"))) {
                dictionaryCellMap.put(obj.get("aliasName"), obj.get("name"));
            } else if ("5".equals(obj.get("fieldType"))) {
                dateFormdataCellMap.put(obj.get("name"), obj.get("name"));
                dateFormdataCellList.add(obj.get("aliasName"));
            }
            if (String.valueOf(obj.get("writeRequired")).equals("true")) {
                excelCellMap.put(obj.get("aliasName"), "* " + obj.get("aliasName"));
                cellNameMap.put("* " + obj.get("aliasName"), obj.get("nameDescription"));
            } else {
                excelCellMap.put(obj.get("aliasName"), obj.get("aliasName"));
                cellNameMap.put(obj.get("aliasName"), obj.get("nameDescription"));
            }
        }
        cellNameMap.put("* 所属系统", "所属系统");
        cellNameMap.put("所属模块", "所属模块");
        cellNameMap.put("* 所属交易", "所属交易");
        cellNameMap.put("测试方式", "测试方式");
        excelCellMap.put("所属系统", "* 所属系统");
        excelCellMap.put("所属模块", "所属模块");
        excelCellMap.put("所属交易", "* 所属交易");
        excelCellMap.put("测试方式", "测试方式");
        //配置字段的数据字典值
        Map<String, Map<String, String>> commentsDicMap = new HashMap<String, Map<String, String>>();
        if (!dictionaryCellMap.isEmpty()) {
            for (String nameDescription : dictionaryCellMap.keySet()) {
                List<Map<String, Object>> list = testCaseService.findTestCaseDictionaryDataByInfoName(nameDescription);
                Map<String, String> commentsDicMap1 = new HashMap<>();
                for (Map<String, Object> map1 : list) {
                    commentsDicMap1.put(String.valueOf(map1.get("value")), String.valueOf(map1.get("textName")));
                }
                commentsDicMap.put(dictionaryCellMap.get(nameDescription), commentsDicMap1);
            }
        }

        dataDicUtil.getDicMap("testSystem");
        List<Map<String, Object>> testModeList = dataDicUtil.getList("testMode");
        Map<String, String> tmMap = testModeList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));

        List<String> pgResourceIDList = new ArrayList<>();
        pgResourceIDList.add(projectgroupResourceID);
        //根据标签匹配案例信息
        List<String> testCaseRid = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(tagResourceIDs)) {
            Result res = feignDataDesignToBasicService.findTagRelationByTagResourceID(tagResourceIDs, "4");
            if (res.getCode() != StatusCode.OK) return null;
            Map<String, Object> obj = (Map<String, Object>) res.getObj();
            List<Map<String, Object>> relationList = (List<Map<String, Object>>) obj.get("relation");
            List<Map<String, Object>> tagList = (List<Map<String, Object>>) obj.get("tag");
            testCaseRid = relationList.stream().map(s -> s.get("relationResourceID").toString()).distinct().collect(Collectors.toList());
            if (testCaseRid == null || testCaseRid.size() == 0) {
                return null;
            }
        }
        PageImpl<TestCaseDTO> pages=testCaseService.findProjectTestCase(pageRequest,testSystem,trade,caseId,intent
            ,caseType,casetLevel,resourceID,nodeType,demandFlag,tagResourceIDs,caseFinalResult,userNumber);
//        List<TestCase> tc_list = testCaseService.findByProjectGroupResourceIDs(pgResourceIDList,testSystem,trade,caseId,intent,caseType,casetLevel,demandFlag);
        List<String> caseRids = pages.stream().map(item -> item.getResourceID() != null ? item.getResourceID().toString() : "").collect(Collectors.toList());
        List<TestCase> tc_list = testCaseService.findByResourceIDIn(caseRids);
        //根据resourceid过滤出需要插入excel中的数据
        tc_list = tc_list.stream().filter(e -> {
            for (TestCaseDTO caseDTO : pages) {
                if (e.getResourceID().equals(caseDTO.getResourceID())&&(org.apache.commons.lang3.StringUtils.isNotEmpty(testCaseRids)?testCaseRids.contains(caseDTO.getResourceID().toString()):true)) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
        tc_list.stream().forEach(x -> {
            x.setCaseType(caseTypeMap.get(x.getCaseType()));
            x.setCasetLevel(caseLevelMap.get(x.getCasetLevel()));
            x.setReviewStatus(caseReviewStatusMap.get(x.getReviewStatus()));
            //处理测试步骤和预期结果
            x.setTestStep(x.getTestStep()==null?"":getText(x.getTestStep()));
            x.setExpectedResult(x.getExpectedResult()==null?"":getText(x.getExpectedResult()));

            if (x.getTestMode()!=null) {
                List<String> strs = BinaryDecimalUtil.TenToDicVal(x.getTestMode());
                String testModeValue = "";
                for (String s : strs) {
                    testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) +",";
                }
                x.setTestModeName("".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
            }

            if ("1".equals(x.getIsNegative())) {
                x.setIsNegative("正例");
            } else {
                x.setIsNegative("反例");
            }
            if (commentsDicMap.get("comments1") != null && !StringUtils.isEmpty(x.getComments1())) {
                List<String> keyList = Arrays.asList(x.getComments1().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments1");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments1(value);
            } else if (dateFormdataCellMap.get("comments1") != null && !StringUtils.isEmpty(x.getComments1())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments1(df.format(new Date(Long.valueOf(x.getComments1()))));
            }
            if (commentsDicMap.get("comments2") != null && !StringUtils.isEmpty(x.getComments2())) {
                List<String> keyList = Arrays.asList(x.getComments2().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments2");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments2(value);
            } else if (dateFormdataCellMap.get("comments2") != null && !StringUtils.isEmpty(x.getComments2())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments2(df.format(new Date(Long.valueOf(x.getComments2()))));
            }
            if (commentsDicMap.get("comments3") != null && !StringUtils.isEmpty(x.getComments3())) {
                List<String> keyList = Arrays.asList(x.getComments3().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments3");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments3(value);
            } else if (dateFormdataCellMap.get("comments3") != null && !StringUtils.isEmpty(x.getComments3())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments3(df.format(new Date(Long.valueOf(x.getComments3()))));
            }
            if (commentsDicMap.get("comments4") != null && !StringUtils.isEmpty(x.getComments4())) {
                List<String> keyList = Arrays.asList(x.getComments4().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments4");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments4(value);
            } else if (dateFormdataCellMap.get("comments4") != null && !StringUtils.isEmpty(x.getComments4())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments4(df.format(new Date(Long.valueOf(x.getComments4()))));
            }
            if (commentsDicMap.get("comments5") != null && !StringUtils.isEmpty(x.getComments5())) {
                List<String> keyList = Arrays.asList(x.getComments5().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments5");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments5(value);
            } else if (dateFormdataCellMap.get("comments5") != null && !StringUtils.isEmpty(x.getComments5())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments5(df.format(new Date(Long.valueOf(x.getComments5()))));
            }
            if (commentsDicMap.get("comments6") != null && !StringUtils.isEmpty(x.getComments6())) {
                List<String> keyList = Arrays.asList(x.getComments6().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments6");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments6(value);
            } else if (dateFormdataCellMap.get("comments6") != null && !StringUtils.isEmpty(x.getComments6())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments6(df.format(new Date(Long.valueOf(x.getComments6()))));
            }
            if (commentsDicMap.get("comments7") != null && !StringUtils.isEmpty(x.getComments7())) {
                List<String> keyList = Arrays.asList(x.getComments7().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments7");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments7(value);
            } else if (dateFormdataCellMap.get("comments7") != null && !StringUtils.isEmpty(x.getComments7())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments7(df.format(new Date(Long.valueOf(x.getComments7()))));
            }
            if (commentsDicMap.get("comments8") != null && !StringUtils.isEmpty(x.getComments8())) {
                List<String> keyList = Arrays.asList(x.getComments8().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments8");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments8(value);
            } else if (dateFormdataCellMap.get("comments8") != null && !StringUtils.isEmpty(x.getComments8())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments8(df.format(new Date(Long.valueOf(x.getComments8()))));
            }
            if (commentsDicMap.get("comments9") != null && !StringUtils.isEmpty(x.getComments9())) {
                List<String> keyList = Arrays.asList(x.getComments9().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments9");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments9(value);
            } else if (dateFormdataCellMap.get("comments9") != null && !StringUtils.isEmpty(x.getComments9())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments9(df.format(new Date(Long.valueOf(x.getComments9()))));
            }
            if (commentsDicMap.get("comments10") != null && !StringUtils.isEmpty(x.getComments10())) {
                List<String> keyList = Arrays.asList(x.getComments10().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments10");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments10(value);
            } else if (dateFormdataCellMap.get("comments10") != null && !StringUtils.isEmpty(x.getComments10())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments10(df.format(new Date(Long.valueOf(x.getComments10()))));
            }
        });
        // 读取模板
        String[][] data;
        try {
            OutputStream output = response.getOutputStream();
            response.reset();
            response.setHeader("Content-disposition",
                    "attachment; filename=" + encodeDownloadFilename("projectCase.xls", request.getHeader("User-Agent")));
            response.setContentType("application/msexcel");
            response.addHeader("X-Frame-Options", "DENY");
            data = getExcelData(selectedFields, tc_list, cellNameMap, excelCellMap);
            List<Integer> cellNums = new ArrayList<Integer>();
            int m=CheckUtil.checkLoop(selectedFields.size());
            for (int i = 0; i < m; i++) {
                if (dateFormdataCellList.contains(selectedFields.get(i))) {
                    cellNums.add(i);
                }
            }
            HSSFWorkbook wb = exportExcel(data, cellNums);
            wb.write(output);
        } catch (ParseException | IOException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }

    public String[][] getExcelData(List<String> selectedFields, List<TestCase> defect_list, Map<String, String> cellNamemMap, Map<String, String> excelCellMap)
            throws IllegalAccessException, ParseException {
        String[][] str = new String[defect_list.size() + 1][selectedFields.size()];
        int m=CheckUtil.checkLoop(defect_list.size());
        int n=CheckUtil.checkLoop(selectedFields.size());
        for (int row = 0; row < m + 1; row++) {
            for (int cell = 0; cell < n; cell++) {
                if (row == 0) {
                    str[row][cell] = excelCellMap.get(selectedFields.get(cell));
                    continue;
                }
                String x = cellNamemMap.get(str[0][cell]);
                str[row][cell] = getStringValue(cellNamemMap.get(str[0][cell]), defect_list.get(row - 1));
            }
        }
        return str;
    }


    /**
     * @Title querySheetNames
     * @Description 查询导入文件的sheet页名称
     * @author:
     * @date: 2020年7月15日 下午4:16:29
     */
    @Override
    public Result<?> querySheetNames(MultipartFile file) {
        if (file == null) {
            return Result.renderError("导入文件为空！");
        }
        Workbook workbook=null;
        InputStream is=null;
        try {
            is=file.getInputStream();
              workbook = WorkbookFactory.create(is);
        } catch (Exception e) {
        	logger.info(e.getMessage());

            return Result.renderError("程序出错!");
        }
        try{
            List<String> sheetNames = new ArrayList<>();
            int m=CheckUtil.checkLoop(workbook.getNumberOfSheets());
            for (int i = 0; i <m ; i++) {
                Sheet sheet = workbook.getSheetAt(i);
                sheetNames.add(sheet.getSheetName());
            }
            return Result.renderSuccess(sheetNames);
        }finally {
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){

            }
        }

    }

    /**
     * @Title projectCaseImport
     * @Description 项目案例库导入
     * @author: slq
     * @date: 2020年7月15日 上午11:16:22
     */
    @Override
    public Result<?> projectCaseImport(MultipartFile file, String projectgroupResourceID, String sheetNames,
                                       String userNumber) {
        if (file == null) {
            return Result.renderError("导入文件为空！");
        }
        String fileName = file.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        if (!fileType.toLowerCase().equals("xls") && !fileType.toLowerCase().equals("xlsx")) {
            return Result.renderError("导入的文件格式不正确，必须为excel文件！");
        }
        if (StringUtils.isEmpty(projectgroupResourceID)) {
            return Result.renderError("参数【projectgroupResourceID】为空！");
        }
        ProjectGroup projectGroup = projectGroupService.findByResourceID(Long.parseLong(projectgroupResourceID));
        if (projectGroup == null) {
            return Result.renderError("当前选中的节点不是项目组节点或已被删除！");
        }
        if (!projectGroup.isSmallPoints()) {
            return Result.renderError("当前选中的节点不是最末级节点，不允许导入！");
        }
        if (StringUtils.isEmpty(sheetNames)) {
            return Result.renderError("参数【sheetNames】为空！");
        }
        String[] sheetNameStr = sheetNames.split(",");
        List<String> sheetNameList = new ArrayList<>();
        for (String s : sheetNameStr) {
            sheetNameList.add(s);
        }
        if (sheetNameList.isEmpty()) {
            return Result.renderError("数据异常！");
        }
        return readExcelOfProjectCase(file, Long.parseLong(projectgroupResourceID), sheetNameList, userNumber);
    }

    /**
     * <AUTHOR>
     * @description 导入案例库
     * @date 2020年11月03日 17:27
     * @param [file, projectgroupResourceID, sheetNames, userNumber]
     * @return com.jettech.dto.Result<?>
     **/
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Result<?> importProjectCase(MultipartFile file, String projectgroupResourceID, String projectResourceID,String isBuildGroup,String sheetNames, String userNumber) {
        if (file == null) {
            return Result.renderError("导入文件为空！");
        }
        String fileName = file.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        if (!fileType.toLowerCase().equals("xls") && !fileType.toLowerCase().equals("xlsx")) {
            return Result.renderError("导入的文件格式不正确，必须为excel文件！");
        }
        if (StringUtils.isEmpty(projectgroupResourceID) && StringUtils.isEmpty(projectResourceID)) {
            return Result.renderError("参数【projectgroupResource或projectResourceID】为空！");
        }

        List<TestProject> childByParentRid = projectService.findChildTestProjectByResourceID(projectResourceID);
        if (childByParentRid.size()>0) {
            return Result.renderError("选择的该项目不为末级项目！");
        }
        if (StringUtils.isEmpty(sheetNames)) {
            return Result.renderError("参数【sheetNames】为空！");
        }
        String[] sheetNameStr = sheetNames.split(",");
        List<String> sheetNameList = new ArrayList<>();
        for (String s : sheetNameStr) {
            sheetNameList.add(s);
        }
        if (sheetNameList.isEmpty()) {
            return Result.renderError("数据异常！");
        }
        return readTestCaseExcel(file, projectgroupResourceID.equals("")?0:Long.parseLong(projectgroupResourceID), Long.parseLong(projectResourceID),isBuildGroup,sheetNameList, userNumber);
    }

    private Result<?> readTestCaseExcel(MultipartFile file, long projectgroupResourceID, long projectResourceID,String isBuildGroup,List<String> sheetNameList, String userNumber) {
        logger.info(">>>>>>>>>>>>项目案例库开始导入>>>>>>>>>>>>>>>>,projectgroupResourceID：{}", projectgroupResourceID);
        String token = HttpRequestUtils.getCurrentRequestToken();
        JettechUserDTO jettechUserDTO = feignDataDesignToBasicService.findByNumber(userNumber, token);
//        String token = "zhangsheng";

        // 查询全部系统
        List<TestSystem> testSystemList = testSystemService.findAll();
        // 查询全部交易
        List<Trade> tradeList = tradeService.findAll();
        // 查询全部模块
        List<SystemModule> moduleList = systemModuleService.findAll();
        // 查询全部案例
       // List<TestCase> testCaseList = testCaseService.findAll();
        //判断是否选择的末级节点
        List<TestCase> testCaseList = new ArrayList<>();
        if(projectgroupResourceID!=0){
            testCaseList = testCaseService.findByProjectGroupResourceID(projectgroupResourceID);
        }


        // 查询案例配置启用的字段
        Result<?> resultData = feignDataDesignToBasicService.findAllTestCaseFields(token);
        List<Map<String, Object>> fieldDefinitionList = (List<Map<String, Object>>) resultData.getObj();
        Map<String, Map<String, String>> fieldDictionary = getDic(fieldDefinitionList);

		//案例字段-评审状态未启用时导入案例的评审状态默认为"已评审",20211008dingwl
		boolean reviewStatusUsingEnable = this.reviewStatusUsingEnable();

		//api
        Long testProjectId = null;
        Long tradeFlowCaseFolderId = null;
        ProjectGroup projectGroup = projectGroupService.findByResourceID(projectgroupResourceID);
        if (projectGroup != null) {
            testProjectId = projectGroup.getTestProjectResourceID();
            List<Map<String, Object>> folderList = testProjectService.findTradeFlowCaseFolder(projectGroup.getTestProjectResourceID());
            if (folderList != null && folderList.size() > 0) {
                tradeFlowCaseFolderId = (long) folderList.get(0).get("id");
            }
        }

        TestCaseExcelReadHandler readHandler = new TestCaseExcelReadHandler(projectResourceID,projectgroupResourceID, isBuildGroup,fieldDefinitionList, fieldDictionary,
                testSystemList, tradeList, moduleList, testCaseService, projectGroupService,userNumber, jettechUserDTO.getUserName(), testCaseList,reviewStatusUsingEnable);

        ExcelReader excelReader = null;
        InputStream is=null;
        try {
            is=file.getInputStream();
            excelReader = EasyExcel.read(is,readHandler).build();
            List<ReadSheet> readSheets = new ArrayList<>();
            int m=CheckUtil.checkLoop(excelReader.excelExecutor().sheetList().size());
            for (int i = 0; i <m ; i++) {
                String sheetName = excelReader.excelExecutor().sheetList().get(i).getSheetName();
                if (sheetNameList.contains(sheetName)){
                    readSheets.add(EasyExcel.readSheet(i).build());
                }
            }
            // 读取Excel
            excelReader.read(readSheets);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("导入Excel案例失败", e);
            Result.renderError("项目案例库导入失败");
        } finally {
            if (excelReader != null) {
                // 这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
                excelReader.finish();
            }
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){

            }
        }

        // 返回Excel表中的错误信息
        if (!readHandler.getErrorMsg().isEmpty()) {
            return Result.renderError(readHandler.getErrorMsg());
        }
        logger.info(">>>>>>>>>>>>项目案例库导入结束>>>>>>>>>>>>>>>>,projectgroupResourceID：{}", projectgroupResourceID);
        return  Result.renderSuccess();
    }

    public Result<?> readExcelOfProjectCase(MultipartFile file, Long projectgroupResourceID, List<String> sheetNames,
                                            String userNumber) {
        //处理错误信息
        String errorMsg = "";
        HSSFWorkbook workbook = null;
        InputStream is=null;
        try {
            is=file.getInputStream();
            workbook = new HSSFWorkbook(is);
        } catch (Exception e) {
            logger.debug(e.getMessage());
            return Result.renderError("模板异常，请重新下载模板！");
        }
         try{

             String sheetNameStr = "";
             for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                 HSSFSheet sheet = workbook.getSheetAt(i);
                 String sheetName = sheet.getSheetName();
                 if (sheetNames.contains(sheetName)) {
                     HSSFRow row = sheet.getRow(0);
                     if (row != null) {
                         boolean boo = ImportExcelUtils.valitaileProjectCaseExcelTitle(row, feignDataDesignToBasicService);
                         if (!boo) {
                             sheetNameStr += sheetName + "，";
                         }
                     } else {
                         sheetNameStr += sheetName + "，";
                     }
                 }
             }
             if (ImportExcelUtils.TRADE_IMPORT_TITLE1 == null || ImportExcelUtils.caseFieldConfiguration1.isEmpty()) {
                 return Result.renderError("请先查看案例字段配置是否正常！");
             }
             if (!StringUtils.isEmpty(sheetNameStr)) {
                 sheetNameStr = sheetNameStr.substring(0, sheetNameStr.length() - 1);
                 return Result.renderError("名称为【" + sheetNameStr + "】的sheet页标题某些列名与案例字段名称不匹配或必输项不存在，请重新维护或下载模板！");
             }
             HSSFSheet sheet0 = workbook.getSheetAt(0);
             Row row0 = sheet0.getRow(0);
             Map<Integer, String> cellNameMap = new HashMap<Integer, String>();
             Map<String, Integer> cellNunberMap = new HashMap<String, Integer>();
             Map<String, String> writeRequiredCellMap = new HashMap<String, String>();
             Map<String, String> aliasNameCellMap = new HashMap<String, String>();
             Map<String, String> dictionaryCellMap = new HashMap<String, String>();
             Map<String, String> dateFormdataCellMap = new HashMap<String, String>();
             for (int i = 0; i < row0.getPhysicalNumberOfCells(); i++) {
                 for (Map<String, String> map : ImportExcelUtils.caseFieldConfiguration1) {
                     if (ImportExcelUtils.getValue(row0.getCell(i)).equals(map.get("aliasName"))) {
                         cellNameMap.put(i, map.get("name"));
                         cellNunberMap.put(map.get("name"), i);
                         writeRequiredCellMap.put(map.get("name"), String.valueOf(map.get("writeRequired")));
                         aliasNameCellMap.put(map.get("name"), map.get("aliasName"));
                         if ("2".equals(map.get("fieldType")) || "3".equals(map.get("fieldType"))) {
                             dictionaryCellMap.put(map.get("nameDescription"), map.get("name"));
                         } else if ("5".equals(map.get("fieldType"))) {
                             dateFormdataCellMap.put(map.get("name"), map.get("name"));
                         }
                     }
                 }
             }
             Map<String, String> allTestCaseFieldMap = new HashMap<String, String>();
             for (Map<String, String> obj : ImportExcelUtils.caseFieldConfiguration1) {
                 allTestCaseFieldMap.put(obj.get("name"), obj.get("name"));
             }
             //数据字典-案例级别
             List<Map<String, Object>> caseLevel_list = testCaseService.findTestCaseDictionaryData("CASETLEVEL");
             Map<String, String> caseLevelDicMap = new HashMap<>();
             for (Map<String, Object> map : caseLevel_list) {
                 caseLevelDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
             }
             //数据字典-案例类型
             List<Map<String, Object>> caseType_list = testCaseService.findTestCaseDictionaryData("CASETYPE");
             Map<String, String> caseTypeDicMap = new HashMap<>();
             for (Map<String, Object> map : caseType_list) {
                 caseTypeDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
             }
             //数据字典-评审状态
             List<Map<String, Object>> reviewStatus_list = testCaseService.findTestCaseDictionaryData("REVIEWSTATUS");
             Map<String, String> reviewStatusDicMap = new HashMap<>();
             for (Map<String, Object> map : reviewStatus_list) {
                 reviewStatusDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
             }

             //数据字典-正反例
             List<Map<String, Object>> isNegative_list = testCaseService.findTestCaseDictionaryData("NEGATIVE");
             Map<String, String> isNegativeDicMap = new HashMap<>();
             for (Map<String, Object> map : isNegative_list) {
                 isNegativeDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
             }

             List<Map<String, String>> allPersonInfo = testCaseService.findAllUsersNameAndNumber();
             Map<String, String> users = new HashMap<>();
             for (Map<String, String> map : allPersonInfo) {
                 users.put(String.valueOf(map.get("userName")), String.valueOf(map.get("userName")));
             }
             //配置字段的数据字典值
             Map<String, Map<String, String>> commentsDicMap = new HashMap<String, Map<String, String>>();
             if (!dictionaryCellMap.isEmpty()) {
                 for (String nameDescription : dictionaryCellMap.keySet()) {
                     List<Map<String, Object>> list1 = testCaseService.findTestCaseDictionaryData(nameDescription);
                     Map<String, String> commentsDicMap1 = new HashMap<>();
                     for (Map<String, Object> map : list1) {
                         commentsDicMap1.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
                     }
                     commentsDicMap.put(dictionaryCellMap.get(nameDescription), commentsDicMap1);
                 }
             }
             Map<Long, List<Map<String, Long>>> systemModuleMap = new HashMap<>();
             Map<Long, List<Map<String, Long>>> systemTradeMap = new HashMap<>();
             List<TestSystem> testSystems = testSystemService.findAll();
             Map<String, Long> testSystemMap = new HashMap<String, Long>();
             testSystems.stream().forEach(x -> testSystemMap.put(x.getName(), x.getResourceID()));
             testSystems.stream().forEach(x -> findModuleLevelBySystemRid(x.getResourceID(), systemModuleMap, systemTradeMap));
             Map<String, Long> tradeModuleMap = new HashMap<>();
             Map<Long, String> allTradeMap = new HashMap<>();
             List<Trade> trades = tradeService.findAll();
             for (Trade x : trades) {
                 if (x.getModuleResourceID() != null) {
                     tradeModuleMap.put(x.getModuleResourceID().toString() + x.getName(), x.getResourceID());
                 }
                 allTradeMap.put(x.getResourceID(), x.getName());
             }
             List<TestCase> addCase = new ArrayList<>();
             List<TestCase> updateCase = new ArrayList<>();
             Map<String, String> sheetErrorMap = new HashMap<String, String>();
             Map<Long, List<TestCase>> existCaseMap = new HashMap<>();
             Map<Long, String> caseIDInTradeMap = new HashMap<>();
             for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                 Map<Long, String> projectGroupNameMap = new HashMap<>();
                 HSSFSheet sheet = workbook.getSheetAt(i);
                 String sheetName = sheet.getSheetName();
                 if (sheetNames.contains(sheetName)) {
                     int totalNumbers = sheet.getPhysicalNumberOfRows();
                     Map<String, String> mapError = createErrorList();//用于存储各种错误信息
                     int cellNumber = sheet.getRow(0).getPhysicalNumberOfCells();
                     //key-功能resID value-caseID
                     Map<Long, List<String>> caseIDMap = new HashMap<>();
                     for (int j = 1; j < totalNumbers; j++) {
                         //遍历每一行
                         Row rowj = sheet.getRow(j);
                         if (rowj == null) {
                             //跳过空行
                             totalNumbers++;
                             continue;
                         }

                         TestCase tc = new TestCase();
                         for (int k = 0; k < cellNumber; k++) {
                             if (cellNameMap.get(k) == null) {
                                 return Result.renderError("导入异常，模板错误！");
                             }
                             tc = setProjectTestCase(tc, cellNameMap.get(k), ImportExcelUtils.getValue(rowj.getCell(k)), j, allTestCaseFieldMap, writeRequiredCellMap, mapError, caseLevelDicMap, caseTypeDicMap, reviewStatusDicMap, commentsDicMap, isNegativeDicMap, users, rowj.getCell(k), dateFormdataCellMap, testSystemMap);
                             if (!StringUtils.isEmpty(tc.getTestsystem()) && !StringUtils.isEmpty(tc.getSystemmodule()) && !StringUtils.isEmpty(tc.getTrade())) {
                                 String moduleName = tc.getSystemmodule();
                                 Optional<Map<String, Long>> systemModule = systemModuleMap.get(tc.getTestsystemResourceID()).stream().filter(x -> (x.get(moduleName) != null)).findAny();
                                 if (systemModule.isPresent()) {
                                     tc.setSystemmoduleResourceID(systemModule.get().get(moduleName));
                                     if (tradeModuleMap.get(tc.getSystemmoduleResourceID().toString() + tc.getTrade()) != null) {
                                         tc.setTradeResourceID(tradeModuleMap.get(tc.getSystemmoduleResourceID().toString() + tc.getTrade()));
                                     } else {
                                         mapError.put("tradeModuleError", mapError.get("tradeModuleError") + (j + 1) + ",");
                                     }
                                 } else {
                                     mapError.put("systemmoduleError", mapError.get("systemmoduleError") + (j + 1) + ",");
                                 }
                             } else if (!StringUtils.isEmpty(tc.getTestsystem()) && StringUtils.isEmpty(tc.getSystemmodule()) && !StringUtils.isEmpty(tc.getTrade())) {
                                 String tradeName = tc.getTrade();
                                 if (systemTradeMap.get(tc.getTestsystemResourceID()) != null) {
                                     Optional<Map<String, Long>> tradeMap = systemTradeMap.get(tc.getTestsystemResourceID()).stream().filter(x -> (x.get(tradeName) != null)).findAny();
                                     if (tradeMap.isPresent()) {
                                         tc.setTradeResourceID(tradeMap.get().get(tradeName));
                                     } else {
                                         mapError.put("systemtradeError", mapError.get("systemtradeError") + (j + 1) + ",");
                                     }
                                 }
                             }
                         }
                         if (caseIDMap.get(tc.getTradeResourceID()) == null) {
                             List<String> caseIDs = new ArrayList<>();
                             caseIDs.add(tc.getCaseId());
                             caseIDMap.put(tc.getTradeResourceID(), caseIDs);
                         } else {
                             List<String> caseIDs = caseIDMap.get(tc.getTradeResourceID());
                             caseIDs.add(tc.getCaseId());
                             caseIDMap.put(tc.getTradeResourceID(), caseIDs);
                         }
                         if (existCaseMap.get(tc.getTradeResourceID()) == null) {
                             List<TestCase> tc_list = testCaseService.findbyTradeResourceID(tc.getTradeResourceID());
                             existCaseMap.put(tc.getTradeResourceID(), tc_list);
                             String caseIDs = tc.getCaseId();
                             Optional<TestCase> update = tc_list.stream().filter(e -> caseIDs.equals(e.getCaseId())).findAny();
                             if (update.isPresent()) {
                                 if (projectgroupResourceID.equals(update.get().getProjectgroupResourceID())) {
                                     tc.setResourceID(update.get().getResourceID());
                                     tc.setId(update.get().getId());
                                     tc.setLeadsource(1);
                                     tc.setIdentitynumber(update.get().getIdentitynumber() == null ? 1 : update.get().getIdentitynumber() + 1);
                                     updateCase.add(tc);
                                 } else {
                                     if (projectGroupNameMap.get(update.get().getProjectgroupResourceID()) == null) {
                                         ProjectGroup testProject = projectGroupService.findByResourceID(update.get().getProjectgroupResourceID());
                                         if (testProject == null) {
                                             return Result.renderError("数据异常，当前的项目组不存在！");
                                         }
                                         String projectgroupName = mapError.get("projectgroupName") == null ? "" : mapError.get("projectgroupName");
                                         if (StringUtils.isEmpty(projectgroupName)) {
                                             mapError.put("projectgroupName", testProject.getName() + "，");
                                         } else {
                                             mapError.put("projectgroupName", projectgroupName + testProject.getName() + "，");
                                         }

                                         projectGroupNameMap.put(update.get().getProjectgroupResourceID(), testProject.getName());
                                     }
                                 }
                             } else {
                                 tc.setProjectgroupResourceID(projectgroupResourceID);
                                 tc.setLeadsource(1);
                                 tc.setIdentitynumber(0);
                                 if (caseIDInTradeMap.get(tc.getTradeResourceID()) == null) {
                                     String newTestCaseCaseId = testCaseService.getNewTestCaseCaseId(tc.getTradeResourceID());
                                     tc.setCaseEditId(newTestCaseCaseId);
                                     newTestCaseCaseId = String.format("%07d", Integer.valueOf(newTestCaseCaseId) + 1);
                                     caseIDInTradeMap.put(tc.getTradeResourceID(), newTestCaseCaseId);
                                 } else {
                                     String newTestCaseCaseId = caseIDInTradeMap.get(tc.getTradeResourceID());
                                     tc.setCaseEditId(newTestCaseCaseId);
                                     newTestCaseCaseId = String.format("%07d", Integer.valueOf(newTestCaseCaseId) + 1);
                                     caseIDInTradeMap.put(tc.getTradeResourceID(), newTestCaseCaseId);
                                 }
                                 addCase.add(tc);
                             }
                         } else {
                             List<TestCase> tc_list = existCaseMap.get(tc.getTradeResourceID());
                             existCaseMap.put(tc.getTradeResourceID(), tc_list);
                             String caseIDs = tc.getCaseId();
                             Optional<TestCase> update = tc_list.stream().filter(e -> caseIDs.equals(e.getCaseId())).findAny();
                             if (update.isPresent()) {
                                 if (projectgroupResourceID.equals(update.get().getProjectgroupResourceID())) {
                                     tc.setResourceID(update.get().getResourceID());
                                     tc.setId(update.get().getId());
                                     tc.setLeadsource(1);
                                     tc.setIdentitynumber(update.get().getIdentitynumber() == null ? 1 : update.get().getIdentitynumber() + 1);
                                     updateCase.add(tc);
                                 } else {
                                     if (projectGroupNameMap.get(update.get().getProjectgroupResourceID()) == null) {
                                         ProjectGroup testProject = projectGroupService.findByResourceID(update.get().getProjectgroupResourceID());
                                         if (testProject == null) {
                                             return Result.renderError("数据异常，当前的项目组不存在！");
                                         }
                                         String projectgroupName = mapError.get("projectgroupName") == null ? "" : mapError.get("projectgroupName");
                                         if (StringUtils.isEmpty(projectgroupName)) {
                                             mapError.put("projectgroupName", testProject.getName() + "，");
                                         } else {
                                             mapError.put("projectgroupName", projectgroupName + testProject.getName() + "，");
                                         }

                                         projectGroupNameMap.put(update.get().getProjectgroupResourceID(), testProject.getName());
                                     }
                                     mapError.put("projectgroupError", mapError.get("projectgroupError") + (j + 1) + ",");
                                 }
                             } else {
                                 tc.setProjectgroupResourceID(projectgroupResourceID);
                                 tc.setLeadsource(1);
                                 tc.setIdentitynumber(0);
                                 if (caseIDInTradeMap.get(tc.getTradeResourceID()) == null) {
                                     String newTestCaseCaseId = testCaseService.getNewTestCaseCaseId(tc.getTradeResourceID());
                                     tc.setCaseEditId(newTestCaseCaseId);
                                     newTestCaseCaseId = String.format("%07d", Integer.valueOf(newTestCaseCaseId) + 1);
                                     caseIDInTradeMap.put(tc.getTradeResourceID(), newTestCaseCaseId);
                                 } else {
                                     String newTestCaseCaseId = caseIDInTradeMap.get(tc.getTradeResourceID());
                                     tc.setCaseEditId(newTestCaseCaseId);
                                     newTestCaseCaseId = String.format("%07d", Integer.valueOf(newTestCaseCaseId) + 1);
                                     caseIDInTradeMap.put(tc.getTradeResourceID(), newTestCaseCaseId);
                                 }
                                 addCase.add(tc);
                             }
                         }
                     }
                     for (Long tradeResID : caseIDMap.keySet()) {
                         List<String> caseIDs = caseIDMap.get(tradeResID);
                         Set<String> caseIDSet = new HashSet<>(caseIDs);
                         if (caseIDs.size() != caseIDSet.size()) {
                             mapError.put("sameCaseIdInSheetOfTrade", mapError.get("sameCaseIdInSheetOfTrade") + allTradeMap.get(tradeResID) + ",");
                         }
                     }
                     //处理错误信息
                     errorMsg = this.dealErrorLog(mapError, aliasNameCellMap);
                     if (!StringUtils.isEmpty(errorMsg)) {
                         sheetErrorMap.put(sheetName, errorMsg);
                     }
                 }
             }

             if (sheetErrorMap.isEmpty()) {
                 if (!addCase.isEmpty()) {
                     testCaseService.batchesSaveTestCaseByLimit(addCase, userNumber);
                 }
                 if (!updateCase.isEmpty()) {
                     testCaseService.batchesUpdateTestCaseByLimit(updateCase, userNumber);
                 }
                 return Result.renderSuccess();
             } else {
                 String allErrorMsg = "";
                 for (String sheetName : sheetErrorMap.keySet()) {
                     allErrorMsg = allErrorMsg + "在名称为【" + sheetName + "】的sheet页内，存在以下错误：" + sheetErrorMap.get(sheetName);
                 }
                 return Result.renderError(allErrorMsg);
         }
        }catch (Exception e){
             return Result.renderError(e.getMessage());
         }finally {
             try {
                 if(is!=null){
                     is.close();
                 }
             }catch (Exception e){

             }
         }
    }

    public void findModuleLevelBySystemRid(Long systemResourceID, Map<Long, List<Map<String, Long>>> systemModuleMap, Map<Long, List<Map<String, Long>>> systemTradeMap) {
        List<SystemModule> modules = systemModuleService.findbyTestSystemResourceID(String.valueOf(systemResourceID));
        List<Map<String, Long>> systemModuleList = new ArrayList<>();
        List<Map<String, Long>> systemTradeList = new ArrayList<>();
        if (!modules.isEmpty()) {
            //所有和被测系统直接关联的
            List<SystemModule> rootModules = modules.stream().filter(x -> systemResourceID.equals(x.getParentResourceID())).collect(Collectors.toList());

            for (SystemModule systemModule : rootModules) {
                Map<String, Long> moduleMap = new HashMap<>();
                String parentName = systemModule.getName();
                List<SystemModule> childs = modules.stream().filter(x -> String.valueOf(systemModule.getResourceID()).equals(String.valueOf(x.getParentResourceID()))).collect(Collectors.toList());
                if (!childs.isEmpty()) {
                    for (SystemModule child : childs) {
                        this.buildChildMudule(moduleMap, child, parentName, modules);
                    }
                    systemModuleList.add(moduleMap);
                } else {
                    moduleMap.put(parentName, systemModule.getResourceID());
                    systemModuleList.add(moduleMap);
                }
            }
        }
        List<Trade> tradeUnderTestSystem = tradeService.findOnlyTradebyTestSystemResourceID(String.valueOf(systemResourceID), null);
        for (Trade trade : tradeUnderTestSystem) {
            Map<String, Long> tradeMap = new HashMap<>();
            tradeMap.put(trade.getName(), trade.getResourceID());
            systemTradeList.add(tradeMap);
        }
        systemModuleMap.put(systemResourceID, systemModuleList);
        systemTradeMap.put(systemResourceID, systemTradeList);
    }

    /**
     * 递归查找拼接模块
     *
     * @param moduleMap
     * @param current
     * @param parentName
     * @param modules
     */
    private void buildChildMudule(Map<String, Long> moduleMap, SystemModule current, String parentName, List<SystemModule> modules) {
        List<SystemModule> childs = modules.stream().filter(x -> String.valueOf(current.getResourceID()).equals(String.valueOf(x.getParentResourceID()))).collect(Collectors.toList());
        if (!childs.isEmpty()) {
            for (SystemModule child : childs) {
                this.buildChildMudule(moduleMap, child, parentName + "/" + current.getName(), modules);
            }
        } else {
            moduleMap.put(parentName + "/" + current.getName(), current.getResourceID());
        }
    }

    private TestCase setProjectTestCase(TestCase testCase, String caseFieldName, String cellValue, int rowNum,
                                        Map<String, String> allTestCaseFieldMap, Map<String, String> writeRequiredCellMap,
                                        Map<String, String> mapError, Map<String, String> caseLevelDicMap,
                                        Map<String, String> caseTypeDicMap, Map<String, String> reviewStatusDicMap,
                                        Map<String, Map<String, String>> commentsDicMap, Map<String, String> isNegativeDicMap,
                                        Map<String, String> users, Cell cell, Map<String, String> dateFormdataCellMap,
                                        Map<String, Long> testSystemMap) {
        if (allTestCaseFieldMap.get("caseId").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("caseEditIdBlank", mapError.get("caseEditIdBlank") + (rowNum + 1) + ",");
            }
            if (!StringUtils.isEmpty(cellValue)) {
                testCase.setCaseId(cellValue);
            }

        } else if (allTestCaseFieldMap.get("intent").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("intentBlank", mapError.get("intentBlank") + (rowNum + 1) + ",");
            }
            testCase.setIntent(cellValue);
        } else if (allTestCaseFieldMap.get("isNegative").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("isNegativeBlank", mapError.get("isNegativeBlank") + (rowNum + 1) + ",");
            } else {
                if (!isNegativeDicMap.keySet().contains(cellValue)) {
                    mapError.put("isNegativeError", mapError.get("isNegativeError") + (rowNum + 1) + ",");
                } else {
                    testCase.setIsNegative(isNegativeDicMap.get(cellValue));
                }
            }
        } else if (allTestCaseFieldMap.get("caseType").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("caseTypeBlank", mapError.get("caseTypeBlank") + (rowNum + 1) + ",");
            } else {
                if (!caseTypeDicMap.keySet().contains(cellValue)) {
                    mapError.put("caseTypeError", mapError.get("caseTypeError") + (rowNum + 1) + ",");
                } else {
                    testCase.setCaseType(caseTypeDicMap.get(cellValue));
                }
            }
        } else if (allTestCaseFieldMap.get("casetLevel").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("casetLevelBlank", mapError.get("casetLevelBlank") + (rowNum + 1) + ",");
            } else {
                if (!caseLevelDicMap.keySet().contains(cellValue)) {
                    mapError.put("casetLevelError", mapError.get("casetLevelError") + (rowNum + 1) + ",");
                } else {
                    testCase.setCasetLevel(caseLevelDicMap.get(cellValue));
                }
            }
        } else if (allTestCaseFieldMap.get("preconditions").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("preconditionsBlank", mapError.get("preconditionsBlank") + (rowNum + 1) + ",");
            } else {
                testCase.setPreconditions(cellValue);
            }
        } else if (allTestCaseFieldMap.get("dataRequirements").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("dataRequirementsBlank", mapError.get("dataRequirementsBlank") + (rowNum + 1) + ",");
            } else {
                testCase.setDataRequirements(cellValue);
            }
        } else if (allTestCaseFieldMap.get("checkPoint").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("checkPointBlank", mapError.get("checkPointBlank") + (rowNum + 1) + ",");
            } else {
                testCase.setCheckPoint(cellValue);
            }
        } else if (allTestCaseFieldMap.get("testStep").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("testStepBlank", mapError.get("testStepBlank") + (rowNum + 1) + ",");
            } else {
                testCase.setTestStep(cellValue);
            }
        } else if (allTestCaseFieldMap.get("expectedResult").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("expectedResultBlank", mapError.get("expectedResultBlank") + (rowNum + 1) + ",");
            } else {
                testCase.setExpectedResult(cellValue);
            }
        } else if (allTestCaseFieldMap.get("timingName").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("timingNameBlank", mapError.get("timingNameBlank") + (rowNum + 1) + ",");
            } else {
                testCase.setTimingName(cellValue);
            }
        } else if (allTestCaseFieldMap.get("reviewStatus").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("reviewStatusBlank", mapError.get("reviewStatusBlank") + (rowNum + 1) + ",");
            } else {
                if (!reviewStatusDicMap.keySet().contains(cellValue)) {
                    mapError.put("reviewStatusError", mapError.get("reviewStatusError") + (rowNum + 1) + ",");
                } else {
                    testCase.setReviewStatus(reviewStatusDicMap.get(cellValue));
                }
            }
        } else if (allTestCaseFieldMap.get("comment").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("commentBlank", mapError.get("commentBlank") + (rowNum + 1) + ",");
            } else {
                testCase.setComment(cellValue);
            }
        } else if (allTestCaseFieldMap.get("maintainer").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("maintainerBlank", mapError.get("maintainerBlank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    if (!users.keySet().contains(cellValue)) {
                        mapError.put("maintainerError", mapError.get("maintainerError") + (rowNum + 1) + ",");
                    } else {
                        testCase.setMaintainer(users.get(cellValue));
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("maintenanceTime").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("maintenanceTimeBlank", mapError.get("maintenanceTimeBlank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    try {
                        try {
                            Calendar calendar = new GregorianCalendar(1900, 0, -1);
                            Date d = calendar.getTime();
                            Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                            testCase.setMaintenanceTime(dd);
                        } catch (Exception e) {
                            e.printStackTrace();
                            mapError.put("maintenanceTimeError", mapError.get("maintenanceTimeError") + (rowNum + 1) + ",");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        mapError.put("maintenanceTimeError", mapError.get("maintenanceTimeError") + (rowNum + 1) + ",");
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments1").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments1Blank", mapError.get("comments1Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments1") != null && !commentsDicMap.get("comments1").keySet().containsAll(com)) {
                        mapError.put("comments1Error", mapError.get("comments1Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments1") != null && commentsDicMap.get("comments1").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments1").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments1(value);
                    } else if (dateFormdataCellMap.get("comments1") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments1(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments1TimeFormatError", mapError.get("comments1TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments1(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments2").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments2Blank", mapError.get("comments2Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments2") != null && !commentsDicMap.get("comments2").keySet().containsAll(com)) {
                        mapError.put("comments2Error", mapError.get("comments2Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments2") != null && commentsDicMap.get("comments2").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments2").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments2(value);
                    } else if (dateFormdataCellMap.get("comments2") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments2(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments2TimeFormatError", mapError.get("comments2TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments2(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments3").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments3Blank", mapError.get("comments3Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments3") != null && !commentsDicMap.get("comments3").keySet().containsAll(com)) {
                        mapError.put("comments3Error", mapError.get("comments3Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments3") != null && commentsDicMap.get("comments3").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments3").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments3(value);
                    } else if (dateFormdataCellMap.get("comments3") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments3(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments3TimeFormatError", mapError.get("comments3TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments3(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments4").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments4Blank", mapError.get("comments4Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments4") != null && !commentsDicMap.get("comments4").keySet().containsAll(com)) {
                        mapError.put("comments4Error", mapError.get("comments4Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments4") != null && commentsDicMap.get("comments4").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments4").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments4(value);
                    } else if (dateFormdataCellMap.get("comment4s") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments4(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments4TimeFormatError", mapError.get("comments4TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments4(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments5").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments5Blank", mapError.get("comments5Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments5") != null && !commentsDicMap.get("comments5").keySet().containsAll(com)) {
                        mapError.put("comments5Error", mapError.get("comments5Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments5") != null && commentsDicMap.get("comments5").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments5").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments5(value);
                    } else if (dateFormdataCellMap.get("comments5") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments5(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments5TimeFormatError", mapError.get("comments5TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments5(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments6").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments6Blank", mapError.get("comments6Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments6") != null && !commentsDicMap.get("comments6").keySet().containsAll(com)) {
                        mapError.put("comments6Error", mapError.get("comments6Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments6") != null && commentsDicMap.get("comments6").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments6").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments6(value);
                    } else if (dateFormdataCellMap.get("comments6") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments6(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments6TimeFormatError", mapError.get("comments6TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments6(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments7").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments7Blank", mapError.get("comments7Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments7") != null && !commentsDicMap.get("comments7").keySet().containsAll(com)) {
                        mapError.put("comments7Error", mapError.get("comments7Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments7") != null && commentsDicMap.get("comments7").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments7").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments7(value);
                    } else if (dateFormdataCellMap.get("comments7") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments7(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments7TimeFormatError", mapError.get("comments7TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments7(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments8").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments8Blank", mapError.get("comments8Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments8") != null && !commentsDicMap.get("comments8").keySet().containsAll(com)) {
                        mapError.put("comments8Error", mapError.get("comments8Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments8") != null && commentsDicMap.get("comments8").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments8").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments8(value);
                    } else if (dateFormdataCellMap.get("comments8") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments8(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments8TimeFormatError", mapError.get("comments8TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments8(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments9").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments9Blank", mapError.get("comments9Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments9") != null && !commentsDicMap.get("comments9").keySet().containsAll(com)) {
                        mapError.put("comments9Error", mapError.get("comments9Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments9") != null && commentsDicMap.get("comments9").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments9").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments9(value);
                    } else if (dateFormdataCellMap.get("comments9") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments9(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments9TimeFormatError", mapError.get("comments9TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments9(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("comments10").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("comments10Blank", mapError.get("comments10Blank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    String[] str = cellValue.split(",");
                    List<String> com = new ArrayList<>();
                    String value = "";
                    for (int i = 0; i < str.length; i++) {
                        com.add(str[i]);
                    }
                    if (commentsDicMap.get("comments10") != null && !commentsDicMap.get("comments10").keySet().containsAll(com)) {
                        mapError.put("comments10Error", mapError.get("comments10Error") + (rowNum + 1) + ",");
                    } else if (commentsDicMap.get("comments10") != null && commentsDicMap.get("comments10").keySet().containsAll(com)) {
                        for (int i = 0; i < str.length; i++) {
                            value = value + commentsDicMap.get("comments10").get(str[i]) + ",";
                        }
                        value = value.substring(0, value.length() - 1);
                        testCase.setComments10(value);
                    } else if (dateFormdataCellMap.get("comments10") != null) {
                        if (!StringUtils.isEmpty(cellValue)) {
                            try {
                                Calendar calendar = new GregorianCalendar(1900, 0, -1);
                                Date d = calendar.getTime();
                                Date dd = DateUtils.addDays(d, Integer.valueOf(cellValue));
                                testCase.setComments10(String.valueOf(dd.getTime()));
                            } catch (Exception e) {
                                e.printStackTrace();
                                mapError.put("comments10TimeFormatError", mapError.get("comments10TimeFormatError") + (rowNum + 1) + ",");
                            }
                        }
                    } else {
                        testCase.setComments10(cellValue);
                    }
                }
            }
        } else if (allTestCaseFieldMap.get("testsystem").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("testsystemBlank", mapError.get("testsystemBlank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue) && !testSystemMap.keySet().contains(cellValue)) {
                    mapError.put("testsystemError", mapError.get("testsystemError") + (rowNum + 1) + ",");
                } else {
                    testCase.setTestsystem(cellValue);
                    testCase.setTestsystemResourceID(testSystemMap.get(cellValue));
                }
            }
        } else if (allTestCaseFieldMap.get("systemmodule").equals(caseFieldName)) {
            if (!StringUtils.isEmpty(cellValue)) {
                testCase.setSystemmodule(cellValue);
            }
        } else if (allTestCaseFieldMap.get("trade").equals(caseFieldName)) {
            if ("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
                mapError.put("tradeBlank", mapError.get("tradeBlank") + (rowNum + 1) + ",");
            } else {
                if (!StringUtils.isEmpty(cellValue)) {
                    testCase.setTrade(cellValue);
                }
            }
        }
        return testCase;

    }

    /**
     * @Title: importIdentifyCaseID
     * @description: excel导入(当有案例编号重复时提示)
     * @param  "[file, request]"
     * @return com.jettech.dto.Result<?>
     * @throws
     * <AUTHOR>
     * @date 2021-06-11
     */
    @Override
    public Result<?> importIdentifyCaseID(MultipartFile file, String nodeType, String nodeResourceID,
                                 String userNumber, String taskResourceID, Boolean committed) {
        if (file == null) {
            return Result.renderError("导入文件为空！");
        }
        String fileName = file.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!fileType.toLowerCase().equals("xls") && !fileType.toLowerCase().equals("xlsx")) {
            return Result.renderError("导入的文件格式不正确，必须为excel文件！");
        }

        List<TestSystem> testSystems = null;
        List<Trade> tradeList = null;

        if (StringUtils.isEmpty(taskResourceID)) {
//            return Result.renderError("参数【taskResourceID】为空！");
            if ("system".equals(nodeType)) {
                TestSystem ts = testSystemService.findByResourceID(Long.valueOf(nodeResourceID));
                if (ts == null) {
                    return Result.renderError("未能获取到被测系统！");
                }
                testSystems = new ArrayList<>();
                testSystems.add(ts);
                tradeList = tradeService.findbyTestSystemResourceID(nodeResourceID,null,null,null);
                if (tradeList == null || tradeList.size() == 0) {
                    return Result.renderError("未能获取到交易！");
                }
            } else {
                Trade trade = tradeService.findByResourceID(Long.valueOf(nodeResourceID));
                if (trade == null) {
                    return Result.renderError("未能获取到交易！");
                }
                TestSystem testSystem = testSystemService.findByResourceID(trade.getTestSystemResourceID());
                if (testSystem == null) {
                    return Result.renderError("未能获取到被测系统！");
                }
                testSystems = new ArrayList<>();
                testSystems.add(testSystem);
                tradeList = new ArrayList<>();
                tradeList.add(trade);
            }
        } else {
            if ("system".equals(nodeType)) {
                TestSystem ts = testSystemService.findByResourceID(Long.valueOf(nodeResourceID));
                testSystems = new ArrayList<>();
                testSystems.add(ts);
            } else {
                testSystems = testSystemService.findTestSystemsByTestTaskResourceId(taskResourceID).getObj();
            }
            if (testSystems == null || testSystems.size() == 0) {
                return Result.renderError("未能获取到被测系统！");
            }
            tradeList = tradeService.findSelectedTradeByTaskResourceID(taskResourceID);
            if (tradeList == null || tradeList.size() == 0) {
                return Result.renderError("未能获取到交易！");
            }
        }
        return readExcelIdentifyCaseID(file, testSystems, tradeList, userNumber, taskResourceID, committed);
    }

    /**
     * @Title: importIdentifyCaseID
     * @description: excel导入(当有案例编号重复时提示)
     * @param  "[file, request]"
     * @return com.jettech.dto.Result<?>
     * @throws
     * <AUTHOR>
     * @date 2021-06-11
     */
    private Result<?> readExcelIdentifyCaseID(MultipartFile file, List<TestSystem> testSystems, List<Trade> tradeList, String userNumber
            , String taskResourceID, Boolean committed) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        Map<String, String> testPlan = null;
        if (!org.apache.commons.lang3.StringUtils.isEmpty(taskResourceID)) {
            testPlan = feignDataDesignToManexecuteService.findTestPlanByTestTaskResourceID(Long.parseLong(taskResourceID), token);
            if (testPlan == null || testPlan.isEmpty()) {
                return Result.renderError("测试任务或测试计划异常！");
            }
        }


        //回收站案例
        Map<Long, List<TestCaseRecycleBin>> testCaseRecycleBinsOfTrade = new HashMap<>();
        //交易下所有的caseId
        Map<Long, List<String>> caseIdsOfTrade = new HashMap<>();
        //此任务下已有的testCase
        Map<Long, List<TestCase>> caseOfTaskTrade = new HashMap<>();

        //？？？作用不明
        Map<Long, String> baseCaseEditIdOfTaskTrade = new HashMap<>();

        for (Trade trade : tradeList) {
            //在循环里面查数据库  再爆炸的机器也受不了
            List<TestCaseRecycleBin> testCaseRecycleBins = iTestCaseRecycleBinService.findByTradeResourceID(trade.getResourceID());
            testCaseRecycleBinsOfTrade.put(trade.getResourceID(), testCaseRecycleBins);

            Result<List<String>> result = feignDataDesignToAssets.findCaseIDByTradeResourceID(trade.getResourceID(), token);
            caseIdsOfTrade.put(trade.getResourceID(), result.getObj());

            List<TestCase> testCases = testCaseService.findbyTradeResourceID(trade.getResourceID());
            caseOfTaskTrade.put(trade.getResourceID(), testCases);

            String newTestCaseCaseId = testCaseService.getNewTestCaseCaseId(trade.getResourceID());
            baseCaseEditIdOfTaskTrade.put(trade.getResourceID(), newTestCaseCaseId);
        }


        //获取字典
        Result<?> resultData = feignDataDesignToBasicService.findByTestCaseExportFields(token);
        List<Map<String, Object>> fieldDefinitionList = (List<Map<String, Object>>) resultData.getObj();
        Map<String, Map<String, String>> fieldDictionary = getDic(fieldDefinitionList);

        //案例字段-评审状态未启用时导入案例的评审状态默认为"已评审",20211008dingwl
        boolean reviewStatusUsingEnable = this.reviewStatusUsingEnable();
        JettechUserDTO byNumber = feignDataDesignToBasicService.findByNumber(userNumber, token);

        //api案例显示  需要设置tradeFlowCaseFolderId,testProjectId
        Long testProjectId = null;
        Long tradeFlowCaseFolderId = null;
        if (testPlan != null && testPlan.get("demandResourceID") != null) {
            Demand demand = demandService.findByResourceID(Long.parseLong(testPlan.get("demandResourceID")));
            if (demand != null) {
                testProjectId = demand.getTestProjectResourceID();
                List<Map<String, Object>> folderList = testProjectService.findTradeFlowCaseFolder(demand.getTestProjectResourceID());
                if (folderList != null && folderList.size() > 0) {
                    tradeFlowCaseFolderId = Long.parseLong(folderList.get(0).get("id").toString());
                }
            }
        }

        TradeExcelIdentifyCaseIDReadHandler handler = new TradeExcelIdentifyCaseIDReadHandler(taskResourceID, fieldDefinitionList, fieldDictionary,
                testSystems, tradeList, userNumber, testPlan, testCaseRecycleBinsOfTrade, caseIdsOfTrade, caseOfTaskTrade,
                baseCaseEditIdOfTaskTrade, reviewStatusUsingEnable, byNumber.getUserName(), testProjectId, tradeFlowCaseFolderId, committed);
        ExcelReader excelReader = null;
        InputStream is=null;
        try {
            is=file.getInputStream();
            excelReader = EasyExcel.read(is, handler).build();

            List<ReadSheet> readSheets = new ArrayList<>();
            int m=CheckUtil.checkLoop(excelReader.excelExecutor().sheetList().size());
            for (int i = 0; i <m ; i++) {
                if (excelReader.excelExecutor().sheetList().get(i).getSheetName().equals("Dictionary")) {
                    continue;
                }
                readSheets.add(EasyExcel.readSheet(i).build());
            }
            excelReader.read(readSheets);

        } catch (Exception e) {
            logger.error("导入错误", e);
            if (handler.getErrorMsg().isEmpty()) {
                handler.setErrorMsg("导入时发生未知错误");
            }
        }finally {
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){

            }
        }

        if (!handler.getErrorMsg().isEmpty()) {
            return Result.renderError(handler.getErrorMsg());
        }

        if (!handler.getCasesToAdd().isEmpty()) {
            Result<?> result = testCaseService.batchesSaveTestCaseByLimit(handler.getCasesToAdd(), userNumber);
            if (!result.isSuccess()) {
                return Result.renderError("案例名称超出长度限制，导入失败。");
            }
        }
        if (!handler.getCaseToUpdate().isEmpty()) {
            testCaseService.batchesUpdateTestCaseByLimit(handler.getCaseToUpdate(), userNumber);
        }
        if (!handler.getCaseRecycleBinsToDelete().isEmpty()) {
            iTestCaseRecycleBinService.deleteInBatch(handler.getCaseRecycleBinsToDelete(), userNumber);
        }
        if (!org.apache.commons.lang3.StringUtils.isEmpty(taskResourceID)) {
            Result<?> result = feignDataDesignToManexecuteService.updateTestTaskStatus(taskResourceID, "2", userNumber);
            if (!result.isSuccess()) {
                return Result.renderError("更新任务状态失败！");
            }
        }
        return Result.renderSuccess();
    }

	/**
	 * @Title: reviewStatusUsingEnable
	 * @Description: 查询案例配置字段-评审状态是否启用
	 * @author: dingwenlong
	 * @date: 2021年10月8日
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private boolean reviewStatusUsingEnable() {
		Map<String, Object> fieldConfigMap;
    	Result<Map<String, Object>> fieldConfigResult = feignDataDesignToBasicService.initTestCaseConfigData();
    	if (!fieldConfigResult.isSuccess()) {
    		logger.error("查询案例字段配置信息异常,异常信息: {}", fieldConfigResult.getMsg());
			throw new RuntimeException(fieldConfigResult.getMsg());
		} else {
			List<Map<String, Object>> fieldConfigMapList = (List<Map<String, Object>>) fieldConfigResult.getObj();
			if (fieldConfigMapList.isEmpty()) {
				logger.error("案例字段配置为空！");
				throw new RuntimeException("案例字段配置为空！");
			}
			fieldConfigMap = fieldConfigMapList.stream().collect(Collectors.toMap(x -> x.get("name").toString(), y -> y.get("usingenable").toString()));
		}
    	return Boolean.parseBoolean(fieldConfigMap.get(TestCaseConstant.REVIEW_STATUS_FIELD).toString());
	}


    @Override
    public Result<?> projectAllCaseExport(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) {
        if (map.get("resourceID") == null) {
            return Result.renderError("入参[resourceID]为空！");
        }
        //查询日期筛选
        if (map.get("time") != null){
            List<String> timeList = (List<String>) map.get("time") ;
            map.put("startTime", timeList.get(0));
            map.put("endTime", timeList.get(1));
        }
        TestProject testProject = testProjectService.findByResourceID(Long.parseLong(map.get("resourceID").toString()));
        if (testProject == null) {
            return Result.renderError("项目不存在！");
        }
        if (map.get("selectedFields") == null) {
            return Result.renderError("入参[selectedFields]为空！");
        }
        //勾选项目案例id
        String projectCaseIds = map.get("projectCaseIds") == null ? "" : String.valueOf(map.get("projectCaseIds")).trim();
        // 导出字段
        final List<String> selectedFields = (List<String>) map.get("selectedFields");
        map.put("parent_url", paramConfig.getAttachmentPath() + File.separator + paramConfig.getTemporaryFolderName() + File.separator);
        Map<String, String> caseTypeMap = new HashMap<>();
        Map<String, String> caseLevelMap = new HashMap<>();
        Map<String, String> caseReviewStatusMap = new HashMap<>();
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result result1 = feignDataDesignToBasicService.findByName("CASETYPE", token);//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list1 = (List<Map<String, String>>) result1.getObj();
        list1.stream().forEach(x -> {
            caseTypeMap.put(x.get("value"), x.get("textName"));
        });
//    List<DataDictionaryDTO>  caseLevel = feignDataDesignToBasicService.findByNamesFromDataDesign("CASETLEVEL");
        Result result2 = feignDataDesignToBasicService.findByName("CASETLEVEL", token);//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list2 = (List<Map<String, String>>) result2.getObj();
        list2.stream().forEach(x -> {
            caseLevelMap.put(x.get("value"), x.get("textName"));
        });
//    List<DataDictionaryDTO>  caseReviewStatus = feignDataDesignToBasicService.findByNamesFromDataDesign("REVIEWSTATUS");
        Result result3 = feignDataDesignToBasicService.findByName("REVIEWSTATUS", token);//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list3 = (List<Map<String, String>>) result3.getObj();
        list3.stream().forEach(x -> {
            caseReviewStatusMap.put(x.get("value"), x.get("textName"));
        });

        //查询案例字段配置启用字段
        //Result<?> resultData = feignDataDesignToBasicService.findAllTestCaseFields(token);
        //查询案例字段配置启用字段
        Result<List<FieldConfigurationDTO>> fields = feignDataDesignToBasicService.findFieldAndDicValuesByConfigType(token);
        List<FieldConfigurationDTO> fieldDefinitionList = fields.getObj();
        //List<Map<String, String>> resultDataList = (List<Map<String, String>>) resultData.getObj();
        Map<String, String> cellNameMap = new HashMap<>();
        Map<String, String> dictionaryCellMap = new HashMap<>();
        Map<String, String> dateFormdataCellMap = new HashMap<>();
        List<String> dateFormdataCellList = new ArrayList<>();
        Map<String, String> excelCellMap = new HashMap<>();
        for (FieldConfigurationDTO obj : fieldDefinitionList) {
            if ("2".equals(obj.getFieldType()) || "3".equals(obj.getFieldType())) {
                dictionaryCellMap.put(obj.getNameDescription(), obj.getName());
            } else if ("5".equals(obj.getFieldType())) {
                dateFormdataCellMap.put(obj.getName(), obj.getName());
                dateFormdataCellList.add(obj.getAliasName());
            }
            if (String.valueOf(obj.isWriteRequired()).equals("true")) {
                excelCellMap.put(obj.getAliasName(), "* " + obj.getAliasName());
                cellNameMap.put("* " + obj.getAliasName(), obj.getNameDescription());
            } else {
                excelCellMap.put(obj.getAliasName(), obj.getAliasName());
                cellNameMap.put(obj.getAliasName(), obj.getNameDescription());
            }
        }
        cellNameMap.put("* 所属系统", "所属系统");
        cellNameMap.put("所属模块", "所属模块");
        cellNameMap.put("* 所属交易", "所属交易");
        cellNameMap.put("测试方式", "测试方式");
        cellNameMap.put("所属项目", "所属项目");
        excelCellMap.put("所属系统", "* 所属系统");
        excelCellMap.put("所属模块", "所属模块");
        excelCellMap.put("所属交易", "* 所属交易");
        excelCellMap.put("测试方式", "测试方式");
        excelCellMap.put("所属项目", "所属项目");
        selectedFields.add("所属项目");
        //配置字段的数据字典值
        Map<String, Map<String, String>> commentsDicMap = new HashMap<String, Map<String, String>>();
        if (!dictionaryCellMap.isEmpty()) {
            for (String nameDescription : dictionaryCellMap.keySet()) {
                List<FieldConfigurationDTO> fieldList = fieldDefinitionList.stream().filter(item->item.getNameDescription().equals(nameDescription)).collect(Collectors.toList());
                Map<String, String> commentsDicMap1 = new HashMap<>();
                for(FieldConfigurationDTO field:fieldList){
                    List<DataDictionaryDTO> dataDictionaryList= field.getDictionary();
                    if(dataDictionaryList!=null ){
                    	 for(DataDictionaryDTO dataDictionaryDTO:dataDictionaryList){
                             commentsDicMap1.put(dataDictionaryDTO.getValue(), dataDictionaryDTO.getTextName());
                         }
                    }
                }
                commentsDicMap.put(dictionaryCellMap.get(nameDescription), commentsDicMap1);
            }
        }

        dataDicUtil.getDicMap("testSystem");
        List<Map<String, Object>> testModeList = dataDicUtil.getList("testMode");
        Map<String, String> tmMap = testModeList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));

        List<TestCase> tc_list=new ArrayList<>();
        if(!projectCaseIds.equals("[]") && projectCaseIds != ""){
            tc_list = testCaseService.findProjectCase(map).stream().filter(item->projectCaseIds.contains(item.getResourceID().toString())).collect(Collectors.toList());
        }else{
            tc_list = testCaseService.findProjectCase(map);
        }

        List<Long> tradeResourceIDStringList = tc_list.stream().map(item->item.getTradeResourceID()).collect(Collectors.toList());
        //设置结果集中的系统 模块 交易
        List<SystemModuleTradeDTO> systemModuleTradeDTOList = tradeService.getTradeSystemModule(tradeResourceIDStringList);

        tc_list.stream().forEach(x -> {
            x.setCaseType(caseTypeMap.get(x.getCaseType()));
            x.setCasetLevel(caseLevelMap.get(x.getCasetLevel()));
            x.setReviewStatus(caseReviewStatusMap.get(x.getReviewStatus()));

            x.setTestProjectName(testProject.getName());
            Optional<SystemModuleTradeDTO> optional = systemModuleTradeDTOList.stream().filter(y -> y.getTradeResourceID().equals(x.getTradeResourceID())).findFirst();
            if (optional.isPresent()) {
                x.setTrade(optional.get().getTradeName());
                x.setSystemmoduleResourceID(optional.get().getModuleResourceID());
                x.setSystemmodule(optional.get().getModuleNames());
                x.setTestsystemResourceID(optional.get().getSystemResourceID());
                x.setTestsystem(optional.get().getSystemName());
            }

            if (x.getTestMode()!=null) {
                List<String> strs = BinaryDecimalUtil.TenToDicVal(x.getTestMode());
                String testModeValue = "";
                for (String s : strs) {
                    testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) +",";
                }
                x.setTestModeName("".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
            }

            if ("1".equals(x.getIsNegative())) {
                x.setIsNegative("正例");
            } else {
                x.setIsNegative("反例");
            }
            if (commentsDicMap.get("comments1") != null && !StringUtils.isEmpty(x.getComments1())) {
                List<String> keyList = Arrays.asList(x.getComments1().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments1");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments1(value);
            } else if (dateFormdataCellMap.get("comments1") != null && !StringUtils.isEmpty(x.getComments1())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments1(df.format(new Date(Long.valueOf(x.getComments1()))));
            }
            if (commentsDicMap.get("comments2") != null && !StringUtils.isEmpty(x.getComments2())) {
                List<String> keyList = Arrays.asList(x.getComments2().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments2");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments2(value);
            } else if (dateFormdataCellMap.get("comments2") != null && !StringUtils.isEmpty(x.getComments2())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments2(df.format(new Date(Long.valueOf(x.getComments2()))));
            }
            if (commentsDicMap.get("comments3") != null && !StringUtils.isEmpty(x.getComments3())) {
                List<String> keyList = Arrays.asList(x.getComments3().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments3");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments3(value);
            } else if (dateFormdataCellMap.get("comments3") != null && !StringUtils.isEmpty(x.getComments3())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments3(df.format(new Date(Long.valueOf(x.getComments3()))));
            }
            if (commentsDicMap.get("comments4") != null && !StringUtils.isEmpty(x.getComments4())) {
                List<String> keyList = Arrays.asList(x.getComments4().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments4");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments4(value);
            } else if (dateFormdataCellMap.get("comments4") != null && !StringUtils.isEmpty(x.getComments4())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments4(df.format(new Date(Long.valueOf(x.getComments4()))));
            }
            if (commentsDicMap.get("comments5") != null && !StringUtils.isEmpty(x.getComments5())) {
                List<String> keyList = Arrays.asList(x.getComments5().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments5");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments5(value);
            } else if (dateFormdataCellMap.get("comments5") != null && !StringUtils.isEmpty(x.getComments5())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments5(df.format(new Date(Long.valueOf(x.getComments5()))));
            }
            if (commentsDicMap.get("comments6") != null && !StringUtils.isEmpty(x.getComments6())) {
                List<String> keyList = Arrays.asList(x.getComments6().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments6");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments6(value);
            } else if (dateFormdataCellMap.get("comments6") != null && !StringUtils.isEmpty(x.getComments6())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments6(df.format(new Date(Long.valueOf(x.getComments6()))));
            }
            if (commentsDicMap.get("comments7") != null && !StringUtils.isEmpty(x.getComments7())) {
                List<String> keyList = Arrays.asList(x.getComments7().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments7");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments7(value);
            } else if (dateFormdataCellMap.get("comments7") != null && !StringUtils.isEmpty(x.getComments7())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments7(df.format(new Date(Long.valueOf(x.getComments7()))));
            }
            if (commentsDicMap.get("comments8") != null && !StringUtils.isEmpty(x.getComments8())) {
                List<String> keyList = Arrays.asList(x.getComments8().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments8");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments8(value);
            } else if (dateFormdataCellMap.get("comments8") != null && !StringUtils.isEmpty(x.getComments8())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments8(df.format(new Date(Long.valueOf(x.getComments8()))));
            }
            if (commentsDicMap.get("comments9") != null && !StringUtils.isEmpty(x.getComments9())) {
                List<String> keyList = Arrays.asList(x.getComments9().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments9");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments9(value);
            } else if (dateFormdataCellMap.get("comments9") != null && !StringUtils.isEmpty(x.getComments9())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments9(df.format(new Date(Long.valueOf(x.getComments9()))));
            }
            if (commentsDicMap.get("comments10") != null && !StringUtils.isEmpty(x.getComments10())) {
                List<String> keyList = Arrays.asList(x.getComments10().split(","));
                Map<String, String> dataMap = (Map<String, String>) commentsDicMap.get("comments10");
                String value = keyList.stream().map(k -> dataMap.get(k)).collect(Collectors.joining(","));
                x.setComments10(value);
            } else if (dateFormdataCellMap.get("comments10") != null && !StringUtils.isEmpty(x.getComments10())) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                x.setComments10(df.format(new Date(Long.valueOf(x.getComments10()))));
            }
        });
        // 读取模板
        String[][] data;
        try {
            OutputStream output = response.getOutputStream();
            response.reset();
            response.setHeader("Content-disposition",
                    "attachment; filename=" + encodeDownloadFilename("projectCase.xls", request.getHeader("User-Agent")));
            response.setContentType("application/msexcel");
            response.addHeader("X-Frame-Options", "DENY");
            data = getExcelData(selectedFields, tc_list, cellNameMap, excelCellMap);
            List<Integer> cellNums = new ArrayList<Integer>();
            int m=CheckUtil.checkLoop(selectedFields.size());
            for (int i = 0; i <m ; i++) {
                if (dateFormdataCellList.contains(selectedFields.get(i))) {
                    cellNums.add(i);
                }
            }
            HSSFWorkbook wb = exportExcel(data, cellNums);
            wb.write(output);
        } catch (ParseException | IOException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }
    public int check(Row titleRow ) {
    	int rowNumber = titleRow.getLastCellNum();
    	rowNumber=CheckUtil.checkLoop(rowNumber);
    	for (int k = 0; k < rowNumber; k++) {
			Cell cell = titleRow.getCell(k);
			String cellValue = cell.getStringCellValue();
			if(cellValue.equals("所属系统")) {
				return k;
			}
		}
		return rowNumber;
    }
    public List<String> getSystemName(MultipartFile file) {
    	 Workbook wb = ImportExcelUtils.getExcelInfo(file);
          // 读取标题 找到案例的所属系统
    	 List<String> systemList=new ArrayList<>();
		 Sheet sheeti = wb.getSheetAt(1);
		 Row titleRow = sheeti.getRow(0); //读取第一行 读取标题
		 int check = check(titleRow);//所属系统所在的行 读取数据时直接读取行数
	     int totalNumbers = sheeti.getPhysicalNumberOfRows();
        totalNumbers=CheckUtil.checkLoop(totalNumbers);
	     for (int j = 1; j < totalNumbers; j++) {// 剔除标题行
		// 遍历每一行
			Row rowj = sheeti.getRow(j);
			if (rowj == null) {
				// 跳过空行
				totalNumbers++;
				continue;
			}
			Cell cell = rowj.getCell(check);
			if(cell!=null) {
				systemList.add(cell.getStringCellValue());
			}
	     }
    	return systemList;
    }
    /**
     *
     * @Description  修改手自一体导入（覆盖）
     * @param file
     * @param nodeType
     * @param nodeResourceID
     * @param userNumber
     * @param taskResourceID
     * @return
     * tianliping
     * 2023年2月22日
     */
	@Override
	public Result<?> importShouzi(MultipartFile file, String nodeType, String nodeResourceID, String userNumber,
			String taskResourceID) {
		 if (file == null) {
	            return Result.renderError("导入文件为空！");
	        }
	        String fileName = file.getOriginalFilename();
	        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
	        if (!fileType.toLowerCase().equals("xls") && !fileType.toLowerCase().equals("xlsx")) {
	            return Result.renderError("导入的文件格式不正确，必须为excel文件！");
	        }
	        List<TestSystem> testSystems = null;
	        if ("system".equals(nodeType)) {
	            TestSystem ts = testSystemService.findByResourceID(Long.valueOf(nodeResourceID));
	            testSystems = new ArrayList<>();
	            testSystems.add(ts);
	        } else {
	            testSystems = testSystemService.findTestSystemsByTestTaskResourceId(taskResourceID).getObj();
	        }
	        if (testSystems == null || testSystems.size() == 0) {
	            return Result.renderError("未能获取到被测系统！");
	        }
        	List<String> systemName = getSystemName(file);
 	        List<TestSystem> collect = testSystems.stream().filter(x->systemName.contains(x.getName())).collect(Collectors.toList());
 	        List<Long> sysList = collect.stream().map(x->x.getResourceID()).collect(Collectors.toList());
 	        if(sysList.isEmpty()) {
 	        	return Result.renderError("未能获取到被测系统的信息！");
 	        }
 	        List<Trade> tradeList =tradeService.findBySystesmResourceIDIn(sysList);
	        if (tradeList == null || tradeList.size() == 0) {
	            return Result.renderError("未能获取到交易！");
	        }
	        return readExcel(file, testSystems, tradeList, userNumber, taskResourceID);

	}

    private String getText(String richText) {
        String regx = "(<.+?>)|(</.+?>)";
        Matcher matcher = Pattern.compile(regx).matcher(richText);
        while (matcher.find()) {
            richText = matcher.replaceAll("").replace("&nbsp;", " ");
        }
        return richText;
    }

	@Override
	public Result<?> exportExcelByDemandCase(TaskTradeCaseDto dto,HttpServletRequest request, HttpServletResponse response) {
        List<TaskTradeCaseView> testCaseList = testCaseDao.findTaskTradeCaseExport(dto);
        Result caseTypeResult = feignDataDesignToBasicService.findByName("CASETYPE", null);
        List<Map<String, String>> caseTypesList = (List<Map<String, String>>) caseTypeResult.getObj();
        List<TestCaseExcelView> excelViews = new ArrayList<>();
        for (TaskTradeCaseView taskTradeCaseView : testCaseList) {
            TestCaseExcelView view = new TestCaseExcelView();
            BeanUtils.copyProperties(taskTradeCaseView, view);
            for (Map<String, String> map2 : caseTypesList) {
                if (Objects.equals(map2.get("value"), taskTradeCaseView.getCaseType())) {
                    view.setCaseType(map2.get("textName"));
                }
            }
            view.setCreateTimeStr(DateUtil.getDateStr(view.getCreateTime(), DateUtil.TIME_PATTREN));
            excelViews.add(view);
        }

        ByteArrayOutputStream outputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, TestCaseExcelView.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("需求案例").doWrite(excelViews);
            byte[] bytes = outputStream.toByteArray();
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("案例数据", "utf-8") + ".xlsx");
            response.getOutputStream().write(bytes);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return Result.renderError("导出缺陷失败");
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
        return null;


        /*List<Map<String, String>> testCaseMapList = new ArrayList<Map<String, String>>();
        for (TaskTradeCaseView taskTradeCaseView : testCaseList) {
            Map<String, String> map1 = new HashMap<>();
            map1.put("案例编号", taskTradeCaseView.getCaseId());
            map1.put("测试目的（描述）", (null == taskTradeCaseView.getIntent() ? null : taskTradeCaseView.getIntent()));
            if(taskTradeCaseView.getCaseType() != null){
                Result caseTypeResult = feignDataDesignToBasicService.findByName("CASETYPE", null);
                List<Map<String, String>> caseTypesList = (List<Map<String, String>>) caseTypeResult.getObj();
                for (Map<String, String> map2 : caseTypesList) {
                    if (map2.get("value").contains(taskTradeCaseView.getCaseType())) {
                        map1.put("案例类型", map2.get("textName").toString());
                    }
                }
            }  else {
                map1.put("案例类型", null);
            }

            map1.put("创建人", (null == taskTradeCaseView.getMaintainer() ? null : taskTradeCaseView.getMaintainer()));
            map1.put("创建时间", (null == taskTradeCaseView.getCreateTime() ? null : df.format(taskTradeCaseView.getCreateTime())));
            map1.put("执行人", (null == taskTradeCaseView.getExecutor() ? null : taskTradeCaseView.getExecutor()));
//            map1.put("执行时间", (null == taskTradeCaseView.getExecuteTime() ? null : taskTradeCaseView.getExecuteTime()));
            map1.put("执行结果", (null == taskTradeCaseView.getCaseResult() ? null : taskTradeCaseView.getCaseResult()));
            map1.put("执行次数", String.valueOf(taskTradeCaseView.getRunCount()));
            testCaseMapList.add(map1);
        }
        // 显示的导出表的标题
        String title = "需求案例执行表";
        //导出的文件名
        String fileName = "需求案例";
        //导出表内所需要的字段
      //  String[] rowName = {"案例编号", "测试目的（描述）", "案例类型", "创建人", "创建时间", "执行人", "执行时间", "执行结果", "执行次数"};
        String[] rowName = {"案例编号", "测试目的（描述）", "案例类型", "创建人", "创建时间","执行人","执行结果", "执行次数"};
        try {
            ExcelUtils.exportExcel(title, title, rowName, rowName, testCaseMapList, fileName, response, request);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("批量导出需求任务失败");
            Result.renderError();
        }
        return null;*/
    }

    @Override
    public Result downProjectCaseTemp(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) {
        if (map.get("selectedFields") == null) {
            return Result.renderError("入参[selectedFields]为空！");
        }
        // 导出字段
        final List<String> selectedFields = (List<String>) map.get("selectedFields");
        selectedFields.add(0, "所属模块交易层级");

        //查询案例字段配置启用字段
        Result<?> resultData = feignDataDesignToBasicService.findAllTestCaseFields(null);
        List<Map<String, String>> resultDataList = (List<Map<String, String>>) resultData.getObj();
        Map<String, String> cellNameMap = new HashMap<>();
        Map<String, String> excelCellMap = new HashMap<>();
        for (Map<String, String> obj : resultDataList) {
            if (String.valueOf(obj.get("writeRequired")).equals("true")) {
                excelCellMap.put(obj.get("aliasName"), "* " + obj.get("aliasName"));
                cellNameMap.put("* " + obj.get("aliasName"), obj.get("nameDescription"));
            } else {
                excelCellMap.put(obj.get("aliasName"), obj.get("aliasName"));
                cellNameMap.put(obj.get("aliasName"), obj.get("nameDescription"));
            }
        }
        cellNameMap.put("* 所属模块交易层级", "所属模块交易层级");
        cellNameMap.put("* 所属系统", "所属系统");
        cellNameMap.put("所属模块", "所属模块");
        cellNameMap.put("* 所属交易", "所属交易");
        cellNameMap.put("测试方式", "测试方式");
        excelCellMap.put("所属模块交易层级", "* 所属模块交易层级");
        excelCellMap.put("所属系统", "* 所属系统");
        excelCellMap.put("所属模块", "所属模块");
        excelCellMap.put("所属交易", "* 所属交易");
        excelCellMap.put("测试方式", "测试方式");

        // 读取模板
        String[][] data;
        try {
            OutputStream output = response.getOutputStream();
            response.reset();
            response.setHeader("Content-disposition",
                    "attachment; filename=" + encodeDownloadFilename("projectCase.xls", request.getHeader("User-Agent")));
            response.setContentType("application/msexcel");
            response.addHeader("X-Frame-Options", "DENY");
            data = getExcelData(selectedFields, new ArrayList<>(), cellNameMap, excelCellMap);
            List<Integer> cellNums = new ArrayList<Integer>();
            HSSFWorkbook wb = exportExcel(data, cellNums);
            wb.write(output);
        } catch (Exception e) {
            logger.error("下载案例模板失败", e);
        }
        return null;
    }
}
