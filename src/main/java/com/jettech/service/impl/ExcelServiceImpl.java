package com.jettech.service.impl;

import com.google.common.collect.Iterators;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.BinaryDecimalUtil;
import com.jettech.common.util.CheckUtil;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.common.util.constants.testcase.TestCaseConstant;
import com.jettech.feign.IFeignAssetsToBasic;
import com.jettech.feign.IFeignAssetsToDataDesign;
import com.jettech.model.AtTestCase;
import com.jettech.service.iservice.IExcelService;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.util.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @title: ImportExcelServiceImpl
 * @projectName jettopro
 * @description: excel导入导出
 * @date 2019/11/411:53
 */
@Service
@Transactional
public class ExcelServiceImpl implements IExcelService {

    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private IFeignAssetsToDataDesign feignAssetsToDataDesign;
    @Autowired
    private IFeignAssetsToBasic feignAssetsToBasic;
    @Autowired
    private DataDicUtil dataDicUtil;
    private SimpleDateFormat sfEnd = new SimpleDateFormat("yyyy-MM-dd");
	private SimpleDateFormat sfStart = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", java.util.Locale.ENGLISH);
    private static final Logger logger = LoggerFactory.getLogger(ExcelServiceImpl.class);

    /**
     * @param "[file, userNumber]"
     * @param nodeType
     * @param nodeResourceID
     * @return com.jettech.dto.Result<?>
     * @throws
     * @Title: importExcel
     * @description: 导入excel（模板文件，不支持批量操作）
     * <AUTHOR>
     * @date 2019/11/4 11:57
     */
    @Override
    public Result<?> importExcel(MultipartFile file, String nodeType, String nodeResourceID, String userNumber,String testEnviroment) throws Exception {
    	String token = HttpRequestUtils.getCurrentRequestToken();
//        Result userResult = feignAssetsToDataDesign.findTestSystemUserByUserNumber(nodeType,nodeResourceID,token);
//        String b = (String) userResult.getObj();
//        if(!Boolean.valueOf(b)) return Result.renderError("当前登录无权限操作该被测系统！");
        Result result = new Result();
        //创建处理excel的类
        ReadExcel readExcel = new ReadExcel();
        //解析excel文件，获取文件数据
        Map<String, Object> excelInfo = readExcel.getExcelInfo(file);
        ArrayList<AtTestCase> addCase = new ArrayList<>();
        ArrayList<AtTestCase> updateCase = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        switch (readExcel.getCode()) {
            //使用未指定的模板
            case 30000:
                result = Result.renderError("使用非指定法模板", 30000);
                break;
            //excel案例编号重复
            case 50000:
                result = Result.renderError(readExcel.getErrorInfo(), 50000);
                break;
            case 20000:
                synchronized (this){
                    Map<String, Object> feignMap = feignAssetsToDataDesign.getTradeListAndObj(nodeType, nodeResourceID,token);
                    //对象
                   // Map<String,Object> obj = (Map<String, Object>) feignMap.get("obj");
                    //交易集合
                    List<Map<String,Object>> trade_list = (List<Map<String, Object>>) feignMap.get("list");

                    Map<String, Long> trade_map = trade_list.stream()
                            .collect(Collectors.toMap(e -> e.get("name").toString(), e -> Long.valueOf(e.get("resourceID").toString()), (oldVal, currVal) -> currVal));

                    Set<String> keySet = excelInfo.keySet();
                    Iterator<String> iterator = keySet.iterator();

                    while(iterator.hasNext()){
                        String tradeName = iterator.next();
                        Long tradeResourceID = trade_map.get(tradeName);
                        if(tradeResourceID == null){
                            return Result.renderError("交易名称：“"+tradeName+"”，在当前节点下不存在！");
                        }

                        HashMap<String, String> maps = new HashMap<>();
                        maps.put("tradeResourceID",tradeResourceID.toString());
//                        Result res = feignAssetsToDataDesign.getTestCaseCaseId(maps);
//                        String prefix = res.getObj().toString();
                        List<Map<String,Object>> excelValue = (List<Map<String, Object>>) excelInfo.get(tradeName);

//                        List<String> caseIds = excelValue.stream()
//                                .map(e -> prefix +  String.valueOf(e.get("caseId")))
//                                .collect(Collectors.toList());
//                        List<AtTestCase> tc_list = testCaseService.findByCaseIdsAndTradeResourceID(caseIds,tradeResourceID.toString());

                        dataDicUtil.getDicMap("testCase");
                        List<JettechUserDTO> allPerson = feignAssetsToBasic.findAllPerson(token);
                        Map<String, String> users=new HashMap<>();
    					for (JettechUserDTO jettechUserDTO : allPerson) {
    						users.put(jettechUserDTO.getUserName(), jettechUserDTO.getNumber());
    					}

                        String newTestCaseCaseId = testCaseService.getNewTestCaseCaseId(tradeResourceID);

                        for (Map<String, Object> map : excelValue) {
                            String caseId =  String.valueOf(map.get("caseId"));
//                            Optional<AtTestCase> any = tc_list.stream()
//                                    .filter(e -> e.getCaseEditId().equals(caseId))
//                                    .findAny();
                            AtTestCase tc;
//                            if(any.isPresent()){
//                                //修改
//                                tc = any.get();
//                                FillTestCase(tradeResourceID.toString(), sdf, map, tc,caseId,newTestCaseCaseId);
//                                updateCase.add(tc);
//                            }else{
                                //添加
                                tc = new AtTestCase();
                            Result fillResult = FillTestCase(tradeResourceID.toString(), sdf, map, tc, caseId, newTestCaseCaseId,users);
                            if(fillResult.getCode() != 20000){
                                return fillResult;
                            }
                            //添加导入环境
                            tc.setTestEnviroment(testEnviroment);
                            addCase.add(tc);
//                            }
                            newTestCaseCaseId = String.format("%07d", Integer.valueOf(newTestCaseCaseId) + 1);
                        }
                    }

                    //批量录入,修改
                    testCaseService.saveImportList(addCase, userNumber);
                    testCaseService.update(updateCase,userNumber);
                    result = Result.renderSuccess("导入成功！");
                }
                break;
            default:
                result = Result.renderError();
                break;
        }
        return result;
    }

    private Result FillTestCase(String tradeResourceID, SimpleDateFormat sdf, Map<String, Object> map, AtTestCase tc, String editCaseId, String caseId,
    		Map<String, String> allPerson) throws ParseException {
        try {
            tc.setCaseId(caseId);
            tc.setCaseEditId(StringUtils.isBlank(editCaseId) || "null".equals(editCaseId) ? null : editCaseId);
            tc.setCasetLevel(String.valueOf(dataDicUtil.getValue("caseLevel", String.valueOf(map.get("casetLevel")))));
            tc.setCaseType(String.valueOf(dataDicUtil.getValue("caseType", String.valueOf(map.get("caseType")))));
            tc.setComment("null".equals(String.valueOf(map.get("comment"))) ? null : String.valueOf(map.get("comment")));
            tc.setExpectedResult("null".equals(String.valueOf(map.get("expectedResult"))) ? null : String.valueOf(map.get("expectedResult")));
            tc.setIntent("null".equals(String.valueOf(map.get("intent"))) ? null : String.valueOf(map.get("intent")));
            tc.setIsNegative(String.valueOf(dataDicUtil.getValue("isNegative", String.valueOf(map.get("isNegative")))));
            tc.setPreconditions("null".equals(String.valueOf(map.get("preconditions"))) ? null : String.valueOf(map.get("preconditions")));
            tc.setTestStep("null".equals(String.valueOf(map.get("testStep"))) ? null : String.valueOf(map.get("testStep")));
            tc.setTradeResourceID(Long.valueOf(tradeResourceID));
            tc.setTimingName("null".equals(String.valueOf(map.get("timingName"))) ? null : String.valueOf(map.get("timingName")));
           /* String createUser = "null".equals(String.valueOf(map.get("maintainer"))) ? null : String.valueOf(map.get("maintainer"));
            if(createUser != null){
                tc.setCreateUser(allPerson.stream().filter(e -> e.getUserName().equals(createUser)).findFirst().get().getNumber());
            }*/
            String maintainer=String.valueOf(map.get("maintainer"));
			if(!allPerson.containsKey(maintainer)) {
				return Result.renderError("创建人【"+maintainer+"】不存在!");
			}
			tc.setMaintainer(maintainer);
			tc.setCreateUser(allPerson.get(maintainer));
            tc.setCreateTime(map.get("maintenanceTime") == null ? new Date() : sdf.parse(String.valueOf(map.get("maintenanceTime"))));
            tc.setReviewStatus(String.valueOf(dataDicUtil.getValue("reviewStatus", String.valueOf(map.get("reviewStatus")))));
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return Result.renderError("时间格式错误！");
        } catch (ParseException e) {
            e.printStackTrace();
            return Result.renderError("时间格式错误！");
        } catch (NoSuchElementException e){
            return  Result.renderError("导入参数类型与字典参数不符,请下载最新模板！");
        }
        return Result.renderSuccess();
    }

    /**
     * @param "[typeResourceID, type]"
     * @return com.jettech.dto.Result<?>
     * @throws
     * @Title: exportExcel
     * @description: 导出excel（支持批量）
     * <AUTHOR>
     * @date 2019/11/4 15:19
     */
    @SuppressWarnings("unchecked")
	@Override
	public Result<?> exportExcel(Map map) {
		String agent = String.valueOf(map.get("Agent"));
//		if(map.get("testEnviroment") == null) {
//			return Result.renderError("入参[testEnviroment]为空！");
//		}
		String maintainer = map.get("maintainer") == null ? "" : String.valueOf(map.get("maintainer")).trim();
		String isNegative = map.get("isNegative") == null ? "" : String.valueOf(map.get("isNegative")).trim();
		String caseType = map.get("caseType") == null ? "" : String.valueOf(map.get("caseType")).trim();
		//勾选案例id
		String testCaseResourceIDs = map.get("testCaseResourceIDs") == null ? "" : String.valueOf(map.get("testCaseResourceIDs")).trim();
		// 添加测试环境
//		String testEnviroment = String.valueOf(map.get("testEnviroment"));
//		if("全部".equals(testEnviroment)) testEnviroment = "";
		String testEnviroment = "";

				HttpServletResponse response = (HttpServletResponse) map.get("response");
		HttpServletRequest request = (HttpServletRequest) map.get("request");
		String parent_url = request.getSession().getServletContext().getRealPath("/") + File.separator;
		map.remove("request");
		map.remove("response");
		map.put("parent_url", parent_url);
		logger.info("导出案例库案例路径。。" + map.get("parent_url"));
		String trades = (String) map.get("trade");
		List<String> tradesList = new ArrayList<>();
		if(trades!=null &&trades!=""){
			String[] tradeRid = trades.split(",");
			for (String string : tradeRid) {
				tradesList.add(string);
			}
		}

		if(map.get("selectedFields") == null) {
			return Result.renderError("入参[selectedFields]为空！");
		}
		//测试方式字段
		String testMode = map.get("testMode") == null || "".equals(map.get("testMode")) ? "0"
				: String.valueOf(map.get("testMode")).trim();// 测试方式
		testMode = BinaryDecimalUtil.DicValToBin(testMode);
		map.put("testMode", testMode);
		// 导出字段
		final List<String> selectedFields = (List<String>) map.get("selectedFields");

		// 判断当前登录人是否具有权限
		// Set set = map.keySet();
		// Iterator<String> its = set.iterator();
		// while (its.hasNext()){
		// String nodeType = its.next();
		// if("parent_url".equals(nodeType) || "Agent".equals(nodeType)) continue;
		// String resourceIDs = (String) map.get(nodeType);
		// String[] split = resourceIDs.split(",");
		// for(int i = 0 ; i < split.length ; i++){
		// Result res = feignAssetsToDataDesign.findTestSystemUserByUserNumber(nodeType,
		// split[i]);
		// String obj = (String) res.getObj();
		// if(!Boolean.valueOf(obj)) return Result.renderError("当前登录无权限操作该被测系统！");
		// }
		// }

		HashMap<String, Object> url_map = feignAssetsToDataDesign.getUrl_Map(map);

		Set<String> keySet = url_map.keySet();
		Iterator<String> it = keySet.iterator();

		String token = HttpRequestUtils.getCurrentRequestToken();
		Map<String, String> caseTypeMap = new HashMap<>();
		Map<String, String> caseLevelMap = new HashMap<>();
		Map<String, String> caseReviewStatusMap = new HashMap<>();
		Result<?> CASETYPE_result = feignAssetsToBasic.findByName("CASETYPE", token);
		Result<?> CASETLEVEL_result = feignAssetsToBasic.findByName("CASETLEVEL", token);
		Result<?> REVIEWSTATUS_result = feignAssetsToBasic.findByName("REVIEWSTATUS", token);
		List<Map<String, String>> caseLevel_list = (List<Map<String, String>>) CASETLEVEL_result.getObj();
		caseLevel_list.stream().forEach(x -> {
			caseLevelMap.put(x.get("value"), x.get("textName"));
		});
		List<Map<String, String>> caseType_list = (List<Map<String, String>>) CASETYPE_result.getObj();
		caseType_list.stream().forEach(x -> {
			caseTypeMap.put(x.get("value"), x.get("textName"));
		});
		List<Map<String, String>> reviewStatus_list = (List<Map<String, String>>) REVIEWSTATUS_result.getObj();
		reviewStatus_list.stream().forEach(x -> {
			caseReviewStatusMap.put(x.get("value"), x.get("textName"));
		});
		//查询案例字段配置启用字段
		Result<?> resultData = feignAssetsToBasic.findAllTestCaseFields(token);
		List<Map<String, String>> resultDataList = (List<Map<String, String>>)resultData.getObj();
		Map<String, String> cellNameMap = new HashMap<>();
		Map<String, String> dictionaryCellMap = new HashMap<>();
		List<String> dictionaryMultiDataList = new ArrayList<>();
		Map<String, String> dateFormdataCellMap = new HashMap<>();
		List<String> dateFormdataCellList = new ArrayList<>();
		Map<String, String> excelCellMap = new HashMap<>();
		for (Map<String, String> obj : resultDataList) {
			if("2".equals(obj.get("fieldType")) || "3".equals(obj.get("fieldType"))) {
				dictionaryCellMap.put(obj.get("aliasName"), obj.get("name"));
				if( "3".equals(obj.get("fieldType"))){//多选下拉
					dictionaryMultiDataList.add(String.valueOf(obj.get("name")));
				}
			}else if("5".equals(obj.get("fieldType"))) {
				dateFormdataCellMap.put(obj.get("name"), obj.get("name"));
				dateFormdataCellList.add(obj.get("aliasName"));
			}
			if(String.valueOf(obj.get("writeRequired")).equals("true")) {
				excelCellMap.put(obj.get("aliasName"), "* "+ obj.get("aliasName"));
				cellNameMap.put("* "+ obj.get("aliasName"), obj.get("nameDescription"));
			}else {
				excelCellMap.put(obj.get("aliasName"), obj.get("aliasName"));
				cellNameMap.put(obj.get("aliasName"), obj.get("nameDescription"));
			}
		}
		//配置字段的数据字典值
		Map<String,Map<String,String>> commentsDicMap = new HashMap<String,Map<String,String>>();
		if(!dictionaryCellMap.isEmpty()) {
			for(String nameDescription : dictionaryCellMap.keySet()) {
				//由于备选再短10特殊处理了，所以这里我只能这么改了,正常逻辑数据字典的name = 字段配置的nameDescription
				/*if("案例备用字段10".equals(nameDescription)){
					nameDescription = "COMMENTS10";
				}*/
				List<Map<String,Object>> list =  testCaseService.findTestCaseDictionaryDataByInfoName(nameDescription);
		    	Map<String,String> commentsDicMap1 = new HashMap<>();
				for(Map<String,Object> map1 : list) {
					commentsDicMap1.put(String.valueOf(map1.get("value")), String.valueOf(map1.get("textName")));
				}
				if("COMMENTS10".equals(nameDescription)){
					//commentsDicMap.put(dictionaryCellMap.get("案例备用字段10"), commentsDicMap1);
				}else{
					commentsDicMap.put(dictionaryCellMap.get(nameDescription), commentsDicMap1);
				}
			}
		}
		try {
			int m=CheckUtil.checkLoop(Iterators.size(it));
			if(m==CheckUtil.MAX_LOOPS){
				return null;
			}
			it = keySet.iterator();
			while (it.hasNext()) {
				String key1 = it.next();
				String url = String.valueOf(url_map.get(key1));
				String[] split = key1.split(",");
				System.out.println(split[0]);
				String split2 = key1.split(",")[1];
				if (!tradesList.contains(split2)) {
					continue;
				}
				File file = null;
				System.out.println("file:|" + url);
				file = new File(url);
				if (!file.exists()) {
					file.mkdirs();
				}
				String excelName = HanyuPinyinUtil.getPinyinString(split[0]);
				url = url + excelName + ".xls";
				System.out.println("fileUrl:|" + url);
				logger.info("fileUrl:|" + url);
				// List<AtTestCase> tc_list =
				// testCaseService.findbyTradeResourceID(Long.valueOf(split[1]));
				List<AtTestCase> tc_list;
				// 需求变更，添加按照测试环境导出
				if(testCaseResourceIDs!=""){
					tc_list = testCaseService
							.findbyTradeResourceIDAndTestEnviroment(Long.valueOf(split[1]), testEnviroment,maintainer, isNegative,caseType).stream().filter(item->testCaseResourceIDs.contains(item.getResourceID().toString())).collect(Collectors.toList());
				}else{
					tc_list = testCaseService
							.findbyTradeResourceIDAndTestEnviroment(Long.valueOf(split[1]), testEnviroment,maintainer, isNegative,caseType);
				}
				tc_list.stream().forEach(x ->{
					x.setCaseType(caseTypeMap.get(x.getCaseType()));
					x.setCasetLevel(caseLevelMap.get(x.getCasetLevel()));
					x.setReviewStatus(caseReviewStatusMap.get(x.getReviewStatus()));
					if("1".equals(x.getIsNegative())) {
						x.setIsNegative("正例");
					}else {
						x.setIsNegative("反例");
					}
					if(commentsDicMap.get("comments1") != null && !StringUtils.isEmpty(x.getComments1())) {
						if(dictionaryMultiDataList.contains("comments1")){
							x.setComments1(this.dealMultiData(commentsDicMap,x,"comments1",x.getComments1()));
						}else{
							x.setComments1(commentsDicMap.get("comments1").get(x.getComments1()));
						}
					}else if(dateFormdataCellMap.get("comments1") != null && !StringUtils.isEmpty(x.getComments1())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments1(df.format(new Date(Long.valueOf(x.getComments1()))));
					}
					if(commentsDicMap.get("comments2") != null && !StringUtils.isEmpty(x.getComments2())) {
						if(dictionaryMultiDataList.contains("comments2")){
							x.setComments2(this.dealMultiData(commentsDicMap,x,"comments2",x.getComments2()));
						}else{
							x.setComments2(commentsDicMap.get("comments2").get(x.getComments2()));
						}
					}else if(dateFormdataCellMap.get("comments2") != null && !StringUtils.isEmpty(x.getComments2())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments2(df.format(new Date(Long.valueOf(x.getComments2()))));
					}
					if(commentsDicMap.get("comments3") != null && !StringUtils.isEmpty(x.getComments3())) {
						if(dictionaryMultiDataList.contains("comments3")){
							x.setComments3(this.dealMultiData(commentsDicMap,x,"comments3",x.getComments3()));
						}else{
							x.setComments3(commentsDicMap.get("comments3").get(x.getComments3()));
						}
					}else if(dateFormdataCellMap.get("comments3") != null && !StringUtils.isEmpty(x.getComments3())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments3(df.format(new Date(Long.valueOf(x.getComments3()))));
					}
					if(commentsDicMap.get("comments4") != null && !StringUtils.isEmpty(x.getComments4())) {
						if(dictionaryMultiDataList.contains("comments4")){
							x.setComments4(this.dealMultiData(commentsDicMap,x,"comments4",x.getComments4()));
						}else{
							x.setComments4(commentsDicMap.get("comments4").get(x.getComments4()));
						}
					}else if(dateFormdataCellMap.get("comments4") != null && !StringUtils.isEmpty(x.getComments4())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments4(df.format(new Date(Long.valueOf(x.getComments4()))));
					}
					if(commentsDicMap.get("comments5") != null && !StringUtils.isEmpty(x.getComments5())) {
						if(dictionaryMultiDataList.contains("comments5")){
							x.setComments5(this.dealMultiData(commentsDicMap,x,"comments5",x.getComments5()));
						}else{
							x.setComments5(commentsDicMap.get("comments5").get(x.getComments5()));
						}
					}else if(dateFormdataCellMap.get("comments5") != null && !StringUtils.isEmpty(x.getComments5())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments5(df.format(new Date(Long.valueOf(x.getComments5()))));
					}
					if(commentsDicMap.get("comments6") != null && !StringUtils.isEmpty(x.getComments6())) {
						if(dictionaryMultiDataList.contains("comments6")){
							x.setComments6(this.dealMultiData(commentsDicMap,x,"comments6",x.getComments6()));
						}else{
							x.setComments6(commentsDicMap.get("comments6").get(x.getComments6()));
						}
					}else if(dateFormdataCellMap.get("comments6") != null && !StringUtils.isEmpty(x.getComments6())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments6(df.format(new Date(Long.valueOf(x.getComments6()))));
					}
					if(commentsDicMap.get("comments7") != null && !StringUtils.isEmpty(x.getComments7())) {
						if(dictionaryMultiDataList.contains("comments7")){
							x.setComments7(this.dealMultiData(commentsDicMap,x,"comments7",x.getComments7()));
						}else{
							x.setComments7(commentsDicMap.get("comments7").get(x.getComments7()));
						}
					}else if(dateFormdataCellMap.get("comments7") != null && !StringUtils.isEmpty(x.getComments7())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments7(df.format(new Date(Long.valueOf(x.getComments7()))));
					}
					if(commentsDicMap.get("comments8") != null && !StringUtils.isEmpty(x.getComments8())) {
						if(dictionaryMultiDataList.contains("comments8")){
							x.setComments8(this.dealMultiData(commentsDicMap,x,"comments8",x.getComments8()));
						}else{
							x.setComments8(commentsDicMap.get("comments8").get(x.getComments8()));
						}
					}else if(dateFormdataCellMap.get("comments8") != null && !StringUtils.isEmpty(x.getComments8())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments8(df.format(new Date(Long.valueOf(x.getComments8()))));
					}
					if(commentsDicMap.get("comments9") != null && !StringUtils.isEmpty(x.getComments9())) {
						if(dictionaryMultiDataList.contains("comments9")){
							x.setComments9(this.dealMultiData(commentsDicMap,x,"comments9",x.getComments9()));
						}else{
							x.setComments9(commentsDicMap.get("comments9").get(x.getComments9()));
						}
					}else if(dateFormdataCellMap.get("comments9") != null && !StringUtils.isEmpty(x.getComments9())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments9(df.format(new Date(Long.valueOf(x.getComments9()))));
					}
					if(commentsDicMap.get("comments10") != null && !StringUtils.isEmpty(x.getComments10())) {
						if(dictionaryMultiDataList.contains("comments10")){
							x.setComments10(this.dealMultiData(commentsDicMap,x,"comments10",x.getComments10()));
						}else{
							x.setComments10(commentsDicMap.get("comments10").get(x.getComments10()));
						}
					}else if(dateFormdataCellMap.get("comments10") != null  && !StringUtils.isEmpty(x.getComments10())) {
						SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
						x.setComments10(df.format(new Date(Long.valueOf(x.getComments10()))));
					}

				});
				// 读取模板,传入交易名
				readModelExcel(url, tc_list, selectedFields, cellNameMap, dateFormdataCellList,excelCellMap,split[0]);
			}
			logger.info("本地文件夹生成成功。。");
			OutputStream out = response.getOutputStream();
			byte[] data = createZip(parent_url);// 服务器存储地址
			response.setHeader("Content-Disposition",
					"attachment;filename=" + resFileUtils.encodeDownloadFilename("a.zip", agent));
			;
			response.setContentType("multipart/form-data;charset=gbk");
			response.setCharacterEncoding("gbk");
			response.addHeader("X-Frame-Options", "DENY");
			IOUtils.write(data, out);
			// out.write(data.toString().getBytes("utf-8"));
			// 删除本地文件夹
			delFile(new File(parent_url));
			logger.info("本地文件夹删除成功。。");
			if (out != null) {
				out.flush();
				out.close();
			}
			return null;
		} catch (Exception e) {
			logger.info("导出报错。。" + e.getStackTrace());
			e.printStackTrace();
			return null;
		}

	}

	private  String dealMultiData(Map<String,Map<String,String>> commentsDicMap,AtTestCase x,String commentsName,String commentsValue){
		Map<String,String> values = commentsDicMap.get(commentsName);
		String[] keys = commentsValue.split(",");
		StringBuffer value = new StringBuffer();
		for (String key :keys){
			String valueOne = values.get(key);
			if(!org.springframework.util.StringUtils.isEmpty(valueOne)){
				value.append(valueOne).append(",");
			}
		}
		String Final = value.toString().substring(0,value.toString().length()-1);
		return Final;
	}

    /**
     * 读取模板并导出
     * @param url
     * @param list
     * @param dateFormdataCellList
     * @param cellNameMap
     * @param selectedFields
     * @param excelCellMap
     */
    public void readModelExcel(String url, List<AtTestCase> list, List<String> selectedFields, Map<String, String> cellNameMap, List<String> dateFormdataCellList, Map<String, String> excelCellMap,String tradeName){
    	try {
			String[][] data = getExcelData(selectedFields, list, cellNameMap,excelCellMap);
			List<Integer> cellNums = new ArrayList<Integer>();
			int m=CheckUtil.checkLoop(selectedFields.size());
			for(int i = 0; i <m ; i++)  {
				if(dateFormdataCellList.contains(selectedFields.get(i))) {
					cellNums.add(i);
				}
			}
			HSSFWorkbook wb = exportExcel(data,cellNums,tradeName);
			/*if (!FileValidUtil.valid(url)) {
				throw new RuntimeException("文件路径不合法！");
			}*/
			FileOutputStream out = new FileOutputStream(url); // 创建文件输出流
			wb.write(out);
			out.flush();
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (ParseException e) {
			e.printStackTrace();
		}
    }

    public HSSFWorkbook exportExcel(String[][] data,List<Integer> cellNums,String tradeName) throws ParseException {
		HSSFWorkbook wb = new HSSFWorkbook();
		HSSFSheet sheet = wb.createSheet((tradeName == null || tradeName.equals("")) ? "案例数据" : tradeName);
		sheet.setDefaultColumnWidth(16);
		HSSFCellStyle cellStyle = wb.createCellStyle();
        HSSFDataFormat format= wb.createDataFormat();
        cellStyle.setDataFormat(format.getFormat("yyyy-MM-dd"));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        int m=CheckUtil.checkLoop(data.length);
		for (int i = 0; i <m ; i++) {
			HSSFRow row = sheet.createRow(i);
			// sheet.autoSizeColumn(i);
			for (int j = 0; j < data[i].length; j++) {
				HSSFCell cell = row.createCell(j);
				if(i > 0 && cellNums.contains(j)) {
					cell.setCellStyle(cellStyle);
					if(!org.springframework.util.StringUtils.isEmpty(data[i][j])) {
						cell.setCellValue(simpleDateFormat.parse(data[i][j]));
					}
				}else {
					cell.setCellValue(data[i][j]);
				}
			}
		}
		return wb;
	}
	@Autowired
	private IFeignAssetsToBasic basicService;
    public String[][] getExcelData(List<String> selectedFields, List<AtTestCase> defect_list, Map<String, String> cellNamemMap, Map<String, String> excelCellMap)
			throws IllegalAccessException, ParseException {
		String[][] str = new String[defect_list.size() + 1][selectedFields.size()];
		int m=CheckUtil.checkLoop(defect_list.size());
		int n=CheckUtil.checkLoop(selectedFields.size());
		for (int row = 0; row < m + 1; row++) {
			for (int cell = 0; cell < n; cell++) {
				if (row == 0) {
					str[row][cell] = excelCellMap.get(selectedFields.get(cell));
					continue;
				}

				//转换测试方式字典
				if(selectedFields.get(cell).equals("测试方式")&&defect_list.get(row - 1).getTestMode()!= null){
					String testMode = defect_list.get(row - 1).getTestMode().toString();
					//测试方式
					Result tmRes = basicService.findByName("TESTMODE", null);
					List<Map<String, Object>> tmList = (List<Map<String, Object>>) tmRes.getObj();
					Map<String, String> tmMap = tmList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));

					if (testMode != null) {
						List<String> strs = BinaryDecimalUtil.TenToDicVal(Integer.valueOf(testMode));
						String testModeValue = "";
						for (String s : strs) {
							testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) +",";
						}
						str[row][cell] = "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1);
					}
				}else{
					str[row][cell] = getStringValue(cellNamemMap.get(str[0][cell]), defect_list.get(row - 1));
				}
			}
		}
		return str;
	}

    public String getStringValue(String selectField, AtTestCase defect) throws IllegalAccessException, ParseException {
		String val = "";
		Class clazz = defect.getClass();
		Field[] fields = clazz.getDeclaredFields();
		Field[] declaredFields = clazz.getSuperclass().getDeclaredFields();
		for (Field f : fields) {
			f.setAccessible(true);
			FieldNote fieldNote = f.getAnnotation(FieldNote.class);
			if (fieldNote == null)
				continue;
			String value = fieldNote.value();
			String name = f.getName();
			if (selectField.equals(value)) {
				if (fieldNote.type() == FieldType.Date) {
					if (null == f.get(defect)) {
						val = "";
					} else {
						val = sfEnd.format(sfStart.parse(f.get(defect).toString()));
					}
				} else {
					val = String.valueOf(f.get(defect) == null ? "" : f.get(defect));
				}
				break;
			}

		}
		for (Field f : declaredFields) {
			f.setAccessible(true);
			FieldNote fieldNote = f.getAnnotation(FieldNote.class);
			if (fieldNote == null || "创建时间".equals(fieldNote.value()))
				continue;
			String value = fieldNote.value();
			if (selectField.equals(value)) {
				if (fieldNote.type() == FieldType.Date) {
					if (null == f.get(defect)) {
						val = "";
					} else {
						val = sfEnd.format(sfStart.parse(f.get(defect).toString()));
					}
				} else {
					val = String.valueOf(f.get(defect) == null ? "" : f.get(defect));
				}
				break;
			}
		}
		return val;
	}

    public byte[] createZip(String srcSource) throws Exception {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		ZipOutputStream zip = new ZipOutputStream(outputStream);
		zip.setEncoding("gbk");
		// 将目标文件打包成zip导出
		logger.info("》》》》》》》》》》》》》》》》》》》》file.encoding:"+System.getProperty("file.encoding"));
		logger.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<sun.jnu.encoding:"+System.getProperty("sun.jnu.encoding"));
		File file = new File(new String(srcSource.getBytes(),"gbk"));
		a(zip, file, "");
		IOUtils.closeQuietly(zip);
		return outputStream.toByteArray();
	}

    public void a(ZipOutputStream zip, File file, String dir) throws Exception {
		try {
			zip.setEncoding("gbk");
			int len;
			byte[] buf = new byte[8192];
			// 如果当前的是文件夹，则进行进一步处理
			if (file.isDirectory()) {
				// 得到文件列表信息
				File[] files = file.listFiles();
				// 将文件夹添加到下一级打包目录
				zip.putNextEntry(new ZipEntry(dir + "/"));
				dir = dir.length() == 0 ? "" : dir + "/";
				// 循环将文件夹中的文件打包
				for (int i = 0; i < files.length; i++) {
					a(zip, files[i], dir + files[i].getName()); // 递归处理
				}
			} else { // 当前的是文件，打包处理
				// 文件输入流
				BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
				ZipEntry entry = new ZipEntry(dir);
				zip.putNextEntry(entry);
				int m=CheckUtil.checkLoop(bis.available()/8192);
				if(m==CheckUtil.MAX_LOOPS){
					return;
				}
				while ((len = bis.read(buf)) > 0) {
					zip.write(buf, 0, len);
				}
				// zip.write(FileUtils.readFileToByteArray(file));
				IOUtils.closeQuietly(bis);
			}
		} catch (Exception e) {
			zip.flush();
			zip.close();
		}

	}


    public boolean delFile(File file) {
        if (!file.exists()) {
            return false;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                delFile(f);
            }
        }
        return file.delete();
    }

    /**
     * @Title: downloadModel
     * @description: 下载模板
     * @param "[response]"
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2019/11/7 14:55
     */
    @Override
    public void downloadModel(String agent,HttpServletResponse response) {
        try {
            createAutoModal(agent,response);
        } catch (Exception e) {
            e.printStackTrace();
            InputStream is = ExcelServiceImpl.class.getResourceAsStream("/modal.xlsx");
            try {
                OutputStream os = response.getOutputStream();
                response.setHeader("Content-Disposition", "attachment;filename=" + resFileUtils.encodeDownloadFilename("modal.xlsx", agent));
                //response.setContentType("multipart/form-data;charset=UTF-8");
                response.setContentType("application/vnd.ms-excel;charset=UTF-8");
				response.addHeader("X-Frame-Options", "DENY");
                response.setCharacterEncoding("utf-8");
                byte[] b = new byte[1024];
                int len ;
                while ((len = is.read(b)) > 0){
                    os.write(b, 0, len);
                }
                os.close();
                is.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
    }

    private void createAutoModal(String agent, HttpServletResponse response) throws Exception {
        dataDicUtil.getDicMap("testCase");
        List<Map<String,Object>> caseType_list = dataDicUtil.getList("caseType");
        List<Map<String,Object>> caseLevel_list = dataDicUtil.getList("caseLevel");
        List<Map<String,Object>> reviewStatus_list = dataDicUtil.getList("reviewStatus");
        List<Map<String,Object>> isNegative_list = dataDicUtil.getList("isNegative");

        String[] caseType_arr = caseType_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);
        String[] caseLevel_arr = caseLevel_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);
        String[] reviewStatus_arr = reviewStatus_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);
        String[] isNegative_arr = isNegative_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet();
        sheet.setColumnWidth(0,100 * 35);
        sheet.setColumnWidth(1,100 * 35);
        sheet.setColumnWidth(2,100 * 35);
        sheet.setColumnWidth(3,100 * 35);
        sheet.setColumnWidth(4,100 * 35);
        sheet.setColumnWidth(5,100 * 35);
        sheet.setColumnWidth(6,100 * 35);
        sheet.setColumnWidth(7,100 * 35);
        sheet.setColumnWidth(8,100 * 35);
        sheet.setColumnWidth(9,100 * 35);
        sheet.setColumnWidth(10,100 * 35);
        sheet.setColumnWidth(11,100 * 35);
        sheet.setColumnWidth(12,100 * 35);

        sheet.setDefaultRowHeight((short) (22*20));

        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);

        //批注
        XSSFDrawing p = sheet.createDrawingPatriarch();
        XSSFComment isNegative_Com = p.createCellComment(new XSSFClientAnchor(0,0,0,0,(short)3,2,(short)4,5));
        isNegative_Com.setString(new XSSFRichTextString("输入正例或者反例，如果输入正或者反系统系统导入转换为正例/反例"));

        XSSFComment caseType_com = p.createCellComment(new XSSFClientAnchor(0,0,0,0,(short)5,2,(short)6,6));
        caseType_com.setString(new XSSFRichTextString("功能案例/流程案例"));

        XSSFComment maintenanceTime_com = p.createCellComment(new XSSFClientAnchor(0,0,0,0,(short)12,2,(short)13,6));
        maintenanceTime_com.setString(new XSSFRichTextString("建议时间格式为：yyyy/MM/dd"));

        XSSFComment caseId_com = p.createCellComment(new XSSFClientAnchor(0,0,0,0,(short)1,2,(short)2.3,6));
        caseId_com.setString(new XSSFRichTextString("建议案例编号格式：编号000001"));

        //模板列
        XSSFRow row = sheet.createRow(0);
        row.setHeight((short) (22*20));

        XSSFCell caseId = row.createCell(0);
        caseId.setCellValue("案例编号");
        caseId.setCellStyle(cellStyle);
        caseId.setCellComment(caseId_com);

        XSSFCell intent = row.createCell(1);
        intent.setCellValue("测试意图");
        intent.setCellStyle(cellStyle);

        XSSFCell isNegative = row.createCell(2);
        isNegative.setCellValue("正反例");
        isNegative.setCellStyle(cellStyle);
        isNegative.setCellComment(isNegative_Com);

        XSSFCell caseType = row.createCell(3);
        caseType.setCellValue("案例类型");
        caseType.setCellStyle(cellStyle);
        caseType.setCellComment(caseType_com);

        XSSFCell casetLevel = row.createCell(4);
        casetLevel.setCellValue("案例级别");
        casetLevel.setCellStyle(cellStyle);

        XSSFCell preconditions = row.createCell(5);
        preconditions.setCellValue("前置条件");
        preconditions.setCellStyle(cellStyle);

        XSSFCell testStep = row.createCell(6);
        testStep.setCellValue("测试步骤");
        testStep.setCellStyle(cellStyle);

        XSSFCell expectedResult = row.createCell(7);
        expectedResult.setCellValue("预期结果");
        expectedResult.setCellStyle(cellStyle);

        XSSFCell timingName = row.createCell(8);
        timingName.setCellValue("运行条件");
        timingName.setCellStyle(cellStyle);

        XSSFCell comment = row.createCell(9);
        comment.setCellValue("备注");
        comment.setCellStyle(cellStyle);

        XSSFCell maintainer = row.createCell(10);
        maintainer.setCellValue("创建人");
        maintainer.setCellStyle(cellStyle);

        XSSFCell maintenanceTime = row.createCell(11);
        maintenanceTime.setCellValue("创建时间");
        maintenanceTime.setCellStyle(cellStyle);
        maintenanceTime.setCellComment(maintenanceTime_com);

        XSSFCell reviewStatus = row.createCell(12);
        reviewStatus.setCellValue("评审状态");
        reviewStatus.setCellStyle(cellStyle);

        this.setValidationData(sheet,1,9999,3,3,caseType_arr);
        this.setValidationData(sheet,1,9999,2,2,isNegative_arr);
        this.setValidationData(sheet,1,9999,4,4,caseLevel_arr);
        this.setValidationData(sheet,1,9999,12,12,reviewStatus_arr);

        ServletOutputStream out = response.getOutputStream();
        response.setHeader("Content-Disposition", "attachment;filename=" + resFileUtils.encodeDownloadFilename("modal.xlsx", agent));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding("utf-8");
		response.addHeader("X-Frame-Options", "DENY");

        wb.write(out);
        out.close();
    }

    /**
     * 添加数据有效性检查.
     * @param sheet 要添加此检查的Sheet
     * @param firstRow 开始行
     * @param lastRow 结束行
     * @param firstCol 开始列
     * @param lastCol 结束列
     * @param explicitListValues 有效性检查的下拉列表
     * @throws IllegalArgumentException 如果传入的行或者列小于0(< 0)或者结束行/列比开始行/列小
     */
    public static void setValidationData(Sheet sheet, int firstRow, int lastRow,
                                         int firstCol, int lastCol, String[] explicitListValues) throws IllegalArgumentException{
        if (firstRow < 0 || lastRow < 0 || firstCol < 0 || lastCol < 0 || lastRow < firstRow || lastCol < firstCol) {
            throw new IllegalArgumentException("Wrong Row or Column index : " + firstRow+":"+lastRow+":"+firstCol+":" +lastCol);
        }
        if (sheet instanceof XSSFSheet) {
            XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet)sheet);
            XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
                    .createExplicitListConstraint(explicitListValues);
            CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
            XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        } else if(sheet instanceof HSSFSheet){
            CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
            DVConstraint dvConstraint = DVConstraint.createExplicitListConstraint(explicitListValues);
            DataValidation validation = new HSSFDataValidation(addressList, dvConstraint);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        }
    }
    /**
     * @Title importExcelNew
     * @description 导入案例（需求变更）
     * @date 20200525
     * <AUTHOR>
     */
	@SuppressWarnings("unchecked")
	@Override
	public Result<?> importExcelNew(MultipartFile file, String nodeType, String nodeResourceID, String userNumber,
			String testEnviroment) {
		if(file == null) {
			return Result.renderError("导入文件为空！");
		}
		String fileName = file.getOriginalFilename();
		String fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.lastIndexOf(".") + 4);
		if (!fileType.toLowerCase().equals("xls") && !fileType.toLowerCase().equals("xlsx")) {
			return Result.renderError("导入的文件格式不正确，必须为excel文件！");
		}

		if (StringUtils.isEmpty(nodeType) || "trade".equals(nodeType)) {
			return Result.renderError("被导入节点错误，请选择正确模块！");
		}
		if (StringUtils.isEmpty(nodeResourceID)) {
			return Result.renderError("参数【nodeResourceID】为空！");
		}
		/*if (StringUtils.isEmpty(testEnviroment)) {
			return Result.renderError("测试阶段为空，请选择测试阶段！");
		}*/
		String token = HttpRequestUtils.getCurrentRequestToken();
		Map<String, Object> feignMap = feignAssetsToDataDesign.getTradeListAndObj(nodeType, nodeResourceID,token);
		//交易集合
        List<Map<String,Object>> trade_list = (List<Map<String, Object>>) feignMap.get("list");
        Map<String, Long> trade_map = trade_list.stream()
                .collect(Collectors.toMap(e -> e.get("name").toString(), e -> Long.valueOf(e.get("resourceID").toString()), (oldVal, currVal) -> currVal));
		return readExcel(file,testEnviroment,trade_map,userNumber);
	}
	/**
	 * 读取excel文件
	 * @param file
	 * @param deptResourceID
	 * @param depttype
	 * @param createUser
	 * @return
	 */
	public Result<?> readExcel(MultipartFile file, String testEnviroment, Map<String, Long> trade_map, String userNumber) {
		//处理错误信息
		String errorMsg = "";
		HSSFWorkbook workbook = null;
		try {
			workbook = new HSSFWorkbook(file.getInputStream());
		} catch (Exception e) {
			errorMsg = "程序出错!";
		}
		String sheetNameStr = "";
		String tradeNameStr = "";
		int m=CheckUtil.checkLoop(workbook.getNumberOfSheets());
		for (int i = 0; i <m ; i++) {
			HSSFSheet sheet = workbook.getSheetAt(i);
			String sheetName = sheet.getSheetName();
			Long tradeResourceID = trade_map.get(sheetName);
			// resourceID不存在，交易不存在
			if (tradeResourceID == null) {
				tradeNameStr += sheetName + "，";
			}else {
				HSSFRow row=sheet.getRow(0);
				if(row != null) {
					boolean boo = ImportExcelUtils.valitaileExcelTitle(row,feignAssetsToBasic);
					if(!boo) {
						sheetNameStr += sheetName + "，";
					}
				}else {
					sheetNameStr += sheetName + "，";
				}
			}
		}
		if(!StringUtils.isEmpty(tradeNameStr)) {
			tradeNameStr = tradeNameStr.substring(0, tradeNameStr.length()-1);
			return Result.renderError("交易名称：【" + tradeNameStr + "】在当前节点下不存在！");
		}
		if(!StringUtils.isEmpty(sheetNameStr)) {
			sheetNameStr = sheetNameStr.substring(0, sheetNameStr.length()-1);
			return Result.renderError("名称为【"+sheetNameStr+"】的sheet页标题某些列名不存在，请重新维护或下载模板！");
		}
		if(ImportExcelUtils.TRADE_IMPORT_TITLE.isEmpty() || ImportExcelUtils.caseFieldConfiguration.isEmpty()) {
			return Result.renderError("请先查看案例字段配置是否正常！");
		}
		HSSFSheet sheet0 = workbook.getSheetAt(0);
		Row row0 = sheet0.getRow(0);
		Map<Integer,String> cellNameMap = new HashMap<Integer,String>();
		Map<String,Integer> cellNunberMap = new HashMap<String,Integer>();
		Map<String,String> writeRequiredCellMap = new HashMap<String,String>();
		Map<String,String> aliasNameCellMap = new HashMap<String,String>();
		Map<String,String> dictionaryCellMap = new HashMap<String,String>();
		Map<String,String> dateFormdataCellMap = new HashMap<String,String>();
		for(int i = 0; i < row0.getPhysicalNumberOfCells(); i++) {
			for(Map<String, String> map : ImportExcelUtils.caseFieldConfiguration) {
				if(ImportExcelUtils.getValue(row0.getCell(i)).equals(map.get("aliasName"))) {
					cellNameMap.put(i, map.get("name"));
					cellNunberMap.put(map.get("name"),i);
					writeRequiredCellMap.put(map.get("name"), String.valueOf(map.get("writeRequired")));
					aliasNameCellMap.put(map.get("name"), map.get("aliasName"));
					if("2".equals(map.get("fieldType")) || "3".equals(map.get("fieldType"))) {
						dictionaryCellMap.put(map.get("aliasName"), map.get("name"));
					}else if("5".equals(map.get("fieldType"))) {
						dateFormdataCellMap.put(map.get("name"), map.get("name"));
					}
				}
			}
		}
		Map<String,String> allTestCaseFieldMap = new HashMap<String,String>();
		for (Map<String, String> obj : ImportExcelUtils.caseFieldConfiguration) {
			allTestCaseFieldMap.put(obj.get("name"),obj.get("name"));
		}
		//数据字典-案例级别
		List<Map<String,Object>> caseLevel_list = testCaseService.findTestCaseDictionaryData("CASETLEVEL");
		Map<String,String> caseLevelDicMap = new HashMap<>();
		for(Map<String,Object> map : caseLevel_list) {
			caseLevelDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
		}
		//数据字典-案例类型
    	List<Map<String,Object>> caseType_list =  testCaseService.findTestCaseDictionaryData("CASETYPE");
    	Map<String,String> caseTypeDicMap = new HashMap<>();
		for(Map<String,Object> map : caseType_list) {
			caseTypeDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
		}
		//数据字典-评审状态
    	List<Map<String,Object>> reviewStatus_list =  testCaseService.findTestCaseDictionaryData("REVIEWSTATUS");
    	Map<String,String> reviewStatusDicMap = new HashMap<>();
		for(Map<String,Object> map : reviewStatus_list) {
			reviewStatusDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
		}

		//数据字典-正反例
    	List<Map<String,Object>> isNegative_list =  testCaseService.findTestCaseDictionaryData("NEGATIVE");
    	Map<String,String> isNegativeDicMap = new HashMap<>();
		for(Map<String,Object> map : isNegative_list) {
			isNegativeDicMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
		}
		List<Map<String,Object>> testModel_list =  testCaseService.findTestCaseDictionaryData("TESTMODE");
    	Map<String,String> testmodelMap = new HashMap<>();
		for(Map<String,Object> map : testModel_list) {
			testmodelMap.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
		}

    	List<Map<String, String>>  allPersonInfo = testCaseService.findAllUsersNameAndNumber();
		Map<String, String> users=new HashMap<>();
		for (Map<String, String> map : allPersonInfo) {
			users.put(String.valueOf(map.get("userName")), String.valueOf(map.get("userName")));
		}
		//配置字段的数据字典值
		Map<String,Map<String,String>> commentsDicMap = new HashMap<String,Map<String,String>>();
		if(!dictionaryCellMap.isEmpty()) {
			for(String nameDescription : dictionaryCellMap.keySet()) {
				String newNameDes = "";
				if(nameDescription.startsWith("*")){
					newNameDes = nameDescription.replace("* ","");
				}else{
					newNameDes = nameDescription;
				}
				List<Map<String,Object>> list1 =  testCaseService.findTestCaseDictionaryDataByInfoName(newNameDes);
		    	Map<String,String> commentsDicMap1 = new HashMap<>();
				for(Map<String,Object> map : list1) {
					commentsDicMap1.put(String.valueOf(map.get("textName")), String.valueOf(map.get("value")));
				}
				commentsDicMap.put(dictionaryCellMap.get(nameDescription), commentsDicMap1);
			}
		}

		String token = HttpRequestUtils.getCurrentRequestToken();
		//案例字段-评审状态未启用时导入案例的评审状态默认为"已评审",20211008dingwl
		boolean reviewStatusUsingEnable = this.reviewStatusUsingEnable();
		JettechUserDTO byNumber = feignAssetsToBasic.findByNumber(userNumber, token);

		List<AtTestCase> addCase = new ArrayList<>();
		List<AtTestCase> updateCase = new ArrayList<>();
		Map<String,String> sheetErrorMap = new HashMap<String,String>();
		for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
			HSSFSheet sheet = workbook.getSheetAt(i);
			String sheetName = sheet.getSheetName();
			Long tradeResourceID = trade_map.get(sheetName);
			int totalNumbers = sheet.getPhysicalNumberOfRows();
			Map<String,String> mapError = createErrorList();//用于存储各种错误信息
			Map<String, List<String>> sameCaseIDMap = new HashMap<String, List<String>>();
			List<AtTestCase> tc_list = testCaseService.findbyTradeResourceID(tradeResourceID);
			Result<List<String>> result = feignAssetsToDataDesign.findCaseIDByTradeResourceID(tradeResourceID, token);
			List<String> caseIDlist = result.getObj();
			Map<String,Object> testSystem = testCaseService.findTestSystemByTradeResourceID(tradeResourceID);
			if(testSystem == null) {
				return Result.renderError("导入异常，模块对应的系统不存在！");
			}
			int cellNumber = sheet.getRow(0).getPhysicalNumberOfCells();
			for (int j = 1; j < totalNumbers; j++) {
				//遍历每一行
				Row rowj = sheet.getRow(j);
				if(rowj == null) {
					//跳过空行
					totalNumbers ++;
					continue;
				}
				String caseId = ImportExcelUtils.getValue(rowj.getCell(cellNunberMap.get("caseId").intValue()));
				Optional<AtTestCase> any = tc_list.stream().filter(e -> caseId.equals(e.getCaseId()))
						.findAny();


				AtTestCase tc;

				if (any.isPresent()) {
					// 修改
					tc = any.get();
					for(int k = 0; k < cellNumber; k++) {
						if(cellNameMap.get(k) == null) {
							return Result.renderError("导入异常，模板错误！");
						}
						tc = setTestCase(tc, cellNameMap.get(k), ImportExcelUtils.getValue(rowj.getCell(k)), j , allTestCaseFieldMap, writeRequiredCellMap, mapError, sameCaseIDMap,caseLevelDicMap,caseTypeDicMap,reviewStatusDicMap,commentsDicMap,isNegativeDicMap,users,rowj.getCell(k),dateFormdataCellMap,testmodelMap);
					}
					if (!reviewStatusUsingEnable) {//评审状态未启用设置为已评审
						tc.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
					}
					//【PRO-3890】逻辑改为启用非必填为空就设为空
//					if (StringUtils.isBlank(tc.getReviewStatus())) {
//						tc.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
//					}
					tc.setMaintainer(byNumber.getUserName());// 维护人
					tc.setMaintenanceTime(new Date());// 维护时间
					updateCase.add(tc);

				}else if(caseIDlist.contains(caseId)){
					mapError.put("sameCaseEditIdInDataDesign", mapError.get("sameCaseEditIdInDataDesign") +(j+1)+"," );
				}else {
					String newTestCaseCaseId = testCaseService.getNewTestCaseCaseId(tradeResourceID);
					// 添加
					tc = new AtTestCase();
					tc.setCaseEditId(newTestCaseCaseId);
					tc.setTestEnviroment(testEnviroment);
					tc.setTradeResourceID(tradeResourceID);
					//添加系统模块交易落库
					if(tradeResourceID!=null){
						//根据交易id查询系统、交易及模块
						Result tradeAndModuleResult =feignAssetsToDataDesign.getTradeSystemModule(tradeResourceID,token);
						List<Map<String,Object>> tradeAndModule = (List<Map<String,Object>>)tradeAndModuleResult.getObj();
						for (Map<String,Object> t : tradeAndModule) {
							tc.setTestsystem(t.get("systemName").toString());
							tc.setTestsystemResourceID(Long.valueOf(t.get("systemResourceID").toString()));
							tc.setSystemmoduleResourceID(t.get("moduleResourceID")==null?null:Long.valueOf(t.get("moduleResourceID").toString()));
							tc.setSystemmodule(t.get("moduleName")==null?"":t.get("moduleName").toString());
							tc.setTrade(t.get("tradeName").toString());
						}
					}
					for(int k = 0; k < cellNumber; k++) {
						if(cellNameMap.get(k) == null) {
							return Result.renderError("导入异常，模板错误！");
						}
						tc = setTestCase(tc, cellNameMap.get(k), ImportExcelUtils.getValue(rowj.getCell(k)), j , allTestCaseFieldMap, writeRequiredCellMap, mapError, sameCaseIDMap,caseLevelDicMap,caseTypeDicMap,reviewStatusDicMap,commentsDicMap,isNegativeDicMap,users,rowj.getCell(k),dateFormdataCellMap,testmodelMap);
					}
					if (!reviewStatusUsingEnable) {//评审状态未启用设置为已评审
						tc.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
					}
					if (StringUtils.isBlank(tc.getReviewStatus())) {
						tc.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
					}
					tc.setMaintainer(byNumber.getUserName());// 维护人
					tc.setMaintenanceTime(new Date());// 维护时间
					addCase.add(tc);
					newTestCaseCaseId = String.format("%07d", Integer.valueOf(newTestCaseCaseId) + 1);
				}
			}
			List<String> caseIdRepeatRows = new ArrayList<String>();
			for(String key : sameCaseIDMap.keySet()) {
				if(sameCaseIDMap.get(key).size()>1) {
					caseIdRepeatRows.addAll(sameCaseIDMap.get(key));
				}
			}
			if(!caseIdRepeatRows.isEmpty()) {
				mapError.put("sameCaseEditIdInSheet", mapError.get("sameCaseEditIdInSheet") + org.apache.commons.lang3.StringUtils.join(caseIdRepeatRows.toArray(), ",")+",");
			}
			//处理错误信息
			errorMsg = this.dealErrorLog(mapError,aliasNameCellMap);
			if(!StringUtils.isEmpty(errorMsg)) {
				sheetErrorMap.put(sheetName, errorMsg);
			}
		}
		if(sheetErrorMap.isEmpty()) {
			if(!addCase.isEmpty()) {
				testCaseService.batchesSaveTestCaseByLimit(addCase, userNumber);
			}
			if(!updateCase.isEmpty()) {
				testCaseService.batchesUpdateTestCaseByLimit(updateCase, userNumber);
			}
			return Result.renderSuccess();
		}else {
			String allErrorMsg = "";
			for(String sheetName : sheetErrorMap.keySet()) {
				allErrorMsg = allErrorMsg + "在名称为【"+sheetName+"】的sheet页内，存在以下错误：" + sheetErrorMap.get(sheetName);
			}
			return Result.renderError(allErrorMsg);
		}

	}

	private AtTestCase setTestCase(AtTestCase testCase, String caseFieldName, String cellValue, int rowNum,
			Map<String, String> allTestCaseFieldMap, Map<String, String> writeRequiredCellMap,
			Map<String, String> mapError, Map<String, List<String>> sameCaseIDMap, Map<String, String> caseLevelDicMap,
			Map<String, String> caseTypeDicMap, Map<String, String> reviewStatusDicMap,
			Map<String, Map<String, String>> commentsDicMap, Map<String,String> isNegativeDicMap,
			Map<String, String> users, Cell cell,Map<String,String> dateFormdataCellMap,Map<String,String> testModeMap) {
			if(allTestCaseFieldMap.get("name").equals(caseFieldName)) {
				if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
					mapError.put("nameBlank", mapError.get("nameBlank") +(rowNum+1)+"," );
				}
				testCase.setName(cellValue);
			}else if(allTestCaseFieldMap.get("caseId").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("caseEditIdBlank", mapError.get("caseEditIdBlank") +(rowNum+1)+"," );
			}
			if(!StringUtils.isEmpty(cellValue)) {
				if(sameCaseIDMap.get(cellValue)==null || sameCaseIDMap.get(cellValue).isEmpty()){
					List<String> rowNums = new ArrayList<String>();
					rowNums.add(rowNum+1+"");
					sameCaseIDMap.put(cellValue, rowNums);
				}else {
					List<String> rowNums = sameCaseIDMap.get(cellValue);
					rowNums.add(rowNum+1+"");
					sameCaseIDMap.put(cellValue, rowNums);
				}
			}
			testCase.setCaseId(cellValue);
		}else if(allTestCaseFieldMap.get("intent").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("intentBlank", mapError.get("intentBlank") +(rowNum+1)+"," );
			}
			testCase.setIntent(cellValue);
		}else if(allTestCaseFieldMap.get("isNegative").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("isNegativeBlank", mapError.get("isNegativeBlank") +(rowNum+1)+"," );
			}else {
				if(!isNegativeDicMap.keySet().contains(cellValue)) {
					mapError.put("isNegativeError", mapError.get("isNegativeError") +(rowNum+1)+"," );
				}else {
					testCase.setIsNegative(isNegativeDicMap.get(cellValue));
				}
			}
		}else if(allTestCaseFieldMap.get("caseType").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("caseTypeBlank", mapError.get("caseTypeBlank") +(rowNum+1)+"," );
			}else {
				if(!caseTypeDicMap.keySet().contains(cellValue)) {
					mapError.put("caseTypeError", mapError.get("caseTypeError") +(rowNum+1)+"," );
				}else {
					testCase.setCaseType(caseTypeDicMap.get(cellValue));
				}
			}
		}else if(allTestCaseFieldMap.get("casetLevel").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("casetLevelBlank", mapError.get("casetLevelBlank") +(rowNum+1)+"," );
			}else {
				if(!caseLevelDicMap.keySet().contains(cellValue)) {
					mapError.put("casetLevelError", mapError.get("casetLevelError") +(rowNum+1)+"," );
				}else {
					testCase.setCasetLevel(caseLevelDicMap.get(cellValue));
				}
			}
		}else if(allTestCaseFieldMap.get("testMode").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("testModeBlank", mapError.get("testModeBlank") +(rowNum+1)+"," );
			}else {
				String testModes[] = cellValue.split(",");
				String string = "";//拼接测试方式
				for(String testMode:testModes){
					if(!testModeMap.keySet().contains(testMode)) {
						mapError.put("testModeError", mapError.get("testModeError") +(rowNum+1)+"," );
					}else {
						string = string + testModeMap.get(testMode);
					}
				}
				String s = BinaryDecimalUtil.DicValToBin(string);
				testCase.setTestMode(BinaryDecimalUtil.BinToTen(s));

			}
		}else if(allTestCaseFieldMap.get("preconditions").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("preconditionsBlank", mapError.get("preconditionsBlank") +(rowNum+1)+"," );
			}else {
				testCase.setPreconditions(cellValue);
			}
		}else if(allTestCaseFieldMap.get("checkPoint").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("checkPointBlank", mapError.get("checkPointBlank") +(rowNum+1)+"," );
			}else {
				testCase.setCheckPoint(cellValue);
			}
		}else if(allTestCaseFieldMap.get("testStep").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("testStepBlank", mapError.get("testStepBlank") +(rowNum+1)+"," );
			}else {
				testCase.setTestStep(cellValue);
			}
		}else if(allTestCaseFieldMap.get("expectedResult").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("expectedResultBlank", mapError.get("expectedResultBlank") +(rowNum+1)+"," );
			}else {
				testCase.setExpectedResult(cellValue);
			}
		}else if(allTestCaseFieldMap.get("timingName").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("timingNameBlank", mapError.get("timingNameBlank") +(rowNum+1)+"," );
			}else {
				testCase.setTimingName(cellValue);
			}
		}else if(allTestCaseFieldMap.get("reviewStatus").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("reviewStatusBlank", mapError.get("reviewStatusBlank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					if (!reviewStatusDicMap.keySet().contains(cellValue)) {
						mapError.put("reviewStatusError", mapError.get("reviewStatusError") + (rowNum + 1) + ",");
					} else {
						testCase.setReviewStatus(reviewStatusDicMap.get(cellValue));
					}
				} else{
					testCase.setReviewStatus(cellValue);
				}
			}
		}else if(allTestCaseFieldMap.get("comment").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("commentBlank", mapError.get("commentBlank") +(rowNum+1)+"," );
			}else {
				testCase.setComment(cellValue);
			}
		}else if(allTestCaseFieldMap.get("maintainer").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("maintainerBlank", mapError.get("maintainerBlank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					if(!users.keySet().contains(cellValue)) {
						mapError.put("maintainerError", mapError.get("maintainerError") +(rowNum+1)+"," );
					}else {
						testCase.setMaintainer(users.get(cellValue));
					}
				}
			}
		}else if(allTestCaseFieldMap.get("maintenanceTime").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("maintenanceTimeBlank", mapError.get("maintenanceTimeBlank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					try {
						try {
							Calendar calendar = new GregorianCalendar(1900,0,-1);
							Date d = calendar.getTime();
							Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
							testCase.setMaintenanceTime(dd);
						} catch (Exception e) {
							e.printStackTrace();
							mapError.put("maintenanceTimeError", mapError.get("maintenanceTimeError") + (rowNum + 1) + ",");
						}
					} catch (Exception e) {
						e.printStackTrace();
						mapError.put("maintenanceTimeError", mapError.get("maintenanceTimeError") + (rowNum + 1) + ",");
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments1").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments1Blank", mapError.get("comments1Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments1")!=null && !commentsDicMap.get("comments1").keySet().containsAll(com)) {
						mapError.put("comments1Error", mapError.get("comments1Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments1")!=null && commentsDicMap.get("comments1").keySet().containsAll(com)) {
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments1").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments1(value);
					}else if(dateFormdataCellMap.get("comments1")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments1(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments1TimeFormatError", mapError.get("comments1TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else{
						testCase.setComments1(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments2").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments2Blank", mapError.get("comments2Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments2")!=null && !commentsDicMap.get("comments2").keySet().containsAll(com)) {
						mapError.put("comments2Error", mapError.get("comments2Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments2")!=null && commentsDicMap.get("comments2").keySet().containsAll(com)) {
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments2").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments2(value);
					}else if(dateFormdataCellMap.get("comments2")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments2(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments2TimeFormatError", mapError.get("comments2TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else{
						testCase.setComments2(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments3").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments3Blank", mapError.get("comments3Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments3")!=null && !commentsDicMap.get("comments3").keySet().containsAll(com)) {
						mapError.put("comments3Error", mapError.get("comments3Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments3")!=null && commentsDicMap.get("comments3").keySet().containsAll(com)){
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments3").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments3(value);
					}else if(dateFormdataCellMap.get("comments3")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments3(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments3TimeFormatError", mapError.get("comments3TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else {
						testCase.setComments3(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments4").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments4Blank", mapError.get("comments4Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments4")!=null && !commentsDicMap.get("comments4").keySet().containsAll(com)) {
						mapError.put("comments4Error", mapError.get("comments4Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments4")!=null && commentsDicMap.get("comments4").keySet().containsAll(com)){
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments4").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments4(value);
					}else if(dateFormdataCellMap.get("comment4s")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments4(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments4TimeFormatError", mapError.get("comments4TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else {
						testCase.setComments4(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments5").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments5Blank", mapError.get("comments5Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments5")!=null && !commentsDicMap.get("comments5").keySet().containsAll(com)) {
						mapError.put("comments5Error", mapError.get("comments5Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments5")!=null && commentsDicMap.get("comments5").keySet().containsAll(com)){
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments5").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments5(value);
					}else if(dateFormdataCellMap.get("comments5")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments5(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments5TimeFormatError", mapError.get("comments5TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else {
						testCase.setComments5(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments6").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments6Blank", mapError.get("comments6Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments6")!=null && !commentsDicMap.get("comments6").keySet().containsAll(com)) {
						mapError.put("comments6Error", mapError.get("comments6Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments6")!=null && commentsDicMap.get("comments6").keySet().containsAll(com)){
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments6").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments6(value);
					}else if(dateFormdataCellMap.get("comments6")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments6(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments6TimeFormatError", mapError.get("comments6TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else {
						testCase.setComments6(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments7").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments7Blank", mapError.get("comments7Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments7")!=null && !commentsDicMap.get("comments7").keySet().containsAll(com)) {
						mapError.put("comments7Error", mapError.get("comments7Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments7")!=null && commentsDicMap.get("comments7").keySet().containsAll(com)){
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments7").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments7(value);
					}else if(dateFormdataCellMap.get("comments7")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments7(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments7TimeFormatError", mapError.get("comments7TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else {
						testCase.setComments7(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments8").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments8Blank", mapError.get("comments8Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments8")!=null && !commentsDicMap.get("comments8").keySet().containsAll(com)) {
						mapError.put("comments8Error", mapError.get("comments8Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments8")!=null && commentsDicMap.get("comments8").keySet().containsAll(com)){
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments8").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments8(value);
					}else if(dateFormdataCellMap.get("comments8")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments8(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments8TimeFormatError", mapError.get("comments8TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else {
						testCase.setComments8(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments9").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments9Blank", mapError.get("comments9Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments9")!=null && !commentsDicMap.get("comments9").keySet().containsAll(com)) {
						mapError.put("comments9Error", mapError.get("comments9Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments9")!=null && commentsDicMap.get("comments9").keySet().containsAll(com)) {
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments9").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments9(value);
					}else if(dateFormdataCellMap.get("comments9")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments9(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments9TimeFormatError", mapError.get("comments9TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else{
						testCase.setComments9(cellValue);
					}
				}
			}
		}else if(allTestCaseFieldMap.get("comments10").equals(caseFieldName)) {
			if("true".equals(writeRequiredCellMap.get(caseFieldName)) && StringUtils.isEmpty(cellValue)) {
				mapError.put("comments10Blank", mapError.get("comments10Blank") +(rowNum+1)+"," );
			}else {
				if(!StringUtils.isEmpty(cellValue)) {
					String[] str = cellValue.split(",");
					List<String> com = new ArrayList<>();
					String value = "";
					for(int i = 0; i < str.length; i++) {
						com.add(str[i]);
					}
					if(commentsDicMap.get("comments10")!=null && !commentsDicMap.get("comments10").keySet().containsAll(com)) {
						mapError.put("comments10Error", mapError.get("comments10Error") +(rowNum+1)+"," );
					}else if(commentsDicMap.get("comments10")!=null && commentsDicMap.get("comments10").keySet().containsAll(com)) {
						for(int i = 0; i < str.length; i++) {
							value = value + commentsDicMap.get("comments10").get(str[i]) + ",";
						}
						value = value.substring(0, value.length()-1);
						testCase.setComments10(value);
					}else if(dateFormdataCellMap.get("comments10")!=null) {
						if(!StringUtils.isEmpty(cellValue)) {
							try {
								Calendar calendar = new GregorianCalendar(1900,0,-1);
								Date d = calendar.getTime();
								Date dd = DateUtils.addDays(d,Integer.valueOf(cellValue));
								testCase.setComments10(String.valueOf(dd.getTime()));
							} catch (Exception e) {
								e.printStackTrace();
								mapError.put("comments10TimeFormatError", mapError.get("comments10TimeFormatError") + (rowNum + 1) + ",");
							}
						}
					}else{
						testCase.setComments10(cellValue);
					}
				}
			}
		}
		return testCase;

	}
	private Map<String,String> createErrorList(){
		Map<String,String> mapError = new HashMap<>();
		mapError.put("caseEditIdBlank", "");
		mapError.put("intentBlank", "");
		mapError.put("isNegativeBlank", "");
		mapError.put("isNegativeError", "");
		mapError.put("caseTypeBlank", "");
		mapError.put("caseTypeError", "");
		mapError.put("testModeError", "");
		mapError.put("testModeBlank", "");
		mapError.put("casetLevelBlank", "");
		mapError.put("casetLevelError", "");
		mapError.put("preconditionsBlank", "");
		mapError.put("checkPointBlank", "");
		mapError.put("testStepBlank", "");
		mapError.put("expectedResultBlank", "");
		mapError.put("timingNameBlank", "");
		mapError.put("reviewStatusBlank", "");
		mapError.put("reviewStatusError", "");
		mapError.put("commentBlank", "");
		mapError.put("maintainerBlank", "");
		mapError.put("maintainerError", "");
		mapError.put("maintenanceTimeBlank", "");
		mapError.put("maintenanceTimeError", "");
		mapError.put("comments1Blank", "");
		mapError.put("comments2Blank", "");
		mapError.put("comments3Blank", "");
		mapError.put("comments4Blank", "");
		mapError.put("comments5Blank", "");
		mapError.put("comments6Blank", "");
		mapError.put("comments7Blank", "");
		mapError.put("comments8Blank", "");
		mapError.put("comments9Blank", "");
		mapError.put("comments10Blank", "");
		mapError.put("sameCaseEditIdInSheet", "");
		mapError.put("sameCaseEditIdInTask", "");
		mapError.put("comments1Error", "");
		mapError.put("comments2Error", "");
		mapError.put("comments3Error", "");
		mapError.put("comments4Error", "");
		mapError.put("comments5Error", "");
		mapError.put("comments6Error", "");
		mapError.put("comments7Error", "");
		mapError.put("comments8Error", "");
		mapError.put("comments9Error", "");
		mapError.put("comments10Error", "");
		mapError.put("comments1TimeFormatError", "");
		mapError.put("comments2TimeFormatError", "");
		mapError.put("comments3TimeFormatError", "");
		mapError.put("comments4TimeFormatError", "");
		mapError.put("comments5TimeFormatError", "");
		mapError.put("comments6TimeFormatError", "");
		mapError.put("comments7TimeFormatError", "");
		mapError.put("comments8TimeFormatError", "");
		mapError.put("comments9TimeFormatError", "");
		mapError.put("comments10TimeFormatError", "");
		mapError.put("sameCaseEditIdInDataDesign", "");
		return mapError;

	}

	/**
	 * 处理错误信息
	 * @param mapError
	 * @return
	 */
	private String dealErrorLog(Map<String,String> mapError, Map<String,String> aliasNameCellMap) {
		String ErrorLog = "";
		if(!StringUtils.isEmpty(mapError.get("caseEditIdBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("caseEditIdBlank").substring(0, mapError.get("caseEditIdBlank").length()-1)+"】行【"+aliasNameCellMap.get("caseId")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("sameCaseEditIdInSheet"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("sameCaseEditIdInSheet").substring(0, mapError.get("sameCaseEditIdInSheet").length()-1)+"】行【"+aliasNameCellMap.get("caseId")+"】在导入文件内重复；";
		}
		if(!StringUtils.isEmpty(mapError.get("sameCaseEditIdInTask"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("sameCaseEditIdInTask").substring(0, mapError.get("sameCaseEditIdInTask").length()-1)+"】行【"+aliasNameCellMap.get("caseId")+"】在其他任务下已经存在；";
		}
		if(!StringUtils.isEmpty(mapError.get("sameCaseEditIdInDataDesign"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("sameCaseEditIdInDataDesign").substring(0, mapError.get("sameCaseEditIdInDataDesign").length()-1)+"】行【"+aliasNameCellMap.get("caseId")+"】在案例编写或回收站下存在；";
		}
		if(!StringUtils.isEmpty(mapError.get("intentBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("intentBlank").substring(0, mapError.get("intentBlank").length()-1)+"】行【"+aliasNameCellMap.get("intent")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("isNegativeBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("isNegativeBlank").substring(0, mapError.get("isNegativeBlank").length()-1)+"】行【"+aliasNameCellMap.get("isNegative")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("caseTypeBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("caseTypeBlank").substring(0, mapError.get("caseTypeBlank").length()-1)+"】行【"+aliasNameCellMap.get("caseType")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("casetLevelBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("casetLevelBlank").substring(0, mapError.get("casetLevelBlank").length()-1)+"】行【"+aliasNameCellMap.get("casetLevel")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("testModeBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("testModeBlank").substring(0, mapError.get("testModeBlank").length()-1)+"】行【"+aliasNameCellMap.get("testMode")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("preconditionsBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("preconditionsBlank").substring(0, mapError.get("preconditionsBlank").length()-1)+"】行【"+aliasNameCellMap.get("preconditions")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("checkPointBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("checkPointBlank").substring(0, mapError.get("checkPointBlank").length()-1)+"】行【"+aliasNameCellMap.get("checkPoint")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("testStepBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("testStepBlank").substring(0, mapError.get("testStepBlank").length()-1)+"】行【"+aliasNameCellMap.get("testStep")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("expectedResultBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("expectedResultBlank").substring(0, mapError.get("expectedResultBlank").length()-1)+"】行【"+aliasNameCellMap.get("expectedResult")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("timingNameBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("timingNameBlank").substring(0, mapError.get("timingNameBlank").length()-1)+"】行【"+aliasNameCellMap.get("timingName")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("reviewStatusBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("reviewStatusBlank").substring(0, mapError.get("reviewStatusBlank").length()-1)+"】行【"+aliasNameCellMap.get("reviewStatus")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("commentBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("commentBlank").substring(0, mapError.get("commentBlank").length()-1)+"】行【"+aliasNameCellMap.get("comment")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("maintainerBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("maintainerBlank").substring(0, mapError.get("maintainerBlank").length()-1)+"】行【"+aliasNameCellMap.get("maintainer")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("maintenanceTimeBlank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("maintenanceTimeBlank").substring(0, mapError.get("maintenanceTimeBlank").length()-1)+"】行【"+aliasNameCellMap.get("maintenanceTime")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments1Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments1Blank").substring(0, mapError.get("comments1Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments1")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments2Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments2Blank").substring(0, mapError.get("comments2Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments2")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments3Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments3Blank").substring(0, mapError.get("comments3Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments3")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments4Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments4Blank").substring(0, mapError.get("comments4Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments4")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments5Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments5Blank").substring(0, mapError.get("comments5Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments5")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments6Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments6Blank").substring(0, mapError.get("comments6Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments6")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments7Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments7Blank").substring(0, mapError.get("comments7Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments7")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments8Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments8Blank").substring(0, mapError.get("comments8Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments8")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments9Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments9Blank").substring(0, mapError.get("comments9Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments9")+"】为空；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments10Blank"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments10Blank").substring(0, mapError.get("comments10Blank").length()-1)+"】行【"+aliasNameCellMap.get("comments10")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("isNegativeError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("isNegativeError").substring(0, mapError.get("isNegativeError").length()-1)+"】行【"+aliasNameCellMap.get("isNegative")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("caseTypeError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("caseTypeError").substring(0, mapError.get("caseTypeError").length()-1)+"】行【"+aliasNameCellMap.get("caseType")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("casetLevelError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("casetLevelError").substring(0, mapError.get("casetLevelError").length()-1)+"】行【"+aliasNameCellMap.get("casetLevel")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("reviewStatusError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("reviewStatusError").substring(0, mapError.get("reviewStatusError").length()-1)+"】行【"+aliasNameCellMap.get("reviewStatus")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("maintainerError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("maintainerError").substring(0, mapError.get("maintainerError").length()-1)+"】行【"+aliasNameCellMap.get("maintainer")+"】用户名称不存在；";
		}
		if(!StringUtils.isEmpty(mapError.get("maintenanceTimeError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("maintenanceTimeError").substring(0, mapError.get("maintenanceTimeError").length()-1)+"】行【"+aliasNameCellMap.get("maintenanceTime")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments1Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments1Error").substring(0, mapError.get("comments1Error").length()-1)+"】行【"+aliasNameCellMap.get("comments1")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments2Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments2Error").substring(0, mapError.get("comments2Error").length()-1)+"】行【"+aliasNameCellMap.get("comments2")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments3Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments3Error").substring(0, mapError.get("comments3Error").length()-1)+"】行【"+aliasNameCellMap.get("comments3")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments4Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments4Error").substring(0, mapError.get("comments4Error").length()-1)+"】行【"+aliasNameCellMap.get("comments4")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments5Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments5Error").substring(0, mapError.get("comments5Error").length()-1)+"】行【"+aliasNameCellMap.get("comments5")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments6Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments6Error").substring(0, mapError.get("comments6Error").length()-1)+"】行【"+aliasNameCellMap.get("comments6")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments7Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments7Error").substring(0, mapError.get("comments7Error").length()-1)+"】行【"+aliasNameCellMap.get("comments7")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments8Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments8Error").substring(0, mapError.get("comments8Error").length()-1)+"】行【"+aliasNameCellMap.get("comments8")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments9Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments9Error").substring(0, mapError.get("comments9Error").length()-1)+"】行【"+aliasNameCellMap.get("comments9")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments10Error"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments10Error").substring(0, mapError.get("comments10Error").length()-1)+"】行【"+aliasNameCellMap.get("comments10")+"】填写错误，必须为数据字典内的值；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments1TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments1TimeFormatError").substring(0, mapError.get("comments1TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments1")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments2TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments2TimeFormatError").substring(0, mapError.get("comments2TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments2")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments3TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments3TimeFormatError").substring(0, mapError.get("comments3TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments3")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments4TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments4TimeFormatError").substring(0, mapError.get("comments4TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments4")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments5TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments5TimeFormatError").substring(0, mapError.get("comments5TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments5")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments6TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments6TimeFormatError").substring(0, mapError.get("comments6TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments6")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments7TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments7TimeFormatError").substring(0, mapError.get("comments7TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments7")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments8TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments8TimeFormatError").substring(0, mapError.get("comments8TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments8")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments9TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments9TimeFormatError").substring(0, mapError.get("comments9TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments9")+"】时间格式填写错误；";
		}
		if(!StringUtils.isEmpty(mapError.get("comments10TimeFormatError"))) {
			ErrorLog = ErrorLog + "第【"+mapError.get("comments10TimeFormatError").substring(0, mapError.get("comments10TimeFormatError").length()-1)+"】行【"+aliasNameCellMap.get("comments10")+"】时间格式填写错误；";
		}
		return ErrorLog;
	}

	/**
	 * @Title: reviewStatusUsingEnable
	 * @Description: 查询案例配置字段-评审状态是否启用
	 * @author: dingwenlong
	 * @date: 2021年10月8日
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private boolean reviewStatusUsingEnable() {
		Map<String, Object> fieldConfigMap;
    	Result<Map<String, Object>> fieldConfigResult = feignAssetsToBasic.initTestCaseConfigData();
    	if (!fieldConfigResult.isSuccess()) {
    		logger.error("查询案例字段配置信息异常,异常信息: {}", fieldConfigResult.getMsg());
			throw new RuntimeException(fieldConfigResult.getMsg());
		} else {
			List<Map<String, Object>> fieldConfigMapList = (List<Map<String, Object>>) fieldConfigResult.getObj();
			if (fieldConfigMapList.isEmpty()) {
				logger.error("案例字段配置为空！");
				throw new RuntimeException("案例字段配置为空！");
			}
			fieldConfigMap = fieldConfigMapList.stream().collect(Collectors.toMap(x -> x.get("name").toString(), y -> y.get("usingenable").toString()));
		}
    	return Boolean.parseBoolean(fieldConfigMap.get(TestCaseConstant.REVIEW_STATUS_FIELD).toString());
	}
}
