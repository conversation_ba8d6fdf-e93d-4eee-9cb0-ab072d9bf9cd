package com.jettech.service.impl;

import com.jettech.common.util.FileValidUtil;
import com.jettech.common.util.UserVo;
import com.jettech.dao.idao.IDataBackupHistoryDao;
import com.jettech.model.DataBackupHistory;
import com.jettech.service.iservice.IDataBackupHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DataBackupHistoryServiceImpl extends BaseServiceImpl<DataBackupHistory> implements IDataBackupHistoryService {

    @Autowired
    private IDataBackupHistoryDao dataBackupHistoryDao;

    @PostConstruct
    private void postConstruct() {
        baseDao = this.dataBackupHistoryDao;
    }

    @Override
    public List<DataBackupHistory> findUnBackup() {
        return dataBackupHistoryDao.findUnBackup();
    }

    @Override
    public List<DataBackupHistory> findByParams(Map<String, Object> params) {
        return dataBackupHistoryDao.findByParams(params);
    }

    @Override
    public int del(String ids, UserVo user) {
        String[] idsArr = ids.split(",");
        if (idsArr != null && idsArr.length > 0) {
            List<Integer> idsList = Arrays.asList(idsArr).stream().map(item -> Integer.parseInt(item)).collect(Collectors.toList());
            List<DataBackupHistory> list = this.findByIdIn(idsList);
            if (!list.isEmpty()) {
                this.deleteInBatch(list, user.getUserNumber());
                List<String> fileList = list.stream().map(item -> item.getFilePath() + File.separator + item.getFileName()).collect(Collectors.toList());
                for (String file : fileList) {
                    if (!FileValidUtil.valid(file)) {
                        continue;
                    }
                    File f = new File(file);
                    if (f.exists()) {
                        f.delete();
                    }
                }
                return list.size();
            }
        }
        return 0;
    }
}
