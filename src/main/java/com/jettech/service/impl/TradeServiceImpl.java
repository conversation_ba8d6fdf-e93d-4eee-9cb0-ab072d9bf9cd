package com.jettech.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Iterators;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.datadesign.DemandToTradeDTO;
import com.jettech.common.dto.datadesign.SystemModuleTradeDTO;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.CheckUtil;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.dao.idao.ITradeDao;
import com.jettech.feign.IFeignDataDesignToAssets;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToBugService;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import com.jettech.util.DataDicUtil;
import com.jettech.util.LongUtil;
import com.jettech.util.ReadExcel;
import com.jettech.util.resFileUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName TradeServiceImpl
 * @description TradeServiceImpl
 * @create 2019-11-04 19:24
 */
@Service
@Transactional
public class TradeServiceImpl extends BaseServiceImpl<Trade> implements ITradeService {

    // 日志对象
    private final static Logger logger = LoggerFactory.getLogger(TradeServiceImpl.class);

    @Autowired
    private ITradeDao tradeDao;
    @Autowired
    private ITestCaseService testCaseService;

    @Autowired
    private ITestSystemService testSystemService;

    @Autowired
    private ISystemModuleService systemModuleService;

    @Autowired
    private IFeignDataDesignToBugService feignDataDesignToBugService;
    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasic;

    @Autowired
    private IFeignDataDesignToAssets feignDataDesignToAssets;

    @Autowired
    private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;

    @Autowired
    private ITradeTestManagerService iTradeTestManagerService;

    @Autowired
    private DataDicUtil dataDicUtil;

    private final static String TEST_SYSTEM = "testSystem";
    private final static String SYSTEM_MODULE = "systemModule";
    private final static String TRADE = "trade";

    @PostConstruct
    private void postConstruct() {
        this.baseDao = tradeDao;
    }

    /**
     * @param strings
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description 通过被测系统resourceID查询交易数据
     * <AUTHOR>
     * @date 2019-11-06 14:11
     */
    @Override
    public List<Map<String, Object>> findByTestSystemResourceIDs(List<String> strings) {
        return tradeDao.findByTestSystemResourceIDs(strings);
    }

    @Override
    /**
     * @Title saveOrUpdateTrade
     * @Description 新增或修改交易
     * @Params [params，userNumber]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    public Result saveOrUpdateTrade(Map<String, String> params, String userNumber) {
        try {
            String name = params.get("name");
            String resourceID = params.get("resourceID");
            String moduleResourceID = params.get("moduleResourceID");
            String testSystemResourceID = params.get("testSystemResourceID");
            String belongManager = params.get("belongTestManager");//测试经理
            String number = params.get("number");

            Trade trade = this.findByResourceID(LongUtil.parseLong(params.get("resourceID")));

            if (name == null || "".equals(name)) {
                return new Result(false, 20001, "名称为必输项不能为空");
            }
            List<Trade> repeated = verifyTradeNameNotRepeatedBySystem(name, resourceID, testSystemResourceID);

            if (!repeated.isEmpty()) {
                return new Result(false, 20001, "交易名称已存在");
            }
            if (StringUtils.isNoneEmpty(number)) {
                repeated = verifyTradeNumberNotRepeatedBySystem(number, resourceID, testSystemResourceID);
                if (!repeated.isEmpty()) {
                    return new Result(false, 20001, "交易编号已存在");
                }
            }
//            if (belongManager == null || "".equals(belongManager)) {
//                return new Result(false, 20001, "归属测试经理不能为空");
//            }

            if (null == trade) {
                trade = new Trade();
            } else {
                // 修改交易的时候，同步更新defect表中的数据名称
                if (!trade.getName().equals(name)) {
                    String token = HttpRequestUtils.getCurrentRequestToken();
                    // 当名称不一致，调用同步方法
                    Result r = feignDataDesignToBugService.updateDefectSubordinateName("feature", trade.getResourceID(), name, trade.getName(),token);
                    if (!r.isSuccess()) {
                        return Result.renderError("同步更新defect表中的数据名称失败");
                    }
                    // 同步更改案例编写库的交易名称
                    Result testCaseResult = testCaseService.updateTestCaseTradeNameByTradeId(name,trade.getResourceID());
                    if (!testCaseResult.isSuccess()) {
                        return Result.renderError("同步更新ds_testcase表中的数据名称失败");
                    }
                    // 同步更改案例执行中的交易名称
                    Result performcaseResult = feignDataDesignToManexecuteService.updatePerformcaseTradeNameByTradeId(name,trade.getResourceID(),token);
                    if (!performcaseResult.isSuccess()) {
                        return Result.renderError("同步更新performcase表中的数据名称失败");
                    }
                }
            }
            trade.setName(params.get("name"));
            trade.setComment(params.get("comment"));
            trade.setNumber(params.get("number"));
            trade.setStatus(params.get("status"));
            //添加交易类型
            if (org.springframework.util.StringUtils.isEmpty(params.get("type"))) {
                return Result.renderError("交易类型不能为空！");
            }
            trade.setType(params.get("type"));
            trade.setExecuteMode(params.get("executeMode"));
            trade.setModuleResourceID(LongUtil.parseLong(params.get("moduleResourceID")));
            trade.setTestSystemResourceID(LongUtil.parseLong(params.get("testSystemResourceID")));
            if (null == trade.getResourceID()) {
                this.save(trade, userNumber);
            } else {
                this.update(trade, userNumber);
            }

            //根据交易的归属经理
            List<TradeAndTestManager>  managerList=iTradeTestManagerService.findByTradeResourceID(trade.getResourceID());
            if(!managerList.isEmpty()) {
                iTradeTestManagerService.deleteInBatch(managerList, userNumber);
            }
          //处理测试经理数据
            if (StringUtils.isNotEmpty(belongManager)){
                String[] split = belongManager.split(",");
                List<TradeAndTestManager> newList=new ArrayList<TradeAndTestManager>();
                for(String s :split) {
                    TradeAndTestManager tt=new TradeAndTestManager();
                    tt.setBelongTestManager(s);
                    tt.setTradeResourceID(trade.getResourceID());
                    newList.add(tt);
                }

                if(!newList.isEmpty()) {
                    iTradeTestManagerService.save(newList, userNumber);
                }
            }

           return Result.renderSuccess(trade);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("保存交易数据异常！");
        }
    }

    /**
     * @Title verifyTradeNameNotRepeatedByModule /
     * verifyTradeNameNotRepeatedBySystem
     * @Description 用于校验父节点（被测系统或者模块）下交易名称唯一
     * @Params [name, nodeResourceID, parentResourceID]
     * @Return java.util.List<SystemModule>
     * <AUTHOR>
     * @Date 2019/11/5
     */
    @Override
    public List<Trade> verifyTradeNameNotRepeatedByModule(String name, String nodeResourceID, String moduleResourceID) {
        return tradeDao.verifyTradeNameNotRepeatedByModule(name, nodeResourceID, moduleResourceID);
    }

    @Override
    public List<Trade> verifyTradeNameNotRepeatedBySystem(String name, String nodeResourceID,
                                                          String testSystemResourceID) {
        return tradeDao.verifyTradeNameNotRepeatedBySystem(name, nodeResourceID, testSystemResourceID);
    }

    @Override
    public List<Trade> verifyTradeNumberNotRepeatedBySystem(String number, String tradeResourceID, String testSystemResourceID) {
        return tradeDao.verifyTradeNumberNotRepeatedBySystem(number, tradeResourceID, testSystemResourceID);
    }

    /**
     * @param "[testSystemResourceID]"
     * @return java.util.List<Trade>
     * @throws <AUTHOR>
     * @Title: findbyTestSystemResourceID
     * @description: 被测系统resourceID查询交易
     * @date 2019/11/6 10:18
     */
    @Override
    public List<Trade> findbyTestSystemResourceID(String testSystemResourceID, String name, String versionNumber, String executeMode) {
        return tradeDao.findbyTestSystemResourceID(testSystemResourceID, name, versionNumber, executeMode);
    }

    /**
     * @param "[resourceID_list]"
     * @return java.util.List<Trade>
     * @throws <AUTHOR>
     * @Title: getObjectByTestSystemResourceIDs
     * @description: 被测系统查询交易(返回值对象)
     * @date 2019/11/7 10:29
     */
    @Override
    public List<Trade> getObjectByTestSystemResourceIDs(List<String> resourceIDs) {
        return tradeDao.getObjectByTestSystemResourceIDs(resourceIDs);
    }

    @Override
    /**
     * @Title findBySystemModule
     * @Description 查询模块下的交易
     * @Params
     * @Return
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public List<Trade> findBySystemModule(List<String> collect) {
        return tradeDao.findBySystemModule(collect);
    }

    @Override
    /**
     * @Title deleteSystemModule
     * @Description 删除交易
     * @Params [request, resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result deleteTradeByResourceID(String tradeResourceID, String userNumber) {
        List<Trade> tradeList = new ArrayList<>();
        return this.checkDeleteTrade(tradeResourceID, tradeList);
    }

    /**
     * <AUTHOR>
     * @description 确认删除交易
     * @date 2020年11月25日 15:38
     * @param [resourceID, userNumber]
     * @return com.jettech.dto.Result
     **/
    @Override
    @Transactional
    public Result confirmDeleteTrade(String tradeResourceID, String userNumber) {
        List<Trade> tradeList = new ArrayList<>();
        Result result = this.checkDeleteTrade(tradeResourceID, tradeList);
        if (!result.isSuccess()) {
            return result;
        }
        List<Long> ridList = tradeList.stream().map(x->x.getResourceID()).collect(Collectors.toList());
        if(ridList!=null&&ridList.size()>0) {
        	List<TradeAndTestManager> maList=iTradeTestManagerService.findByTradeResourceIDIn(ridList);
            if(maList!=null&& !maList.isEmpty()) {
            	iTradeTestManagerService.deleteInBatch(maList, userNumber);
            }
        }

        this.deleteInBatch(tradeList, userNumber);
        return Result.renderSuccess("删除成功");
    }

    /**
     * <AUTHOR>
     * @description 校验是否可以删除交易
     * @date 2020年11月25日 15:40
     * @param [tradeResourceID, tradeList]
     * @return com.jettech.dto.Result
     **/
    private Result checkDeleteTrade(String tradeResourceID, List<Trade> tradeList) {
        String[] tradeIds = tradeResourceID.split(",");
        for (String tradeId : tradeIds) {
            Trade byResourceID = this.findByResourceID(LongUtil.parseLong(tradeId));
            if (byResourceID == null) {
                return Result.renderError("请先选择交易！");
            }
            List<TestCase> testCases = testCaseService.findbyTradeResourceID(LongUtil.parseLong(tradeId));

            if (!testCases.isEmpty() && null != testCases) {
                return Result.renderError("当前交易下已经维护案例不允许删除");
            }
            tradeList.add(byResourceID);
        }
        return Result.renderSuccess();
    }

    @Override
    /**
     * @Title saveorUpdateTestSystem
     * @Description 校验 删除时交易下是否维护数据
     * @Params [resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result findResourceByTrade(String resourceID) {
        List<TestCase> testCases = testCaseService.findbyTradeResourceID(LongUtil.parseLong(resourceID));
        if (testCases.isEmpty() || testCases == null) {
            return Result.renderSuccess("当前交易下没有维护案例数据，请确认删除");
        }
        return Result.renderSuccess("当前交易下已维护案例数据，请确认删除");
    }

    /**
     * @Title: whetherToRepeat
     * @Description: 判断交易名称是否重复
     * @Param: "[request, testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/21
     */
    @Override
    public Result whetherToRepeat(Map<String, String> paramsMap) {
        String name = paramsMap.get("name");
        String resourceID = paramsMap.get("resourceID");
        String moduleResourceID = paramsMap.get("moduleResourceID");
        String testSystemResourceID = paramsMap.get("testSystemResourceID");
        String number = paramsMap.get("number");
        List<Trade> repeated = verifyTradeNameNotRepeatedBySystem(name, resourceID, testSystemResourceID);

        if (!repeated.isEmpty()) {
            return new Result(false, 20001, "交易名称已存在");
        }

        if (StringUtils.isNoneEmpty(number)) {
            repeated = verifyTradeNumberNotRepeatedBySystem(name, resourceID, testSystemResourceID);
            if (!repeated.isEmpty()) {
                return new Result(false, 20001, "交易编号已存在");
            }
        }
        return Result.renderSuccess();
    }

    /**
     * 根据交易rid反查树 QiaoHongju
     *
     * @param tradeResourceIDList 交易rid
     * @return 被测系统-系统模块-交易树
     */
    @Override
    public List<Map<String, Object>> getTestProjectTree(List<String> tradeResourceIDList) {
        List<Trade> tradeList = tradeDao.findByResourceIDIn(tradeResourceIDList);
        List<Map<String, Object>> tree01 = tradeWithSystemTree(tradeList);
        List<Map<String, Object>> tree02 = tradeWithSystemModuleAndTestSystemTree(tradeList);
        List<Map<String, Object>> temp = new ArrayList<>();
        // 合并测试系统
        tree01.forEach(e0 -> tree02.forEach(e1 -> {
            if (e0.get("resourceID").equals(e1.get("resourceID"))) {
                ((List) e0.get("children")).addAll((List) e1.get("children"));
            } else {
                temp.add(e1);
            }
        }));
        tree01.addAll(temp);
        return tree01;
    }

    /**
     * @Title: findBySystemModuleResourceID
     * @Description: 根据当前模块查询交易
     * @Param: "[resourceID]"
     * @Return: "java.util.List<Trade>"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    @Override
    public List<Trade> findBySystemModuleResourceID(String resourceID, String name, String versionNumber, String executeMode) {
        return tradeDao.findBySystemModuleResourceID(resourceID, name, versionNumber, executeMode);
    }

    /**
     * 根据模块查询交易
     * @param resourceIDs
     * @param name
     * @param versionNumber
     * @param executeMode
     * @return
     */
    @Override
    public List<Trade> findBySystemModuleResourceIDsIn(List<String> resourceIDs, String name, String versionNumber, String executeMode) {
        return tradeDao.findBySystemModuleResourceIDsIn(resourceIDs, name, versionNumber, executeMode);
    }

    /**
     * 系统下直接挂载交易的树 QiaoHongju
     *
     * @param tradeList 挂载在被测系统下的交易RID
     * @return 树
     */
    private List<Map<String, Object>> tradeWithSystemTree(List<Trade> tradeList) {
        List<Map<String, Object>> res = new ArrayList<>();
        Map<Long, List<Trade>> systemGroup = tradeList.stream().filter(e -> e.getModuleResourceID() == null) // 直接挂载在测试系统下
                .collect(Collectors.groupingBy(Trade::getTestSystemResourceID)); // 通过parentID分组
        systemGroup.forEach((k, v) -> {
            List<Map<String, Object>> tradeLs = new ArrayList<>();
            v.forEach(x -> tradeLs
                    .add(mappingTree(x.getName(), TRADE, x.getResourceID(), x.getTestSystemResourceID(), null)));
            TestSystem ts = testSystemService.findByResourceID(k);
            res.add(mappingTree(ts.getName(), TEST_SYSTEM, ts.getResourceID(), null, tradeLs));
        });
        return res;
    }

    /**
     * 系统下挂载系统模块的树 QiaoHongju
     *
     * @param tradeList 交易列表
     * @return 树SinglePointLeftTree
     */
    private List<Map<String, Object>> tradeWithSystemModuleAndTestSystemTree(List<Trade> tradeList) {
        List<Map<String, Object>> res = new ArrayList<>();
        List<Map<String, Object>> firstModule = new ArrayList<>();
        Map<Long, List<Trade>> moduleGroup = tradeList.stream().filter(e -> e.getModuleResourceID() != null) // 挂载在系统模块下
                .collect(Collectors.groupingBy(Trade::getModuleResourceID)); // 通过父ID分组
        // 查询第一层systemModule, 并为其设置children
        moduleGroup.forEach((k, v) -> {
            List<Map<String, Object>> tradeLs = new ArrayList<>();
            v.forEach(x -> tradeLs
                    .add(mappingTree(x.getName(), TRADE, x.getResourceID(), x.getModuleResourceID(), null)));
            SystemModule sm = systemModuleService.findByResourceID(k);
            firstModule.add(
                    mappingTree(sm.getName(), SYSTEM_MODULE, sm.getResourceID(), sm.getParentResourceID(), tradeLs));
        });
        iterator(firstModule, res);
        return res;
    }

    /**
     * 子项挂载迭代器 QiaoHongju
     *
     * @param module 步进项
     * @param res    迭代结果
     */
    private void iterator(List<Map<String, Object>> module, List<Map<String, Object>> res) {
        if (module.isEmpty())
            return;
        List<Map<String, Object>> tempModule = new ArrayList<>();
        Map<Object, List<Map<String, Object>>> fm = module.stream()
                .collect(Collectors.groupingBy(e -> e.get("parentID")));
        fm.forEach((k, v) -> {
            SystemModule sm = systemModuleService.findByResourceID((Long) k);
            if (sm != null) {
                tempModule
                        .add(mappingTree(sm.getName(), SYSTEM_MODULE, sm.getResourceID(), sm.getParentResourceID(), v));
            } else {
                TestSystem ts = testSystemService.findByResourceID((Long) k);
                res.add(mappingTree(ts.getName(), SYSTEM_MODULE, ts.getResourceID(), null, v));
            }
        });
        iterator(tempModule, res);
    }

    /**
     * 映射树结构 QiaoHongju
     *
     * @param name       节点名称
     * @param type       节点类型
     * @param resourceID 节点RID
     * @param parentID   节点父ID
     * @param children   节点子项
     * @return 节点
     */
    private Map<String, Object> mappingTree(String name, String type, Long resourceID, Long parentID, List children) {
        Map<String, Object> mp = new HashMap<>();
        mp.put("name", name);
        mp.put("type", type);
        mp.put("resourceID", resourceID);
        mp.put("parentID", parentID);
        mp.put("children", children);
        return mp;
    }

    /**
     * @param "[file, moduleResourceID, userNumber]"
     * @return com.jettech.dto.Result
     * @throws <AUTHOR>
     * @Title: importTradeExcel
     * @description: 导入交易
     * @date 2019/12/7 17:25
     */
    @Override
    public Result importTradeExcel(MultipartFile file, String moduleResourceID, String testSystemResourceID,
                               String userNumber) throws IOException {
        InputStream is=null;
        try {
            String fileName = file.getOriginalFilename();// 获取文件名
            if (fileName == null || !(ReadExcel.isExcel2003(fileName) || ReadExcel.isExcel2007(fileName))) {
                return Result.renderError("文件名不是excel格式");
            }
            Workbook wb = null;
            if (ReadExcel.isExcel2003(fileName)) {
                is=file.getInputStream();
                wb = new HSSFWorkbook(is);
            } else {
                is=file.getInputStream();
                wb = new XSSFWorkbook(is);
            }

            Result resultInfo = getTradeExcelInfo(wb);
            int code = resultInfo.getCode();
            if (code != 20000) {
                return resultInfo;
            }
            List<Map<String, Object>> excelInfo = (List<Map<String, Object>>) resultInfo.getObj();
            if (excelInfo == null || excelInfo.isEmpty())
                return Result.renderError("模板中无数据！");
            List<String> trade_numbers = excelInfo.stream().map(e -> String.valueOf(e.get("number")))
                    .filter(e -> StringUtils.isNotBlank(e) && !"null".equals(e)).collect(Collectors.toList());

            List<String> trade_names = excelInfo.stream().map(e -> String.valueOf(e.get("name")))
                    .filter(e -> StringUtils.isNotBlank(e) && !"null".equals(e)).collect(Collectors.toList());

            List<Trade> trade_list = this.findByNumberAndTestSystemResourceID(trade_numbers, trade_names, testSystemResourceID);


            ArrayList<Trade> addTrade = new ArrayList<>();
            ArrayList<Trade> updateTrade = new ArrayList<>();

            for (Map<String, Object> map : excelInfo) {
//            String number = String.valueOf(map.get("number"));
//            Optional<Trade> any = trade_list.stream()
//                    .filter(e -> e.getNumber().equals(number))
//                    .findAny();

                String name = String.valueOf(map.get("name"));
                Optional<Trade> nameAny = trade_list.stream().filter(e -> e.getName().equals(name)).findAny();
                Trade t;
                if (nameAny.isPresent()) {
                    // 修改
                    t = nameAny.get();
                    FillTrade(moduleResourceID, testSystemResourceID, map, t);
                    updateTrade.add(t);
                } else {
                    // 添加
                    t = new Trade();
                    FillTrade(moduleResourceID, testSystemResourceID, map, t);
                    addTrade.add(t);
                }
            }
            // 批量录入,修改
            this.save(addTrade, userNumber);
            this.update(updateTrade, userNumber);
            return Result.renderSuccess("导入成功！");
        }catch (Exception e){
             return null;
        }finally {
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){

            }
        }

    }

    private void FillTrade(String moduleResourceID, String testSystemResourceID, Map<String, Object> map, Trade t) {
        t.setNumber(map.get("number").toString());
        t.setName(map.get("name").toString());
        t.setTestSystemResourceID(Long.valueOf(testSystemResourceID));
        if (!StringUtils.isBlank(moduleResourceID)) {
            t.setModuleResourceID(Long.valueOf(moduleResourceID));
        }
        t.setStatus(map.get("status").toString());
    }

    /**
     * @param "[trade_numbers, moduleResourceID]"
     * @param trade_names
     * @return java.util.List<TestCase>
     * @throws <AUTHOR>
     * @Title: findByNumberAndModuleResourceID
     * @description: 交易编码和所属模块查询
     * @date 2019/12/9 10:42
     */
    private List<Trade> findByNumberAndModuleResourceID(List<String> trade_numbers, List<String> trade_names,
                                                        String moduleResourceID) {
        return tradeDao.findByNumberAndModuleResourceID(trade_numbers, trade_names, moduleResourceID);
    }

    /**
     * @param "[trade_numbers, testSystemResourceID]"
     * @param trade_names
     * @return java.util.List<TestCase>
     * @throws <AUTHOR>
     * @Title: findByNumberAndTestSystemResourceID
     * @description: 交易编码和被测系统rid查询
     * @date 2019/12/9 10:38
     */
    private List<Trade> findByNumberAndTestSystemResourceID(List<String> trade_numbers, List<String> trade_names,
                                                            String testSystemResourceID) {
        return tradeDao.findByNumberAndTestSystemResourceID(trade_numbers, trade_names, testSystemResourceID);
    }

    /**
     * @param "[wb]"
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @throws <AUTHOR>
     * @Title: getTradeExcelInfo
     * @description: 交易导入去读数据
     * @date 2019/12/9 10:07
     */
    private Result getTradeExcelInfo(Workbook wb) {
        dataDicUtil.getDicMap("trade");
        // 模板列名
        String[] cell_name = {"交易代码", "交易名称", "交易状态"};
        // 得到第一个shell
        Sheet sheet = wb.getSheetAt(0);
        // 得到Excel的行数
        int totalRows = sheet.getLastRowNum();
        int totalCells = 0;
        // 得到Excel的列数(前提是有行数)
        if (sheet.getRow(1) != null) {
            totalCells = sheet.getRow(1).getPhysicalNumberOfCells();
        }
        List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
        // 校验判断模板是否是指定模板
        Row title_row = sheet.getRow(1);
        for (int i = 0; i < 3; i++) {
            String cellValue = title_row.getCell(i).getStringCellValue();
            if (!ReadExcel.useList(cell_name, cellValue)) {
                return Result.renderError("非指定模板", 30000);
            }
        }

        // 判断交易名称是否重复，caseId出现重复，取消本次录入
        ArrayList<String> list = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        totalRows= CheckUtil.checkLoop(totalRows);
        for (int i = 2; i <= totalRows; i++) {
            Row row = sheet.getRow(i);
            if (row == null || isRowEmpty(row))
                continue;
            Cell cell = row.getCell(1);
            Cell status_cell = row.getCell(2);
            if (cell == null)
                return Result.renderError("交易名称不能为空！");
            if (status_cell == null)
                return Result.renderError("交易状态不能为空！");
            String tradeName = cell.getStringCellValue().trim();
            String status = status_cell.getStringCellValue();
            if (StringUtils.isBlank(tradeName)) {
                return Result.renderError("交易名称不能为空！");
            }
            if (StringUtils.isBlank(status)) {
                return Result.renderError("交易状态不能为空！");
            }

            list.add(tradeName);

        }
        // 寻找重复值isRowEmpty
        HashMap<String, String> maps = new HashMap<>();
        int m=CheckUtil.checkLoop( list.size());
        for (int i = 0; i <m; i++) {
            String val = maps.get(list.get(i));
            if (StringUtils.isNotBlank(val)) {
                // 有重复值
                sb.setLength(0);
                sb.append(val).append(",").append(i + 1);
                maps.put(list.get(i), sb.toString());
            } else {
                maps.put(list.get(i), String.valueOf(i + 1));
            }
        }
        if (!maps.isEmpty() && maps.size() > 0) {
            int index = 0;
            Set<String> set = maps.keySet();
            sb.setLength(0);
            Iterator<String> iterator = set.iterator();
            int m1=CheckUtil.checkLoop(Iterators.size(iterator));
            if(m1==CheckUtil.MAX_LOOPS){
                return null;
            }
            iterator = set.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String value = maps.get(key);
                String[] split = value.split(",");
                if (split.length < 2) {
                    ++index;
                    continue;
                }
                sb.append("交易名称").append("“").append(key).append("”").append("，第").append(value).append("行重复。")
                        .append("\r\n");
            }
            if (index < maps.size()) {
                return Result.renderError(sb.toString(), 50000);
            }
        }
        // 循环Excel行数
        totalRows=CheckUtil.checkLoop(totalRows);
        for (int r = 2; r <= totalRows; r++) {
            Row row = sheet.getRow(r);
            if (row == null || isRowEmpty(row)) {
                continue;
            }
            // 循环Excel的列
            Map<String, Object> map = new HashMap<String, Object>();
            totalCells=CheckUtil.checkLoop(totalCells);
            for (int c = 0; c < totalCells; c++) {
                Cell cell = row.getCell(c);
                if (null != cell) {
                    switch (c) {
                        case 0:
                            // 交易编号
                            map.put("number", getCellValue(cell));
                            break;
                        case 1:
                            // 交易名称
                            map.put("name", getCellValue(cell));
                            break;
                        case 2:
                            // 交易状态
                            map.put("status", dataDicUtil.getValue("status", getCellValue(cell)));
                            break;
                        case 3:
                            // 案例类型
                            map.put("comment", getCellValue(cell));
                            break;
                        default:
                            break;
                    }

                }
            }
            // 添加到list
            userList.add(map);
        }
        return Result.renderSuccess(userList);
    }

    public static boolean isRowEmpty(Row row) {
        int m=CheckUtil.checkLoop(row.getLastCellNum());
        for (int c = row.getFirstCellNum(); c < m; c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK)
                return false;
        }
        return true;
    }

    private String getCellValue(Cell cell) {
        return cell.getStringCellValue().trim();
    }

    /**
     * @param "[agent, response]"
     * @return void
     * @throws <AUTHOR>
     * @Title: downloadModel
     * @description: 下载模板
     * @date 2019/12/9 11:19
     */
    @Override
    public void downloadModel(String agent, HttpServletResponse response) {
        InputStream is = TradeServiceImpl.class.getResourceAsStream("/tradeModal.xlsx");
        try {
            int realLength = is.available();
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + resFileUtils.encodeDownloadFilename("tradeModal.xlsx", agent));
            // response.setContentType("multipart/form-data;charset=UTF-8");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            OutputStream os = response.getOutputStream();
            byte[] b = new byte[1024];
            int totalLength = 0;
            while (true) {
                int len = is.read(b);
                if (len <= 0) {
                    break;
                }
                totalLength += len;
                os.write(b, 0, len);
                os.flush();
            }
            logger.debug("模版文件长度：" + totalLength + " 文件的字节数：" + realLength);
            os.flush();
            os.close();
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){

            }
        }
    }

    /**
     * @param testSystemResourceID
     * @return java.util.List<java.util.HashMap < java.lang.String, java.lang.Object>>
     * @Description 查询直接挂在该被测系统下的Map
     * <AUTHOR>
     * @date 2019-12-12 20:51
     */
    @Override
    public List<HashMap<String, Object>> findNextLowerLevelMapByTestSystemResourceID(Long testSystemResourceID) {
        return tradeDao.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);
    }

    /**
     * @param "[resourceID, testSystemResourceID]"
     * @return java.util.List<Trade>
     * @throws <AUTHOR>
     * @Title: getByTsResourceIDAndModulResourceID
     * @description:
     * @date 2019/12/24 17:53
     */
    @Override
    public List<Trade> getByTsResourceIDAndModulResourceID(Long moduleResourceID, Long testSystemResourceID) {
        return tradeDao.getByTsResourceIDAndModulResourceID(moduleResourceID, testSystemResourceID);
    }

    /**
     * @param "[nodeType, nodeResourceID]"
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @throws <AUTHOR>
     * @Title: getTradeListAndObj
     * @description: 案例资产库服务接口，返回节点对象，交易list
     * @date 2019/12/26 12:09
     */
    @Override
    public Map<String, Object> getTradeListAndObj(String nodeType, String nodeResourceID) {

        HashMap<String, Object> map = new HashMap<>();
        SystemModule sm = null;
        TestSystem ts = null;
        List<Trade> trade_list = null;
        if ("module".equals(nodeType)) {
            sm = systemModuleService.findByResourceID(Long.valueOf(nodeResourceID));
            trade_list = this.getByTsResourceIDAndModulResourceID(sm.getResourceID(), sm.getTestSystemResourceID());
            map.put("obj", sm);
            map.put("list", trade_list);
        } else {
            ts = testSystemService.findByResourceID(Long.valueOf(nodeResourceID));
            //此处入参，不能写死为null，存在不为null的情况
            //trade_list = this.getByTsResourceIDAndModulResourceID(null, ts.getResourceID());
            ArrayList<String> testSystemRids = new ArrayList<>();
            testSystemRids.add(String.valueOf(ts.getResourceID()));
            //根据被测系统查询所有需要被导出的交易信息
            List<Map<String, Object>> tradeMapInfo = this.findByTestSystemResourceIDs(testSystemRids);
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(tradeMapInfo);
            trade_list = jsonArray.toJavaList(Trade.class);
            map.put("obj", ts);
            map.put("list", trade_list);
        }
        return map;
    }

    /**
     * @Title: findbyTradeName
     * @Description: 根据交易名称查询交易
     * @Param: "[tradeName]"
     * @Return: "java.util.List<Trade>"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    @Override
    public List<Trade> findbyTradeName(String tradeName) {
        return tradeDao.findbyTradeName(tradeName);
    }

    /**
     * @param ""
     * @return java.util.List<Trade>
     * @throws <AUTHOR>
     * @Title: findbyTestSystemResourceID
     * @description: 被测系统resourceID查询交易(补差模块带的交易)
     * @date 2019/11/6 10:13
     */
    @Override
    public List<Trade> findOnlyTradebyTestSystemResourceID(String testSystemResourceID, String name) {

        return tradeDao.findOnlyTradebyTestSystemResourceID(testSystemResourceID, name);
    }

    @Override
    public List<Trade> findOnlyTradebyTestSystemResourceID2(String testSystemResourceID) {
        return tradeDao.findOnlyTradebyTestSystemResourceID2(testSystemResourceID);
    }

    /**
     * 查询任务下选中的交易
     *
     * @param @return 参数
     * @return List<Trade> 返回类型
     * @throws <AUTHOR>
     * @Title: findSelectedTradeByTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    @Override
    public List<Trade> findSelectedTradeByTaskResourceID(String taskResourceID) {
        List<String> taskResourceIDList = new ArrayList<>();
        if (!StringUtils.isEmpty(taskResourceID)) {
            taskResourceIDList = Arrays.asList(taskResourceID.split(","));
        }
        return tradeDao.findSelectedTradeByTaskResourceID(taskResourceIDList);
    }

    /**
     * @param testSystemResource
     * @param name
     * @return
     * @Title: findByTestSystemResourceAndName
     * @Description:
     * <AUTHOR>
     * @date 2020年5月27日 上午9:52:37
     */
    @Override
    public Trade findByTestSystemResourceAndName(Long testSystemResource, String name) {
        return tradeDao.findByTestSystemResourceAndName(testSystemResource, name);
    }

    /**
     * @param moduleResourceID
     * @return
     * @Title: findByModuleResourceID
     * @Description: 查询模块下的交易
     * <AUTHOR>
     * @date 2020年5月27日 下午12:50:40
     */
    @Override
    public List<Trade> findByModuleResourceID(Long moduleResourceID) {
        return tradeDao.findByModuleResourceID(moduleResourceID);
    }

    /**
     * @param modeleResourceID
     * @param name
     * @param modeleResourceID
     * @return
     * @Title: findByNameAndModuleResourceID
     * @Description: 查询交易
     * <AUTHOR>
     * @date 2020年5月27日 下午2:53:29
     */
    @Override
    public Trade findByNameAndModuleResourceID(String name, Long modeleResourceID) {
        return tradeDao.findByNameAndModuleResourceID(name, modeleResourceID);
    }

    /**
     * 系统交易管理-根据系统导出系统交易excel
     *
     * @param @param  request
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: exportTestSystemTradeExcel
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public Result<?> exportTestSystemTradeExcel(Map<String, Object> mapData) {
        String testSystemRidStr = String.valueOf(mapData.get("testSystemRids"));
        HttpServletResponse response = (HttpServletResponse) mapData.get("response");

        //查看当前选择的系统下是否存在交易，如果存在交易才进行导出，如果不存在交易则提示（没有交易不做导出操作）
        ArrayList<String> testSystemRids = new ArrayList<>();
        String[] testSystemRidArr = testSystemRidStr.split(",");
        for (String rid : testSystemRidArr) {
            testSystemRids.add(rid);
        }
        //根据被测系统查询所有需要被导出的交易信息
        List<Map<String, Object>> tradeMapInfo = this.findByTestSystemResourceIDs(testSystemRids);
        if (!tradeMapInfo.isEmpty() && tradeMapInfo.size() > 0) {//所选系统下存在交易，则进行导出操作
            //查询交易状态
            List<Map<String, String>> tradeStatus = this.findTradeDicsByDicName("STATUS");
            Map<String, String> mapTradeStatus = new HashMap<>();
            if (!tradeStatus.isEmpty() && tradeStatus.size() > 0) {
                for (Map<String, String> map : tradeStatus) {
                    mapTradeStatus.put(map.get("value"), map.get("textName"));
                }
            } else {
                return Result.renderError("数据字典数据有误！");
            }

            //查询交易类型
            List<Map<String, String>> tradeType = this.findTradeDicsByDicName("TRADETYPE");
            Map<String, String> mapTradeType = new HashMap<>();
            if (!tradeType.isEmpty() && tradeType.size() > 0) {
                for (Map<String, String> map : tradeType) {
                    mapTradeType.put(map.get("value"), map.get("textName"));
                }
            } else {
                return Result.renderError("数据字典数据有误！");
            }
            //查询执行方式
            List<Map<String, String>> executeModes = this.findTradeDicsByDicName("executeMode");
            HashMap<String, String> mapExecuteModes = new HashMap<>();
            if (!executeModes.isEmpty() && executeModes.size() > 0) {
                for (Map<String, String> map : executeModes) {
                    mapExecuteModes.put(map.get("value"), map.get("textName"));
                }
            } else {
                return Result.renderError("数据字典数据有误！");
            }
            //创建excel

            // 创建工作簿对象
            Workbook wb = new XSSFWorkbook();
            // 创建工作表
            Sheet sheet = wb.createSheet("交易列表");
            Sheet sheet1 = wb.createSheet("说明");
            this.addSheetComments(sheet1, mapTradeStatus, mapTradeType, mapExecuteModes);
            // 产生表格标题行(第零行)
            Row rowm0 = sheet.createRow(0);
            //记录标题的最大行,初始为0
            int rowMaxIndex = 0;

            // 创建表格标题列
            Cell cellTitle0 = rowm0.createCell(0);
            cellTitle0.setCellValue("交易编码");
            Cell cellTitle1 = rowm0.createCell(1);
            cellTitle1.setCellValue("*交易名称");
            Cell cellTitle2 = rowm0.createCell(2);
            cellTitle2.setCellValue("*交易类型");
            Cell cellTitle3 = rowm0.createCell(3);
            cellTitle3.setCellValue("*交易状态");
            Cell cellTitle4 = rowm0.createCell(4);
            cellTitle4.setCellValue("交易描述");
            Cell cellTitle5 = rowm0.createCell(5);
            cellTitle5.setCellValue("执行方式");
            Cell cellTitle6 = rowm0.createCell(6);
            cellTitle6.setCellValue("*所属系统");
            //记录标题的最大列,初始为6
            int cellTitleMaxIndex = 6;

            //筛选出所有存在交易的被测系统
            Set<Long> testSystemSet = new HashSet<>();
            //被测系统去重处理
            tradeMapInfo.stream().forEach(x -> testSystemSet.add(Long.valueOf(x.get("testSystemResourceID").toString())));
            //查询所有的被测系统
            List<TestSystem> allTestSystem = testSystemService.findByResourceIDIn(testSystemRids);
            Map<Long, TestSystem> systemMap = new HashMap<>();
            allTestSystem.stream().forEach(x -> {
                systemMap.put(x.getResourceID(), x);
            });
            //查询所有模块
            Map<Long, SystemModule> moduleMap = new HashMap<>();
            List<SystemModule> allModules = systemModuleService.findAll();
            allModules.stream().forEach(x -> {
                moduleMap.put(x.getResourceID(), x);
            });

            //处理每一个被测系统
            for (Long rid : testSystemSet) {
                //被测系统直属的交易
                List<Trade> systemDirectTrades = this.findOnlyTradebyTestSystemResourceID(String.valueOf(rid), "");
                for (Trade model : systemDirectTrades) {
                    Row row = sheet.createRow(rowMaxIndex + 1);
                    rowMaxIndex++;
                    Cell cell0 = row.createCell(0);
                    cell0.setCellValue(model.getNumber());
                    Cell cell1 = row.createCell(1);
                    cell1.setCellValue(model.getName());
                    Cell cell2 = row.createCell(2);
                    cell2.setCellValue(mapTradeType.get(model.getType()));
                    Cell cell3 = row.createCell(3);
                    cell3.setCellValue(mapTradeStatus.get(model.getStatus()));
                    Cell cell4 = row.createCell(4);
                    cell4.setCellValue(model.getComment());
                    Cell cell5 = row.createCell(5);
                    if (!org.springframework.util.StringUtils.isEmpty(model.getExecuteMode())) {
                        cell5.setCellValue(mapExecuteModes.get(model.getExecuteMode()) == null ? "" : mapExecuteModes.get(model.getExecuteMode()));
                    }
                    Cell cell6 = row.createCell(6);
                    cell6.setCellValue(systemMap.get(model.getTestSystemResourceID()).getName());
                }

                //被测系统下是模块
                //查询被测系统下的挂在模块下的交易(按照模块 的rid排序，保证相同模块下的交易一并处理)
                List<Trade> tradeModuleIsNull = this.findTradeByTestSystemRidAndModuleIsNotNull(String.valueOf(rid));
                for (Trade model : tradeModuleIsNull) {
                    Row row = sheet.createRow(rowMaxIndex + 1);
                    rowMaxIndex++;
                    Cell cell0 = row.createCell(0);
                    cell0.setCellValue(model.getNumber());
                    Cell cell1 = row.createCell(1);
                    cell1.setCellValue(model.getName());
                    Cell cell2 = row.createCell(2);
                    cell2.setCellValue(mapTradeType.get(model.getType()));
                    Cell cell3 = row.createCell(3);
                    cell3.setCellValue(mapTradeStatus.get(model.getStatus()));
                    Cell cell4 = row.createCell(4);
                    cell4.setCellValue(model.getComment());
                    Cell cell5 = row.createCell(5);
                    if (!org.springframework.util.StringUtils.isEmpty(model.getExecuteMode())) {
                        cell5.setCellValue(mapExecuteModes.get(model.getExecuteMode()) == null ? "" : mapExecuteModes.get(model.getExecuteMode()));
                    }
                    Cell cell6 = row.createCell(6);
                    cell6.setCellValue(systemMap.get(model.getTestSystemResourceID()).getName());
                    //开始处理上级模块
                    int moduleTitleIndex = 7;//记录每一次标题模块的位置
                    if (model.getModuleResourceID() != null) {
                        if (moduleTitleIndex > cellTitleMaxIndex) {
                            Cell cell = rowm0.createCell(moduleTitleIndex);
                            cell.setCellValue("所属模块");
                            cellTitleMaxIndex++;
                        }
                        Row rowForFillIn = sheet.getRow(rowMaxIndex);
                        Cell cell = rowForFillIn.createCell(moduleTitleIndex);
                        cell.setCellValue(moduleMap.get(model.getModuleResourceID()).getName());

                        //模块父级
                        Long parentRid = moduleMap.get(model.getModuleResourceID()).getParentResourceID();
                        //如果父级是被测系统，那么停止，进行下一个交易处理,如果是模块则继续处理
                        while (!rid.equals(parentRid)) {
                            moduleTitleIndex++;
                            if (moduleTitleIndex > cellTitleMaxIndex) {
                                Cell cellParent = rowm0.createCell(moduleTitleIndex);
                                cellParent.setCellValue("上级模块");
                                cellTitleMaxIndex++;
                            }
                            Row rowForFillInNext = sheet.getRow(rowMaxIndex);
                            Cell cellParent = rowForFillInNext.createCell(moduleTitleIndex);
                            cellParent.setCellValue(moduleMap.get(parentRid).getName());
                            parentRid = moduleMap.get(parentRid).getParentResourceID();
                        }
                    }

                }


            }
            ;
            int allRows = sheet.getPhysicalNumberOfRows();
            for (int i = 0; i < allRows; i++) {
                sheet.setColumnWidth(i, 4000);
            }
            OutputStream output;
            try {
                output = response.getOutputStream();
                response.reset();
                response.setHeader("Content-disposition", "attachment;");
                response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                response.setCharacterEncoding("utf-8");
                wb.write(output);
                if (output != null) {
                    try {
                        output.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            return Result.renderSuccess("操作成功！");
        } else {//所选所又系统下没有任何交易
//			return Result.renderError("当前所选被测系统下不存在任何交易！");
            return this.createSystemTradeExportExcelModel(response);
        }
    }

    /**
     * 查询被测系统下的挂在模块下的交易
     *
     * @param @param  valueOf
     * @param @return 参数
     * @return List<Trade>    返回类型
     * @throws
     * @Title: findTradeByTestSystemRidAndModuleIsNull
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<Trade> findTradeByTestSystemRidAndModuleIsNotNull(String testSystemRid) {

        return tradeDao.findTradeByTestSystemRidAndModuleIsNotNull(testSystemRid);
    }

    /**
     * 给sheet页添加说明
     *
     * @param  sheet
     * @return void    返回类型
     * @throws
     * @Title: addSheetComments
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private void addSheetComments(Sheet sheet, Map<String, String> mapTradeStatus, Map<String, String> mapTradeType, Map<String, String> mapExecuteModes) {
        Row rowm0 = sheet.createRow(0);
        Cell cell0 = rowm0.createCell(0);
        cell0.setCellValue("注：带“*”列为必填");
        sheet.createRow(1);
        // 产生表格标题行(第2行)
        Row rowm2 = sheet.createRow(2);
        // 创建表格标题列
        Cell cellTitle0 = rowm2.createCell(0);
        cellTitle0.setCellValue("交易类型取值范围");
        sheet.setColumnWidth(0, 6000);
        Cell cellTitle1 = rowm2.createCell(1);
        cellTitle1.setCellValue("交易状态取值范围");
        sheet.setColumnWidth(1, 6000);
        Cell cellTitle2 = rowm2.createCell(2);
        cellTitle2.setCellValue("执行方式取值范围");
        sheet.setColumnWidth(2, 6000);

        Cell cellTitle3 = rowm2.createCell(3);
        cellTitle3.setCellValue("归属测试经理多人时请用,分割");
        sheet.setColumnWidth(3, 6000);

        Set<String> mapTradeTypeKey = mapTradeType.keySet();
        Set<String> mapTradeStatusKey = mapTradeStatus.keySet();
        Set<String> mapExecuteModeSet = mapExecuteModes.keySet();
        if (mapTradeTypeKey.size() >= mapTradeStatusKey.size()) {
            int rowNumType = 3;
            for (String typeKey : mapTradeTypeKey) {
                Row row = sheet.createRow(rowNumType);
                Cell cell = row.createCell(0);
                cell.setCellValue(mapTradeType.get(typeKey));
                rowNumType++;
            }
            int rowNumStatus = 3;
            for (String statusKey : mapTradeStatusKey) {
                Row row = sheet.getRow(rowNumStatus);
                Cell cell = row.createCell(1);
                cell.setCellValue(mapTradeStatus.get(statusKey));
                rowNumStatus++;
            }
            int rowNumMode = 3;
            for (String s : mapExecuteModeSet) {
                Row row = sheet.getRow(rowNumMode);
                Cell cell = row.createCell(2);
                cell.setCellValue(mapExecuteModes.get(s));
                rowNumMode++;
            }


        } else {
            int rowNumStatus = 3;
            for (String statusKey : mapTradeStatusKey) {
                Row row = sheet.getRow(rowNumStatus);
                Cell cell = row.createCell(1);
                cell.setCellValue(mapTradeStatus.get(statusKey));
                rowNumStatus++;
            }
            int rowNumType = 3;
            for (String typeKey : mapTradeTypeKey) {
                Row row = sheet.createRow(rowNumType);
                Cell cell = row.createCell(0);
                cell.setCellValue(mapTradeType.get(typeKey));
                rowNumType++;
            }
            int rowNumMode = 3;
            for (String s : mapExecuteModeSet) {
                Row row = sheet.getRow(rowNumMode);
                Cell cell = row.createCell(2);
                cell.setCellValue(mapExecuteModes.get(s));
                rowNumMode++;
            }
        }


    }

    /**
     * 查询交易数据字典
     *
     * @param @return 参数
     * @return Map<String, String>    返回类型
     * @throws
     * @Title: findTradeDicsByDicName
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public List<Map<String, String>> findTradeDicsByDicName(String name) {

        return tradeDao.findTradeDicsByDicName(name);
    }

    /**
     * 交易类型数据字典查询
     *
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: getTradeTypeDictionary
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public Result getTradeTypeDictionary() {
        //查询交易类型
        List<Map<String, String>> tradeType = this.findTradeDicsByDicName("TRADETYPE");
        if (!tradeType.isEmpty() && tradeType.size() > 0) {
            return Result.renderSuccess(tradeType);
        } else {
            return Result.renderError("数据字典数据有误！");
        }
    }

    /**
     * 系统模块交易导出通用模板
     *
     * @param @param  response
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: createSystemTradeExportExcelModel
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private Result createSystemTradeExportExcelModel(HttpServletResponse response) {
        //查询交易状态
        List<Map<String, String>> tradeStatus = this.findTradeDicsByDicName("STATUS");
        Map<String, String> mapTradeStatus = new HashMap<>();
        if (!tradeStatus.isEmpty() && tradeStatus.size() > 0) {
            for (Map<String, String> map : tradeStatus) {
                mapTradeStatus.put(map.get("value"), map.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }

        //查询交易类型
        List<Map<String, String>> tradeType = this.findTradeDicsByDicName("TRADETYPE");
        Map<String, String> mapTradeType = new HashMap<>();
        if (!tradeType.isEmpty() && tradeType.size() > 0) {
            for (Map<String, String> map : tradeType) {
                mapTradeType.put(map.get("value"), map.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }
        //查询执行方式
        List<Map<String, String>> executeModes = this.findTradeDicsByDicName("executeMode");
        HashMap<String, String> mapExecuteModes = new HashMap<>();
        if (!executeModes.isEmpty() && executeModes.size() > 0) {
            for (Map<String, String> map : executeModes) {
                mapExecuteModes.put(map.get("value"), map.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }
        //创建excel

        // 创建工作簿对象
        Workbook wb = new XSSFWorkbook();
        // 创建工作表
        Sheet sheet = wb.createSheet("交易列表");
        Sheet sheet1 = wb.createSheet("说明");
        this.addSheetComments(sheet1, mapTradeStatus, mapTradeType, mapExecuteModes);
        // 产生表格标题行(第零行)
        Row rowm0 = sheet.createRow(0);
        // 创建表格标题列
        Cell cellTitle0 = rowm0.createCell(0);
        cellTitle0.setCellValue("交易编码");
        Cell cellTitle1 = rowm0.createCell(1);
        cellTitle1.setCellValue("*交易名称");
        Cell cellTitle2 = rowm0.createCell(2);
        cellTitle2.setCellValue("*交易类型");
        Cell cellTitle3 = rowm0.createCell(3);
        cellTitle3.setCellValue("*交易状态");
        Cell cellTitle4 = rowm0.createCell(4);
        cellTitle4.setCellValue("交易描述");
        Cell cellTitle5 = rowm0.createCell(5);
        cellTitle5.setCellValue("执行方式");
        Cell cellTitle6 = rowm0.createCell(6);
        cellTitle6.setCellValue("*所属系统");
        Cell cellTitle7 = rowm0.createCell(7);
        cellTitle7.setCellValue("所属模块");
        Cell cellTitle8 = rowm0.createCell(8);
        cellTitle8.setCellValue("上级模块");
        Cell cellTitle9 = rowm0.createCell(9);
        cellTitle9.setCellValue("上级模块");
        Cell cellTitle10 = rowm0.createCell(10);
        cellTitle10.setCellValue("上级模块");
        Cell cellTitle11 = rowm0.createCell(11);
        cellTitle11.setCellValue("上级模块");
        for (int i = 0; i < 20; i++) {
            sheet.setColumnWidth(i, 4000);
        }
        OutputStream output;
        try {
            output = response.getOutputStream();
            response.reset();
            response.setHeader("Content-disposition", "attachment;");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            wb.write(output);
            if (output != null) {
                try {
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.renderSuccess("操作成功！");

    }

    /**
     * 根据需求名和被测系统名获取需求到交易的关系
     *
     * @param demandName
     * @param testSystemName
     * @return
     */
    @Override
    public List<DemandToTradeDTO> getDemandToTradeMapperByDemandNameAndTestSystemName(String demandName, String testSystemName) {
        List<DemandToTradeDTO> result = new ArrayList<>();
        tradeDao.getDemandToTradeMapperByDemandNameAndTestSystemName(demandName, testSystemName).forEach(demandToTradeMapper -> {
            DemandToTradeDTO demandToTradeDTO = new DemandToTradeDTO();
            demandToTradeDTO.nameGroup = demandToTradeMapper.getDemandName() + DemandToTradeDTO.SEPARATOR + demandToTradeMapper.getTestSystemName() + DemandToTradeDTO.SEPARATOR + demandToTradeMapper.getTradeName();
            demandToTradeDTO.resourceIDGroup = demandToTradeMapper.getDemandResourceID() + DemandToTradeDTO.SEPARATOR + demandToTradeMapper.getTestSystemResourceID() + DemandToTradeDTO.SEPARATOR + demandToTradeMapper.getTradeResourceID();
            result.add(demandToTradeDTO);
        });

        return result;
    }

    /**
     * 案例编写、手工执行系通过任务查找任务下维护的统模块交易树
     *
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: initTestTaskTradeTree
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR> 2020-06-09
     */
    @Override
    public Result<?> initTestTaskTradeTree(String userNumber, String taskResourceID) {

//        if (StringUtils.isEmpty(taskResourceID)) {
//            return Result.renderError("taskResourceID参数为空！");
//        }
        //查询当前任务
//        Map<String, Object> taskMap = tradeDao.findTestTaskByTestTaskResourceID(taskResourceID);
//        if (taskMap == null || taskMap.isEmpty()) {
//            return Result.renderError("当前任务不存在或已被删除！");
//        }
        //查询所有和该任务关联的交易
        List<Trade> tradesFind = this.findSelectedTradeByTaskResourceID(taskResourceID);
        //构建根节点
        List<Map<String, Object>> listRoot = new ArrayList<>();
        Map<String, Object> mapRoot = new HashMap<>();
        mapRoot.put("id", "0");
        mapRoot.put("name", "系统交易");
        mapRoot.put("type", "root");
        //如果有保存任务的交易数据，构建系统模块交易树
        if (!tradesFind.isEmpty() && tradesFind.size() > 0) {
            //构造树结构
            mapRoot.put("children", this.constructSystemTradeTree(tradesFind));
            listRoot.add(mapRoot);
            return Result.renderSuccess(listRoot);
        } else {//没有交易数据，返回空
            mapRoot.put("children", new ArrayList<List<Map<String, Object>>>());
            listRoot.add(mapRoot);
        }
        return Result.renderSuccess(listRoot);
    }

    @Override
    public Result<?> initTradeTree() {
        //查询所有交易
        List<Trade> tradesFind = this.findAll();
        //构建根节点
        List<Map<String, Object>> listRoot = new ArrayList<>();
        Map<String, Object> mapRoot = new HashMap<>();
        mapRoot.put("id", "0");
        mapRoot.put("name", "系统交易");
        mapRoot.put("type", "root");
        //如果有保存任务的交易数据，构建系统模块交易树
        if (!tradesFind.isEmpty() && tradesFind.size() > 0) {
            //构造树结构
            mapRoot.put("children", this.constructSystemTradeTree(tradesFind));
            listRoot.add(mapRoot);
            return Result.renderSuccess(listRoot);
        } else {//没有交易数据，返回空
            mapRoot.put("children", new ArrayList<List<Map<String, Object>>>());
            listRoot.add(mapRoot);
        }
        return Result.renderSuccess(listRoot);
    }


    /**
     * 通过交易反查构造系统交易树
     *
     * @param @param  tradesFind
     * @param @return 参数
     * @return Object    返回类型
     * @throws
     * @Title: constructSystemTradeTree
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<Map<String, Object>> constructSystemTradeTree(List<Trade> tradesFind) {
        //存储被测系统树顶级节点
        List<Map<String, Object>> testSystemListResult = new ArrayList<Map<String, Object>>();
        //被测系统和交易直接关系map
        Map<String, List<Trade>> mapTestSystemTrade = new HashMap<>();
        //模块和交易直接关系map
        Map<String, List<Trade>> mapModuleTrade = new HashMap<>();
        //保存所有关联的被测系统
        Set<String> testSystemRids = new HashSet<>();
        //遍历交易
        for (Trade trade : tradesFind) {
            if (!org.springframework.util.StringUtils.isEmpty(trade.getModuleResourceID())) {//模块不空，交易挂载模块下
                if (!mapModuleTrade.isEmpty() && mapModuleTrade.containsKey(trade.getModuleResourceID().toString())) {
                    mapModuleTrade.get(trade.getModuleResourceID().toString()).add(trade);
                } else {
                    List<Trade> listTrade = new ArrayList<Trade>();
                    listTrade.add(trade);
                    mapModuleTrade.put(trade.getModuleResourceID().toString(), listTrade);
                }
            } else {//否则直接挂在被测系统下
                if (!mapTestSystemTrade.isEmpty() && mapTestSystemTrade.containsKey(trade.getTestSystemResourceID().toString())) {
                    mapTestSystemTrade.get(trade.getTestSystemResourceID().toString()).add(trade);
                } else {
                    List<Trade> listTrade = new ArrayList<Trade>();
                    listTrade.add(trade);
                    mapTestSystemTrade.put(trade.getTestSystemResourceID().toString(), listTrade);
                }
            }
            testSystemRids.add(trade.getTestSystemResourceID().toString());
        }

        List<String> systemRids = new ArrayList<>();
        testSystemRids.stream().forEach(x -> systemRids.add(x));
//        List<TestSystem> allSystem = testSystemService.findByResourceIDIn(systemRids);
        List<TestSystem> allSystem = testSystemService.findAllByDataAuth();
        allSystem = allSystem.stream().filter(item -> systemRids.contains(item.getResourceID().toString())).collect(Collectors.toList());
        Map<String, TestSystem> allSystemMap = new HashMap<>();
        allSystem.stream().forEach(system -> {
            allSystemMap.put(String.valueOf(system.getResourceID()), system);
        });

        List<SystemModule> systemModuleList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(systemRids)) {
            systemModuleList = this.systemModuleService.findByTestSystemResIdIn(systemRids);
        }

        for (TestSystem testSystem : allSystem) {
            TestSystem currentSystem = allSystemMap.get(testSystem.getResourceID().toString());
            Map<String, Object> systemMap = new HashMap<>();
            systemMap.put("id", currentSystem.getResourceID());
            systemMap.put("name", currentSystem.getName());
            systemMap.put("resourceID", currentSystem.getResourceID());
            systemMap.put("type", "system");
            systemMap.put("parentID", "0");
            List<Map<String, Object>> childrenTradeList = new ArrayList<>();
            if (mapTestSystemTrade.containsKey(testSystem.getResourceID().toString())) {
                for (Trade trade : mapTestSystemTrade.get(testSystem.getResourceID().toString())) {
                    Map<String, Object> tradeMap = new HashMap<>();
                    tradeMap.put("id", trade.getResourceID());
                    tradeMap.put("name", trade.getName());
                    tradeMap.put("resourceID", trade.getResourceID());
                    //todo 增加判断交易下是否有模型，返回此信息
                    tradeMap.put("type", "trade");
                    tradeMap.put("parentID", testSystem.getResourceID());
                    tradeMap.put("children", new ArrayList<>());
                    childrenTradeList.add(tradeMap);
                }
            }
            List<SystemModule> tempModuleList = systemModuleList.stream().filter(item -> item.getTestSystemResourceID().equals(testSystem.getResourceID())).collect(Collectors.toList());
            List<Map<String, Object>> childrenTradeListModule = this.makeChild(testSystem.getResourceID().toString(), mapModuleTrade, tempModuleList);
            childrenTradeList.addAll(childrenTradeListModule);
            systemMap.put("children", childrenTradeList);

            testSystemListResult.add(systemMap);
        }
        return testSystemListResult;
    }


    private List<Map<String, Object>> makeChild(String parentRid, Map<String, List<Trade>> mapModuleTrade, List<SystemModule> systemModuleList) {
        //查询系统下的子模块
        List<Map<String, Object>> childrenTradeList = new ArrayList<>();
//        List<SystemModule> childModule = systemModuleService.findByParentResourceID(parentRid);
        List<SystemModule> childModule = systemModuleList.stream().filter(item -> parentRid.equals(item.getParentResourceID() != null ? item.getParentResourceID().toString() : "")).collect(Collectors.toList());
        if (childModule.isEmpty()) {
            if (mapModuleTrade.containsKey(parentRid)) {
                for (Trade trade : mapModuleTrade.get(parentRid)) {
                    Map<String, Object> tradeMap = new HashMap<>();
                    tradeMap.put("id", trade.getResourceID());
                    tradeMap.put("name", trade.getName());
                    tradeMap.put("resourceID", trade.getResourceID());
                    tradeMap.put("type", "trade");
                    tradeMap.put("parentID", parentRid);
                    tradeMap.put("children", new ArrayList<>());
                    childrenTradeList.add(tradeMap);
                }
            }
            return childrenTradeList;
        }
        for (SystemModule module : childModule) {
            List<Map<String, Object>> childrenTradeList2 = new ArrayList<>();
            if (mapModuleTrade.containsKey(module.getResourceID().toString())) {

                for (Trade trade : mapModuleTrade.get(module.getResourceID().toString())) {
                    Map<String, Object> tradeMap = new HashMap<>();
                    tradeMap.put("id", trade.getResourceID());
                    tradeMap.put("name", trade.getName());
                    tradeMap.put("resourceID", trade.getResourceID());
                    tradeMap.put("type", "trade");
                    tradeMap.put("parentID", module.getResourceID());
                    tradeMap.put("children", new ArrayList<>());
                    childrenTradeList2.add(tradeMap);
                }
                if (!childrenTradeList2.isEmpty()) {
                    Map<String, Object> moduleMap = new HashMap<>();
                    moduleMap.put("id", module.getResourceID());
                    moduleMap.put("name", module.getName());
                    moduleMap.put("resourceID", module.getResourceID());
                    moduleMap.put("type", "module");
                    moduleMap.put("parentID", module.getParentResourceID());
                    moduleMap.put("children", childrenTradeList2);
                    childrenTradeList.add(moduleMap);
                }
            } else {
                childrenTradeList2 = this.makeChild(module.getResourceID().toString(), mapModuleTrade, systemModuleList);
                if (!childrenTradeList2.isEmpty()) {
                    Map<String, Object> moduleMap = new HashMap<>();
                    moduleMap.put("id", module.getResourceID());
                    moduleMap.put("name", module.getName());
                    moduleMap.put("resourceID", module.getResourceID());
                    moduleMap.put("type", "module");
                    moduleMap.put("parentID", module.getParentResourceID());
                    moduleMap.put("children", childrenTradeList2);
                    childrenTradeList.add(moduleMap);
                }
            }

        }
        return childrenTradeList;
    }


    /**
     * 在扁平化的tree节点中根据父级节点类型及id寻找交易节点
     *
     * @param type
     * @param resId
     * @return
     */
    @Override
    public List<Map<String, Object>> findAllTradeByParentResIDAndTaskResID(String testTaskResourceID, String type, String resId) {
        List<Map<String, Object>> res = new ArrayList<>();

        List<Map<String, Object>> tree = (List<Map<String, Object>>) this.initTestTaskTradeTree
                (null, testTaskResourceID).getObj(); //获取任务交易树数据
        Map<String, Map<String, Object>> treeNodeList = reduceTheTree(tree); //扁平化树形结构
        List<Map<String, Object>> treeNodes = new ArrayList<>(treeNodeList.values());

        List<Map<String, Object>> nodes = treeNodes.stream().
                filter(x -> type.equals(x.get("type").toString()) && resId.equals(x.get("resourceID").toString())).collect(Collectors.toList());
        Deque<Map<String, Object>> nodeQueue = new ArrayDeque<>(nodes);
        while (!nodeQueue.isEmpty()) {
            Map<String, Object> node = nodeQueue.poll();
            if ("trade".equals(node.get("type"))) {
                res.add(node);
            } else {
                nodes = (List<Map<String, Object>>) node.get("children");
                if (!nodes.isEmpty()) {
                    nodeQueue.addAll(nodes);
                }
            }
        }
        return res;
    }

    /**
     * 扁平化tree节点组织tree的path(即层级结构）
     *
     * @param treeNodes
     * @return
     */
    private Map<String, Map<String, Object>> reduceTheTree(List<Map<String, Object>> treeNodes) {
        Deque<Map<String, Object>> deque = new ArrayDeque<>();
        Map<String, Map<String, Object>> nodes = new HashMap<>();
        deque.addAll(treeNodes);
        while (!deque.isEmpty()) {
            Map<String, Object> node = deque.pollLast();
            if (!node.containsKey("resourceID")) {
                node.put("resourceID", "0");
            }
            List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("children");
            if (!children.isEmpty()) {
                deque.addAll(children);
            }
            String path;
            if (node.containsKey("parentID") && StringUtils.isNotEmpty(node.get("parentID").toString()) &&
                    !node.get("parentID").toString().equals("0")) {
                String pid = node.get("parentID").toString();
                Map<String, Object> pNode = nodes.get(pid);
                path = pNode.get("path").toString() + "/" + node.get("name");
            } else {
                path = node.get("name").toString();
            }
            node.put("path", path);
            nodes.put(node.get("resourceID").toString(), node);
        }
        return nodes;
    }


    /**
     * @Title findTradeBySystemRIDOrModuleRID
     * @Description 添加范围弹框根据系统或者模块查询交易列表
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2020/6/10
     */
    @Override
    public Result findTradeBySystemRIDOrModuleRID(Map<String, String> params) {
        Integer page = params.get("page") == null ? 1 : Integer.valueOf(params.get("page").toString());
        Integer pageSize = params.get("pageSize") == null ? 10 : Integer.valueOf(params.get("pageSize").toString());
        String nodeResourceID = params.get("nodeResourceID");
        String taskResourceID = params.get("taskResourceID");
        String nodeType = params.get("nodeType");
        String name = params.get("name");
        String executeMode = params.get("executeMode");
        List<Trade> trades = null;
        switch (nodeType) {
            case "system":
                // 根据被测系统查询交易
                trades = tradeDao.findTradeBySystemAndNameAndType(nodeResourceID, name, executeMode);
                break;
            case "module":
                List<SystemModule> modules = systemModuleService.findModuleAndSubModelByResId(nodeResourceID);
                List<String> resIds = modules.stream().map(x -> x.getResourceID().toString()).collect(Collectors.toList());
                // 根据当前模块查询交易
                trades = tradeDao.findTradeByModuleAndNameAndType(resIds, name, executeMode);
                break;
            default:
                break;
        }
        List<Trade> tradeList = new ArrayList<>();
        if (!trades.isEmpty() && trades.size() > 0) {
            List<String> tradeRids = trades.stream().map(x -> x.getResourceID().toString()).collect(Collectors.toList());
            List<String> relationTrades = tradeDao.findTradeIDsByTaskResourceID(taskResourceID, tradeRids);
            tradeRids.removeAll(relationTrades);
//			tradeRids.remove(relationTrades);
            PageHelper.startPage(page, pageSize);
            tradeList = this.findByResourceIDIn(tradeRids);
        }
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result executeModeResult = feignDataDesignToBasic.findByName("executeMode", token);
        List<Map<String, String>> executeModes = (List<Map<String, String>>) executeModeResult.getObj();
        tradeList.stream().forEach(ts -> {
            String executeModeStr = executeModes.stream().filter(l -> l.get("value").equals(ts.getExecuteMode()))
                    .findFirst().map(l -> l.get("textName")).orElse("");
            ts.setExecuteMode(executeModeStr);
        });
        PageInfo<Trade> pageInfo = new PageInfo<Trade>(tradeList);
        JSONObject result = new JSONObject();
        // 当前页
        result.put("page", page);
        // 当前页的条数
        result.put("pageSize", pageSize);
        // 总的条数
        result.put("total", pageInfo.getTotal());
        // 总的页数
        result.put("totalPage", pageInfo.getPages());
        // 返回数据
        result.put("pageData", pageInfo.getList());
        return Result.renderSuccess(result);
    }

    /**
     * 根据任务rid查询当前任务
     *
     * @param @param  taskResourceID
     * @param @return 参数
     * @return Map<String, String>    返回类型
     * @throws
     * @Title: findTestTaskByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public Map<String, Object> findTestTaskByTestTaskResourceID(String taskResourceID) {

        return tradeDao.findTestTaskByTestTaskResourceID(taskResourceID);
    }

    /**
     * @param enableSystemResourceID : 开启状态的系统resourceIDs
     * @param enableModuleResourceID : 开启状态的模块resourceIDs
     * @param keyWord                : 关键字
     * @return : java.util.List<com.jettech.model.TreeNode>
     * @Method : findBySystemResourceIDsOrModuleResourceIDsOrKeyWord
     * @Description : 通过系统resourceIDs,模块resourceIDs,关键字,查询交易树节点
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-06-24 周三 15:33:54
     */
    @Override
    public List<TreeNode> findBySystemResourceIDsOrModuleResourceIDsOrKeyWord(ArrayList<String> enableSystemResourceID, ArrayList<String> enableModuleResourceID, String keyWord) {
        return tradeDao.findBySystemResourceIDsOrModuleResourceIDsOrKeyWord(enableSystemResourceID, enableModuleResourceID, keyWord);
    }

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findTradeByModuleResourceID
     * @Description: 根据模块查询交易
     * <AUTHOR> 2020-07-16
     */
    @Override
    public Result findTradeByModuleResourceID(String moduleResourceID) {

        if(StringUtils.isEmpty(moduleResourceID)){
            return Result.renderError("moduleResourceID！");
        }
        return Result.renderSuccess(tradeDao.findTradeByModuleResourceID(moduleResourceID));
    }

    /**
     * 根据被测系统和交易编号查询交易
     *
     * @param testSystemRid
     * @param number
     * @return
     */
    @Override
    public Trade findByTestSystemResourceAndNumber(Long testSystemRid, String number) {
        return tradeDao.findByTestSystemResourceAndNumber(testSystemRid,number);
    }

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findTradeByModuleResourceIDs
     * @Description: 根据模块查询交易
     * <AUTHOR> 2020-07-16
     */
    @Override
    public Result findTradeByModuleResourceIDs(String moduleResourceID ,String taskResourceID) {
        List<SystemModule> modules = systemModuleService.findModuleAndSubModelByResId(moduleResourceID);
        List<String> resIds = modules.stream().map(x -> x.getResourceID().toString()).collect(Collectors.toList());
        List<Trade> trades=tradeDao.findTradeByModuleAndNameAndTask(resIds, taskResourceID);
        List<String> tradeIds=trades.stream().map(e->e.getResourceID().toString()).collect(Collectors.toList());
        // 根据当前模块查询交易
        return Result.renderSuccess(tradeIds);
    }

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findTradeBySystemAndTask
     * @Description: 根据模块查询交易
     * <AUTHOR> 2020-07-16
     */
    @Override
    public Result findTradeBySystemAndTask(String systemResourceID,String taskResourceID) {
        List<Trade> trades=tradeDao.findTradeBySystemAndTask(systemResourceID, taskResourceID);
        List<String> tradeIds=trades.stream().map(e->e.getResourceID().toString()).collect(Collectors.toList());
        // 根据当前模块查询交易
        return Result.renderSuccess(tradeIds);
    }

    @Override
    public List<SystemModuleTradeDTO> getTradeSystemModule(List<Long> tradeResourceIDList) {
        return tradeDao.getTradeSystemModule(tradeResourceIDList);
    }

	@Override
	public List<Map<String, Object>> findTradeByModuleResourceIDs(List<String> moduleResourceIDs) {
		return tradeDao.findTradeByModuleResourceIDs(moduleResourceIDs);
	}

    @Override
    public Result<?> initTradeTreeByDemand(Long demandResourceID) {

        if (demandResourceID == null) {
            return Result.renderError("需求ResourceID不能为空");
        }

        List<Trade> tradesFind = null;
        if (demandResourceID == 0) {
            long l = System.currentTimeMillis();
            List<TestSystem> testSystemList = testSystemService.findAllByDataAuth();

            if (testSystemList.isEmpty()) {
                tradesFind = new ArrayList<>();
            } else {
                List<String> resourceIDList = testSystemList.stream().map(s -> s.getResourceID().toString()).collect(Collectors.toList());
                l = System.currentTimeMillis();
                tradesFind = tradeDao.getObjectByTestSystemResourceIDs(resourceIDList);
            }
        } else {
            tradesFind = tradeDao.findTradeByDemandResourceID(demandResourceID);
        }

        //构建根节点
        List<Map<String, Object>> listRoot = new ArrayList<>();
        Map<String, Object> mapRoot = new HashMap<>();
        mapRoot.put("id", "0");
        mapRoot.put("name", "系统交易");
        mapRoot.put("type", "root");
        //如果有保存任务的交易数据，构建系统模块交易树
        if (!tradesFind.isEmpty() && tradesFind.size() > 0) {
            //构造树结构
            long l = System.currentTimeMillis();
            mapRoot.put("children", this.constructSystemTradeTree(tradesFind));
            listRoot.add(mapRoot);
            return Result.renderSuccess(listRoot);
        } else {//没有交易数据，返回空
            mapRoot.put("children", new ArrayList<List<Map<String, Object>>>());
            listRoot.add(mapRoot);
        }
        return Result.renderSuccess(listRoot);
    }

    @Override
    public Result<?> exportTestSystemTradeExcel2(Map<String, Object> mapData) {
    	//交易所属系统模块最大层级数
    	int moduleMaxLevel = 0;

        String testSystemRidStr = String.valueOf(mapData.get("testSystemRids"));
        HttpServletResponse response = (HttpServletResponse) mapData.get("response");

        //查看当前选择的系统下是否存在交易，如果存在交易才进行导出，如果不存在交易则提示（没有交易不做导出操作）
        ArrayList<String> testSystemRids = new ArrayList<>();
        String[] testSystemRidArr = testSystemRidStr.split(",");
        for (String rid : testSystemRidArr) {
            testSystemRids.add(rid);
        }
        //根据被测系统查询所有需要被导出的交易信息
        List<Map<String, Object>> tradeMapInfo = this.findByTestSystemResourceIDs(testSystemRids);
        if (!tradeMapInfo.isEmpty() && tradeMapInfo.size() > 0) {//所选系统下存在交易，则进行导出操作
            //查询交易状态
            List<Map<String, String>> tradeStatus = this.findTradeDicsByDicName("STATUS");
            Map<String, String> mapTradeStatus = new HashMap<>();
            if (!tradeStatus.isEmpty() && tradeStatus.size() > 0) {
                for (Map<String, String> map : tradeStatus) {
                    mapTradeStatus.put(map.get("value"), map.get("textName"));
                }
            } else {
                return Result.renderError("数据字典数据有误！");
            }

            //查询交易类型
            List<Map<String, String>> tradeType = this.findTradeDicsByDicName("TRADETYPE");
            Map<String, String> mapTradeType = new HashMap<>();
            if (!tradeType.isEmpty() && tradeType.size() > 0) {
                for (Map<String, String> map : tradeType) {
                    mapTradeType.put(map.get("value"), map.get("textName"));
                }
            } else {
                return Result.renderError("数据字典数据有误！");
            }
            //查询执行方式
            List<Map<String, String>> executeModes = this.findTradeDicsByDicName("executeMode");
            HashMap<String, String> mapExecuteModes = new HashMap<>();
            if (!executeModes.isEmpty() && executeModes.size() > 0) {
                for (Map<String, String> map : executeModes) {
                    mapExecuteModes.put(map.get("value"), map.get("textName"));
                }
            } else {
                return Result.renderError("数据字典数据有误！");
            }
            String token = HttpRequestUtils.getCurrentRequestToken();
            Result r5 = feignDataDesignToBasic.findTypeAllUser(token);
            List<Map<String, String>> l5 = (List<Map<String, String>>) r5.getObj();
            List<TradeAndTestManager> findAll = iTradeTestManagerService.findAll();
            Map<Long, List<TradeAndTestManager>> tradeMap = findAll.stream()
                    .collect(Collectors.groupingBy(s -> s.getTradeResourceID()));
            // 创建工作簿对象
            Workbook wb = new XSSFWorkbook();
            // 创建工作表
            Sheet sheet = wb.createSheet("交易列表");
            Sheet sheet1 = wb.createSheet("说明");
            this.addSheetComments(sheet1, mapTradeStatus, mapTradeType, mapExecuteModes);

            //筛选出所有存在交易的被测系统
            Set<Long> testSystemSet = new HashSet<>();
            //被测系统去重处理
            tradeMapInfo.stream().forEach(x -> testSystemSet.add(Long.valueOf(x.get("testSystemResourceID").toString())));
            //查询所有的被测系统
            List<TestSystem> allTestSystem = testSystemService.findByResourceIDIn(testSystemRids);
            Map<Long, TestSystem> systemMap = new HashMap<>();
            allTestSystem.stream().forEach(x -> {
                systemMap.put(x.getResourceID(), x);
            });
            //查询所有模块
            Map<Long, SystemModule> moduleMap = new HashMap<>();
            List<SystemModule> allModules = systemModuleService.findAll();
            allModules.stream().forEach(x -> {
                moduleMap.put(x.getResourceID(), x);
            });

        	//计算待导出系统下所有交易，模块层级的最大数
            int i = 0;//模块默认1层
            int n = 0;//记录模块最大层数
            //处理每一个被测系统
            for (Long rid : testSystemSet) {
                //被测系统下是模块
                //查询被测系统下的挂在模块下的交易(按照模块 的rid排序，保证相同模块下的交易一并处理)
            	List<Trade> tradeModuleIsNull = this.findTradeByTestSystemRidAndModuleIsNotNull(String.valueOf(rid));
                for (Trade model : tradeModuleIsNull) {
                    i = 1;//模块默认1层
                    if (model.getModuleResourceID() != null) {
                    	//模块父级
                        Long parentRid = moduleMap.get(model.getModuleResourceID()).getParentResourceID();
                        while (!rid.equals(parentRid)) {
                            i++;
                            parentRid = moduleMap.get(parentRid).getParentResourceID();
                        }
                        if (i > n) {
							n = i;
						}
                    }
                }
            }
//            System.out.println("模块最大层数============="+n);
            moduleMaxLevel = n;
            //设计表头
            // 产生表格标题行(第零行)
            Row rowm0 = sheet.createRow(0);

            Cell cellTitle0 = rowm0.createCell(0);
            cellTitle0.setCellValue("*所属系统");
            if (moduleMaxLevel > 0) {
    			Map<Integer, String> map = new HashMap<>();
    			map.put(1, "一");
    			map.put(2, "二");
    			map.put(3, "三");
    			map.put(4, "四");
    			map.put(5, "五");
    			map.put(6, "六");
    			map.put(7, "七");
    			map.put(8, "八");
    			map.put(9, "九");
    			map.put(10, "十");
    			for (int k = 1; k <= moduleMaxLevel; k++) {
    				Cell cellTitle = rowm0.createCell(k);
    				cellTitle.setCellValue(map.get(k)+"级模块");
    			}
    		}
            Cell cellTitle1 = rowm0.createCell(moduleMaxLevel+1);
    		cellTitle1.setCellValue("交易编码");
    		Cell cellTitle2 = rowm0.createCell(moduleMaxLevel+2);
    		cellTitle2.setCellValue("*交易名称");
    		Cell cellTitle3 = rowm0.createCell(moduleMaxLevel+3);
    		cellTitle3.setCellValue("*交易类型");
    		Cell cellTitle4 = rowm0.createCell(moduleMaxLevel+4);
    		cellTitle4.setCellValue("*交易状态");
    		Cell cellTitle5 = rowm0.createCell(moduleMaxLevel+5);
    		cellTitle5.setCellValue("交易描述");
    		Cell cellTitle6 = rowm0.createCell(moduleMaxLevel+6);
    		cellTitle6.setCellValue("执行方式");

    		Cell cellTitle7 = rowm0.createCell(moduleMaxLevel+7);
    		cellTitle7.setCellValue("归属测试经理");


    		//写入内容
    		//记录标题的最大行,初始为0
            int rowMaxIndex = 0;
    		//处理每一个被测系统
            for (Long rid : testSystemSet) {
                //被测系统直属的交易
                List<Trade> systemDirectTrades = this.findOnlyTradebyTestSystemResourceID(String.valueOf(rid), "");
                for (Trade model : systemDirectTrades) {
                	 Row row = sheet.createRow(rowMaxIndex + 1);
                     rowMaxIndex++;
                     Cell cell0 = row.createCell(0);
                     cell0.setCellValue(systemMap.get(model.getTestSystemResourceID()).getName());
                     Cell cell1 = row.createCell(moduleMaxLevel+1);
                     cell1.setCellValue(model.getNumber());
                     Cell cell2 = row.createCell(moduleMaxLevel+2);
                     cell2.setCellValue(model.getName());
                     Cell cell3 = row.createCell(moduleMaxLevel+3);
                     cell3.setCellValue(mapTradeType.get(model.getType()));
                     Cell cell4 = row.createCell(moduleMaxLevel+4);
                     cell4.setCellValue(mapTradeStatus.get(model.getStatus()));
                     Cell cell5 = row.createCell(moduleMaxLevel+5);
                     cell5.setCellValue(model.getComment());
                     Cell cell6 = row.createCell(moduleMaxLevel+6);
                     if (!org.springframework.util.StringUtils.isEmpty(model.getExecuteMode())) {
                         cell6.setCellValue(mapExecuteModes.get(model.getExecuteMode()) == null ? "" : mapExecuteModes.get(model.getExecuteMode()));
                     }
                     List<TradeAndTestManager> list = tradeMap.get(model.getResourceID()) ;
                     if(list!=null) {
                     	 List<String> mamangerss = list.stream().map(x->x.getBelongTestManager()).collect(Collectors.toList());
                     	 List<String> userNameList=new ArrayList<>();
                          for (Map<String, String> userMap : l5) {
                              if (null!=mamangerss&&!mamangerss.isEmpty()) {
                                 if(mamangerss.contains(userMap.get("number"))) {
                              	   String userName = userMap.get("userName");
                                     userNameList.add(userName);
                                 }
                              }
                          }
                          String collect = userNameList.stream().collect(Collectors.joining(","));
                          Cell cell7 = row.createCell(moduleMaxLevel+7);
                          cell7.setCellValue(collect);
                     }
                }
                //被测系统下是模块
                //查询被测系统下的挂在模块下的交易(按照模块 的rid排序，保证相同模块下的交易一并处理)
                List<Trade> tradeModuleIsNull = this.findTradeByTestSystemRidAndModuleIsNotNull(String.valueOf(rid));
                for (Trade model : tradeModuleIsNull) {
                    Row row = sheet.createRow(rowMaxIndex + 1);
                    rowMaxIndex++;
                    Cell cell0 = row.createCell(0);
                    cell0.setCellValue(systemMap.get(model.getTestSystemResourceID()).getName());
                    Cell cell1 = row.createCell(moduleMaxLevel+1);
                    cell1.setCellValue(model.getNumber());
                    Cell cell2 = row.createCell(moduleMaxLevel+2);
                    cell2.setCellValue(model.getName());
                    Cell cell3 = row.createCell(moduleMaxLevel+3);
                    cell3.setCellValue(mapTradeType.get(model.getType()));
                    Cell cell4 = row.createCell(moduleMaxLevel+4);
                    cell4.setCellValue(mapTradeStatus.get(model.getStatus()));
                    Cell cell5 = row.createCell(moduleMaxLevel+5);
                    cell5.setCellValue(model.getComment());
                    Cell cell6 = row.createCell(moduleMaxLevel+6);
                    if (!org.springframework.util.StringUtils.isEmpty(model.getExecuteMode())) {
                        cell6.setCellValue(mapExecuteModes.get(model.getExecuteMode()) == null ? "" : mapExecuteModes.get(model.getExecuteMode()));
                    }
                    List<TradeAndTestManager> list = tradeMap.get(model.getResourceID()) ;
                    if(list!=null) {
                    	 List<String> mamangerss = list.stream().map(x->x.getBelongTestManager()).collect(Collectors.toList());
                    	 List<String> userNameList=new ArrayList<>();
                         for (Map<String, String> userMap : l5) {
                             if (null!=mamangerss&&!mamangerss.isEmpty()) {
                                if(mamangerss.contains(userMap.get("number"))) {
                             	   String userName = userMap.get("userName");
                                    userNameList.add(userName);
                                }
                             }
                         }
                         String collect = userNameList.stream().collect(Collectors.joining(","));
                         Cell cell7 = row.createCell(moduleMaxLevel+7);
                         cell7.setCellValue(collect);
                    }
                    if (model.getModuleResourceID() != null) {
//                        String moduleName = moduleMap.get(model.getModuleResourceID()).getName();
                    	List<String> moduleNameList = new LinkedList<String>();
                        moduleNameList.add(moduleMap.get(model.getModuleResourceID()).getName());
                        //模块父级
                        Long parentRid = moduleMap.get(model.getModuleResourceID()).getParentResourceID();
                        //如果父级是被测系统，那么停止，进行下一个交易处理,如果是模块则继续处理
                        while (!rid.equals(parentRid)) {
                        	if(moduleMap.get(parentRid) != null) {
                        		moduleNameList.add(moduleMap.get(parentRid).getName());
                        	}
                            parentRid = moduleMap.get(parentRid).getParentResourceID();
                        }
//                        System.out.println("-----------"+moduleNameList);
                        for (int j = 1; j <= moduleNameList.size(); j++) {
                        	Cell cell = row.createCell(j);
                            cell.setCellValue(moduleNameList.get(moduleNameList.size()-j));
						}
                    }
                }
            }
            for (int m = 0; m <= moduleMaxLevel+7; m++) {
                sheet.setColumnWidth(m, 4000);
            }
            OutputStream output;
            try {
                output = response.getOutputStream();
                response.reset();
//                response.setHeader("Content-disposition", "attachment;");
                response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                response.setCharacterEncoding("utf-8");
                response.addHeader("X-Frame-Options", "DENY");
                wb.write(output);
                if (output != null) {
                    try {
                        output.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
            	try {
					wb.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
            return Result.renderSuccess("操作成功！");
        } else {//所选所又系统下没有任何交易
//			return Result.renderError("当前所选被测系统下不存在任何交易！");
            return this.createSystemTradeExportExcelModel2(response);
        }
    }


    private Result<?> createSystemTradeExportExcelModel2(HttpServletResponse response) {
        //查询交易状态
        List<Map<String, String>> tradeStatus = this.findTradeDicsByDicName("STATUS");
        Map<String, String> mapTradeStatus = new HashMap<>();
        if (!tradeStatus.isEmpty() && tradeStatus.size() > 0) {
            for (Map<String, String> map : tradeStatus) {
                mapTradeStatus.put(map.get("value"), map.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }

        //查询交易类型
        List<Map<String, String>> tradeType = this.findTradeDicsByDicName("TRADETYPE");
        Map<String, String> mapTradeType = new HashMap<>();
        if (!tradeType.isEmpty() && tradeType.size() > 0) {
            for (Map<String, String> map : tradeType) {
                mapTradeType.put(map.get("value"), map.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }
        //查询执行方式
        List<Map<String, String>> executeModes = this.findTradeDicsByDicName("executeMode");
        HashMap<String, String> mapExecuteModes = new HashMap<>();
        if (!executeModes.isEmpty() && executeModes.size() > 0) {
            for (Map<String, String> map : executeModes) {
                mapExecuteModes.put(map.get("value"), map.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }
        //创建excel

        // 创建工作簿对象
        Workbook wb = new XSSFWorkbook();
        // 创建工作表
        Sheet sheet = wb.createSheet("交易列表");
        Sheet sheet1 = wb.createSheet("说明");
        this.addSheetComments(sheet1, mapTradeStatus, mapTradeType, mapExecuteModes);
        // 产生表格标题行(第零行)
        Row rowm0 = sheet.createRow(0);
        // 创建表格标题列
        Cell cellTitle0 = rowm0.createCell(0);
        cellTitle0.setCellValue("*所属系统");
        Cell cellTitle1 = rowm0.createCell(1);
		cellTitle1.setCellValue("交易编码");
		Cell cellTitle2 = rowm0.createCell(2);
		cellTitle2.setCellValue("*交易名称");
		Cell cellTitle3 = rowm0.createCell(3);
		cellTitle3.setCellValue("*交易类型");
		Cell cellTitle4 = rowm0.createCell(4);
		cellTitle4.setCellValue("*交易状态");
		Cell cellTitle5 = rowm0.createCell(5);
		cellTitle5.setCellValue("交易描述");
		Cell cellTitle6 = rowm0.createCell(6);
		cellTitle6.setCellValue("执行方式");

        Cell cellTitle7 = rowm0.createCell(7);
        cellTitle7.setCellValue("归属测试经理");
        for (int i = 0; i < 20; i++) {
            sheet.setColumnWidth(i, 4000);
        }
        OutputStream output;
        try {
            output = response.getOutputStream();
            response.reset();
//            response.setHeader("Content-disposition", "attachment;");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.addHeader("X-Frame-Options", "DENY");
            wb.write(output);
            if (output != null) {
                try {
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
        	try {
				wb.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
        return Result.renderSuccess("操作成功！");
    }
  /**
   *
   * @Description 查询交易
   * @param resourceID
   * @return
   * tianliping
   * 2023年2月15日
   */
	@Override
	public Trade findTradeAndanagerByResourceID(long resourceID) {
		Trade findByResourceID = this.findByResourceID(resourceID);

		List<TradeAndTestManager> findByTradeResourceID = iTradeTestManagerService.findByTradeResourceID(resourceID);
		List<String> mamangerss = findByTradeResourceID.stream().map(x->x.getBelongTestManager()).collect(Collectors.toList());
		String token = HttpRequestUtils.getCurrentRequestToken();
//        Result r5 = feignDataDesignToBasic.findTypeAllUser(token);
//        List<Map<String, String>> l5 = (List<Map<String, String>>) r5.getObj();
//        List<String> userNameList=new ArrayList<>();
//        for (Map<String, String> map : l5) {
//            if (null!=mamangerss&&!mamangerss.isEmpty()) {
//               if(mamangerss.contains(map.get("number"))) {
//            	   String userName = map.get("userName");
//                   userNameList.add(userName);
//
//               }
//           }
//        }
        List<JettechUserDTO> allPerson = feignDataDesignToBasic.findAllPerson(token);
        Map<String, String> userMap = allPerson.stream().collect(Collectors.toMap(JettechUserDTO::getNumber, JettechUserDTO::getUserName));
        JSONArray array = new JSONArray();
            for ( String s :mamangerss) {
                JSONObject json = new JSONObject();
                json.put("text", userMap.get(s));
                json.put("number", s);
                array.add(json);
            }
        // String collect = mamangerss.stream().collect(Collectors.joining(","));
        findByResourceID.setBelongTestManager(array);
		return findByResourceID;
	}
	/**
     *
     *@Description 根据系统的rid查询交易
     *@param
     *@return List<Trade>
     *<AUTHOR>
     *@Date 2023年2月21日
     */
@Override
public List<Trade> findBySystesmResourceIDIn(List<Long> sysList) {
	return tradeDao.findBySystesmResourceIDIn(sysList);
}


}
