package com.jettech.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageSerializable;
import com.jettech.DTO.SceneCaseContainDTO;
import com.jettech.DTO.SceneCaseExecDTO;
import com.jettech.DTO.TestCasequoteDTO;
import com.jettech.common.dto.Result;
import com.jettech.common.util.CheckUtil;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.dao.idao.ISceneCaseContainDao;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.SceneCaseContain;
import com.jettech.model.TestCase;
import com.jettech.model.TestCasequote;
import com.jettech.service.iservice.ISceneCaseContainService;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.util.PageUtil;
import com.jettech.util.jedis.RedisCacheConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SceneCaseContainServiceImpl
 * @projectName jettopro
 * @description: 场景案例
 */
@Service
public class SceneCaseContainServiceImpl extends BaseHBServiceImpl<SceneCaseContain> implements ISceneCaseContainService {

    private static final Logger logger = LoggerFactory.getLogger(SceneCaseContainServiceImpl.class);

    private final ISceneCaseContainDao sceneCaseContainDao;

    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private TestCasequoteServiceImpl testCasequoteService;
    @Autowired
    private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;
    @Autowired
    private RedisTemplate redisTemplate;

    public SceneCaseContainServiceImpl(ISceneCaseContainDao sceneCaseContainDao) {
        this.sceneCaseContainDao = sceneCaseContainDao;
        this.baseDao = sceneCaseContainDao;
    }

    @PostConstruct
    private int init() {
        int maxId = sceneCaseContainDao.selectMaxId();
        redisTemplate.boundValueOps(RedisCacheConfig.SCENE_CASE_CONTAIN_ORDER_INCREMENT).set(maxId);
        return maxId;
    }

    @Override
    public SceneCaseContain save(SceneCaseContain model, String userNumber) {
        preSave(model);
        setOrders(Arrays.asList(model));
        return super.save(model, userNumber);
    }

    @Override
    public List<SceneCaseContain> save(List<SceneCaseContain> modelList, String userNumber) {
        modelList.forEach(this::preSave);
        setOrders(modelList);
        return super.save(modelList, userNumber);
    }

    private void setOrders(List<SceneCaseContain> modelList) {
        if (CollectionUtils.isEmpty(modelList)) return;

        int size = modelList.size();
        Object o = redisTemplate.boundValueOps(RedisCacheConfig.SCENE_CASE_CONTAIN_ORDER_INCREMENT).get();
        int maxId = 0;
        if (o == null) {
            maxId = init();
        } else {
            maxId = redisTemplate.boundValueOps(RedisCacheConfig.SCENE_CASE_CONTAIN_ORDER_INCREMENT).increment(size).intValue() - size;
        }
        int m= CheckUtil.checkLoop(modelList.size());
        for (int i = 0; i < m; i++) {
            modelList.get(i).setCaseOrder(maxId + i + 1);
        }
    }

    private void preSave(SceneCaseContain model){

        List<TestCase> testCaseList = testCaseService.findByResourceIDIn(Arrays.asList(model.getSceneCaseResourceID(), model.getTestCaseResourceID()));
        Assert.isTrue(testCaseList.size()==2, "案例可能已经被删除");
        for (TestCase testCase : testCaseList) {
            String caseId = testCase.getResourceID().toString();
            if(Objects.equals(caseId, model.getSceneCaseResourceID())
                    && !Objects.equals(testCase.getSceneCase(), Boolean.TRUE)){
                throw new RuntimeException("sceneCaseResourceID不是场景案例");
            }
            if(Objects.equals(caseId, model.getTestCaseResourceID())
                    && Objects.equals(testCase.getSceneCase(), Boolean.TRUE)){
                throw new RuntimeException("暂不允许引用场景案例");
            }
        }
    }

    @Override
    public Object findBySceneCaseResourceID(JSONObject params) {
        Long sceneCaseResourceID = params.getLong("sceneCaseResourceID");
        Long testCaseQuoteResourceID = params.getLong("testCaseQuoteResourceID");
        Assert.notNull(sceneCaseResourceID, "sceneCaseResourceID不能为空");
        String caseResultRecordResourceID = params.getString("caseResultRecordResourceID");

        Page page = PageUtil.startPage(params);

        List<SceneCaseContainDTO> result = sceneCaseContainDao.selectByCondition(params);

        if(CollectionUtils.isNotEmpty(result)){

            if (testCaseQuoteResourceID != null) {
                //testCaseQuoteResourceID非空 则查询子案例最新的执行时间和执行结果，并统计执行的次数
                Result<List<SceneCaseContainDTO>> newResult = feignDataDesignToManexecuteService.findNewResult(sceneCaseResourceID.toString(), testCaseQuoteResourceID.toString(), caseResultRecordResourceID);
                if (newResult == null || newResult.getCode() != 20000) {
                    throw new RuntimeException("调用[Manexecute]服务查询最新执行结果失败：" + (newResult == null ? "" : newResult.getMsg()));
                }
                List<SceneCaseContainDTO> newResultObj = newResult.getObj();
                if (CollectionUtils.isNotEmpty(newResultObj)) {
                    Map<Long, SceneCaseContainDTO> newResultMap = newResultObj.stream().collect(Collectors.toMap(s -> s.getSceneCaseContainResourceID(), s -> s, (o, n) -> o));
                    result.forEach(s -> {
                        SceneCaseContainDTO oneResult = newResultMap.get(s.getResourceID());
                        if (oneResult != null) {
                            s.setExecuteTime(oneResult.getExecuteTime());
                            s.setCaseResult(oneResult.getCaseResult());
                            s.setRecordCount(oneResult.getRecordCount());
                            if (StringUtils.isNotBlank(caseResultRecordResourceID)) {
                                s.setExecutor(oneResult.getExecutor());
                                s.setCaseResultType(oneResult.getCaseResultType());
                                s.setCaseResultTypeName(oneResult.getCaseResultTypeName());
                                s.setCaseResultDescription(oneResult.getCaseResultDescription());
                            }
                        }
                    });
                }
            }

            List<String> testCaseResourceIDList = result.stream().map(s -> s.getTestCaseResourceID()).distinct().collect(Collectors.toList());

            List<TestCase> testCaseList = testCaseService.findByResourceIDIn(testCaseResourceIDList);

            Map<String, TestCase> testCaseMap = testCaseList.stream().collect(Collectors.toMap(s -> s.getResourceID().toString(), s -> s));

            result.forEach(s->s.setTestCase(testCaseMap.get(s.getTestCaseResourceID())));
        }

        if(page!=null){
            PageSerializable pageSerializable = page.toPageSerializable();
            pageSerializable.setList(result);
            return pageSerializable;
        }
        return result;
    }

    public List<SceneCaseContain> findBySceneCaseList(List<Long> sceneCaseResourceIdList) {
        if (CollectionUtils.isEmpty(sceneCaseResourceIdList)) {
            return new ArrayList<>();
        }
        QueryWrapper<SceneCaseContain> queryWrapper = Wrappers.query(new SceneCaseContain())
                .select("sceneCaseResourceID", "testCaseResourceID", "initParam")
                .in("sceneCaseResourceID", sceneCaseResourceIdList)
                .orderByAsc("caseOrder");
        return sceneCaseContainDao.selectList(queryWrapper);
    }

    public List<TestCasequoteDTO> findTestCaseQuoteDTOList(SceneCaseExecDTO sceneCaseExecDTO){

        List<TestCasequote> testCasequoteList = null;
        if(CollectionUtils.isNotEmpty(sceneCaseExecDTO.getTestCaseQuoteResourceIDList())){
            testCasequoteList = testCasequoteService.findByResourceIDIn(sceneCaseExecDTO.getTestCaseQuoteResourceIDList());
        }else {
            testCasequoteList = testCasequoteService.findByTaskAndTrade(Long.valueOf(sceneCaseExecDTO.getTaskResourceID()), Long.valueOf(sceneCaseExecDTO.getTradeResourceID()));
        }

        Assert.notEmpty(testCasequoteList, "未找到执行案例");

        List<String> testCaseResourceIDList = testCasequoteList.stream().map(s -> s.getTestCaseResourceID().toString()).distinct().collect(Collectors.toList());

        List<TestCase> testCaseList = testCaseService.findByResourceIDIn(testCaseResourceIDList);

        Assert.notEmpty(testCaseList, "未找到被引用案例");

        Map<Long, TestCase> testCaseMap = testCaseList.stream().collect(Collectors.toMap(s -> s.getResourceID(), s -> s));

        List<TestCasequoteDTO> testCasequoteDTOList = JSON.parseArray(JSON.toJSONString(testCasequoteList), TestCasequoteDTO.class);

        testCasequoteDTOList.stream().forEach(c->{
            TestCase testCase = testCaseMap.get(c.getTestCaseResourceID());
            Assert.notNull(testCase, String.format("案例[%s]不存在", c.getTestCaseResourceID()));
            c.setTestMode(testCase.getTestMode());
            c.setSystemType(testCase.getSystemType());
            c.setSceneCase(testCase.getSceneCase());
            c.setTimeout(testCase.getTimeout());
        });

        QueryWrapper<SceneCaseContain> queryWrapper = Wrappers.query(new SceneCaseContain())
                .in("sceneCaseResourceID", testCaseResourceIDList)
                .orderByAsc("caseOrder");

        List<SceneCaseContain> sceneCaseContainList = sceneCaseContainDao.selectList(queryWrapper);

        if(CollectionUtils.isNotEmpty(sceneCaseContainList)){

            List<TestCase> refTestCase = testCaseService.findByResourceIDIn(sceneCaseContainList.stream()
                    .map(c -> c.getTestCaseResourceID().toString()).collect(Collectors.toList()));

            Map<String, TestCase> refTestCaseMap = refTestCase.stream().collect(Collectors.toMap(c -> c.getResourceID().toString(), c -> c));

            List<SceneCaseContainDTO> sceneCaseContainDTOList = JSON.parseArray(JSON.toJSONString(sceneCaseContainList), SceneCaseContainDTO.class);

            sceneCaseContainDTOList.stream().forEach(s->s.setTestCase(refTestCaseMap.get(s.getTestCaseResourceID())));

            Map<String, List<SceneCaseContainDTO>> sceneCaseContainGroupMap = sceneCaseContainDTOList.stream().collect(Collectors.groupingBy(s -> s.getSceneCaseResourceID()));

            for (TestCasequoteDTO testCasequoteDTO : testCasequoteDTOList) {
                testCasequoteDTO.setSceneCaseContainDTOList(sceneCaseContainGroupMap.get(testCasequoteDTO.getTestCaseResourceID().toString()));
            }
        }

        return testCasequoteDTOList;
    }

    public String getCurUserNumber(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String userNumber = LoginUserUtil.getUserNumber(request);
        return userNumber;
    }

    @Override
    public boolean isSceneQuote(String testCaseResourceID) {
        QueryWrapper<SceneCaseContain> queryWrapper = Wrappers.query(new SceneCaseContain()).eq("testCaseResourceID", testCaseResourceID).or().eq("sceneCaseResourceID", testCaseResourceID);

        Integer count = sceneCaseContainDao.selectCount(queryWrapper);

        return count>0;
    }

    @Override
    public Result<?> checkInitParamBySceneCaseList(List<Long> testCaseResourceIDList) {
        if (CollectionUtils.isEmpty(testCaseResourceIDList)) {
            return Result.renderError("案例ResourceID列表不能为空");
        }
        List<SceneCaseContain> sceneCaseContainList = findBySceneCaseList(testCaseResourceIDList);

        Map<String, List<SceneCaseContain>> caseGroupMap = sceneCaseContainList.stream().collect(Collectors.groupingBy(c -> c.getSceneCaseResourceID()));

        if (sceneCaseContainList.isEmpty() || caseGroupMap.size() != testCaseResourceIDList.size()) {
            return Result.renderError("部分场景案例引用不存在");
        }

        Map<String, TestCase> sceneCaseMap = testCaseService.findByResourceIDIn(testCaseResourceIDList.stream()
                .map(String::valueOf).collect(Collectors.toList())).stream().collect(Collectors.toMap(c -> c.getResourceID().toString(), c -> c));
        List<String> checkResult = caseGroupMap.entrySet().stream().map(m -> {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < m.getValue().size(); i++) {
                SceneCaseContain caseContain = m.getValue().get(i);
                String initParam = caseContain.getInitParam();
                if (StringUtils.isBlank(initParam) || initParam.equals("{}")) {
                    String intent = sceneCaseMap.get(caseContain.getSceneCaseResourceID()).getIntent();
                    list.add(String.format("[%s]第[%d]个案例参数未初始化", intent, i + 1));
                }
            }
            return list;
        }).flatMap(Collection::stream).collect(Collectors.toList());

        if (checkResult.isEmpty()) {
            return Result.renderSuccess();
        } else {
            return Result.renderError(checkResult.stream().reduce((a, b) -> a + "\r\n" + b).get());
        }
    }
}