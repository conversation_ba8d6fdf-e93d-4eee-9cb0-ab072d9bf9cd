package com.jettech.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.common.dto.assets.WikiPageCopyDTO;
import com.jettech.common.dto.assets.WikiPageDTO;
import com.jettech.common.enums.AttachmentStoreTypeEnums;
import com.jettech.common.page.PageResult;
import com.jettech.common.page.PageUtil;
import com.jettech.common.util.AttachmentPathGenerator;
import com.jettech.common.util.FtpUtil;
import com.jettech.common.util.ParamConfig;
import com.jettech.dao.idao.IWikiPageDao;
import com.jettech.model.WikiPage;
import com.jettech.model.WikiPageContent;
import com.jettech.model.WikiPageUserTemConfig;
import com.jettech.service.iservice.IWikiPageContentService;
import com.jettech.service.iservice.IWikiPageService;
import com.jettech.service.iservice.IWikiPageUserTemConfigService;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.DocumentEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WikiPageServiceImpl extends BaseServiceImpl<WikiPage> implements IWikiPageService {

    private static final Logger logger = LoggerFactory.getLogger(WikiPageServiceImpl.class);

    @Autowired
    private ParamConfig paramConfig;

    /*@Autowired
    private FtpUtils ftpUtils;*/

    @Autowired
    private FtpUtil ftpUtil;

    @Autowired
    private IWikiPageDao wikiPageDao;

    @Autowired
    private IWikiPageContentService wikiPageContentService;

    @Autowired
    private IWikiPageUserTemConfigService wikiPageUserTemConfigService;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = wikiPageDao;
    }

    @Override
    public void addPage(WikiPage wikiPage, String loginUserNumber) {
        WikiPage newPage = this.save(wikiPage, loginUserNumber);

        WikiPageContent content = new WikiPageContent();
        content.setPageResourceId(newPage.getResourceID());
        content.setContent(wikiPage.getContent());
        content.setTextContent(wikiPage.getTextContent());
        wikiPageContentService.save(content, loginUserNumber);
    }

    //检查该空间是否存在同名页面
    @Override
    public boolean checkPageName(WikiPage wikiPage){
        //若resourceid不为空，则该页面为修改页面
        if(wikiPage.getResourceID()!=null){
            WikiPage wikiPage1 = this.wikiPageDao.findByResourceID(wikiPage.getResourceID());
            if(wikiPage.getTitle().equals(wikiPage1.getTitle())){
                return true;
            }
        }
        //查看该空间下的所有页面
        List<WikiPage> pageList = this.wikiPageDao.listAllPageBySpace(wikiPage.getSpaceResourceId()).stream().filter(item->item.getTitle().equals(wikiPage.getTitle())).collect(Collectors.toList());
        if (pageList.size()>0){
            return false;
        }
        return true;
    }
    @Override
    public void editPage(WikiPage wikiPage, String loginUserNumber) {
        if (wikiPage.getResourceID() != null) {
            WikiPage page = this.findByResourceID(wikiPage.getResourceID());
            if (page != null) {
                wikiPage.setId(page.getId());
                this.update(wikiPage, loginUserNumber);

                WikiPageContent content = this.wikiPageContentService.findByPageResourceId(page.getResourceID());
                if (content != null) {
                    content.setTextContent(wikiPage.getTextContent());
                    content.setContent(wikiPage.getContent());
                    this.wikiPageContentService.update(content, loginUserNumber);
                } else {
                    content = new WikiPageContent();
                    content.setPageResourceId(page.getResourceID());
                    content.setContent(wikiPage.getContent());
                    content.setTextContent(wikiPage.getTextContent());
                    wikiPageContentService.save(content, loginUserNumber);
                }

                WikiPageUserTemConfig wikiPageUserTemConfig = new WikiPageUserTemConfig();
                wikiPageUserTemConfig.setPageResourceId(page.getResourceID());
                wikiPageUserTemConfig.setSpaceResourceId(page.getSpaceResourceId());
                wikiPageUserTemConfig.setType(1);
                Map<String, Object> map = new HashMap<>();
                map.put("createUser", loginUserNumber);
                map.put("pageResourceId", page.getResourceID());
                map.put("spaceResourceId", page.getSpaceResourceId());
                map.put("type", 1);
                this.wikiPageUserTemConfigService.deleteByParam(map);

                //修改记录
                this.wikiPageUserTemConfigService.save(wikiPageUserTemConfig, loginUserNumber);
            }
        }
    }

    @Override
    public List<WikiPage> getDraftPageList(WikiPageDTO dto) {
        return this.wikiPageDao.getDraftPageList(dto);
    }

    @Override
    public PageResult<WikiPage> draftPage(WikiPageDTO dto) {
        PageHelper.startPage(dto.getPageNumber(), dto.getPageSize());
        List<WikiPage> pageList = this.wikiPageDao.getDraftPageList(dto);
        return PageUtil.getPageResult(new PageInfo<>(pageList));
    }

    @Override
    public WikiPage getPage(Long resourceID, String loginUserNumber) {
        WikiPage page = this.wikiPageDao.getPage(resourceID, loginUserNumber);

        WikiPageUserTemConfig wikiPageUserTemConfig = new WikiPageUserTemConfig();
        wikiPageUserTemConfig.setPageResourceId(page.getResourceID());
        wikiPageUserTemConfig.setSpaceResourceId(page.getSpaceResourceId());
        wikiPageUserTemConfig.setType(2);
        Map<String, Object> map = new HashMap<>();
        map.put("createUser", loginUserNumber);
        map.put("pageResourceId", page.getResourceID());
        map.put("spaceResourceId", page.getSpaceResourceId());
        map.put("type", 2);
        this.wikiPageUserTemConfigService.deleteByParam(map);

        //查看记录
        this.wikiPageUserTemConfigService.save(wikiPageUserTemConfig, loginUserNumber);
        return page;
    }

    @Override
    public List<WikiPage> listPageTreeBySpace(Long spaceId, boolean draft) {
        List<WikiPage> allPages = this.wikiPageDao.listPageBySpace(spaceId, draft);
        List<WikiPage> pages = allPages.stream().filter(page -> page.getParentResourceId() == null || page.getParentResourceId() == 0L).collect(Collectors.toList());
        for (WikiPage page : pages) {
            List<WikiPage> children = this.listPageByParent(page.getResourceID(), allPages);
            page.setChildren(children);
        }
        return pages;
    }

    @Override
    public void collectPage(WikiPageUserTemConfig wikiPageUserTemConfig, String loginUserNumber) {
        if (wikiPageUserTemConfig.getCollect().equals(0)) {
            WikiPageUserTemConfig config = this.wikiPageUserTemConfigService.findByResourceID(wikiPageUserTemConfig.getResourceID());
            if (config != null) {
                this.wikiPageUserTemConfigService.delete(config, loginUserNumber);
            }
        } else {
            wikiPageUserTemConfig.setType(3);
            this.wikiPageUserTemConfigService.save(wikiPageUserTemConfig, loginUserNumber);
        }
    }

    @Override
    public void copyPageById(WikiPageCopyDTO copyDto, String loginUserNumber) {
        //修改当前文档
        WikiPage page = this.findByResourceID(copyDto.getPageResourceId());
        if (page != null) {
            WikiPageContent newContent = null;
            WikiPageContent content = this.wikiPageContentService.findByPageResourceId(page.getResourceID());
            if (content != null) {
                newContent = new WikiPageContent();
                newContent.setContent(content.getContent());
                newContent.setTextContent(content.getTextContent());
            }

            page.setParentResourceId(copyDto.getParentResourceId());
            if (copyDto.getSpaceResourceId() != null) {
                page.setSpaceResourceId(copyDto.getSpaceResourceId());
            }
            if (null == page.getDraft()) {
                page.setDraft(false);
            }
            page.setCreateTime(new Date());
            page.setEditTime(new Date());
            page.setVersion(0);
            page.setResourceID(null);
            WikiPage newPage = this.save(page, loginUserNumber);
            if (newContent != null) {
                newContent.setPageResourceId(newPage.getResourceID());
                this.wikiPageContentService.save(newContent, loginUserNumber);
            }

            //复制子文档
            if (copyDto.getFlag() != null && copyDto.getFlag() == 1) {
                List<WikiPage> allPages = this.findAll();
                List<WikiPage> children = this.listPageByParent(copyDto.getPageResourceId(), allPages);
                if (!children.isEmpty()) {
                    List<WikiPageContent> contents = this.wikiPageContentService.findByPageResourceIdIn(children.stream().map(WikiPage::getResourceID).collect(Collectors.toList()));
                    List<WikiPage> newPages = new ArrayList<>();
                    List<WikiPageContent> newContents = new ArrayList<>();
                    this.copySubPage(newPage, children, contents, newPages, newContents);
                    this.save(newPages, loginUserNumber);
                    if (!newContents.isEmpty()) {
                        this.wikiPageContentService.save(newContents, loginUserNumber);
                    }
                }
            }
        }
    }

    @Override
    public void editSpaceIdByPageId(WikiPageCopyDTO wikiPageCopyDTO, String loginUserNumber) {
        WikiPage page = this.findByResourceID(wikiPageCopyDTO.getPageResourceId());
        if (page != null) {
            List<WikiPage> newPages = new ArrayList<>();
            page.setParentResourceId(wikiPageCopyDTO.getParentResourceId());
            page.setSpaceResourceId(wikiPageCopyDTO.getSpaceResourceId());
            newPages.add(page);
            //移动子页面（修改spaceResourceId就行）
            List<WikiPage> allPages = this.findAll();
            List<WikiPage> children = this.listPageByParent(page.getResourceID(), allPages);
            if (!children.isEmpty()) {
                this.moveSubPage(page, children, newPages);
            }
            this.update(newPages, loginUserNumber);
        }
    }

    @Override
    public PageResult<WikiPage> listPageUserByParam(WikiPageUserTemConfig config, String loginUserNumber) {
        PageHelper.startPage(config.getPageNumber(), config.getPageSize());
        config.setCreateUser(loginUserNumber);
        List<WikiPage> wikiPageList = this.wikiPageUserTemConfigService.listPageUserByParam(config);
        return PageUtil.getPageResult(new PageInfo<>(wikiPageList));
    }

    @Override
    public void delPage(Long resourceID, Boolean delChildren, String loginUserNumber) {
        WikiPage wikiPage = this.findByResourceID(resourceID);
        if (wikiPage != null) {
            List<WikiPage> allList = this.findAll();
            List<WikiPage> updateList = new ArrayList<>();
            if (delChildren) {
                this.recursiveDelChildren(wikiPage, allList, updateList);
            } else {
                this.resetChildrenParentId(wikiPage, allList, updateList);
            }
            this.update(updateList, loginUserNumber);
        }
    }

    @Override
    public List<WikiPage> listRecyclePageBySpace(WikiPageDTO wikiPageDTO) {
        return wikiPageDao.listRecyclePageBySpace(wikiPageDTO);
    }

    @Override
    public void clearRecyclePage(Long pageResourceID) {
        WikiPageDTO dto = new WikiPageDTO();
        if (pageResourceID != null && pageResourceID > 0) {
            dto.setPageResourceId(pageResourceID);
        }
        List<WikiPage> recyclePageList = this.listRecyclePageBySpace(dto);
        if (!recyclePageList.isEmpty()) {
            this.deleteInBatch(recyclePageList, "Admin");
        }
    }

//    @Override
//    public String uploadImg(MultipartFile file) {
//        String fileFullPath = "";
//        try {
//            String fileName = UUID.randomUUID() + ".png";
//            if (this.paramConfig.isFtpOn != null && this.paramConfig.isFtpOn) {
//                String path = AttachmentPathGenerator.getWikiPageContentImgPath(AttachmentStoreTypeEnums.FTP, 0L);
//                fileFullPath = this.paramConfig.ftpPath + path + "/" + fileName;
//                FtpUtils.uploadFile(this.paramConfig.ftpPath + path, fileName, file.getInputStream());
//                /*InputStream is = file.getInputStream();
//                ByteArrayOutputStream output = new ByteArrayOutputStream();
//                byte[] buffer = new byte[1024];
//                int n = 0;
//                while (-1 != (n = is.read(buffer))) {
//                    output.write(buffer, 0, n);
//                }
//                byte[] bytes = output.toByteArray(); //in_b为转换之后的结果
//                this.ftpUtil.upload(this.paramConfig.ftpPath + path, fileName, bytes);*/
//            } else {
//                String path = AttachmentPathGenerator.getWikiPageContentImgPath(AttachmentStoreTypeEnums.LOCAL_FILE, 0L);
//                File uploadPath = new File(path);
//                if (!uploadPath.exists()) {
//                    uploadPath.mkdirs();
//                }
//                File toFile = new File(path, fileName);
//                fileFullPath = path + "/" + fileName;
//                OutputStream os = new FileOutputStream(toFile);
//                byte[] buffer = new byte[1024];
//                int length = 0;
//                InputStream is = file.getInputStream();
//                while ((length = is.read(buffer)) > 0) {
//                    os.write(buffer, 0, length);
//                }
//            }
//        } catch (Exception e) {
//            fileFullPath = "";
//            logger.error("上传图片文件异常：{}", e);
//        } finally {
//            return fileFullPath;
//        }
//    }

//    @Override
//    public InputStream getImg(String filePath) {
//        try {
//            if (this.paramConfig.isFtpOn != null && this.paramConfig.isFtpOn) {
//                return FtpUtils.getInputStream(filePath);
//            } else {
//                File file = new File(filePath);
//                if (file.exists()) {
//                    return new FileInputStream(filePath);
//                } else {
//                    return null;
//                }
//            }
//        } catch (Exception e) {
//            logger.error("获取文件失败！", e);
//            return null;
//        }
//    }

    @Override
    public void exportWord(Long resourceID, HttpServletResponse response) {
        WikiPage page = this.findByResourceID(resourceID);
        if (page != null) {
            WikiPageContent wikiPageContent = wikiPageContentService.findByPageResourceId(page.getResourceID());
            try {
                String content = wikiPageContent.getContent();

                // 需要有html、body，META并且指定字符集
                if (!content.contains("<body")) {
                    content = "<html><head><META http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"></head><body>"
                        + content + "</body></html>";
                }

                byte[] b = content.getBytes(StandardCharsets.UTF_8);
                // byte b[] = page.getContent().getBytes();
                ByteArrayInputStream bais = new ByteArrayInputStream(b);

                //生成word
                POIFSFileSystem poifs = new POIFSFileSystem();
                DirectoryEntry directory = poifs.getRoot();
                DocumentEntry documentEntry = directory.createDocument("WordDocument", bais);
                //输出文件
                response.setCharacterEncoding("utf-8");

                response.setContentType("application/msword");
                response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(page.getTitle() + ".doc", "UTF-8"));

                OutputStream outputStream = response.getOutputStream();

                poifs.writeFilesystem(outputStream);

                bais.close();
                outputStream.close();
            } catch (Exception e) {
                logger.error("导出word失败！", e);
            }
        }
    }

    @Override
    public void restore(Long pageResourceID, String loginUserNumber) {
        WikiPage wikiPage = this.findByResourceID(pageResourceID);
        if (wikiPage != null) {
            WikiPage parentPage = this.findByResourceID(wikiPage.getParentResourceId());
            if (parentPage != null) {
                if (parentPage.getInRecycle() != null || !parentPage.getInRecycle()) {
                    // 父节点存在  直接还原
                    wikiPage.setInRecycle(false);
                } else {
                    // 父节点在回收站中  还原到根节点
                    wikiPage.setInRecycle(false);
                    wikiPage.setParentResourceId(0L);
                }
            } else {
                // 父节点不存在   还原到根节点
                wikiPage.setInRecycle(false);
                wikiPage.setParentResourceId(0L);
            }
            this.update(wikiPage, loginUserNumber);
        }
    }

    private void recursiveDelChildren(WikiPage wikiPage, List<WikiPage> allList, List<WikiPage> updateList) {
        wikiPage.setInRecycle(true);
        updateList.add(wikiPage);

        List<WikiPage> children = this.listPageByParent(wikiPage.getResourceID(), allList);
        for (WikiPage page : children) {
            recursiveDelChildren(page, allList, updateList);
        }
    }

    //重新设置子页面的父ID
    private void resetChildrenParentId(WikiPage currentDelPage, List<WikiPage> allPages, List<WikiPage> updateList) {
        currentDelPage.setInRecycle(true);
        updateList.add(currentDelPage);

        Long currentParentId = currentDelPage.getParentResourceId();
        if (currentParentId == null) {
            currentParentId = 0L;
        }
        List<WikiPage> children = listPageByParent(currentDelPage.getResourceID(), allPages);
        if (children.isEmpty()) {
            return;
        }
        for (WikiPage page : children) {
            page.setParentResourceId(currentParentId);
            updateList.add(page);
        }
    }

    private void copySubPage(WikiPage parentPage, List<WikiPage> allPages, List<WikiPageContent> contents, List<WikiPage> newPages,List<WikiPageContent> newContents) {
        allPages.stream().forEach(page -> {
            WikiPageContent newContent = null;
            WikiPageContent content = contents.stream().filter(item -> item.getPageResourceId().equals(page.getResourceID())).findFirst().orElse(null);
            if (content != null) {
                newContent = new WikiPageContent();
                newContent.setContent(content.getContent());
                newContent.setTextContent(content.getTextContent());
            }

            page.setParentResourceId(parentPage.getResourceID());
            page.setSpaceResourceId(parentPage.getSpaceResourceId());
            page.setCreateTime(new Date());
            page.setEditTime(new Date());
            page.setVersion(0);
            page.setResourceID(SnowflakeIdWorker.getInstance().genNextId());
            page.setDraft(parentPage.getDraft());
            newPages.add(page);

            if (newContent!=null) {
                newContent.setPageResourceId(page.getResourceID());
                newContents.add(newContent);
            }

            if (!page.getChildren().isEmpty()) {
                copySubPage(page, page.getChildren(), contents, newPages, newContents);
            }
        });
    }

    private void moveSubPage(WikiPage parentPage, List<WikiPage> allPages, List<WikiPage> newPages) {
        allPages.stream().forEach(page -> {
            page.setSpaceResourceId(parentPage.getSpaceResourceId());
            newPages.add(page);
            if (!page.getChildren().isEmpty()) {
                moveSubPage(page, page.getChildren(), newPages);
            }
        });
    }

    private List<WikiPage> listPageByParent(Long pageResourceId, List<WikiPage> allPages) {
        List<WikiPage> children = allPages.stream().filter(page -> pageResourceId.equals(page.getParentResourceId()) && (page.getInRecycle() == null || !page.getInRecycle()) && (page.getDraft() == null || !page.getDraft())).collect(Collectors.toList());
        if (children.isEmpty()) {
            return children;
        }
        for (WikiPage page : children) {
            List<WikiPage> pageChildren = this.listPageByParent(page.getResourceID(), allPages);
            page.setChildren(pageChildren);
        }
        return children;
    }


}
