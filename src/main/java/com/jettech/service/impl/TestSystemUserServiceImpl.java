package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.dao.idao.ITestSystemUserDao;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.model.SystemModule;
import com.jettech.model.TestSystem;
import com.jettech.model.TestSystemUser;
import com.jettech.model.Trade;
import com.jettech.service.iservice.ISystemModuleService;
import com.jettech.service.iservice.ITestSystemService;
import com.jettech.service.iservice.ITestSystemUserService;
import com.jettech.service.iservice.ITradeService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: TestSystemUserServiceImpl
 * @projectName jettopro
 * @description: 系统和用户关联关系service实现层
 * @date 2019/12/515:11
 */
@Service
@Transactional
public class TestSystemUserServiceImpl extends BaseServiceImpl<TestSystemUser> implements ITestSystemUserService {
    @Autowired
    private ITestSystemUserDao testSystemUserDao;
    @Autowired
    private ITestSystemService iTestSystemService;
    @Autowired
	private ISystemModuleService systemModuleService;
    @Autowired
	private IFeignDataDesignToBasicService feignDataDesignToBasicService;
    @Autowired
	private ITradeService tradeService;
    @PostConstruct
    public void postConstruct(){
        baseDao = testSystemUserDao;
    }

    /**
     * @Title: findbyTestSystemResourceID
     * @Description: 根据被测系统查询关联关系
     * @Param: "[testSystemResourceID]"
     * @Return: "java.util.List<TestSystemUser>"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    @Override
    public List<TestSystemUser> findbyTestSystemResourceID(String testSystemResourceID) {
        return testSystemUserDao.findbyTestSystemResourceID(testSystemResourceID);
    }

	@Override
	public Boolean addTestSystemModul(String testSystemResourceIDs, Long userresourceID, String userNumber) {
		String[] split = testSystemResourceIDs.split(",");
		if (testSystemResourceIDs=="") {
			Boolean deleteTestSystemModul = deleteTestSystemModul(String.valueOf(userresourceID),userNumber);
			if (deleteTestSystemModul==false) {
				return false;
			}
			return true;
		}
		List<String> list = new ArrayList<>();
		for (int i = 0; i < split.length; i++) {
			list.add(split[i]);
		}
		List<TestSystem> findByResourceIDIn = iTestSystemService.findByResourceIDIn(list);
		if (!(findByResourceIDIn.size()==list.size())) {
			return false;
		}
		List<TestSystemUser> arrayList = new ArrayList<>();
		for (String testmodul : list) {
			TestSystemUser testSystemUser = new TestSystemUser();
			
			testSystemUser.setTestSystemResourceID(Long.valueOf(testmodul));
			testSystemUser.setUserResourceID(userresourceID);
			arrayList.add(testSystemUser);
		}
		Boolean deleteTestSystemModul = deleteTestSystemModul(String.valueOf(userresourceID),userNumber);
		if (deleteTestSystemModul==false) {
			return false;
		}
		this.save(arrayList, userNumber);
		return true;
	}

	@Override
	public Boolean deleteTestSystemModul(String resourceID,String userNumber) {
		String[] split = resourceID.split(",");
		List<String> arrayList = new ArrayList<>();
		for (String string : split) {
			arrayList.add(string);
		}
		List<TestSystemUser> users=testSystemUserDao.findUserResourceID(arrayList);
		if (users.size()==0) {
			return true;
		}
		this.deleteInBatch(users, userNumber);
		return true;
	}

	@Override
	public boolean addTestsystemAndUsers(List<TestSystemUser> TestSystemUsers,String userNumber) {
		this.save(TestSystemUsers, userNumber);
		return true;
	}

	/**
	 * @Title: isOperationAuthority
	 * @description: 登录用户是否有操作案例库的权限
	 * @param "[userNumber]"
	 * @return com.jettech.dto.Result
	 * @throws
	 * <AUTHOR>
	 * @date 2020/1/14 11:18
	 */
	@Override
	public Result isOperationAuthority(String userNumber) {
		TestSystemUser testSystemUser = testSystemUserDao.isOperationAuthority(userNumber);
		if(testSystemUser == null) Result.renderSuccess(false);
		return Result.renderSuccess(true);
	}
    /**
     * 通过系统查询系统下面人员表所有系统人员
     * findbyTestSystemResourceIDsIn
     * @param testSystemResourceIDs
     * @return
     * cao_jinbao
     */
	@Override
	public List<TestSystemUser> findbyTestSystemResourceIDsIn(List<String> testSystemResourceIDs) {
		
		return testSystemUserDao.findbyTestSystemResourceIDsIn(testSystemResourceIDs);
	}


	/**
	 * @Title: findByUserNumber
	 * @description: 查询当前登录用户是否和被测系统关联
	 * @param "[nodeType, nodeResourceID, userNumber]"
	 * @return com.jettech.dto.Result
	 * @throws
	 * <AUTHOR>
	 * @date 2020/2/23 14:30
	 */
	@Override
	public Result findByUserNumber(String nodeType, String nodeResourceID, String userNumber) {
		String token = HttpRequestUtils.getCurrentRequestToken();
		JettechUserDTO user = feignDataDesignToBasicService.findByNumber(userNumber,token);
		Long userResourceID = user.getResourceID();
		if("module".equals(nodeType)){
			SystemModule sm = systemModuleService.findByResourceID(Long.valueOf(nodeResourceID));
			nodeResourceID = sm.getTestSystemResourceID().toString();
		}else if("trade".equals(nodeType)){
			Trade trade = tradeService.findByResourceID(Long.valueOf(nodeResourceID));
			nodeResourceID = trade.getTestSystemResourceID().toString();
		}
		TestSystemUser testSystemUser = testSystemUserDao.findByTestSystemResourceIDAndUserNumber(Long.valueOf(nodeResourceID),userResourceID);
		if(testSystemUser == null){
			return	Result.renderSuccess("false");
		}else{
			return	Result.renderSuccess("true");
		}
	}

	/**
	 * @param name
	 * @param deptName
	 * @param testSystemResourceID
	 * @param pageRequest
	 * @return
	 * @Title: findNotRelateUser
	 * @Description: 查询未关联的人员
	 * <AUTHOR>
	 * @date 2020年9月9日 下午5:05:58
	 */
	@Override
	public Result<?> findNotRelateUser(String name, String deptName, String testSystemResourceID, String userGroupReourceID, PageRequest pageRequest) {
		if(testSystemResourceID==null || "".equals(testSystemResourceID)) {
			return Result.renderError("系统的resourceID为空！");
		}
		List<String> userGroupReourceIDs = null;
		if (StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		Page<Map<String, Object>> list=testSystemUserDao.findNotRelateUser(name,deptName,Long.valueOf(testSystemResourceID),userGroupReourceIDs,pageRequest);
		return Result.renderSuccess(list);
	}

	/**
	 * @param name
	 * @param deptName
	 * @param testSystemResourceID
	 * @param pageRequest
	 * @return
	 * @Title: findRelatedUser
	 * @Description: 查询已关联人员
	 * <AUTHOR>
	 * @date 2020年9月9日 下午5:28:52
	 */
	@Override
	public Result<?> findRelatedUser(String name, String deptName, String testSystemResourceID, String userGroupReourceID, PageRequest pageRequest) {
		if(testSystemResourceID==null || "".equals(testSystemResourceID)) {
			return Result.renderError("系统的resourceID为空！");
		}
		List<String> userGroupReourceIDs = null;
		if (StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		Page<Map<String, Object>> list=testSystemUserDao.findRelatedUser(name,deptName,Long.valueOf(testSystemResourceID),userGroupReourceIDs,pageRequest);
		return Result.renderSuccess(list);
	}

	/**
	 * @param userResourceIDs
	 * @param testSystemResourceID
	 * @param userNumber
	 * @return
	 * @Title: relateUser
	 * @Description: 关联人员
	 * <AUTHOR>
	 * @date 2020年9月4日 下午5:28:52
	 */
//	@Override
//	public Result<?> relateUser(String userResourceIDs, String testSystemResourceID, String userNumber) {
//		if(testSystemResourceID==null || "".equals(testSystemResourceID)) {
//			return Result.renderError("系统的resourceID为空！");
//		}
//		if(userResourceIDs==null || "".equals(userResourceIDs)) {
//			return Result.renderError("请选择人员！");
//		}
//		TestSystem testSystem=iTestSystemService.findByResourceID(Long.valueOf(testSystemResourceID));
//		if(testSystem==null) {
//			return Result.renderError("系统不存在！");
//		}
//		String[] arr=userResourceIDs.split(",");
//		List<TestSystemUser> list=new ArrayList<>();
//		for (String str : arr) {
//			if(str!=null && !"".equals(str)) {
//				TestSystemUser testSystemUser=new TestSystemUser();
//				testSystemUser.setTestSystemResourceID(testSystem.getResourceID());
//				testSystemUser.setUserResourceID(Long.valueOf(str));
//				list.add(testSystemUser);
//			}
//		}
//		if(!list.isEmpty()) {
//			save(list, userNumber);
//		}
//		return Result.renderSuccess();
//	}
	
	/**
	 * 
	 * @Description 多个系统同时关联人员
	 * @param userResourceIDs
	 * @param testSystemResourceIDs
	 * @param userNumber
	 * @return
	 * tianliping
	 * 2022年12月21日
	 */
	
	public Result<?> relateUser(String userResourceIDs, String testSystemResourceID, String userNumber) {
		if(testSystemResourceID==null || "".equals(testSystemResourceID)) {
			return Result.renderError("系统的resourceID为空！");
		}
		if(userResourceIDs==null || "".equals(userResourceIDs)) {
			return Result.renderError("请选择人员！");
		}
		
		List<TestSystemUser> list=new ArrayList<>();
		String[] sysarr=testSystemResourceID.split(",");
		//查询系统和人员的关联信息
		List<String> systemResourceIDList = Arrays.asList(sysarr);
		List<TestSystemUser>  systemuserLists = this.findbyTestSystemResourceIDsIn(systemResourceIDList);
		Map<Long, List<TestSystemUser>> collectMap = systemuserLists.stream().collect(Collectors.groupingBy(TestSystemUser::getTestSystemResourceID));
	    //处理数据
		for(String systemResourceID :sysarr) {
			TestSystem testSystem=iTestSystemService.findByResourceID(Long.valueOf(systemResourceID));
			if(testSystem==null) {
				return Result.renderError("系统不存在！");
			}
			List<TestSystemUser> systemuserList = collectMap.get(Long.valueOf(systemResourceID));
			//系统下关联的人员

			if(systemuserList==null){
				systemuserList = new ArrayList<>();
			}
			List<String> haveUserlist = systemuserList.stream().map(x->x.getUserResourceID().toString()).collect(Collectors.toList());
			String[] arr=userResourceIDs.split(",");
			for (String str : arr) {
				if(!StringUtils.isBlank(str) && !haveUserlist.contains(str)) { //已存在的关联人员不包括当前数据
					TestSystemUser testSystemUser=new TestSystemUser();
					testSystemUser.setTestSystemResourceID(testSystem.getResourceID());
					testSystemUser.setUserResourceID(Long.valueOf(str));
					list.add(testSystemUser);
				}
			}
		}
		if(!list.isEmpty()) {
			save(list, userNumber);
		}
		return Result.renderSuccess();
	}

	/**
	 * @param userResourceIDs
	 * @param testSystemResourceID
	 * @param userNumber
	 * @return
	 * @Title: cancelRelatedUser
	 * @Description: 取消关联人员
	 * <AUTHOR>
	 * @date 2020年9月4日 下午5:28:52
	 */
	@Override
	public Result<?> cancelRelatedUser(String userResourceIDs, String testSystemResourceID, String userNumber) {
		if(testSystemResourceID==null || "".equals(testSystemResourceID)) {
			return Result.renderError("系统的resourceID为空！");
		}
		if(userResourceIDs==null || "".equals(userResourceIDs)) {
			return Result.renderError("请选择人员！");
		}
		String[] sysarr=testSystemResourceID.split(",");
		//查询系统和人员的关联信息
		List<String> systemResourceIDList = Arrays.asList(sysarr);
		List<TestSystemUser>  systemuserLists = this.findbyTestSystemResourceIDsIn(systemResourceIDList);
		Map<Long, List<TestSystemUser>> collectMap = systemuserLists.stream().collect(Collectors.groupingBy(TestSystemUser::getTestSystemResourceID));
		List<TestSystemUser> removeTestSystemUserlist=new ArrayList<>();
		for(String systemResourceID:sysarr) {
			TestSystem testSystem=iTestSystemService.findByResourceID(Long.valueOf(systemResourceID));
			if(testSystem==null) {
				return Result.renderError("系统不存在！");
			}
			List<TestSystemUser> systemuserList = collectMap.get(Long.valueOf(systemResourceID));
			//系统下关联的人员
			List<String> haveUserlist = systemuserList.stream().map(x->x.getUserResourceID().toString()).collect(Collectors.toList());
			Map<Long, List<TestSystemUser>> userMap = systemuserList.stream().collect(Collectors.groupingBy(TestSystemUser::getUserResourceID));

			String[] arr=userResourceIDs.split(",");
 		   for (String str : arr) {
				if(!StringUtils.isBlank(str) && haveUserlist.contains(str)) {
					List<TestSystemUser> list = userMap.get(Long.valueOf(str));
					removeTestSystemUserlist.addAll(list);
				}
 			 }
		}
//		List<Long> list=new ArrayList<>();
//		for (String str : arr) {
//			if(str!=null && !"".equals(str)) {
//				list.add(Long.valueOf(str));
//			}
//		 }
//		}
//		if(list.size()>0)
//		testSystemUserDao.deletebytestSystemtouser(Long.valueOf(testSystemResourceID),list);
		if(removeTestSystemUserlist.size()>0){
			testSystemUserDao.deleteInBatch(removeTestSystemUserlist);
		}
		
		return Result.renderSuccess();
	}
	
	
    /**
     * 
     * @Description 多系统查询关联人员
     * @param name
     * @param deptName
     * @param testSystemResourceID
     * @param userGroupReourceID
     * @param pageRequest
     * @return
     * tianliping
     * 2022年12月21日
     */
	@Override
	public Result<?> findRelatedUserOfMultipleSystem(String name, String deptName, String testSystemResourceID,
			String userGroupReourceID, PageRequest pageRequest) {
		if(testSystemResourceID==null || "".equals(testSystemResourceID)) {
			return Result.renderError("系统的resourceID为空！");
		}
		List<String> userGroupReourceIDs = null;
		if (StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		List<String> testSystemResourceIDs = null;
		if (StringUtils.isNotBlank(testSystemResourceID)) {
			testSystemResourceIDs = Arrays.asList(testSystemResourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		Page<Map<String, Object>> list=testSystemUserDao.findRelatedUserOfMultipleSystem(name,deptName,testSystemResourceIDs,userGroupReourceIDs,pageRequest);
		return Result.renderSuccess(list);
	}
	/**
	 * 
	 * @Description 多系统查询未关联人员
	 * @param name
	 * @param deptName
	 * @param testSystemResourceID
	 * @param userGroupReourceID
	 * @param pageRequest
	 * @return
	 * tianliping
	 * 2022年12月21日
	 */
	@Override
	public Result<?> findNotRelatedUserOfMultipleSystem(String name, String deptName, String testSystemResourceID, String userGroupReourceID, PageRequest pageRequest) {
		if(testSystemResourceID==null || "".equals(testSystemResourceID)) {
			return Result.renderError("系统的resourceID为空！");
		}
		List<String> userGroupReourceIDs = null;
		if (StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		List<String> testSystemResourceIDs = null;
		if (StringUtils.isNotBlank(testSystemResourceID)) {
			testSystemResourceIDs = Arrays.asList(testSystemResourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		Page<Map<String, Object>> list=testSystemUserDao.findNotRelatedUserOfMultipleSystem(name,deptName,testSystemResourceIDs,userGroupReourceIDs,pageRequest);
		return Result.renderSuccess(list);
	}


}
