package com.jettech.service.impl.bugmanager;

import com.alibaba.fastjson.JSON;
import com.google.common.io.Files;
import com.jettech.common.dto.BaseMessage;
import com.jettech.common.dto.FileInfo;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.basic.FieldConfigurationDTO;
import com.jettech.common.dto.basic.JettechUserDeptDTO;
import com.jettech.common.dto.defect.DefectConfigDTO;
import com.jettech.common.dto.defect.DefectInfoDTO;
import com.jettech.common.dto.trp.DataDictionaryDTO;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.enums.AttachmentStoreTypeEnums;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.redis.JedisClient;
import com.jettech.common.util.*;
import com.jettech.common.util.exception.JettechException;
import com.jettech.dao.idao.bugmanager.IDefectDao;
import com.jettech.dao.idao.processconfig.IProcessConfigureDao;
import com.jettech.exception.DefectImportException;
import com.jettech.feign.IFeignBugToBasic;
import com.jettech.feign.IFeignBugToDataDesign;
import com.jettech.feign.IFeignBugToFileService;
import com.jettech.feign.IFeignBugToManexecute;
import com.jettech.model.*;
import com.jettech.service.impl.BaseServiceImpl;
import com.jettech.service.impl.SnowflakeIdWorker;
import com.jettech.service.iservice.bugmanager.IDefectFileService;
import com.jettech.service.iservice.bugmanager.IDefectOperationHistoryService;
import com.jettech.service.iservice.bugmanager.IDefectService;
import com.jettech.service.iservice.processconfig.*;
import com.jettech.util.DateUtil;
import com.jettech.util.FtpUtil;
import com.jettech.util.*;
import com.jettech.util.jedis.RedisCacheConfig;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Whitelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.util.FtpUtil.input2byte;

/**
 * <AUTHOR>
 * @ClassName: DefectServiceImpl
 * @Description: 缺陷管理
 * @date 2020年8月19日
 */
@Service
public class DefectServiceImpl extends BaseServiceImpl<Defect> implements IDefectService {

    // 日志对象
    private final static Logger logger = LoggerFactory.getLogger(DefectServiceImpl.class);

    @Autowired
    private IDefectDao defectDao;
    @Autowired
    private IFeignBugToBasic feignBugToBasic;
    @Autowired
    private IFeignBugToDataDesign feignBugToDataDesign;
    @Autowired
    private IFeignBugToManexecute feignBugToManexecute;
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private DataDicUtil dataDicUtil;
    @Autowired
    private DefectMaintainServiceImpl defectMaintainService;
    @Autowired
    private IDefectService defectService;
    @Autowired
    private SerialGenerator serialGenerator;
    @Value("${use_file_service}")
    private boolean useFileService;
    @Autowired
    private IFeignBugToFileService feignBugToFileService;
    //缺陷编号开头
    private static final String BUG_FRONT = "BUG_";
    //开发人员
    private static final String DEVELOPER = "开发人员";
    //缺陷流程配置状态开始
    private static final String START = "start";
    //缺陷流程配置状态结束
    private static final String END = "end";

    private static final String FINAL_STR = "靐爨";
    //格式化缺陷编号
    private static final NumberFormat format = new DecimalFormat("000000");

    public static final String DEFECT_UPLOAD_IMAGE_CMD = "defect_upload_image_cmd";

    public static final String DEFECT_FILE_TMP_DIR = System.getProperty("java.io.tmpdir") + File.separator + "defecttmp";

    public static final String DEFECT_SERIAL_KEY = "defect_serial_key";


//	public static Object OBJ = new Object();
//
//	public static Object getOBJ() {
//		return OBJ;
//	}

    @Autowired
    private ParamConfig paramConfig;
    @Autowired
    private FtpUtil ftpUtil;

    private static final List<String> FieldValidate = new ArrayList<String>() {{
        //创建人createUserName
        add("createUserName");
        //处理人handleUser
        add("handleUser");
        //归属开发人员/缺陷的责任人belongDeveloperName
        add("belongDeveloperName");
        //被测系统testSystem
        add("testSystem");
        //所属模块subordinateFeature
        add("subordinateFeature");
        //所属交易subordinateTrade
        add("subordinateTrade");
        //缺陷归属系统subordinateSystem
        add("subordinateSystem");
        //所属项目subordinateProject
        add("subordinateProject");
        //所属需求subordinateDemand
        add("subordinateDemand");
        //缺陷责任系统defectDutySystem
        add("defectDutySystem");
    }};
    /**
     * 缺陷和附件关联表
     */
    @Autowired
    private IDefectFileService defectFileService;
    /**
     * 流程配置状态表
     */
    @Autowired
    private IProcessStateService processStateService;
    /**
     * 流转配置表
     */
    @Autowired
    private IProcessConfigureService processConfigureService;
    /**
     * 工具类
     */
    @Autowired
    private CompareDefectFieldChangeUtils compareDefectFieldChangeUtils;
    /**
     * 历史表service
     */
    @Autowired
    private IDefectOperationHistoryService defectOperationHistoryService;
    /**
     * 流转状态和用户组关系
     */
    @Autowired
    private IProcessConfigureUserGroupService processConfigureUserGroupService;
    /**
     * 流转状态和属性（是否高亮显示等）
     */
    @Autowired
    private IProcessConfigureFieldService processConfigureFieldService;
    /**
     * 流转人设置
     */
    @Autowired
    private ITransferorConfigureService transferorConfigureService;
    @Autowired
    private IDefectProcessService defectProcessService;

    @Autowired
    private RedisUtils redisUtils;

//	@Autowired
//	private PrivideInterfaceUtils privideInterfaceUtils;

    @Autowired
    private JedisClient jedisUtil;

    @Autowired
    private IProcessConfigureDao processConfigureDao;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = defectDao;
    }

    //'默认流转人，0-创建人，1-修改人，2-处理人，3-当前登录用户'
    private static final String CREATE_USER = "0";
    private static final String UPDATE_USER = "1";
    private static final String HANDLE_USER = "2";
    private static final String LOGIN_USER = "3";

    /**
     * @param @param             defect
     * @param @return            参数
     * @param imageDirResourceID
     * @return Result<?>    返回类型
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws
     * @Title: addDefect
     * @Description: 添加缺陷
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> addDefect(Defect defect, MultipartFile[] files, String filesIDs, String imageDirResourceID) throws Exception {
        //查询字段配置
        Result<?> defectConfig = feignBugToBasic.findDefectBasicConfig("defect", "");
        if (defectConfig == null || defectConfig.getObj() == null) {
            return Result.renderError("读取缺陷配置文件失败！");
        }
        Map<String, FieldConfigurationDTO> baseConfigMap = this.convertConfigurationData(defectConfig);
        //校验启用字段必输项
        Set<String> usingFields = baseConfigMap.keySet();
        Field[] fields = defect.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            //缺陷编号新建时候过滤掉
            if ("defectNumber".equals(fieldName) || "subordinateProject".equals(fieldName)) {
                continue;
            }
            //启用字段
            if (usingFields.contains(fieldName)) {
                Object fieldValue = field.get(defect);

                //必输字段是否为空
                if (baseConfigMap.get(fieldName).isWriteRequired() && (fieldValue == null || StringUtils.isBlank(String.valueOf(fieldValue)))) {
                    return Result.renderError("已启用的配置字段：" + baseConfigMap.get(fieldName).getAliasName() + "为必输字段！");
                }
            }
        }
        //缺陷编号
        JettechUserDTO jettechUserDTO = null;
        String userKey = RedisCacheConfig.DEFECT_FIND_TRANSF_USERNUMBER + defect.getCreateUser();
        Object userDTO = this.jedisUtil.getObject(userKey);
        if (userDTO == null) {
            jettechUserDTO = feignBugToBasic.findByNumber(defect.getCreateUser(), "");
            this.jedisUtil.setObject(userKey, jettechUserDTO, 60 * 30);
        } else {
            jettechUserDTO = (JettechUserDTO) userDTO;
        }
        if (jettechUserDTO == null) {
            return Result.renderError("当前用户不存在!");
        }
        defect.setCreateUserName(jettechUserDTO.getUserName());
        //所属项目
        String projectKey = RedisCacheConfig.DEFECT_UPDATE_PROJECT_NAME;
        String projectField = String.valueOf(defect.getSubordinateProjectResourceID());
        Object projectNameObj = this.jedisUtil.getHsetValue(projectKey, projectField);
        String projectName = null;
        if (projectNameObj == null) {
            //所属项目
            Result<?> projectResult = feignBugToDataDesign.findTestProjectByResourceID(String.valueOf(defect.getSubordinateProjectResourceID()));
            projectName = (String) projectResult.getObj();
            this.jedisUtil.setHash(projectKey, projectField, projectName, 5 * 60);
        } else {
            projectName = String.valueOf(projectNameObj);
        }
        defect.setSubordinateProject(projectName);
        //处理需求名称，有可能是编号和名称拼在一起的
        if (!org.springframework.util.StringUtils.isEmpty(defect.getSubordinateDemand())) {
            if (defect.getSubordinateDemand().indexOf("---") > 0) {
                defect.setSubordinateDemand(defect.getSubordinateDemand().split("---")[1]);
            }
        }
        //重新打开次数新建时计0
        defect.setReopenTimes("0");
        //添加流转记录
        Map<Object, Object> processStateDic = redisUtils.getHashEntries("缺陷流程状态配置");
        Map<String, String> processStateDictionary = new HashMap<String, String>();
        for (Object key : processStateDic.keySet()) {
            processStateDictionary.put(String.valueOf(key), String.valueOf(processStateDic.get(key)));
        }

        String transferRecord = DateUtil.getDateStr(new Date(), DateUtil.TIME_PATTREN) + " " + jettechUserDTO.getUserName() + " 将缺陷指派给：" + defect.getHandleUser() + "（" + processStateDictionary.get(defect.getDefectState()) + "）";

        //处理缺陷注释
        if (!StringUtils.isBlank(defect.getExplanatoryNote())) {
            String newNote = defect.getExplanatoryNote();
            String newStr = "注释内容：" + newNote;
            defect.setExplanatoryNote(transferRecord + FINAL_STR + newStr);
        } else {
            defect.setExplanatoryNote(transferRecord);
        }

        defect.setDefectNumber(this.generateDefectNumber());
        defect = this.save(defect, defect.getCreateUser());

        this.uploadDefectFiles(String.valueOf(defect.getResourceID()), files, defect.getCreateUser(), filesIDs, imageDirResourceID);
        //添加缺陷的消息通知
        if (defect.getHandleUserNumber() != null && !defect.getHandleUserNumber().equals("")) {
            Map<String, Object> notifyMap = new HashMap<>();
            notifyMap.put("type", "1");//消息类型：1-缺陷，2-要求
            notifyMap.put("level", "");
            if (!org.springframework.util.StringUtils.isEmpty(defect.getSeverity())) {
                notifyMap.put("level", defect.getSeverity());
            }
            notifyMap.put("content", defect.getDefectTitle());
            notifyMap.put("allotUserName", jettechUserDTO.getUserName());
            notifyMap.put("allotUserNumber", defect.getCreateUser());
            notifyMap.put("handleUserName", defect.getHandleUser());
            notifyMap.put("handleUserNumber", defect.getHandleUserNumber());
            notifyMap.put("allotTime", defect.getCreateTime());
            notifyMap.put("targetResourceID", defect.getResourceID());
            notifyMap.put("operaterUserNumber", defect.getCreateUser());
            Result<?> result2 = feignBugToBasic.addAndUpdataDefectNotify(notifyMap);
            if (!result2.isSuccess()) {
                return Result.renderError("新增缺陷消息失败");
            }
        }
        return Result.renderSuccess(defect);
    }

    /**
     * @param @return 参数
     * @return long    返回类型
     * @throws
     * @Title: generateResourceID
     * @Description: 生成ResourceID
     * <AUTHOR>
     */
    private long generateResourceID() {
        SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
        return worker.genNextId();
    }

    /**
     * @param @return 参数
     * @return List<FieldConfigurationDTO>    返回类型
     * @throws
     * @Title: convertConfigurationData
     * @Description: 处理数据
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    private Map<String, FieldConfigurationDTO> convertConfigurationData(Result defectConfig) {
        List<Map<String, Object>> configurations = (List<Map<String, Object>>) defectConfig.getObj();
        Map<String, FieldConfigurationDTO> mapResult = new HashMap<String, FieldConfigurationDTO>();
        //处理成只有已启用的数据
        for (Map<String, Object> map : configurations) {
            if (!Boolean.valueOf(String.valueOf(map.get("usingenable")))) {
                continue;
            }
            FieldConfigurationDTO model = new FieldConfigurationDTO();
            model.setDefaultValue(String.valueOf(map.get("defaultValue")));//默认值
            model.setWriteRequired(Boolean.valueOf(String.valueOf(map.get("writeRequired"))));//是否必输
            model.setName(String.valueOf(map.get("name")));//字段名
            model.setNameDescription(String.valueOf(map.get("nameDescription")));//字段中文名
            model.setAliasName(String.valueOf(map.get("aliasName")));//字段中文别名
            model.setDefaultValue(String.valueOf(map.get("defaultValue")));//默认值
            mapResult.put(model.getName(), model);
        }
        return mapResult;
    }

    /**
     * @param @return 参数
     * @return String    返回类型
     * @throws
     * @Title: generateDefectNumber
     * @Description: 缺陷编号全库唯一的（编号规则按BUG_5位顺序号，超过5位递增，保证唯一性，顺序性）
     * <AUTHOR>
     */
    private String generateDefectNumber() {
        String[] serials = serialGenerator.batchNextSerialWithPrefix(DEFECT_SERIAL_KEY, 1, 8, () -> {
            String defectNumber = defectDao.findMaxDefectNumber();
            if (defectNumber == null || "".equals(defectNumber)) {
                return 0L;
            }
            String tmp = defectNumber.substring(4);
            return Long.parseLong(tmp);
        });

        return BUG_FRONT + serials[0];
    }

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findDefectConfigDefaultValue
     * @Description: 缺陷新建页面查询配置默认值（缺陷摘要，缺陷描述，注释等）
     * <AUTHOR>
     */
    @Override
    public Result<?> findDefectConfigDefaultValue() {
        //查询字段配置
        Result<?> defectConfig = feignBugToBasic.findDefectBasicConfig("defect", "");
        if (defectConfig == null || defectConfig.getObj() == null) {
            return Result.renderError("读取缺陷配置文件失败！");
        }
        Map<String, FieldConfigurationDTO> baseConfigMap = this.convertConfigurationData(defectConfig);
        Map<String, String> mapResult = new HashMap<>();
        //缺陷摘要
        mapResult.put("defectTitle", baseConfigMap.get("defectTitle").getDefaultValue() == null ? "" : baseConfigMap.get("defectTitle").getDefaultValue());
        //缺陷描述
        mapResult.put("descriptionAndExpectresult", baseConfigMap.get("descriptionAndExpectresult").getDefaultValue() == null ? "" : baseConfigMap.get("descriptionAndExpectresult").getDefaultValue());
        //注释
        mapResult.put("explanatoryNote", baseConfigMap.get("explanatoryNote").getDefaultValue() == null ? "" : baseConfigMap.get("explanatoryNote").getDefaultValue());

        return Result.renderSuccess(mapResult);
    }


    /**
     * @param @param  request
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findDefectDealPersons
     * @Description:查询项目下的处理人(可以根据机构名称和用户姓名进行模糊查询)处理人初始化为项目下所有有权限流转新建状态缺陷的人
     * <AUTHOR>
     */
    @Override
    public Result<?> findDefectDealPersons(String projctResourceID, String name) {
        if (StringUtils.isAllBlank(name)) {
            name = null;
        }
        //当前缺陷所属项目的流程
        DefectProcess defectProcess = defectProcessService.findDefectprocessByProjectResourceID(projctResourceID);
        //当前流程下的所有流程配置
        List<ProcessConfigure> processConfigures = processConfigureService.findByDefectProcessResourceID(String.valueOf(defectProcess.getResourceID()));
        //当前流程下的所有流程状态
        List<ProcessState> processStates = processStateService.findByDefectProcessRID(defectProcess.getResourceID());


        //查找当前缺陷所属项目下的人员
        List<JettechUserDeptDTO> listResult = defectDao.findDefectDealPersons(projctResourceID, name);

        return Result.renderSuccess(listResult);
    }

    /**
     * @param defectFileMap
     * @param @param        resourceID
     * @param @param        fileMap    参数
     * @return void    返回类型
     * @throws IOException
     * @throws
     * @Title: saveAndUpdateAttachment
     * @Description: 保存附件
     * <AUTHOR>
     */
    private void saveAndUpdateAttachment(Map<String, Object> fileMap, Map<String, DefectFile> defectFileMap) throws IOException {

        Set<String> resourceIDs = fileMap.keySet();
        for (String resourceID : resourceIDs) {
            Object fileObj = fileMap.get(resourceID);

            InputStream inputStream = null;
            if (fileObj instanceof MultipartFile) {
                inputStream = ((MultipartFile) fileObj).getInputStream();
            } else if (fileObj instanceof File) {
                inputStream = new FileInputStream((File) fileObj);
            }
            if (inputStream == null) {
                continue;
            }
            String folderPath = "";
            String fileName = "";
            ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
            byte[] buff = new byte[100]; //buff用于存放循环读取的临时数据
            int rc = 0;
            FileOutputStream outPutStream = null;
            try {
                while ((rc = inputStream.read(buff, 0, 100)) > 0) {
                    byteStram.write(buff, 0, rc);
                }
                byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果
                fileName = resourceID + defectFileMap.get(resourceID).getExtname();
                if (paramConfig.getIsFtpOn()) {//判断ftp是否打开

                    folderPath = paramConfig.getFtpPath() + defectFileMap.get(resourceID).getPath();

                    //上传FTP
                    ftpUtil.upload(folderPath, fileName, bytes);

                } else {
                    folderPath = paramConfig.getAttachmentPath();

                    if (!FileValidUtil.valid(folderPath + defectFileMap.get(resourceID).getPath() + File.separator + fileName)) {
                        throw new RuntimeException("文件路径不合法！");
                    }

                    File dir = new File(folderPath + defectFileMap.get(resourceID).getPath());
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }

                    File file = new File(folderPath + defectFileMap.get(resourceID).getPath() + File.separator + fileName);

                    outPutStream = new FileOutputStream(file);

                    outPutStream.write(bytes);

                    outPutStream.flush();

                    outPutStream.close();

                }
            } catch (Exception e1) {

            } finally {
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }
                    if (outPutStream != null) {
                        outPutStream.close();
                    }
                } catch (Exception e) {

                }
            }
        }
    }

    /**
     * @param resourceID
     * @return
     * @Title: findAttachmentList
     * @Description: 获取附件列表
     * <AUTHOR>
     */
    @Override
    public Result<?> findAttachmentList(String resourceID) {
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("缺陷的resourceID为空！");
        }
        String key = RedisCacheConfig.DEFECT_FIND_DEFECTFILE_BY_RID;
        List<DefectFile> listFiles = null;
        List<FileInfo> fileInfoList = null;
        Object dfList = this.jedisUtil.getHsetValue(key, resourceID);
        if (dfList == null) {
            if (useFileService) {
                Result<List<FileInfo>> fileInfos = feignBugToFileService.getFileInfoList(Long.parseLong(resourceID), ObjectTypeEnum.DEFECT.getValue());
                this.jedisUtil.setHash(key, resourceID, listFiles, 8 * 60);
                return fileInfos;
            } else {
                listFiles = defectFileService.findByDefectResourceID(Long.valueOf(resourceID));
                this.jedisUtil.setHash(key, resourceID, listFiles, 8 * 60);
                return Result.renderSuccess(listFiles);
            }

        } else {
            if (useFileService) {
                fileInfoList = (List<FileInfo>) dfList;
                return Result.renderSuccess(fileInfoList);
            } else {
                listFiles = (List<DefectFile>) dfList;
                return Result.renderSuccess(listFiles);
            }

        }

    }

    /**
     * @param resourceIDs
     * @return
     * @Title: deleteAttachmentList
     * @Description: 删除附件
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> deleteAttachmentList(String resourceIDs, String userNumber) {

        if (resourceIDs == null || "".equals(resourceIDs)) {
            return Result.renderError("附件的resourceIDs为空！");
        }
        if (useFileService) {
            feignBugToFileService.deleteFile(resourceIDs);
        } else {
            String[] rids = resourceIDs.split(",");
            List<DefectFile> defectFiles = defectFileService.findByResourceIDIn(Arrays.asList(resourceIDs.split(",")));
            Map<String, DefectFile> map = new HashMap<>();
            defectFiles.stream().forEach(x -> {
                map.put(String.valueOf(x.getResourceID()), x);
            });
            if (paramConfig.getIsFtpOn()) {//判断ftp是否打开
                for (String resourceID : rids) {
                    Long defectResourceID = map.get(resourceID).getDefectResourceID();
                    ftpUtil.removeFile(paramConfig.getFtpPath() + map.get(resourceID).getPath() + File.separator, resourceID + map.get(resourceID).getExtname());
                }

            } else {
                for (String resourceID : rids) {
                    File dir = new File(paramConfig.getAttachmentPath());
                    if (dir.isDirectory()) {
                        File[] fiels = dir.listFiles();
                        for (File file : fiels) {
                            String name = file.getName();
                            if (name.equals(resourceID + map.get(resourceID).getExtname())) {
                                file.delete();
                            }
                        }
                    }
                }
            }
            if (!defectFiles.isEmpty()) {
                defectFileService.deleteInBatch(defectFiles, userNumber);
            }
        }
        //aop进不去
        String fileKey = RedisCacheConfig.DEFECT_FIND_DEFECTFILE_BY_RID;
        if (this.jedisUtil.existsObjectKey(fileKey)) {
            this.jedisUtil.delObject(fileKey);
        }
        return Result.renderSuccess("操作成功！");
    }

    /**
     * @param resourceID
     * @param exName
     * @param path
     * @return
     * @Title: getFileData
     * @Description: 获取附件
     * <AUTHOR>
     */
    @Override
    public byte[] getFileData(String resourceID, String exName, String path) {

        InputStream in = null;
        if (paramConfig.getIsFtpOn()) {//判断ftp是否打开
            in = ftpUtil.startDown(paramConfig.getFtpPath() + path + File.separator + resourceID + exName);
            ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
            byte[] buff = new byte[1024]; //buff用于存放循环读取的临时数据
            int rc = 0;
            try {
                while ((rc = in.read(buff)) != -1) {
                    byteStram.write(buff, 0, rc);
                }
            } catch (IOException e) {
                e.printStackTrace();
                ;
            }
            byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果

            return bytes;
        } else {
            if (!FileValidUtil.valid(paramConfig.getAttachmentPath() + path + File.separator + resourceID + exName)) {
                throw new RuntimeException("文件路径不合法！");
            }
            File file = new File(paramConfig.getAttachmentPath() + path + File.separator + resourceID + exName);
            InputStream inputStream = null;
            if (file.exists()) {
                try {
                    inputStream = new FileInputStream(file);
                    byte[] data = new byte[(int) file.length()];
                    inputStream.read(data);
                    inputStream.close();
                    return data;
                } catch (Exception e) {
                    e.printStackTrace();
                    ;
                } finally {
                    try {
                        if (inputStream != null) {
                            inputStream.close();
                        }
                    } catch (Exception e) {

                    }

                }
            }

        }
        return null;
    }

    /**
     * @param resourceID
     * @return
     * @Title: previewAttachment
     * @Description: 预览附件
     * <AUTHOR>
     */
    @Override
    public Result<?> previewAttachment(String resourceID) {
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("附件的resourceID为空！");
        }
        byte[] data = null;
        String extname = "";
        String picName = "";
        DefectFile defectFile = null;
        if (useFileService) {
            Result<FileInfo> fileInfo = feignBugToFileService.getFileInfo(Long.parseLong(resourceID));
            if (fileInfo != null && fileInfo.getObj() != null) {
                extname = fileInfo.getObj().getExt();
                picName = extname.substring(1, extname.length());
            }

        } else {
            defectFile = defectFileService.findByResourceID(Long.valueOf(resourceID));

            extname = defectFile.getExtname();
            picName = extname.substring(1, extname.length());
        }

        if (EnumUtils.isValidEnum(PictureFormatEnum.class, picName.toUpperCase())) {
            try {
                if (useFileService) {
                    byte[] data1 = feignBugToFileService.getContent(Long.parseLong(resourceID));
                    data = Base64.getMimeDecoder().decode(data1);

                } else {
                    data = getFileData(resourceID, extname, defectFile.getPath());

                }
                Map<String, Object> map = new HashMap<>();
                map.put("extname", "data:image/" + picName + ";base64,");
                map.put("data", data);
                return Result.renderSuccess(map);
            } catch (Exception e) {
                e.printStackTrace();
                return Result.renderError("获取附件失败！");
            }
        } else {
            return Result.renderError("该格式附件不支持预览！");
        }
    }

    /**
     * @param mapParam
     * @return
     * @throws Exception
     * @Title: downloadMultiAttachment
     * @Description: 批量下载附件
     * <AUTHOR>
     */
    @Override
    public String downloadMultiAttachment(Map<String, Object> mapParam) throws Exception {

        HttpServletResponse response = (HttpServletResponse) mapParam.get("response");
        String resourceIDs = (String) mapParam.get("resourceIDs");

        //时间戳。。
        String time = String.valueOf(System.currentTimeMillis());

        if (this.useFileService) {
            for (String rid : resourceIDs.split(",")) {
                Result<FileInfo> fileInfo = this.feignBugToFileService.getFileInfo(Long.parseLong(rid));
                if (fileInfo != null && fileInfo.getObj() != null) {
                    byte[] data = this.feignBugToFileService.getContent(Long.parseLong(rid));
                    data = Base64.getMimeDecoder().decode(data);
                    genertateFile(fileInfo.getObj().getOriginalFilename(), data, time);
                }
            }
        } else {
            List<String> fileResourceIDs = Arrays.asList(resourceIDs.split(","));
            //查询附件信息
            List<DefectFile> defectFiles = defectFileService.findByResourceIDIn(fileResourceIDs);

            Map<String, DefectFile> fileData = new HashMap<>();
            defectFiles.stream().forEach(x -> {
                fileData.put(String.valueOf(x.getResourceID()), x);
            });
            //下载附件信息到本地
            for (Map.Entry<String, DefectFile> entry : fileData.entrySet()) {
                String resourceID = entry.getKey();
                String extname = entry.getValue().getExtname();
                String fileName = entry.getValue().getName();
                DefectFile defectFile = entry.getValue();
                byte[] data = this.getFileData(resourceID, extname, defectFile.getPath());
                genertateFile(fileName, data, time);
            }
        }
        //将文件压缩
        String localPath = paramConfig.getAttachmentPath() + File.separator + paramConfig.getTemporaryFolderName() + File.separator + time;
        byte[] data;
        data = ftpUtil.createZip(localPath);
//        response.setHeader("Content-Disposition", "attachment;filename=" + "XXXX" + "的附件下载.zip");
        response.getOutputStream().write(data);
        //删除本地文件
        this.deleteCurrentFile(localPath);
        return null;

    }

    /**
     * @param path
     * @Title: deleteCurrentFile
     * @Description: 删除本地文件
     * <AUTHOR>
     * @date 2020年4月7日 下午10:12:11
     */
    public void deleteCurrentFile(String path) {

        File file = new File(path);
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                if (f.exists()) {
                    f.delete();
                }
            }
        }
        file.delete();
    }

    /**
     * @param @param name
     * @param @param content
     * @param @param time    参数
     * @return void    返回类型
     * @throws
     * @Title: genertateFile
     * @Description: 生成本地文件
     * <AUTHOR>
     */
    private void genertateFile(String name, byte[] content, String time) {

        byte[] content_byte = null;
        String folderPath = "";
        String fileName = name;
        FileOutputStream outPutStream = null;
        try {
            content_byte = content;
            folderPath = paramConfig.getAttachmentPath() + File.separator + paramConfig.getTemporaryFolderName() + File.separator + time;
            File dir = new File(folderPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            File file = new File(folderPath + File.separator + fileName);
            outPutStream = new FileOutputStream(file);
            outPutStream.write(content_byte);
            outPutStream.flush();
            outPutStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (outPutStream != null) {
                    outPutStream.close();
                }
            } catch (Exception e) {
            }
        }
    }

    /**
     * @param mapParam
     * @return
     * @throws IOException
     * @Title: downloadAttachment
     * @Description: 下载附件
     * <AUTHOR>
     */
    @Override
    public String downloadAttachment(Map<String, Object> mapParam) throws IOException {
        HttpServletResponse response = (HttpServletResponse) mapParam.get("response");
        String resourceID = (String) mapParam.get("resourceID");
        String extname = "";
        String path = "";
        //查询附件信息
        if (useFileService) {
            Result<FileInfo> fileInfo = feignBugToFileService.getFileInfo(Long.valueOf(resourceID));
            if (fileInfo != null && fileInfo.getObj() != null) {
                extname = fileInfo.getObj().getExt();
            }
        } else {
            DefectFile defectFile = defectFileService.findByResourceID(Long.valueOf(resourceID));
            //时间戳。。
            extname = defectFile.getExtname();
            path = defectFile.getPath();
        }

        byte[] data = null;
        if (useFileService) {
            byte[] data1 = feignBugToFileService.getContent(Long.parseLong(resourceID));
            data = Base64.getMimeDecoder().decode(data1);

        } else {
            data = this.getFileData(resourceID, extname, path);

        }
//        response.setHeader("Content-Disposition", "attachment;filename=" + "XXXX" + "的附件下载.zip");
        response.getOutputStream().write(data);
        return null;
    }

    /**
     * @param projectResourceID
     * @return
     * @Title: findDefectProcessStartStatus
     * @Description: 查询当前项目的初始化起始状态状态
     * <AUTHOR>
     */
    @Override
    public Result findDefectProcessStartStatus(String projectResourceID) {
        //查询当前项目配置的缺陷流程起始状态(一个流程只有一个开始节点，但是可以有多个结束节点，不能没有结束节点)
        List<ProcessState> processState = processStateService.findDefectProcessStartOrEndStatus(projectResourceID, START);
        if (processState.isEmpty() || (!processState.isEmpty() && processState.size() > 1)) {
            return Result.renderError("缺陷流程配置有误！");
        }
        return Result.renderSuccess(processState);
    }

    /**
     * @param resourceID
     * @return
     * @throws DefectImportException
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     * @Title: getDefectInfo
     * @Description: 查看或者修改时获取缺陷的信息
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> getDefectInfo(String resourceID, String userNumber) throws Exception {

        //获取缺陷信息
        DefectInfoDTO defect = this.findByDefectResourceID(Long.valueOf(resourceID));
        if (defect == null) {
            return Result.renderError("当前缺陷不存在或者已经被删除！");
        }
        JettechUserDTO currentUserDTO = null;
        String userKey = RedisCacheConfig.DEFECT_FIND_TRANSF_USERNUMBER + userNumber;
        Object userDTO = this.jedisUtil.getObject(userKey);
        if (userDTO == null) {
            currentUserDTO = feignBugToBasic.findByNumber(userNumber, "");
            this.jedisUtil.setObject(userKey, currentUserDTO, 60 * 30);
        } else {
            currentUserDTO = (JettechUserDTO) userDTO;
        }
        if (currentUserDTO == null) {
            return Result.renderError("当前用户不存在!");
        }

        //查询当前人拥有的角色
        Result<?> groupDTOResult = feignBugToBasic.findCurrentUserUserGroup(userNumber);
        List<Map<String, Object>> groupDTOList = (List<Map<String, Object>>) groupDTOResult.getObj();
        List<String> currUserGroupRids = groupDTOList.stream().map(x -> String.valueOf(x.get("resourceID"))).collect(Collectors.toList());
        List<String> groupNamesList = groupDTOList.stream().map(x -> String.valueOf(x.get("name"))).collect(Collectors.toList());
        //当前状态
//		ProcessState currentStatus = processStateService.findByResourceID(defect.getCurrentStatusResourceID());
        ProcessState currentStatus = null;
        String csKey = RedisCacheConfig.DEFECT_FIND_PROCESS_BY_TWO;
        String csField = defect.getDefectState() + defect.getSubordinateProjectResourceID().toString();
        Object cs = this.jedisUtil.getHsetValue(csKey, csField);
        if (cs == null) {
            currentStatus = processStateService.findByDefectByStateKeyAndProjectResourceID(defect.getDefectState(), defect.getSubordinateProjectResourceID().toString());
            this.jedisUtil.setHash(csKey, csField, currentStatus, 5 * 60);
        } else {
            currentStatus = (ProcessState) cs;
        }

        String quoteKey = RedisCacheConfig.DEFECT_FIND_PROCESSQUOTE_BY_RID;
        //当前流程
        DefectProcess defectProcess = null;
        String dpField = String.valueOf(defect.getSubordinateProjectResourceID());
        Object dpObj = this.jedisUtil.getHsetValue(quoteKey, dpField);
        if (dpObj == null) {
            defectProcess = defectProcessService.findDefectprocessByProjectResourceID(dpField);
            this.jedisUtil.setHash(quoteKey, dpField, defectProcess, 10 * 60);
        } else {
            defectProcess = (DefectProcess) dpObj;
        }

        if (defectProcess == null) {
            return Result.renderError("数据有误，当前流程不存在或已被删除！");
        }
        String b = "false";
        //流转信息
        List<Map<String, Object>> listTransferInfo = new ArrayList<>();
        //查看当前状态是否存在，如果不存在，判断当前人员是不是超级管理员，如果是超级管理员，有权限流转到任何状态，如果不是超级挂管理员，并且当前状态不存在，则报错
        if (currentStatus == null) {
            if (groupNamesList.contains("系统管理员")) {
                String dpridKey = RedisCacheConfig.DEFECT_FIND_ALLPS_BY_RID;
                String dpridField = String.valueOf(defectProcess.getResourceID());
                Object asObj = this.jedisUtil.getHsetValue(dpridKey, dpridField);
                List<ProcessState> allStates = null;
                if (asObj == null) {
                    allStates = processStateService.findByDefectProcessRID(defectProcess.getResourceID());
                    this.jedisUtil.setHash(dpridKey, dpridField, allStates, 6 * 60);
                } else {
                    allStates = (List<ProcessState>) asObj;
                }

                for (ProcessState processState : allStates) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("stateKey", processState.getStateKey());
                    map.put("stateValue", processState.getStateValue());
                    map.put("resourceID", processState.getResourceID());
                    map.put("disable", "false");
                    listTransferInfo.add(map);
                }
                //超级管理员默认有流转权限，不管在不在流转人里
                b = "true";
                //校验当前人是否有修改权限
//				b = this.validateCurrUserIsTransferor(defect, currentUserDTO,currUserGroupRids,defectProcess);
//				if(!"true".equals(b) && !"false".equals(b)) {
//					return Result.renderError(b);
//				}
            } else {
                return Result.renderError("当前流程状态已不存在，请联系系统管理员做下一步流转处理！");
            }
        } else {
//			新建状态的缺陷：创建人和缺陷流程配置中的【流转人设置】配置的人都具有修改权限；其他状态的缺陷：缺陷流程配置中的【流转人设置】配置的人具有修改权限。
            b = this.validateCurrUserIsTransferor(defect, currentUserDTO, currUserGroupRids, defectProcess);
            if (!"true".equals(b) && !"false".equals(b)) {
                return Result.renderError(b);
            }
            if (currentStatus.getIsStart()) {
                if (defect.getCreateUser().equals(currentUserDTO.getNumber()) || "true".equals(b)) {
                    b = "true";
                }
            } else {
                if ("true".equals(b)) {
                    b = "true";
                }
            }
            String dpKey = RedisCacheConfig.DEFECT_FIND_PROCESSSTATE_BY_RID;
            String dpFieldRid = String.valueOf(defectProcess.getResourceID());
            Object psObj = this.jedisUtil.getHsetValue(dpKey, dpFieldRid);
            //查询当前流程所有的流转状态
            List<ProcessState> processStates = null;
            if (psObj == null) {
                processStates = processStateService.findByDefectProcessRID(defectProcess.getResourceID());
                this.jedisUtil.setHash(dpKey, dpFieldRid, processStates, 7 * 60);
            } else {
                processStates = (List<ProcessState>) psObj;
            }

            Map<Long, String> stateKeysMap = new HashMap<>();
            for (ProcessState processState : processStates) {
                stateKeysMap.put(processState.getResourceID(), processState.getStateKey());
            }

            //获取可流转状态
            //List<ProcessConfigure> nextProcessConfigures  =  this.findDefectNextTransferStatus(String.valueOf(currentStatus.getResourceID()), String.valueOf(defect.getSubordinateProjectResourceID()));
            List<ProcessConfigure> nextProcessConfigures = this.processConfigureDao.findDefectNextTransferStatus2(String.valueOf(currentStatus.getResourceID()), String.valueOf(defect.getSubordinateProjectResourceID()));
            //如果当前状态存在
            //查询当前人对当前可流转的状态是否有权限看到、可看到的流转的状态是否高亮显示
            for (ProcessConfigure processConfigure : nextProcessConfigures) {
                //当前状态到当前状态的状态流转配置不显示在修改和快捷流转页面里面(当前状态到当前状态)
                if (processConfigure.getTransferStateKey().equals(processConfigure.getCurrentStateKey())) {
                    continue;
                }
                //根据当前流程配置和当前用户组的rids查找当前人是否有权限流转改缺陷
                List<ProcessConfigureUserGroup> processConfigureUserGroup = null;
                String pcuKey = RedisCacheConfig.DEFECT_FIND_PROCESSCONFIGURATION_USER_GROUP;
                String pcuField = String.valueOf(processConfigure.getResourceID()) + currUserGroupRids;
                Object pcuObj = this.jedisUtil.getHsetValue(pcuKey, pcuField);
                if (pcuObj == null) {
                    processConfigureUserGroup = processConfigureUserGroupService.findByProcessRidAndUserGroupRids(String.valueOf(processConfigure.getResourceID()), currUserGroupRids);
                    this.jedisUtil.setHash(pcuKey, pcuField, processConfigureUserGroup, 4 * 60);
                } else {
                    processConfigureUserGroup = (List<ProcessConfigureUserGroup>) pcuObj;
                }
                if (!processConfigureUserGroup.isEmpty()) {//当前用户所在用户组不能看到,只有在配置用户组里面的人才能看到
                    Map<String, Object> map = new HashMap<>();
                    map.put("stateKey", stateKeysMap.get(Long.valueOf(processConfigure.getTransferStateResourceID())));
                    map.put("stateValue", processConfigure.getTransferStateKey());
                    map.put("resourceID", processConfigure.getTransferStateResourceID());

                    List<TransferorConfigure> transferorConfigures = null;
                    String tcKey = RedisCacheConfig.DEFECT_FIND_TRANSFEROR_BY_PROCESS;
                    String tcField = String.valueOf(processConfigure.getDefectProcessResourceID());
                    Object tcObj = this.jedisUtil.getHsetValue(tcKey, tcField);
                    if (tcObj == null) {
                        //能看到按钮之后，查看当前用户是否有可流转权限(查看流转人设置)
                        transferorConfigures = transferorConfigureService.findByDefectProcessResourceID(processConfigure.getDefectProcessResourceID());
                        this.jedisUtil.setHash(tcKey, tcField, transferorConfigures, 5 * 60);
                    } else {
                        transferorConfigures = (List<TransferorConfigure>) tcObj;
                    }
                    map.put("disable", "true");
                    if (!transferorConfigures.isEmpty()) {//该按钮没有流转人设置，不可流转
                        //流转人类型 : 0-用户组，1-用户指定字段，2-其他用户
                        inner:
                        for (TransferorConfigure transferorConfigure : transferorConfigures) {
                            if (transferorConfigure.getTransferorType() == 0) {//用户组
                                if (currUserGroupRids.contains(transferorConfigure.getTransferorResourceID())) {
                                    map.put("disable", "false");
                                    break inner;
                                }
                            }
                            if (transferorConfigure.getTransferorType() == 1) {//用户指定字段(创建人或者处理人)
                                if ("createUser".equals(transferorConfigure.getTransferorResourceID()) && userNumber.equals(defect.getCreateUser())) {//当前人时创建人
                                    map.put("disable", "false");
                                    break inner;
                                }
                                if ("handleUser".equals(transferorConfigure.getTransferorResourceID()) && userNumber.equals(defect.getHandleUserNumber())) {//当前人是处理人
                                    map.put("disable", "false");
                                    break inner;
                                }
                            }
                            if (transferorConfigure.getTransferorType() == 2) {//其他用户
                                List<String> handleUsersRid = Arrays.asList(transferorConfigure.getTransferorResourceID().split(","));
                                if (handleUsersRid.contains(String.valueOf(currentUserDTO.getResourceID()))) {
                                    map.put("disable", "false");
                                    break inner;
                                }
                            }


                        }

                    }

                    listTransferInfo.add(map);
                }
            }
        }
        Map<String, Object> mapCurr = new HashMap<>();
        mapCurr.put("stateKey", "");
        mapCurr.put("stateValue", "");
        if (!org.springframework.util.StringUtils.isEmpty(currentStatus)) {
            mapCurr.put("stateKey", currentStatus.getStateKey());
            mapCurr.put("stateValue", currentStatus.getStateValue());
            mapCurr.put("resourceID", currentStatus.getResourceID());
        } else {
            mapCurr.put("resourceID", null);
            defect.setDefectState("");
        }

        listTransferInfo.add(mapCurr);

        Map<String, Object> result = new HashMap<>();
        if (useFileService) {
            Result<List<FileInfo>> data = feignBugToFileService.getFileInfoList(Long.parseLong(resourceID), ObjectTypeEnum.DEFECT.getValue());
            List<FileInfo> fileInfos = data.getObj();
            result.put("defectFilesInfo", fileInfos);

        } else {
            Result<?> fileInfo = this.findAttachmentList(resourceID);
            List<DefectFile> listFileInfos = (List<DefectFile>) fileInfo.getObj();
            if (listFileInfos == null) {
                listFileInfos = new ArrayList<>();
                result.put("defectFilesInfo", listFileInfos);
            }
        }

        result.put("transferInfo", listTransferInfo);

        result.put("explanatoryNote", this.convertExplanatoryNote(defect.getExplanatoryNote()));
        //处理一下注释字段中隐式分隔符
        if (!StringUtils.isBlank(defect.getExplanatoryNote())) {
            defect.setExplanatoryNote(defect.getExplanatoryNote().replaceAll(FINAL_STR, ""));
        }
        result.put("defect", defect);
        result.put("privilege", Boolean.valueOf(b));
        return Result.renderSuccess(result);
    }

    /**
     * @param @param  valueOf
     * @param @return 参数
     * @return DefectInfoDTO    返回类型
     * @throws
     * @Title: findByDefectResourceID
     * @Description:查询缺陷信息，不进行数据字典转换
     * <AUTHOR>
     */
    private DefectInfoDTO findByDefectResourceID(Long resourceID) {

        return defectDao.findByDefectResourceID(resourceID);
    }

    /**
     * @param @param  defect
     * @param @param  delFiles 删除的附件
     * @param @param  delFiles 新添加的附件
     * @param @return
     * @param @throws Exception    参数
     * @return Result<?>    返回类型
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws IOException
     * @throws
     * @Title: updateDefect
     * @Description: 修改缺陷
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> updateDefect(Defect defect, String delFileRids, MultipartFile[] addFiles) throws Exception {
        //判断当前人是不是流转人，如果不是流转人则看不到修改页面
        JettechUserDTO jettechUserDTO = null;
        String userKey = RedisCacheConfig.DEFECT_FIND_TRANSF_USERNUMBER + defect.getEditUser();
        Object userDTO = this.jedisUtil.getObject(userKey);
        if (userDTO == null) {
            jettechUserDTO = feignBugToBasic.findByNumber(defect.getEditUser(), "");
            this.jedisUtil.setObject(userKey, jettechUserDTO, 60 * 30);
        } else {
            jettechUserDTO = (JettechUserDTO) userDTO;
        }
        if (jettechUserDTO == null) {
            return Result.renderError("当前用户不存在!");
        }
        //查询字段配置
        Result<?> defectConfig = feignBugToBasic.findDefectBasicConfig("defect", "");
        if (defectConfig == null || defectConfig.getObj() == null) {
            return Result.renderError("读取缺陷配置文件失败！");
        }

        String projectKey = RedisCacheConfig.DEFECT_UPDATE_PROJECT_NAME;
        String projectField = String.valueOf(defect.getSubordinateProjectResourceID());
        Object projectNameObj = this.jedisUtil.getHsetValue(projectKey, projectField);
        String projectName = null;
        if (projectNameObj == null) {
            //所属项目
            Result<?> projectResult = feignBugToDataDesign.findTestProjectByResourceID(String.valueOf(defect.getSubordinateProjectResourceID()));
            projectName = (String) projectResult.getObj();
            this.jedisUtil.setHash(projectKey, projectField, projectName, 5 * 60);
        } else {
            projectName = String.valueOf(projectNameObj);
        }

        defect.setSubordinateProject(projectName);
        //处理需求名称，有可能是编号和名称拼在一起的
        if (!org.springframework.util.StringUtils.isEmpty(defect.getSubordinateDemand())) {
            if (defect.getSubordinateDemand().indexOf("---") > 0) {
                defect.setSubordinateDemand(defect.getSubordinateDemand().split("---")[1]);
            }
        }
        Defect defectOld = this.findByResourceID(defect.getResourceID());
        //重新打开次数校验（缺陷从闭环状态变为非闭环状态时记一次）
        //当前配置的闭环状态
        //查询当前项目配置的缺陷流程起始状态(一个流程只有一个开始节点，但是可以有多个结束节点，不能没有结束节点)
        //查流程
        List<ProcessState> processState = processStateService.findDefectProcessStartOrEndStatus(String.valueOf(defect.getSubordinateProjectResourceID()), END);
        if (processState.isEmpty()) {
            return Result.renderError("当前缺陷流程配置有误！没有结束节点！");
        }
        List<String> endValues = processState.stream().map(x -> x.getStateKey()).collect(Collectors.toList());
        if (endValues.contains(defectOld.getDefectState()) && !endValues.contains(defect.getDefectState())) {
//			//缺陷从闭环状态变为非闭环状态时记一次
//			int current = Integer.valueOf(defect.getReopenTimes());
//			defect.setReopenTimes(String.valueOf(++ current));
            //闭环时间重置
            defect.setOffTime(null);
        }
        //闭环时间(之前不是闭环状态，现在是闭环状态)
        if (!endValues.contains(defectOld.getDefectState()) && endValues.contains(defect.getDefectState())) {
            defect.setOffTime(new Date());
        }
        //添加流转记录
        Map<Object, Object> processStateDic = redisUtils.getHashEntries("缺陷流程状态配置");
        Map<String, String> processStateDictionary = new HashMap<String, String>();
        for (Object key : processStateDic.keySet()) {
            processStateDictionary.put(String.valueOf(key), String.valueOf(processStateDic.get(key)));
        }
        //缺陷状态变更、处理人变更或添加了注释信息，都要增加一条流转记录
        String transferRecordFinal = "";
        if (!defect.getDefectState().equals(defectOld.getDefectState()) || !defect.getHandleUserNumber().equals(defectOld.getHandleUserNumber()) || !StringUtils.isBlank(defect.getExplanatoryNote())) {
            String transferRecord = DateUtil.getDateStr(new Date(), DateUtil.TIME_PATTREN) + " " + jettechUserDTO.getUserName() + " 将缺陷指派给：" + defect.getHandleUser();
            if (!defect.getDefectState().equals(defectOld.getDefectState())) {
                //流转记录
                transferRecord = transferRecord + "（" + processStateDictionary.get(defectOld.getDefectState()) + " --> " + processStateDictionary.get(defect.getDefectState()) + "）";
            } else {
                transferRecord = transferRecord + "（" + processStateDictionary.get(defect.getDefectState()) + "）";
            }
            transferRecordFinal += transferRecord;

        }

        //更新之前处理一下注释的问题;前端操作是将最新的记录传递过来，在这里需要追加到现有的记录里面去
        if (StringUtils.isBlank(defect.getExplanatoryNote())) {
            if ("".equals(transferRecordFinal)) {
                //空的，什么都没有传递，把之前的赋值进去
                defect.setExplanatoryNote(defectOld.getExplanatoryNote());
            } else {
                defect.setExplanatoryNote(transferRecordFinal + FINAL_STR + defectOld.getExplanatoryNote());
            }
        } else {//按照格式处理记录
            //靐爨
            String newNote = defect.getExplanatoryNote();
            String newStr = "注释内容：" + newNote;
            if (!StringUtils.isBlank(defectOld.getExplanatoryNote())) {
                if ("".equals(transferRecordFinal)) {
                    defect.setExplanatoryNote(newStr + FINAL_STR + defectOld.getExplanatoryNote());
                } else {
                    defect.setExplanatoryNote(transferRecordFinal + FINAL_STR + newStr + FINAL_STR + defectOld.getExplanatoryNote());
                }

            } else {
                if ("".equals(transferRecordFinal)) {
                    defect.setExplanatoryNote(newStr);
                } else {
                    defect.setExplanatoryNote(transferRecordFinal + FINAL_STR + newStr);
                }
            }
        }
        //查询设置的定则状态的缺陷对应状态值
        List<ProcessState> processStateIsDuty = processStateService.findDefectProcessIsReopenOrIsDuty(String.valueOf(defect.getSubordinateProjectResourceID()), "duty");
        List<String> dutyValues = processStateIsDuty.stream().map(x -> x.getStateKey()).collect(Collectors.toList());

        if (dutyValues.contains(defect.getDefectState())) {//说明当前状态是在设置的定则状态
            //JettechUserDTO handleUser = feignBugToBasic.findByNumber(params.get("handleUserNumber"), token);
            defect.setBelongDeveloperName(jettechUserDTO.getUserName());
            defect.setBelongDeveloperNumber(jettechUserDTO.getNumber());

            defect.setDefectDutySystem(defect.getDefectDutySystem() == null ? defectOld.getSubordinateSystem() : defect.getDefectDutySystem());
            defect.setDefectDutySystemResourceID(defect.getDefectDutySystemResourceID() == null ? defectOld.getSubordinateSystemResourceID() : defect.getDefectDutySystemResourceID());
        }
        //查询设置的重开状态的缺陷值
        List<ProcessState> openProcessState = processStateService.findDefectProcessIsReopenOrIsDuty(String.valueOf(defect.getSubordinateProjectResourceID()), "open");
        List<String> reopenList = openProcessState.stream().map(x -> x.getStateKey()).collect(Collectors.toList());
        if (reopenList.contains(defect.getDefectState())) {//说明当前状态是 重开状态
            defect.setDefectDutySystem(null);
            defect.setDefectDutySystemResourceID(null);
            defect.setBelongDeveloperName(null);
            defect.setBelongDeveloperNumber(null);
        }

        //如果最新状态和历史状态不同并且最新状太是重新打开记录一次
        //if(!defect.getDefectState().equals(defectOld.getDefectState()) && "重新打开".equals(processStateDictionary.get(defect.getDefectState()))) {
        if (!defect.getDefectState().equals(defectOld.getDefectState()) && reopenList.contains(defect.getDefectState())) {
            //缺陷状态变更为重新打开时记一次
            int current = Integer.valueOf(defect.getReopenTimes());
            defect.setReopenTimes(String.valueOf(++current));
        }
        defect.setTestTaskResourceID(defectOld.getTestTaskResourceID());
        defect.setProjectgroupResourceID(defectOld.getProjectgroupResourceID());
        //更新前处理变更历史表
        List<Map<String, Object>> defectConfigTmp = (List<Map<String, Object>>) defectConfig.getObj();
        List<DefectOperationHistory> historyForSave = compareDefectFieldChangeUtils.getChangeDefectField(defect, defectOld, jettechUserDTO.getUserName(), defectConfigTmp);
        defectOperationHistoryService.save(historyForSave, jettechUserDTO.getUserName());
        //更新操作
        defect.setCreateTime(defectOld.getCreateTime());
        defect.setEditTime(new Date());
        defect = this.update(defect, defect.getEditUser());

        //处理上传文件(新添加的)
        List<DefectFile> listDefectFiles = new ArrayList<>();
        Map<String, Object> fileMap = new HashMap<>();
        Map<String, DefectFile> defectFileMap = new HashMap<>();
        for (MultipartFile file : addFiles) {
            DefectFile model = new DefectFile();
            model.setDefectResourceID(defect.getResourceID());
            model.setName(file.getOriginalFilename());
            model.setSize(String.valueOf(file.getSize()));
            model.setUploadUserName(jettechUserDTO.getUserName());
            String fileName = file.getOriginalFilename().trim();
            String[] str = fileName.split("\\.");
            String fileType = str[(str.length - 1)];
            model.setExtname("." + fileType);
            //设置ResourceID
            model.setResourceID(generateResourceID());
            fileMap.put(String.valueOf(model.getResourceID()), file);
            defectFileMap.put(String.valueOf(model.getResourceID()), model);
            listDefectFiles.add(model);
        }
        //保存附件表
        if (!listDefectFiles.isEmpty()) {
            defectFileService.save(listDefectFiles, jettechUserDTO.getNumber());
        }
        //保存附件
        if (!fileMap.isEmpty()) {
            //保存新添加的附件
            this.saveAndUpdateAttachment(fileMap, defectFileMap);
        }
        //删除附件
        if (!StringUtils.isAllBlank(delFileRids)) {
            List<DefectFile> filesDel = defectFileService.findByResourceIDIn(Arrays.asList(delFileRids.split(",")));
            if (!filesDel.isEmpty()) {
                //删除对应的附件
                deleteAttachmentList(delFileRids, defect.getEditUser());
                //删除附件表数据
                defectFileService.deleteInBatch(filesDel, defect.getEditUser());
            }
        }
        // 消息通知更新
        String handleUserNumber = defect.getHandleUserNumber();
        if (handleUserNumber != null && !handleUserNumber.equals("")) {
            // 添加缺陷的消息通知
            Map<String, Object> notifyMap = new HashMap<>();
            notifyMap.put("type", "1");// 消息类型：1-缺陷，2-要求
            notifyMap.put("level", "");
            if (!org.springframework.util.StringUtils.isEmpty(defect.getSeverity())) {
                notifyMap.put("level", defect.getSeverity());
            }
            // notifyMap.put("levelName", "");
            notifyMap.put("content", defect.getDefectTitle());
            notifyMap.put("allotUserName", jettechUserDTO.getUserName());
            notifyMap.put("allotUserNumber", jettechUserDTO.getNumber());
            notifyMap.put("handleUserName", defect.getHandleUser());
            notifyMap.put("handleUserNumber", defect.getHandleUserNumber());
            notifyMap.put("allotTime", defect.getEditTime());
            notifyMap.put("targetResourceID", defect.getResourceID());
            notifyMap.put("operaterUserNumber", jettechUserDTO.getNumber());
            Result<?> result2 = feignBugToBasic.addAndUpdataDefectNotify(notifyMap);
            if (!result2.isSuccess()) {
                return Result.renderError("新增缺陷消息失败");
            }

        }

        return Result.renderSuccess("修改成功！");
    }


    /**
     * @param defect defect
     * @return void    返回类型
     * @throws
     * @Title: validateCurrUserIsTransferor
     * @Description: 验证当前人是不是当前缺陷流程的流转人
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    private String validateCurrUserIsTransferor(DefectInfoDTO defect, JettechUserDTO jettechUserDTO, List<String> currUserGroupRids, DefectProcess process) {
        //查询当前人拥有的角色
        String userNumber = jettechUserDTO.getNumber();
        List<TransferorConfigure> transferorConfigures = null;
        String tcKey = RedisCacheConfig.DEFECT_FIND_TRANSFEROR_BY_PROCESS;
        String tcField = String.valueOf(process.getResourceID());
        Object tcObj = this.jedisUtil.getHsetValue(tcKey, tcField);
        if (tcObj == null) {
            transferorConfigures = transferorConfigureService.findByDefectProcessResourceID(process.getResourceID());
            this.jedisUtil.setHash(tcKey, tcField, transferorConfigures, 5 * 60);
        } else {
            transferorConfigures = (List<TransferorConfigure>) tcObj;
        }

        boolean b = false;
        if (!transferorConfigures.isEmpty()) {//该按钮没有流转人设置，不可流转
            //流转人类型 : 0-用户组，1-用户指定字段，2-其他用户
            inner:
            for (TransferorConfigure transferorConfigure : transferorConfigures) {
                if (transferorConfigure.getTransferorType() == 0) {//用户组
                    if (currUserGroupRids.contains(transferorConfigure.getTransferorResourceID())) {
                        b = true;
                        break inner;
                    }
                }
                if (transferorConfigure.getTransferorType() == 1) {//用户指定字段(创建人或者处理人)
                    if ("createUser".equals(transferorConfigure.getTransferorResourceID()) && userNumber.equals(defect.getCreateUser())) {//当前人时创建人
                        b = true;
                        break inner;
                    }
                    if ("handleUser".equals(transferorConfigure.getTransferorResourceID()) && userNumber.equals(defect.getHandleUserNumber())) {//当前人是处理人
                        b = true;
                        break inner;
                    }
                }
                if (transferorConfigure.getTransferorType() == 2) {//其他用户
                    List<String> handleUsersRid = Arrays.asList(transferorConfigure.getTransferorResourceID().split(","));
                    if (handleUsersRid.contains(String.valueOf(jettechUserDTO.getResourceID()))) {
                        b = true;
                        break inner;
                    }
                }


            }

        }
        return b + "";
    }

    /**
     * @param @param  request
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findDefectNextTransferStatus
     * @Description: 根据缺陷当前状态和所属项目查询可流转的状态集合
     * <AUTHOR>
     */
    @Override
    public List<ProcessConfigure> findDefectNextTransferStatus(String currentStatusResourceID, String projectResourceID) {

        List<ProcessConfigure> processConfigurs = processConfigureService.findDefectNextTransferStatus(currentStatusResourceID, projectResourceID);
        if (processConfigurs.isEmpty()) {
            return new ArrayList<>();
        }
        return processConfigurs;
    }


    /**
     * @param @throws Exception    参数
     * @param tmpDir
     * @return Result<?>    返回类型
     * @throws IOException
     * @throws
     * @Title: uploadDefectFiles
     * @Description: 添加缺陷附件
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> uploadDefectFiles(String defectResourceID, MultipartFile[] files, String userNumber, String filesIDs, String tmpDir) throws IOException {
        return this.uploadDefectFiles(defectResourceID, files, userNumber, filesIDs, tmpDir, false);
    }


    /**
     * @param @throws Exception    参数
     * @param tmpDir
     * @return Result<?>    返回类型
     * @throws IOException
     * @throws
     * @Title: uploadDefectFiles
     * @Description: 添加缺陷附件
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> uploadDefectFiles(String defectResourceID, MultipartFile[] files, String userNumber, String filesIDs, String tmpDir, boolean fromMobile) throws IOException {
        JettechUserDTO jettechUserDTO = feignBugToBasic.findByNumber(userNumber, "");
        //处理上传文件
        List<DefectFile> listDefectFiles = new ArrayList<>();
        Map<String, Object> fileMap = new HashMap<>();
        Map<String, DefectFile> defectFileMap = new HashMap<>();

        String filepath = "";
        String newPath = "";
        if (paramConfig.getIsFtpOn()) {
            filepath = AttachmentPathGenerator.getDefectAttachmentPath(AttachmentStoreTypeEnums.FTP, Long.valueOf(defectResourceID));
            newPath = paramConfig.getFtpPath() + filepath;
        } else {
            filepath = AttachmentPathGenerator.getDefectAttachmentPath(AttachmentStoreTypeEnums.LOCAL_FILE, Long.valueOf(defectResourceID));
            newPath = paramConfig.getAttachmentPath() + filepath;
        }
        if (useFileService) {
            try {
                feignBugToFileService.upload(files, Long.parseLong(defectResourceID), ObjectTypeEnum.DEFECT.getValue(), true);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (StringUtils.isNotBlank(filesIDs)) {
                for (String resourceId : filesIDs.split(","))
                    feignBugToFileService.copyFile(Long.parseLong(resourceId), Long.parseLong(defectResourceID), ObjectTypeEnum.CASERESULTRECORDFILE.getValue(), ObjectTypeEnum.DEFECT.getValue());
            }
        } else {
            for (MultipartFile file : files) {
                DefectFile model = new DefectFile();
                model.setDefectResourceID(Long.valueOf(defectResourceID));
                model.setName(file.getOriginalFilename());
                model.setSize(String.valueOf(file.getSize()));
                model.setUploadUserName(jettechUserDTO.getUserName());
                String fileName = file.getOriginalFilename().trim();
                String[] str = fileName.split("\\.");
                String fileType = str[(str.length - 1)];
                model.setExtname("." + fileType);
                //设置ResourceID
                model.setResourceID(generateResourceID());
                model.setPath(filepath);
                fileMap.put(String.valueOf(model.getResourceID()), file);
                defectFileMap.put(String.valueOf(model.getResourceID()), model);
                listDefectFiles.add(model);
            }
            if (StringUtils.isNotEmpty(filesIDs)) {
                Result result = feignBugToManexecute.getManexecuteFile(filesIDs);
                Map<String, Object> map = (Map) result.getObj();
                copyFilebymanexecute(map.get("path").toString(), newPath, filepath, Long.valueOf(defectResourceID), map, listDefectFiles, jettechUserDTO.getUserName());
            }
            if (StringUtils.isNotEmpty(tmpDir)) {
                String rootPath = DEFECT_FILE_TMP_DIR + File.separator + userNumber + File.separator + tmpDir;
                File fileDir = new File(rootPath);
                if (fileDir.exists() && fileDir.isDirectory()) {
                    File[] tmpFiles = fileDir.listFiles();
                    if (tmpFiles.length > 0) {
                        for (File tmpFile : tmpFiles) {
                            //FileUtils.copyFile(tmpFile, new File(newPath + File.separator + tmpFile.getName()));
                            DefectFile model = new DefectFile();
                            model.setDefectResourceID(Long.valueOf(defectResourceID));
                            model.setName(tmpFile.getName());
                            model.setSize(String.valueOf(FileUtils.sizeOf(tmpFile)));
                            model.setUploadUserName(jettechUserDTO.getUserName());
                            String fileName = tmpFile.getName();
                            String[] str = fileName.split("\\.");
                            String fileType = str[(str.length - 1)];
                            model.setExtname("." + fileType);
                            //设置ResourceID
                            model.setResourceID(generateResourceID());
                            model.setPath(filepath);

                            fileMap.put(String.valueOf(model.getResourceID()), tmpFile);
                            defectFileMap.put(String.valueOf(model.getResourceID()), model);
                            listDefectFiles.add(model);
                        }
                    }
                }
            }
            if (!listDefectFiles.isEmpty()) {
                defectFileService.save(listDefectFiles, userNumber);
                //aop进不去 原因不明
                String fileKey = RedisCacheConfig.DEFECT_FIND_DEFECTFILE_BY_RID;
                if (this.jedisUtil.existsObjectKey(fileKey)) {
                    this.jedisUtil.delObject(fileKey);
                }
            }
            if (!fileMap.isEmpty()) {
                //保存修改附件
                this.saveAndUpdateAttachment(fileMap, defectFileMap);
            }
        }
        if (fromMobile) {
            try {
                BaseMessage message = new BaseMessage();
                message.setToUser(userNumber);
                message.setCmd(DEFECT_UPLOAD_IMAGE_CMD);
                feignBugToBasic.postMessageOne(message);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }

        return Result.renderSuccess();
    }

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: simpleInitDefect
     * @Description: 简易初始化列表当前数据
     * <AUTHOR>
     */
    @Override
    public Result<?> simpleInitDefect() {
        List<Defect> finds = defectDao.simpleInitDefect();
        return Result.renderSuccess(finds);
    }

    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: batchDeleteDefect
     * @Description: 单个或者批量删除
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> batchDeleteDefect(String resourceIDs, String userNumber) {
        List<String> rids = Arrays.asList(resourceIDs.split(","));
        List<Defect> defectDel = this.findByResourceIDIn(rids);
        List<DefectFile> defectFilesDel = new ArrayList<>();
        StringBuffer defectFileRids = new StringBuffer();
        for (Defect defect : defectDel) {
            List<DefectFile> defectFiles = defectFileService.findByDefectResourceID(defect.getResourceID());
            if (!defectFiles.isEmpty()) {
                defectFilesDel.addAll(defectFiles);
                defectFiles.stream().forEach(x -> {
                    defectFileRids.append(x.getResourceID() + ",");
                });
                ;
            }
        }
        //删除附件
        if (useFileService) {
            feignBugToFileService.deleteFileByResourceIds(resourceIDs, ObjectTypeEnum.DEFECT.getValue());
        } else {
            if (!StringUtils.isBlank(defectFileRids)) {
                this.deleteAttachmentList(defectFileRids.toString(), userNumber);
            }
        }

        //删除附加表
        if (!defectFilesDel.isEmpty()) {
            defectFileService.deleteInBatch(defectFilesDel, userNumber);
        }
        //删除历史记录表
        List<DefectOperationHistory> historys = defectOperationHistoryService.findByDefectResourceID(rids);
        if (!historys.isEmpty()) {
            defectOperationHistoryService.deleteInBatch(historys, userNumber);
        }
        //删除当前缺陷
        if (!defectDel.isEmpty()) {
            this.deleteInBatch(defectDel, userNumber);
        }
        Result<?> result = feignBugToBasic.deleteDefectNotify(resourceIDs, userNumber, "");
        if (!result.isSuccess()) {
            return Result.renderError("缺陷的消息通知删除失败！");
        }
        return Result.renderSuccess("删除成功！");
    }


    /**
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findTransferDealPerson
     * @Description: 查找处理人
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> findTransferDealPerson(String testSystemResourceID, String currentStatusResourceID,
                                            String transferStatusResourceID, String name, String rows, String page, String userNumber, String defectResourceID) {
        List<Map<String, Object>> finalResult = null;
        //当状态被删除时，当前状态为空,直接返回
        if (org.springframework.util.StringUtils.isEmpty(currentStatusResourceID) && org.springframework.util.StringUtils.isEmpty(transferStatusResourceID)) {
            return Result.renderSuccess(new ArrayList<>());
        }
        Map<String, Object> currentDefect = null;//空的时候是新建
        if (!org.springframework.util.StringUtils.isEmpty(defectResourceID)) {
            currentDefect = this.defectDao.selectDefectSystemInfo(Long.valueOf(defectResourceID));
        }

        String processKey = RedisCacheConfig.DEFECT_FIND_TRANSF_PROCESS;
        //检查是否有可流转的状态配置
        List<ProcessConfigure> processConfigs = null;
        Object processConfigsObj = this.jedisUtil.getHsetValue(processKey, currentStatusResourceID);
        if (null == processConfigsObj) {
            processConfigs = processConfigureService.findByProcessStateResourceID(Long.valueOf(currentStatusResourceID));
            this.jedisUtil.setHash(processKey, currentStatusResourceID, processConfigs, 10 * 60 * 60);
        } else {
            processConfigs = (List<ProcessConfigure>) processConfigsObj;
        }

        //不选择流转状态时,当前状态和流转状态同
//		if(currentStatusResourceID.equals(transferStatusResourceID)) {
//			//没有配置可流转状态,获取的是当前缺陷归属系统下的所有人员
//			if(processConfigs.isEmpty()){
//				if(currentDefect == null || 0 == currentDefect.size()){
//					String subordinateSystemResourceID = testSystemResourceID;
//					/*
//					//神仙代码，上面if ==null，这里来一个if != null
//					if(currentDefect != null){
//						subordinateSystemResourceID = String.valueOf(currentDefect.getSubordinateSystemResourceID());
//					}*/
//					finalResult = defectDao.findUserBySubordinateSystemResourceID(subordinateSystemResourceID);
//				}else{
//					String handlerUserNumber = String.valueOf(currentDefect.get("handleUserNumber"));
//					String userKey = RedisCacheConfig.DEFECT_FIND_TRANSF_USERNUMBER + handlerUserNumber;
//					Object userDTO = this.jedisUtil.getObject(userKey);
//					JettechUserDTO jettechUserDTO = null;
//					if(userDTO == null)
//					{
//						jettechUserDTO = feignBugToBasic.findByNumber(handlerUserNumber,"");
//						this.jedisUtil.setObject(userKey, jettechUserDTO, 60 * 30);
//					}else{
//						jettechUserDTO = (JettechUserDTO)userDTO;
//					}
//
//					if(jettechUserDTO != null){
//						Map<String,Object> obj = new HashMap<>();
//						obj.put("number",jettechUserDTO.getNumber());
//						obj.put("resourceID",jettechUserDTO.getResourceID());
//						obj.put("userName",jettechUserDTO.getUserName());
//						finalResult = new ArrayList<>();
//						finalResult.add(obj);
//					}
//				}
//
//			}else{//有可以流转的配置
//				boolean flag = false;
//				for (ProcessConfigure processConfigure: processConfigs){
//					if(processConfigure.getCurrentStateKey().equals(processConfigure.getTransferStateKey())){
//						flag = true;
//					}
//				}
//				//如果有当前到当前
//				if(flag || currentDefect == null){
//					Result<?> result = this.getUserByDefectState(currentStatusResourceID,testSystemResourceID,name, rows, page);
//					finalResult = (List<Map<String, Object>>) result.getObj();
//				}else{
//					String handlerUserNumber = String.valueOf(currentDefect.get("handleUserNumber"));
//					String userKey = RedisCacheConfig.DEFECT_FIND_TRANSF_USERNUMBER + handlerUserNumber;
//					Object userDTO = this.jedisUtil.getObject(userKey);
//					JettechUserDTO jettechUserDTO = null;
//					if(userDTO == null)
//					{
//						jettechUserDTO = feignBugToBasic.findByNumber(handlerUserNumber,"");
//						this.jedisUtil.setObject(userKey, jettechUserDTO, 60 * 30);
//					}else{
//						jettechUserDTO = (JettechUserDTO)userDTO;
//					}
//					if(jettechUserDTO != null){
//						Map<String,Object> obj = new HashMap<>();
//						obj.put("number",jettechUserDTO.getNumber());
//						obj.put("resourceID",jettechUserDTO.getResourceID());
//						obj.put("userName",jettechUserDTO.getUserName());
//						finalResult = new ArrayList<>();
//						finalResult.add(obj);
//					}
//
//				}
//			}

        //}else {//选择了流转状态
        //没有配置可流转状态,获取的是当前缺陷归属系统下的所有人员
        if (processConfigs.isEmpty()) {
            String subordinateSystemResourceID = testSystemResourceID;
            if (currentDefect != null && 0 != currentDefect.size()) {
                subordinateSystemResourceID = String.valueOf(currentDefect.get("subordinateSystemResourceID"));
            }
            finalResult = defectDao.findUserBySubordinateSystemResourceID(subordinateSystemResourceID);
        } else {
            Result<?> result = this.getUserByDefectState(transferStatusResourceID, testSystemResourceID, name, rows, page);
            finalResult = (List<Map<String, Object>>) result.getObj();
        }
        //}
        if (finalResult == null) {
            Result.renderSuccess(new ArrayList<Map<String, Object>>());
        }
        return Result.renderSuccess(finalResult);
    }

    /**
     *
     * @Title: findTransferUser
     * @Description: 根据不同的流转状态查找人
     * @param @param statusRid
     * @param @param name
     * @param @param rows
     * @param @param page
     * @param @return    参数
     * @return Result<?>    返回类型
     * @throws
     * <AUTHOR>
     */
//	private Result<?> findTransferUserInProject(String statusRid,String name,String projectResourceID,String rows,String page){
//		List<ProcessConfigure> pcList = processConfigureService.findByProcessStateResourceID(Long.parseLong(statusRid));
//		if(pcList == null || pcList.isEmpty()) return Result.renderSuccess();
//		List<ProcessConfigureUserGroup> pcUserGroupList = processConfigureUserGroupService.findProcessUserGroupByProcessConfigureResourceIDs(pcList);
//		if(pcUserGroupList==null || pcUserGroupList.isEmpty())
//			return  Result.renderError("请维护状态可操作的角色！");
//		List<String> userGroupRids = pcUserGroupList.stream().map(s -> s.getUserGroupResourceID().toString()).collect(Collectors.toList());
//		//查询当前项目组下的并且在这些项目组下的人员
//		Result<?> result = feignBugToBasic.findUserByUserGroupRidsAndProejectRid(userGroupRids,name,projectResourceID, rows, page);
//		if(result == null || result.getCode() != 20000) return  Result.renderError("基础服务异常！");
//		return result;
//	}

    /**
     * @param @param             defect
     * @param @param             files
     * @param @return            参数
     * @param imageDirResourceID
     * @return Result<?>    返回类型
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws
     * @Title: copyDefect
     * @Description: 缺陷复制
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<?> copyDefect(Defect defect, MultipartFile[] files, String userNumber, String imageDirResourceID) throws Exception {
        //查询字段配置
        Result<?> defectConfig = feignBugToBasic.findDefectBasicConfig("defect", "");
        if (defectConfig == null || defectConfig.getObj() == null) {
            return Result.renderError("读取缺陷配置文件失败！");
        }
        Map<String, FieldConfigurationDTO> baseConfigMap = this.convertConfigurationData(defectConfig);
        //校验启用字段必输项
        Set<String> usingFields = baseConfigMap.keySet();
        Field[] fields = defect.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            //缺陷编号新建时候过滤掉
            if ("defectNumber".equals(fieldName) || "subordinateProject".equals(fieldName)) {
                continue;
            }
            //启用字段
            if (usingFields.contains(fieldName)) {
                Object fieldValue = field.get(defect);

                //必输字段是否为空
                if (baseConfigMap.get(fieldName).isWriteRequired() && (fieldValue == null || StringUtils.isBlank(String.valueOf(fieldValue)))) {
                    return Result.renderError("已启用的配置字段：" + baseConfigMap.get(fieldName).getAliasName() + "为必输字段！");
                }
            }
        }
        defect.setResourceID(null);
        JettechUserDTO jettechUserDTO = feignBugToBasic.findByNumber(userNumber, "");
        if (jettechUserDTO == null) {
            return Result.renderError("当前用户不存在!");
        }
        defect.setCreateUserName(jettechUserDTO.getUserName());
        //所属项目
        Result<?> projectResult = feignBugToDataDesign.findTestProjectByResourceID(String.valueOf(defect.getSubordinateProjectResourceID()));
        String projectName = (String) projectResult.getObj();
        defect.setSubordinateProject(projectName);
        //处理需求名称，有可能是编号和名称拼在一起的
        if (!org.springframework.util.StringUtils.isEmpty(defect.getSubordinateDemand())) {
            if (defect.getSubordinateDemand().indexOf("---") > 0) {
                defect.setSubordinateDemand(defect.getSubordinateDemand().split("---")[1]);
            }
        }
        //重新打开次数新建时计0
        defect.setReopenTimes("0");
        defect.setOffTime(null);
        defect.setCreateTime(new Date());
      //注释内容添加流转记录
        Map<Object, Object> processStateDic = redisUtils.getHashEntries("缺陷流程状态配置");
        Map<String, String> processStateDictionary = new HashMap<String, String>();
        for (Object key : processStateDic.keySet()) {
            processStateDictionary.put(String.valueOf(key), String.valueOf(processStateDic.get(key)));
        }

        String transferRecord = DateUtil.getDateStr(new Date(), DateUtil.TIME_PATTREN) + " " + jettechUserDTO.getUserName() + " 将缺陷指派给：" + defect.getHandleUser() + "（" + processStateDictionary.get(defect.getDefectState()) + "）";

        //处理缺陷注释
        if (!StringUtils.isBlank(defect.getExplanatoryNote())) {
            String newNote = defect.getExplanatoryNote();
            String newStr = "==== " + jettechUserDTO.getUserName() + " " + DateUtil.getDateStr(new Date(), DateUtil.TIME_PATTREN) + " ==== 注释内容：" + newNote;
            defect.setExplanatoryNote(newStr);
        }else {
            defect.setExplanatoryNote(transferRecord);

        }
        
        //缺陷编号

        defect.setDefectNumber(this.generateDefectNumber());
        defect = this.save(defect, userNumber);

        this.uploadDefectFiles(String.valueOf(defect.getResourceID()), files, defect.getCreateUser(), "", imageDirResourceID);
        //添加缺陷的消息通知
        if (defect.getHandleUserNumber() != null && !defect.getHandleUserNumber().equals("")) {
            Map<String, Object> notifyMap = new HashMap<>();
            notifyMap.put("type", "1");//消息类型：1-缺陷，2-要求
            notifyMap.put("level", "");
            if (!org.springframework.util.StringUtils.isEmpty(defect.getSeverity())) {
                notifyMap.put("level", defect.getSeverity());
            }
            notifyMap.put("content", defect.getDefectTitle());
            notifyMap.put("allotUserName", jettechUserDTO.getUserName());
            notifyMap.put("allotUserNumber", defect.getCreateUser());
            notifyMap.put("handleUserName", defect.getHandleUser());
            notifyMap.put("handleUserNumber", defect.getHandleUserNumber());
            notifyMap.put("allotTime", defect.getCreateTime());
            notifyMap.put("targetResourceID", defect.getResourceID());
            notifyMap.put("operaterUserNumber", defect.getCreateUser());
            Result<?> result2 = feignBugToBasic.addAndUpdataDefectNotify(notifyMap);
            if (!result2.isSuccess()) {
                return Result.renderError("新增缺陷消息失败");
            }
        }
        return Result.renderSuccess(defect);
    }

    /**
     * @param @return 参数
     * @return List<String>    返回类型
     * @throws
     * @Title: convertExplanatoryNote
     * @Description: 缺陷注释转换格式
     * <AUTHOR>
     */
    public List<String> convertExplanatoryNote(String explanatoryNote) {
        if (StringUtils.isBlank(explanatoryNote)) {
            return new ArrayList<>();
        }
        return Arrays.asList(explanatoryNote.split(FINAL_STR));
    }

    /**
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: judgeDelete
     * @Description: 删除缺陷之前校验是否可以删除
     * <AUTHOR>
     */
    @Override
    public Result<?> validateDelete(String userNumber, String defectResourceIDs) {
        if (defectResourceIDs == null || StringUtils.isBlank(defectResourceIDs)) {
            return Result.renderSuccess();
        }
        List<String> group = feignBugToBasic.synchronizeQueryUserGroupByUserNumber(userNumber, "");
        for (String name : group) {
            if ("系统管理员".equals(name)) {
                Result<Object> result = new Result<>(true, 20000, "当前操作人为系统管理员,允许删除!");
                return result;
            }
        }
        String[] split = defectResourceIDs.split(",");
        //删除单条缺陷
        if (split.length == 1) {
            Long defectResourceID = Long.valueOf(split[0]);
            Defect byResourceID = this.findByResourceID(defectResourceID);
            ProcessState currentState = processStateService.findByResourceID(byResourceID.getCurrentStatusResourceID());
            if (currentState == null) {
                return Result.renderError("当前缺陷流转状态为历史状态数据，不允许删除！");
            } else {
                if (!currentState.getIsStart()) {
                    return Result.renderError("当前缺陷为流转中的缺陷不允许删除！");
                } else {
                    if (!userNumber.equals(byResourceID.getCreateUser())) {
                        return Result.renderError("当前用户非缺陷创建人，不允许删除!");
                    }
                }
            }
        } else {
            StringBuffer sbHistory = new StringBuffer();
            StringBuffer sbTransfer = new StringBuffer();
            StringBuffer sbNotCreator = new StringBuffer();
            for (String defectResourceIDString : split) {
                Long defectResourceID = Long.valueOf(defectResourceIDString);
                Defect byResourceID = this.findByResourceID(defectResourceID);
                ProcessState currentState = processStateService.findByResourceID(byResourceID.getCurrentStatusResourceID());
                if (currentState == null) {
                    sbHistory.append(byResourceID.getDefectNumber() + ",");
                } else {
                    if (!currentState.getIsStart()) {
                        sbTransfer.append(byResourceID.getDefectNumber() + ",");
                    } else {
                        if (!userNumber.equals(byResourceID.getCreateUser())) {
                            sbNotCreator.append(byResourceID.getDefectNumber() + ",");
                        }
                    }
                }

            }
            String all = "";
            if (!StringUtils.isBlank(sbHistory)) {
                all += "缺陷编号【" + sbHistory.toString().substring(0, sbHistory.length() - 1) + "】为历史状态数据，不允许删除！";
            }
            if (!StringUtils.isBlank(sbTransfer)) {
                all += "缺陷编号【" + sbTransfer.toString().substring(0, sbTransfer.length() - 1) + "】已经流转，不允许删除！";
            }
            if (!StringUtils.isBlank(sbNotCreator)) {
                all += "缺陷编号【" + sbNotCreator.toString().substring(0, sbNotCreator.length() - 1) + "】只有创建者可以删除！";
            }
            if (!StringUtils.isBlank(all)) {
                return Result.renderError(all);
            }
        }
        return Result.renderSuccess();
    }

    /**
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws Exception
     * @throws
     * @Title: initDefctListBySearchInfo
     * @Description: 分页初始化列表查询
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> initDefctListBySearchInfo(DefectConfigDTO defectConfigDTO, PageRequest pageRequest) throws Exception {
        //查询字段配置
        Result<?> defectConfig = feignBugToBasic.findDefectBasicConfig("defect", "");
        if (defectConfig == null || defectConfig.getObj() == null) {
            return Result.renderError("读取缺陷配置文件失败！");
        }
        List<Map<String, Object>> defectField = (List<Map<String, Object>>) defectConfig.getObj();
        List<Map<String, Object>> reserveList = new ArrayList<>();
        for (Map<String, Object> map : defectField) {
            //判断缺陷备用字段是否是下拉框,如果是下拉框需要获取对应的数据字典值
            if (map.get("name").toString().startsWith("reserve") && ("2".equals(map.get("fieldType")) || "3".equals(map.get("fieldType")))) {
                reserveList.add(map);
            }
        }
        List<Map<String, Object>> configurations = (List<Map<String, Object>>) defectConfig.getObj();
        //筛选出多选下拉框的备用字段
        List<Map<String, Object>> multiComboboxS = configurations.stream().filter(
                config -> config.get("name").toString().indexOf("reserve") != -1
                        && config.get("fieldType").toString().equals("3")).collect(Collectors.toList());
        //筛选出单选下拉框的备用字段
        List<Map<String, Object>> radioComboboxS = configurations.stream().filter(
                config -> config.get("name").toString().indexOf("reserve") != -1
                        && config.get("fieldType").toString().equals("2")).collect(Collectors.toList());
        //筛选出时间选择框的备用字段
        List<Map<String, Object>> timeComboboxS = configurations.stream().filter(
                config -> config.get("name").toString().indexOf("reserve") != -1
                        && config.get("fieldType").toString().equals("5")).collect(Collectors.toList());
        //单行文本，多行文本，多选下拉，都模糊搜索
        List<Map<String, Object>> otherComboboxS = configurations.stream().filter(
                config -> config.get("name").toString().indexOf("reserve") != -1
                        && (config.get("fieldType").toString().equals("1") ||
                        config.get("fieldType").toString().equals("4"))).collect(Collectors.toList());

        //处理备用字段可能存在时间的问题
        defectConfigDTO = ConvertDefectConfigDTO.convertDTOtimeComboboxS(defectConfigDTO, timeComboboxS);
        defectConfigDTO = ConvertDefectConfigDTO.convertDTOradioComboboxS(defectConfigDTO, radioComboboxS);
        defectConfigDTO = ConvertDefectConfigDTO.convertDTOmultiComboboxS(defectConfigDTO, multiComboboxS);
        defectConfigDTO = ConvertDefectConfigDTO.convertDTOotherComboboxS(defectConfigDTO, otherComboboxS);
        Page<Defect> pageInfo = defectDao.initDefctListBySearchInfo(pageRequest, defectConfigDTO);
        List<Defect> defectList = pageInfo.getContent();
        if (!defectList.isEmpty()) {
            Set<String> parentTypeKey = new HashSet<>();
            defectList.stream().forEach(e -> {
                if (StringUtils.isNoneEmpty(e.getParentDefectType())) {
                    parentTypeKey.add(e.getParentDefectType());
                }
            });
            if (!parentTypeKey.isEmpty()) {
                //查找缺陷大类型的所有数据
                List<DataDictionaryDTO> dataDics = defectDao.findDefectTypeDics();
                //大类key找value
                Map<String, String> mapParentType = new HashMap<>();
                //大类lkey找小类map
                Map<String, Map<String, String>> mapChildType = new HashMap<>();
                dataDics.stream().forEach(e -> {
                    mapParentType.put(e.getName(), e.getInfoName());
                });
                mapChildType = dataDics.stream().collect(Collectors.groupingBy(
                        DataDictionaryDTO::getName, Collectors.toMap(DataDictionaryDTO::getValue, DataDictionaryDTO::getTextName)));
                for (Defect e : defectList) {
                    if (StringUtils.isNoneEmpty(e.getParentDefectType())) {
                        String parentValue = e.getParentDefectType();
                        e.setParentDefectType(mapParentType.get(e.getParentDefectType()));
                        if (StringUtils.isNoneEmpty(e.getDefectType())) {
                            if (!org.springframework.util.StringUtils.isEmpty(mapChildType.get(parentValue))) {
                                e.setDefectType(mapChildType.get(parentValue).get(e.getDefectType()));
                            }
                        }
                    }
                    ;
                }

            }
            this.convertDataDictionaryValue(defectList, reserveList);
        }
        return Result.renderSuccess(pageInfo);
    }

    /**
     * 转换缺陷数据字典值
     *
     * @param defectList
     * @param reserveList
     * @param defectList
     * @return
     * @throws DefectImportException
     */
    @SuppressWarnings("unchecked")
    private List<DefectInfoDTO> convertDataDictionaryValueDefectInfoDTO(List<DefectInfoDTO> defectList, List<Map<String, Object>> reserveList) throws DefectImportException, NoSuchFieldException, IllegalAccessException {

        List<Map<String, Object>> allReserveCache = new ArrayList<>();
        for (Map<String, Object> map : reserveList) {
            Result<?> reserveResult = feignBugToBasic.findTextNameAndValueByDirName(map.get("nameDescription").toString(), "");
            if (!reserveResult.isSuccess()) {
                throw new DefectImportException(map.get("nameDescription") + "值未在数据字典中查询到");
            }
            HashMap<String, String> reserveCache = new HashMap<>();
            ArrayList<DataDictionaryDTO> reserveDataDictionaryDTO = (ArrayList<DataDictionaryDTO>) reserveResult.getObj();
            reserveDataDictionaryDTO.forEach(dictionary -> {
                reserveCache.put(dictionary.getValue(), dictionary.getTextName());
            });
            HashMap<String, Object> reserveMap = new HashMap<>();
            reserveMap.put("defectField", map.get("name"));
            reserveMap.put("aliasName", map.get("aliasName"));
            reserveMap.put("fieldType", map.get("fieldType"));
            reserveMap.put("reserveCache", reserveCache);
            allReserveCache.add(reserveMap);
        }
        for (DefectInfoDTO defect : defectList) {
            if (allReserveCache != null) {
                for (Map<String, Object> map : allReserveCache) {
                    HashMap<String, String> reserveCache = (HashMap<String, String>) map.get("reserveCache");
                    if ("2".equals(map.get("fieldType"))) {
                        //单选下拉框
                        Field defectField = defect.getClass().getDeclaredField(map.get("defectField").toString());
                        defectField.setAccessible(true);
                        if (defectField.get(defect) != null && !"".equals(defectField.get(defect))) {
                            defectField.set(defect, defectField.get(defect));
                            String reserve = reserveCache.get(defectField.get(defect));
                            if (reserve != null) {
                                //查不到时候赋值现有值，查到了赋值应有的值
                                defectField.set(defect, reserve);
//								throw new DefectImportException(map.get("aliasName") +"[" + map.get("name") + "]值未在数据字典中查询到");
                            }
                        }

                    } else {
                        //多选下拉框
                        Field defectField = defect.getClass().getDeclaredField(map.get("defectField").toString());
                        defectField.setAccessible(true);
                        if (defectField.get(defect) != null && !"".equals(defectField.get(defect))) {
                            String value = defectField.get(defect).toString();
                            String[] split = value.split(",");
                            StringBuffer stringBuffer = new StringBuffer();
                            for (String s : split) {
                                if (reserveCache.get(s) != null && !"".equals(reserveCache.get(s))) {
                                    stringBuffer.append(reserveCache.get(s)).append(",");
                                }
                            }
                            if (stringBuffer.length() > 1) {
                                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                            }
                            defectField.set(defect, stringBuffer.toString());
                        }
                    }
                }

            }

        }
        return defectList;
    }

    /**
     * @param @param  testCaseResourceID
     * @param @return 参数
     * @throws
     * @Title: findBugsByTestCaseResourceID
     * @Description: 根据当前案例查询案例所有的缺陷列表分页展示（当前任务下）-手工执行任务查询案例下的缺陷
     * <AUTHOR>
     */
    @Override
    public Result findBugsPageByTestCaseResourceID(PageRequest pageRequest, String testCaseResourceID,
                                                   String testTaskResourceID, String projectgroupResourceID) {
        Page<Defect> pageInfo = defectDao.findBugsPageByTestCaseResourceID(pageRequest, testCaseResourceID, testTaskResourceID, projectgroupResourceID);
        List<Defect> list = pageInfo.getContent();
        list.stream().forEach(x -> {
            if (!org.springframework.util.StringUtils.isEmpty(x.getDescriptionAndExpectresult())) {
                String result = compareDefectFieldChangeUtils.convertText(x.getDescriptionAndExpectresult());
                x.setDescriptionAndExpectresult(result);
            }
        });
        return Result.renderSuccess(pageInfo);
    }

    /**
     * 查询当前项目案例所属项目组
     *
     * @param resourceID
     * @return caojinbao
     */
    private Map<String, Object> findPerformCaseProjectGroupResourceID(String resourceID) {

        return defectDao.findPerformCaseProjectGroupResourceID(resourceID);
    }

    /**
     * 转换缺陷数据字典值
     *
     * @param defectList
     * @param reserveList
     * @param defectList
     * @return
     * @throws DefectImportException
     */
    @SuppressWarnings("unchecked")
    private List<Defect> convertDataDictionaryValue(List<Defect> defectList, List<Map<String, Object>> reserveList) throws DefectImportException, NoSuchFieldException, IllegalAccessException {

        List<Map<String, Object>> allReserveCache = new ArrayList<>();
        for (Map<String, Object> map : reserveList) {
            Result<?> reserveResult = feignBugToBasic.findTextNameAndValueByDirName(map.get("aliasName").toString(), "");
            if (!reserveResult.isSuccess()) {
                throw new DefectImportException(map.get("nameDescription") + "值未在数据字典中查询到");
            }
            HashMap<String, String> reserveCache = new HashMap<>();
            ArrayList<DataDictionaryDTO> reserveDataDictionaryDTO = (ArrayList<DataDictionaryDTO>) reserveResult.getObj();
            reserveDataDictionaryDTO.forEach(dictionary -> {
                reserveCache.put(dictionary.getValue(), dictionary.getTextName());
            });
            HashMap<String, Object> reserveMap = new HashMap<>();
            reserveMap.put("defectField", map.get("name"));
            reserveMap.put("aliasName", map.get("aliasName"));
            reserveMap.put("fieldType", map.get("fieldType"));
            reserveMap.put("reserveCache", reserveCache);
            allReserveCache.add(reserveMap);
        }
        for (Defect defect : defectList) {
            if (allReserveCache != null) {
                for (Map<String, Object> map : allReserveCache) {
                    HashMap<String, String> reserveCache = (HashMap<String, String>) map.get("reserveCache");
                    if ("2".equals(map.get("fieldType"))) {
                        //单选下拉框
                        Field defectField = defect.getClass().getDeclaredField(map.get("defectField").toString());
                        defectField.setAccessible(true);
                        if (defectField.get(defect) != null && !"".equals(defectField.get(defect))) {
                            defectField.set(defect, defectField.get(defect));
                            String reserve = reserveCache.get(defectField.get(defect));
                            if (reserve != null) {
                                //查不到时候赋值现有值，查到了赋值应有的值
                                defectField.set(defect, reserve);
                            }
                        }

                    } else {
                        //多选下拉框
                        Field defectField = defect.getClass().getDeclaredField(map.get("defectField").toString());
                        defectField.setAccessible(true);
                        if (defectField.get(defect) != null && !"".equals(defectField.get(defect))) {
                            String value = defectField.get(defect).toString();
                            String[] split = value.split(",");
                            StringBuffer stringBuffer = new StringBuffer();
                            for (String s : split) {
                                if (reserveCache.get(s) != null && !"".equals(reserveCache.get(s))) {
                                    stringBuffer.append(reserveCache.get(s)).append(",");
                                }
                            }
                            if (stringBuffer.length() > 1) {
                                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                            }
                            defectField.set(defect, stringBuffer.toString());
                        }
                    }
                }

            }

        }
        return defectList;
    }

    /**
     * @param "[map]"
     * @return void
     * @throws
     * @Title: exportExcel
     * @description: 缺陷导出excel
     * <AUTHOR>
     * @date 2019/11/20 15:10
     */
    @Override
    public void exportExcel(Map map) {
        HttpServletRequest request = (HttpServletRequest) map.get("request");
        String agent = request.getHeader("User-Agent");
        HttpServletResponse response = (HttpServletResponse) map.get("response");
        //勾选缺陷id
        String defectResourceIDs = map.get("defectResourceIDs") == null ? "" : String.valueOf(map.get("defectResourceIDs")).trim();
        //导出字段
        final List<String> selectedFields = (List<String>) map.get("selectedFields");
        //缺陷集合
        //List<Defect> defect_list = this.findByDemandResourceID(demandResourceID);
        String beginTime = (String) map.get("beginTime");
        if (!StringUtils.isBlank(beginTime)) {
            beginTime = beginTime + " 00:00:00";
            map.put("beginTime", beginTime);
        }
        String endTime = (String) map.get("endTime");
        if (!StringUtils.isBlank(endTime)) {
            endTime = endTime + " 23:59:59";
            map.put("endTime", endTime);
        }
        List<Defect> defect_list;
        if (defectResourceIDs != null && defectResourceIDs != "") {
            List<String> list = new ArrayList<>();
            String[] rids = defectResourceIDs.split(",");
            for (String id : rids) {
                list.add(id);
            }
            defect_list = defectService.findByResourceIDIn(list);
        } else {
            defect_list = defectService.findExportDefect(map);
        }
        //defect_list = dealValue( defect_list);
        dataDicUtil.getDicMap("defect");
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result defectConRes = feignBugToBasic.findDefectConfig("defect", token);
        List<Map<String, Object>> defectConList = (List<Map<String, Object>>) defectConRes.getObj();
        Map<String, Object> defectNameMap = defectConList.stream().collect(Collectors.toMap(e -> e.get("aliasName").toString(), e -> e.get("name")));
        Map<String, Object> defectTypeMap = defectConList.stream().collect(Collectors.toMap(e -> e.get("name").toString(), e -> e.get("fieldType")));
        Map<String, Object> dictionaryCellMap = defectConList.stream().collect(Collectors.toMap(e -> e.get("aliasName").toString(), e -> e.get("name")));
        Map<String, Map<String, String>> commentsDicMap = new HashMap<String, Map<String, String>>();
        if (!dictionaryCellMap.isEmpty()) {
            for (String nameDescription : dictionaryCellMap.keySet()) {
                Result res = feignBugToBasic.findByName(nameDescription, token);
                List<Map<String, Object>> list = (List<Map<String, Object>>) res.getObj();
                Map<String, String> commentsDicMap1 = new HashMap<>();
                for (Map<String, Object> map1 : list) {
                    commentsDicMap1.put(String.valueOf(map1.get("value")), String.valueOf(map1.get("textName")));
                }
                commentsDicMap.put(dictionaryCellMap.get(nameDescription).toString(), commentsDicMap1);
            }
        }

        //查找缺陷大类型的所有数据
        List<DataDictionaryDTO> dataDics = defectDao.findDefectTypeDics();
        //大类key找value
        Map<String, String> mapParentType = new HashMap<>();
        //大类lkey找小类map

        dataDics.stream().forEach(e -> {
            mapParentType.put(e.getName(), e.getInfoName());
        });
        Map<String, Map<String, String>> mapChildType = dataDics.stream().collect(Collectors.groupingBy(
                DataDictionaryDTO::getInfoName, Collectors.toMap(DataDictionaryDTO::getValue, DataDictionaryDTO::getTextName)));

        defect_list.forEach(m -> {
            m.setPriority(dataDicUtil.getTextName("priority", m.getPriority()));
            m.setSeverity(dataDicUtil.getTextName("severity", m.getSeverity()));
            m.setDefectState(dataDicUtil.getTextName("defectState", m.getDefectState()));
//			m.setTestPhase(dataDicUtil.getTextName("testPhase", m.getTestPhase()));
//			m.setTestRound(dataDicUtil.getTextName("testRound", m.getTestRound()));
//			m.setTestEnvironment(dataDicUtil.getTextName("testEnvironment", m.getTestEnvironment()));
            String content = m.getDescriptionAndExpectresult();
            content = convertHtmlToPlainText(content);
            m.setDescriptionAndExpectresult(content);
            //注释
            if (StringUtils.isNotBlank(m.getExplanatoryNote())) {
                String s = m.getExplanatoryNote().replaceAll(FINAL_STR, ";\r\n");
                m.setExplanatoryNote(s);
            }


            String reserve1 = m.getReserve1();
            if (StringUtils.isNotBlank(reserve1)) {
                String fieldType1 = defectTypeMap.get("reserve1") == null ? null : defectTypeMap.get("reserve1").toString();
                if (fieldType1 != null) {
                    if ("3".equals(fieldType1) || "2".equals(fieldType1)) {
                        String[] split = reserve1.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve1").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve1(str);
                    }
                }
            }

            String reserve2 = m.getReserve2();
            if (StringUtils.isNotBlank(reserve2)) {
                String fieldType2 = defectTypeMap.get("reserve2") == null ? null : defectTypeMap.get("reserve2").toString();
                if (fieldType2 != null) {
                    if ("3".equals(fieldType2) || "2".equals(fieldType2)) {
                        String[] split = reserve2.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve2").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve2(str);
                    }
                }
            }

            String reserve3 = m.getReserve3();
            if (StringUtils.isNotBlank(reserve3)) {
                String fieldType3 = defectTypeMap.get("reserve3") == null ? null : defectTypeMap.get("reserve3").toString();
                if (fieldType3 != null) {
                    if ("3".equals(fieldType3) || "2".equals(fieldType3)) {
                        String[] split = reserve3.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve3").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve3(str);
                    }
                }
            }

            String reserve4 = m.getReserve4();
            if (StringUtils.isNotBlank(reserve4)) {
                String fieldType4 = defectTypeMap.get("reserve4") == null ? null : defectTypeMap.get("reserve4").toString();
                if (fieldType4 != null) {
                    if ("3".equals(fieldType4) || "2".equals(fieldType4)) {
                        String[] split = reserve4.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve4").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve4(str);
                    }
                }
            }

            String reserve5 = m.getReserve5();
            if (StringUtils.isNotBlank(reserve5)) {
                String fieldType5 = defectTypeMap.get("reserve5") == null ? null : defectTypeMap.get("reserve5").toString();
                if (fieldType5 != null) {
                    if ("3".equals(fieldType5) || "2".equals(fieldType5)) {
                        String[] split = reserve5.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve5").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve5(str);
                    }
                }
            }

            String reserve6 = m.getReserve6();
            if (StringUtils.isNotBlank(reserve6)) {
                String fieldType6 = defectTypeMap.get("reserve6") == null ? null : defectTypeMap.get("reserve6").toString();
                if (fieldType6 != null) {
                    if ("3".equals(fieldType6) || "2".equals(fieldType6)) {
                        String[] split = reserve6.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve6").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve6(str);
                    }
                }
            }

            String reserve7 = m.getReserve7();
            if (StringUtils.isNotBlank(reserve7)) {
                String fieldType7 = defectTypeMap.get("reserve7") == null ? null : defectTypeMap.get("reserve7").toString();
                if (fieldType7 != null) {
                    if ("3".equals(fieldType7) || "2".equals(fieldType7)) {
                        String[] split = reserve7.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve7").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve7(str);
                    }
                }
            }

            String reserve8 = m.getReserve8();
            if (StringUtils.isNotBlank(reserve8)) {
                String fieldType8 = defectTypeMap.get("reserve8") == null ? null : defectTypeMap.get("reserve8").toString();
                if (fieldType8 != null) {
                    if ("3".equals(fieldType8) || "2".equals(fieldType8)) {
                        String[] split = reserve8.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve8").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve8(str);
                    }
                }
            }

            String reserve9 = m.getReserve9();
            if (StringUtils.isNotBlank(reserve9)) {
                String fieldType9 = defectTypeMap.get("reserve9") == null ? null : defectTypeMap.get("reserve9").toString();
                if (fieldType9 != null) {
                    if ("3".equals(fieldType9) || "2".equals(fieldType9)) {
                        String[] split = reserve9.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve9").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve9(str);
                    }
                }
            }

            String reserve10 = m.getReserve10();
            if (StringUtils.isNotBlank(reserve10)) {
                String fieldType10 = defectTypeMap.get("reserve10") == null ? null : defectTypeMap.get("reserve10").toString();
                if (fieldType10 != null) {
                    if ("3".equals(fieldType10) || "2".equals(fieldType10)) {
                        String[] split = reserve10.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve10").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve10(str);
                    }
                }
            }

            String reserve11 = m.getReserve11();
            if (StringUtils.isNotBlank(reserve11)) {
                String fieldType11 = defectTypeMap.get("reserve11") == null ? null : defectTypeMap.get("reserve11").toString();
                if (fieldType11 != null) {
                    if ("3".equals(fieldType11) || "2".equals(fieldType11)) {
                        String[] split = reserve11.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve11").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve11(str);
                    }
                }
            }

            String reserve12 = m.getReserve12();
            if (StringUtils.isNotBlank(reserve12)) {
                String fieldType12 = defectTypeMap.get("reserve12") == null ? null : defectTypeMap.get("reserve12").toString();
                if (fieldType12 != null) {
                    if ("3".equals(fieldType12) || "2".equals(fieldType12)) {
                        String[] split = reserve12.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve12").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve12(str);
                    }
                }
            }

            String reserve13 = m.getReserve13();
            if (StringUtils.isNotBlank(reserve13)) {
                String fieldType13 = defectTypeMap.get("reserve13") == null ? null : defectTypeMap.get("reserve13").toString();
                if (fieldType13 != null) {
                    if ("3".equals(fieldType13) || "2".equals(fieldType13)) {
                        String[] split = reserve13.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve13").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve13(str);
                    }
                }
            }

            String reserve14 = m.getReserve14();
            if (StringUtils.isNotBlank(reserve14)) {
                String fieldType14 = defectTypeMap.get("reserve14") == null ? null : defectTypeMap.get("reserve14").toString();
                if (fieldType14 != null) {
                    if ("3".equals(fieldType14) || "2".equals(fieldType14)) {
                        String[] split = reserve14.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve14").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve14(str);
                    }
                }
            }

            String reserve15 = m.getReserve15();
            if (StringUtils.isNotBlank(reserve15)) {
                String fieldType15 = defectTypeMap.get("reserve15") == null ? null : defectTypeMap.get("reserve15").toString();
                if (fieldType15 != null) {
                    if ("3".equals(fieldType15) || "2".equals(fieldType15)) {
                        String[] split = reserve15.split(",");
                        String str = "";
                        for (int i = 0; i < split.length; i++) {
                            str += commentsDicMap.get("reserve15").get(split[i]);
                            if (i + 1 < split.length) {
                                str += ",";
                            }
                        }
                        m.setReserve15(str);
                    }
                }
            }

            //缺陷大小类转换
            String parentDefectType = m.getParentDefectType();
            if (StringUtils.isNotBlank(parentDefectType)) {
                String parentValue = mapParentType.get(parentDefectType);
                if (parentValue != null && StringUtils.isNotBlank(parentValue)) {
                    m.setParentDefectType(parentValue);
                }
            }
            //转换小类
            if (StringUtils.isNotBlank(m.getDefectType())) {
                Map<String, String> childMap = mapChildType.get(m.getParentDefectType());
                if (childMap != null) {
                    String childValue = childMap.get(m.getDefectType());
                    if (childValue != null && StringUtils.isNotBlank(childValue)) {
                        m.setDefectType(childValue);
                    }
                }
            }

        });
        try {
            String[][] data = exportUtil.getExcelData(selectedFields, defect_list, defectNameMap);

            SXSSFWorkbook sxssfWorkbook = exportUtil.exportExcel(data);
            response.reset();
            OutputStream out = response.getOutputStream();
            String fileName = "缺陷信息" + DateUtil.getDateStr(new Date()) + System.currentTimeMillis() + ".xlsx";
            String mimeType = request.getSession().getServletContext().getMimeType(fileName);
            response.setHeader("content-type", mimeType + ";charset=UTF-8");
            String downloadName = DownLoadUtils.getName(agent, fileName);
            response.setHeader("Content-Disposition", "attachment;filename=" + downloadName);
//            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("utf-8");

            sxssfWorkbook.write(out);
            out.flush();
            out.close();

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private String convertHtmlToPlainText(String html) {
        if (StringUtils.isEmpty(html)) {
            return ("");
        }
        Document document = Jsoup.parse(html);
        Document.OutputSettings outputSettings = new Document.OutputSettings().prettyPrint(false);
        document.outputSettings(outputSettings);
        document.select("br").append("\\n");
        document.select("p").prepend("\\n");
        document.select("p").append("\\n");
        String newHtml = document.html().replaceAll("\\\\n", "\n");
        String plainText = Jsoup.clean(newHtml, "", Whitelist.none(), outputSettings);
        return StringEscapeUtils.unescapeHtml(plainText.trim());
    }

    /**
     * 拷贝手工执行附件到缺陷
     *
     * @param path
     * @param path
     */
    public List<DefectFile> copyFilebymanexecute(String path, String newPath, String filePath, Long defectResourceID, Map<String, Object> fileMap, List<DefectFile> listDefectFiles, String name) {
        // 改变文件存储路径
        if (paramConfig.getIsFtpOn()) {
            // FTP的服务可用
            try {
                // 新的文件目录
                // 连接FTP的服务
                FTPClient ftpClient = FtpUtil.connect();
                ftpClient.setCharset(Charset.forName("utf-8"));
                // 存储路径
                logger.info("FTP的路径：" + path);
                // 改变目录
                boolean existDir = true;
                StringTokenizer stringTokenizer = new StringTokenizer(path, "/");
                while (stringTokenizer.hasMoreElements()) {
                    String dir = stringTokenizer.nextElement().toString();
                    logger.info("正在进入目录：" + dir);
                    if (!ftpClient.changeWorkingDirectory(dir)) {
                        existDir = false;
                        break;
                    }
                }
                // 设置数据传输模式
                ftpClient.enterLocalPassiveMode();
                // 读取当前目录下所有文件
                FTPFile[] ftpFiles = null;
                if (existDir) {
                    ftpFiles = ftpClient.listFiles();
                }
                if (ftpFiles != null && ftpFiles.length > 0) {
                    for (FTPFile file : ftpFiles) {
                        byte[] bytes = null;
                        // 创建Bean的对象
                        DefectFile bean = new DefectFile();
                        // 读取文件名称
                        String fileName = file.getName();
                        // 转码
                        fileName = new String(fileName.getBytes("iso-8859-1"), "utf-8");

                        if (StringUtils.isNotEmpty(fileMap.get(fileName).toString())) {
                            //设置resoureID
                            bean.setResourceID(generateResourceID());
                            // 文件名称
                            bean.setName(fileMap.get(fileName).toString());
                            // 设置扩展名称
                            bean.setExtname(Utils.getPostfix(fileName));
                            // 设置缺陷记录的rid
                            bean.setDefectResourceID(defectResourceID);
                            bean.setSize(String.valueOf(file.getSize()));
                            bean.setUploadUserName(name);
                            bean.setPath(filePath);
                            // 加入列表
                            listDefectFiles.add(bean);
                            ftpClient.setConnectTimeout(600000);
                            logger.info("读取附件开始-------------");
                            InputStream in = ftpClient.retrieveFileStream(fileName);
                            bytes = input2byte(in);
                            in.close();
                            ftpClient.completePendingCommand();
                            logger.info("读取附件结束-------------");
                            try {
                                String stringame = bean.getResourceID() + bean.getExtname();
                                String newName = new String(stringame.getBytes("utf-8"), "iso-8859-1");
                                FtpUtil.upload(newPath, newName, bytes);
                            } catch (JettechException e) {
                                e.printStackTrace();
                            }
                        }

                    }

                }
                // 关闭
                ftpClient.disconnect();

            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("从FTP读取记录图片出错。");
            }

        } else {
            // FTP服务不可用
            // 存储路径
            // 创建根的路径
            File dirFile = new File(path);
            if (dirFile.exists()) {
                // 新的文件目录
                // 创建目录
                File newDirFile = new File(newPath);
                if (!newDirFile.exists()) {
                    newDirFile.mkdirs();
                }

                // 文件列表
                File[] dataFiles = dirFile.listFiles();
                if (dataFiles != null && dataFiles.length > 0) {
                    for (File dataFile : dataFiles) {
                        // 创建Bean的对象
                        DefectFile bean = new DefectFile();
                        // 读取文件名称
                        String fileName = dataFile.getName();
                        //设置resoureID
                        bean.setResourceID(generateResourceID());
                        // 文件名称
                        bean.setName(fileMap.get(fileName).toString());
                        // 设置扩展名称
                        bean.setExtname(Utils.getPostfix(fileName));
                        // 设置缺陷记录的rid
                        bean.setDefectResourceID(defectResourceID);
                        bean.setPath(filePath);
                        bean.setSize("");
                        bean.setUploadUserName(name);
                        // 加入列表
                        listDefectFiles.add(bean);
                        // 移动文件
                        File newFile = new File(newDirFile, bean.getResourceID() + bean.getExtname());
                        // 移动文件
                        try {
                            Files.copy(dataFile, newFile);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        return listDefectFiles;
    }

    /**
     * @Method: getUserByDefectState
     * @Description: 缺陷状态查询用户
     * @Param: " [defectStateKey, defectProcessResourceID] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/7/23
     */
    public Result getUserByDefectState(String currentStateResourceID, String testSystemResourceID, String name, String rows, String page) {
        if (StringUtils.isEmpty(currentStateResourceID)) return Result.renderError("传值有误，请重新操作！");
        List<ProcessConfigure> pcList = processConfigureService.findByProcessStateResourceID(Long.parseLong(currentStateResourceID));
        if (pcList == null || pcList.isEmpty()) return Result.renderSuccess();
        List<ProcessConfigureUserGroup> pcUserGroupList = processConfigureUserGroupService.findProcessUserGroupByProcessConfigureResourceIDs(pcList);
        if (pcUserGroupList == null || pcUserGroupList.isEmpty())
            return Result.renderError("请维护状态可操作的角色！");
        List<String> userGroupRids = pcUserGroupList.stream().map(s -> s.getUserGroupResourceID().toString())
                .collect(Collectors.toList());
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userGroupRids", userGroupRids);
        map.put("testSystemResourceID", testSystemResourceID);
        map.put("name", name);
        map.put("rows", rows);
        map.put("page", page);
        Result result = feignBugToBasic.findUserByUserGroupRidsAndProejectRid(map);
        if (result == null || result.getCode() != 20000) return Result.renderError("基础服务异常！");
        return Result.renderSuccess(result.getObj());
    }
    /**
     * 根据条件分页询缺陷列表(对外提供接口)
     * @param pageRequest
     * @param defectConfigDTO
     * @return
     * <AUTHOR>
     */
	/*@Override
	public Result<?> queryDefectInformationByCondition(DefectConfigDTO defectConfigDTO, PageRequest pageRequest,String userNumber,String passWord) throws NoSuchFieldException, IllegalAccessException, DefectImportException {
		//用户名和密码
		if(StringUtils.isNoneEmpty(userNumber) && StringUtils.isNoneEmpty(passWord)){
			JettechUserDTO jettechUserDTO = feignBugToBasic.findByNumber(userNumber,"");
			if(jettechUserDTO == null){
				return Result.renderError("当前用户不存在！");
			}
			if(!jettechUserDTO.getPassword().equals(PrivideInterfaceUtils.md5PWD(passWord))){
				return Result.renderError("密码有误！");
			}
			//查询字段配置
			Result<?> defectConfig = feignBugToBasic.findDefectBasicConfig("defect", "");
			if(defectConfig == null || defectConfig.getObj() == null) {
				return Result.renderError("读取缺陷配置文件失败！");
			}
			List<Map<String,Object>> defectField = (List<Map<String, Object>>) defectConfig.getObj();
			List<Map<String,Object>> reserveList = new ArrayList<>();
			for (Map<String,Object> map : defectField){
				//判断缺陷备用字段是否是下拉框,如果是下拉框需要获取对应的数据字典值
				if(map.get("name").toString().startsWith("reserve") && ("2".equals(map.get("fieldType")) || "3".equals(map.get("fieldType")))){
					reserveList.add(map);
				}
			}
			List<Map<String,Object>> configurations = (List<Map<String, Object>>) defectConfig.getObj();
			//筛选出多选下拉框的备用字段
			List<Map<String, Object>> multiComboboxS = configurations.stream().filter(
					config -> config.get("name").toString().indexOf("reserve") != -1
							&& config.get("fieldType").toString().equals("3")).collect(Collectors.toList());
			//筛选出单选下拉框的备用字段
			List<Map<String, Object>> radioComboboxS = configurations.stream().filter(
					config -> config.get("name").toString().indexOf("reserve") != -1
							&& config.get("fieldType").toString().equals("2")).collect(Collectors.toList());
			//筛选出时间选择框的备用字段
			List<Map<String, Object>> timeComboboxS = configurations.stream().filter(
					config -> config.get("name").toString().indexOf("reserve") != -1
							&& config.get("fieldType").toString().equals("5")).collect(Collectors.toList());
			//单行文本，多行文本，多选下拉，都模糊搜索
			List<Map<String, Object>> otherComboboxS = configurations.stream().filter(
					config -> config.get("name").toString().indexOf("reserve") != -1
							&& (config.get("fieldType").toString().equals("1")||
							config.get("fieldType").toString().equals("4"))).collect(Collectors.toList());

			//处理备用字段可能存在时间的问题
			defectConfigDTO = ConvertDefectConfigDTO.convertDTOtimeComboboxS(defectConfigDTO, timeComboboxS);
			defectConfigDTO = ConvertDefectConfigDTO.convertDTOradioComboboxS(defectConfigDTO, radioComboboxS);
			defectConfigDTO = ConvertDefectConfigDTO.convertDTOmultiComboboxS(defectConfigDTO, multiComboboxS);
			defectConfigDTO = ConvertDefectConfigDTO.convertDTOotherComboboxS(defectConfigDTO, otherComboboxS);
			Page<Defect> pageInfo = defectDao.initDefctListBySearchInfo(pageRequest,defectConfigDTO);
			List<Defect> defectList = pageInfo.getContent();
			if(!defectList.isEmpty()) {
				Set<String> parentTypeKey = new HashSet<>();
				defectList.stream().forEach(e ->{
					if(StringUtils.isNoneEmpty(e.getParentDefectType())) {
						parentTypeKey.add(e.getParentDefectType());
					}
				});
				if(!parentTypeKey.isEmpty()) {
					//查找缺陷大类型的所有数据
					List<DataDictionaryDTO> dataDics = defectDao.findDefectTypeDics();
					//大类key找value
					Map<String,String> mapParentType = new HashMap<>();
					//大类lkey找小类map
					Map<String,Map<String,String>> mapChildType = new HashMap<>();
					dataDics.stream().forEach(e ->{
						mapParentType.put(e.getName(), e.getInfoName());
					});
					mapChildType= dataDics.stream().collect(Collectors.groupingBy(
							DataDictionaryDTO::getName, Collectors.toMap(DataDictionaryDTO::getValue, DataDictionaryDTO::getTextName)));
					for (Defect e : defectList) {
						if(StringUtils.isNoneEmpty(e.getParentDefectType())) {
							String parentValue = e.getParentDefectType();
							e.setParentDefectType(mapParentType.get(e.getParentDefectType()));
							if(StringUtils.isNoneEmpty(e.getDefectType())) {
								if(!org.springframework.util.StringUtils.isEmpty(mapChildType.get(parentValue))) {
									e.setDefectType(mapChildType.get(parentValue).get(e.getDefectType()));
								}
							}
						};
					}

				}
				privideInterfaceUtils.convertDics(defectList);
				this.convertDataDictionaryValue(defectList, reserveList);
			}
			return Result.renderSuccess(pageInfo);
		}
		return Result.renderError("用户名或者密码为空！");
	}*/
    /**
     * 缺陷信息维护，修改缺陷(对外提供接口)
     * @param userNumber
     * @param passWord
     * @param defectConfigDTO
     * @return
     * <AUTHOR>
     */
	/*@Override
	@Transactional(rollbackFor = Exception.class)
	public Result<?> updateDefectInformation(DefectConfigDTO defectConfigDTO, String userNumber, String passWord,String resourceID) {
		//用户名和密码
		if (StringUtils.isNoneEmpty(userNumber) && StringUtils.isNoneEmpty(passWord)) {
			JettechUserDTO jettechUserDTO = feignBugToBasic.findByNumber(userNumber, "");
			if (jettechUserDTO == null) {
				return Result.renderError("当前用户不存在！");
			}
			if (!jettechUserDTO.getPassword().equals(PrivideInterfaceUtils.md5PWD(passWord))) {
				return Result.renderError("密码有误！");
			}
			if (!StringUtils.isNoneEmpty(resourceID)){
				return Result.renderError("当前缺陷的业务主键resourceID参数为空！");
			}
			Defect defectFind = this.findByResourceID(Long.valueOf(resourceID));
			if (defectFind == null) {
				return Result.renderError("当前缺陷不存在或已被删除！");
			}
			if (!StringUtils.isNoneEmpty(defectConfigDTO.getDefectState())){
					return Result.renderError("缺陷流转状态不能为空!");
			}
			//处理人不能为空
			if (!StringUtils.isNoneEmpty(defectConfigDTO.getHandleUser())) {
				return Result.renderError("处理人不能为空!");
			}
			JettechUserDTO HandleUserDTO  = feignBugToBasic.findByUserName(defectConfigDTO.getHandleUser());
			if (HandleUserDTO == null) {
				return Result.renderError("当前处理人用户不存在!");
			}
			defectConfigDTO.setHandleUserNumber(HandleUserDTO.getNumber());
			//校验当前传入的参数是否符合字典值
			String valiResult = privideInterfaceUtils.validateDictionaryKey(defectConfigDTO);
			if(!"pass".equals(valiResult)){
				return Result.renderError(valiResult);
			}
			//重新打开次数校验（缺陷从闭环状态变为非闭环状态时记一次）
			//当前配置的闭环状态
			//查询当前所有的流转状态
			DefectProcess defectProcess = defectProcessService.findDefectprocessByProjectResourceID(String.valueOf(defectFind.getSubordinateProjectResourceID()));
			if(defectProcess == null){
				return Result.renderError("当前缺陷流程不存在!");
			}
			List<ProcessState> allProcessState = processStateService.findByDefectProcessRID(defectProcess.getResourceID());
			//当前流程的各种状态map
			Map<String,String> mapState = new HashMap<>();
			Map<String,Long> mapStateForConvert = new HashMap<>();
			allProcessState.stream().forEach(e ->{
				mapState.put(e.getStateKey(),e.getStateValue());
				mapStateForConvert.put(e.getStateKey(),e.getResourceID());
			});
			if(!mapState.keySet().contains(defectConfigDTO.getDefectState())){
				return Result.renderError("当前流转状态有误，当前流程不存在当前流转状态!");
			}
			//状态改变时，才判断可流转的状态
			if(!defectConfigDTO.getDefectState().equals(defectFind.getDefectState())){
				//获取可流转状态,判断当前传入的状态是否是可以流转的状态
				List<ProcessConfigure> nextProcessConfigures  =  this.findDefectNextTransferStatus(String.valueOf(defectFind.getCurrentStatusResourceID()), String.valueOf(defectFind.getSubordinateProjectResourceID()));
				long countValidate = nextProcessConfigures.stream().filter(e ->e.getTransferStateKey().equals(mapState.get(defectConfigDTO.getDefectState()))).count();
				if(countValidate == 0){
					return Result.renderError("当前状态不是当前缺陷的可流转状态!");
				}
			}
			//查询当前项目配置的缺陷流程起始状态(一个流程只有一个开始节点，但是可以有多个结束节点，不能没有结束节点)
			List<ProcessState> processState = processStateService.findDefectProcessStartOrEndStatus(String.valueOf(defectFind.getSubordinateProjectResourceID()), END);
			if (processState.isEmpty()) {
				return Result.renderError("当前缺陷流程配置有误！没有结束节点！");
			}
			List<String> endValues = processState.stream().map(x -> x.getStateValue()).collect(Collectors.toList());
			if (endValues.contains(defectFind.getDefectState()) && !endValues.contains(defectConfigDTO.getDefectState())) {
				//缺陷从闭环状态变为非闭环状态时记一次
				int current = Integer.valueOf(defectFind.getReopenTimes());
				defectConfigDTO.setReopenTimes(String.valueOf(++current));
				//闭环时间重置
				defectConfigDTO.setOffTime(null);
			}
			//闭环时间(之前不是闭环状态，现在是闭环状态)
			if (!endValues.contains(defectFind.getDefectState()) && endValues.contains(defectConfigDTO.getDefectState())) {
				defectConfigDTO.setOffTime(new Date());
			}
			Map<Object, Object> processStateDic = redisUtils.getHashEntries("缺陷流程状态配置");
			Map<String, String> processStateDictionary = new HashMap<String, String>();
			for (Object key : processStateDic.keySet()) {
				processStateDictionary.put(String.valueOf(key), String.valueOf(processStateDic.get(key)));
			}
			//处理缺陷归属开发人员（修改缺陷时，如果当前人员的角色是开发人员，那么将当前人员更新到表，如果角色不是开发人员，则不更新）
			//查找当前人的人员角色
			Result<?> groupDTOResult = feignBugToBasic.findCurrentUserUserGroup(String.valueOf(jettechUserDTO.getNumber()));
			List<Map<String, Object>> groupDTOList = (List<Map<String, Object>>) groupDTOResult.getObj();
			List<String> groupNamesList = groupDTOList.stream().map(x -> String.valueOf(x.get("name"))).collect(Collectors.toList());
			if (!groupNamesList.isEmpty()) {
				if (groupNamesList.contains(DEVELOPER)) {
					defectConfigDTO.setBelongDeveloperName(jettechUserDTO.getUserName());
					defectConfigDTO.setBelongDeveloperNumber(jettechUserDTO.getNumber());
				}
			}
			if ("已修复".equals(processStateDictionary.get(defectConfigDTO.getDefectState()))) {
				defectConfigDTO.setBelongDeveloperName(jettechUserDTO.getUserName());
				defectConfigDTO.setBelongDeveloperNumber(jettechUserDTO.getNumber());
			}
			//更新前处理变更历史表
			List<DefectOperationHistory> historyForSave = privideInterfaceUtils.getHistoryInfo(defectFind,defectConfigDTO,jettechUserDTO, mapStateForConvert);
			if(historyForSave!= null){
				defectOperationHistoryService.save(historyForSave, jettechUserDTO.getUserName());
			}
			//更新操作
			defectFind = this.update(defectFind, userNumber);

			// 消息通知更新
			String handleUserNumber = defectFind.getHandleUserNumber();
			if (handleUserNumber != null && !handleUserNumber.equals("")) {
				// 添加缺陷的消息通知
				Map<String, Object> notifyMap = new HashMap<>();
				notifyMap.put("type", "1");// 消息类型：1-缺陷，2-要求
				notifyMap.put("level", "");
				if (!org.springframework.util.StringUtils.isEmpty(defectFind.getSeverity())) {
					notifyMap.put("level", defectFind.getSeverity());
				}
				// notifyMap.put("levelName", "");
				notifyMap.put("content", defectFind.getDefectTitle());
				notifyMap.put("allotUserNumber", jettechUserDTO.getNumber());
				notifyMap.put("handleUserName", defectFind.getHandleUser());
				notifyMap.put("handleUserNumber", defectFind.getHandleUserNumber());
				notifyMap.put("allotTime", defectFind.getEditTime());
				notifyMap.put("targetResourceID", defectFind.getResourceID());
				notifyMap.put("operaterUserNumber", jettechUserDTO.getNumber());
				Result<?> result2 = feignBugToBasic.addAndUpdataDefectNotify(notifyMap);
				if (!result2.isSuccess()) {
					return Result.renderError("新增缺陷消息失败");
				}
			}
			return Result.renderSuccess("更新成功！");
		}
		return Result.renderError("用户名或者密码为空！");
	}*/


    /**
     * @Method: updateDefectValueToKey
     * @Description: 字典值更新缺陷数据
     * @Param: " [] "
     * @return: void
     * @Author: wws
     * @Date: 2020/9/16
     */
    @Override
    public void updateDefectValueToKey() {
        defectDao.updateDefectValueToKey();
    }

    /**
     * @Method: updateOldDefect
     * @Description: 清洗缺陷旧数据
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/9/22
     */
    @Override
    public Result updateOldDefect() {
        Result projectRes = feignBugToDataDesign.findProjectByName("新核心测试项目");
        List<Map<String, Object>> projectObj = (List<Map<String, Object>>) projectRes.getObj();
        Map<String, Object> project = projectObj.get(0);

        Date date = DateUtil.getDate("2019-10-29 23:59:59", DateUtil.TIME_PATTREN);
        long scale = date.getTime();

        Result demandRes = feignBugToDataDesign.findAllDemand();
        List<Map<String, Object>> obj = (List<Map<String, Object>>) demandRes.getObj();
        //key：需求rid，value: 项目rid
        Map<String, String> demandMap = obj.stream().collect(Collectors.toMap(s -> s.get("resourceID").toString(), s -> s.get("testProjectResourceID").toString(), (k1, k2) -> k2));
        Map<String, String> projectMap = obj.stream().collect(Collectors.toMap(s -> s.get("testProjectResourceID").toString(), s -> s.get("testProjectName").toString(), (k1, k2) -> k2));
        List<Defect> defectAll = defectDao.findAll();
        ArrayList<Defect> updateList = new ArrayList<>();
        for (Defect defect : defectAll) {
            //1、根据需求寻找所属项目
            if (defect.getDemandResourceID() != null) {
                String projectResourceID = demandMap.get(defect.getDemandResourceID().toString());
                defect.setSubordinateProjectResourceID(Long.valueOf(projectResourceID));
                defect.setSubordinateProject(projectMap.get(projectResourceID));
                updateList.add(defect);
            } else {
                //2、创建时间在2019-10-29之前的数据,放在“新核心测试项目”项目下
                Date createTime = defect.getCreateTime();
                long time = createTime.getTime();
                //当前缺陷时间在规定时间之前
                if (scale - time >= 0) {
                    defect.setSubordinateProject(project.get("name").toString());
                    defect.setSubordinateProjectResourceID(Long.valueOf(project.get("resourceID").toString()));
                    updateList.add(defect);
                } else {
                    //剩余的缺陷按照创建人的所属项目复制给缺陷
                    String userNumber = defect.getCreateUser();

                }
            }
        }
        if (updateList.size() > 0) {
            this.update(updateList, "Admin");
        }
        return Result.renderSuccess();
    }

    /**
     * @Method: findForProjectResourceIDIsNull
     * @Description: 所属项目rid为空
     * @Param: " [] "
     * @return: java.util.List<com.jettech.model.Defect>
     * @Author: wws
     * @Date: 2020/9/23
     */
    @Override
    public List<Defect> findForProjectResourceIDIsNull() {
        return defectDao.findForProjectResourceIDIsNull();
    }

    /**
     * @Method: findAllExcludeDescription
     * @Description: 查询所有除了缺陷描述
     * @Param: " [] "
     * @return: java.util.List<com.jettech.model.Defect>
     * @Author: wws
     * @Date: 2020/10/22
     */
    @Override
    public List<Defect> findAllExcludeDescription() {
        return defectDao.findAllExcludeDescription();
    }

    /**
     * @Method: updateAllExcludeDescription
     * @Description: 更新缺陷除了描述
     * @Param: " [defectAll, admin] "
     * @return: java.util.List<com.jettech.model.Defect>
     * @Author: wws
     * @Date: 2020/10/22
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAllExcludeDescription(List<Defect> defectAll, String admin) {
        for (Defect d : defectAll) {
            d.setEditTime(new Date());
            d.setEditUser("Admin");
        }
        defectDao.updateAllExcludeDescription(defectAll);
    }


    /**
     * @Method: findBysubordinateProjectResourceIDIn
     * @Description:所属项目rid查询缺陷
     * @Param: " [projectRids] "
     * @return: java.util.List<com.jettech.model.Defect>
     * @Author: wws
     * @Date: 2020/10/26
     */
    @Override
    public List<Defect> findBysubordinateProjectResourceIDIn(List<String> projectRids) {
        return defectDao.findBysubordinateProjectResourceIDIn(projectRids);
    }

    /**
     * @param testCaseResourceID
     * @param @param             testCaseResourceID
     * @param @return            参数
     * @throws
     * @Title: findBugsByTestCaseResourceIDs
     * @Description: 根据当前案例查询案例所有的缺陷列表案例下的缺陷
     * <AUTHOR>
     */
    @Override
    public Result findBugsByTestCaseResourceIDs(String testCaseResourceID, String projectgroupResourceID) {
        List<String> rids = Arrays.asList(testCaseResourceID.split(","));
        List<Defect> listDefect = defectDao.findBugsByTestCaseResourceIDs(rids, projectgroupResourceID);
        if (listDefect != null && listDefect.size() > 0) {
            return Result.renderSuccess();
        }
        return Result.renderError();
    }

    /**
     * @param testTaskResourceID
     * @return com.jettech.dto.Result
     * <AUTHOR>
     * @description 手工执行任务查询案例下的缺陷
     * @date 2020年11月04日 16:43
     **/
    @Override
    public Result findBugsByTestTaskResourceID(String testTaskResourceID) {
        List<Defect> bugsByTestTaskResourceID = defectDao.findBugsByTestTaskResourceID(testTaskResourceID);
        return Result.renderSuccess(bugsByTestTaskResourceID);
    }

    @Value("${spring.datasource.driver-class-name}")
    private String driver;

    /**
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: bugFindDataForSearchInfo
     * @Description: 缺陷列表查询时，涉及到人员，系统/模块/交易、项目和需求等的筛选条件查询下拉列表数据（只查询缺陷率列表里面已有的数据）
     * <AUTHOR>
     */
    @Override
    public Result bugFindDataForSearchInfo(String dataFlag) {

        if (org.springframework.util.StringUtils.isEmpty(dataFlag)) {
            return Result.renderError("查询字段类型不能为空!");
        }
        String dataColumn = dataFlag;

        if (!FieldValidate.contains(dataFlag)) {
            return Result.renderError("查询字段类型非法!");
        }
        //被测系统testSystem
        if ("testSystem".equals(dataFlag)) {
            //判断数据库类型
            if (driver.contains("postgresql")) {
                dataColumn = dataFlag + " as \"" + dataFlag + "\"," + "testSystemResourceID as \"testSystemResourceID\"";
            } else {
                dataColumn = dataFlag + "," + "testSystemResourceID";
            }
        }
        //所属模块subordinateFeature
        else if ("subordinateFeature".equals(dataFlag)) {
            //判断数据库类型
            if (driver.contains("postgresql")) {
                dataColumn = dataFlag + " as \"" + dataFlag + "\"," + "subordinateFeatureResourceID as \"subordinateFeatureResourceID\"";
            } else {
                dataColumn = dataFlag + "," + "subordinateFeatureResourceID";
            }

        } else {
            if (driver.contains("postgresql")) {
                dataColumn = dataFlag + " as \"" + dataFlag + "\"";
            }
        }
        List<Map<String, Object>> findResult = null;
        //所属项目subordinateProject（返显列表为项目编号和项目名称拼接：[项目编号] 项目名称）
        if ("subordinateProject".equals(dataFlag)) {
            findResult = defectDao.bugFindProjectDataForSearchInfo();
        } else {//除了所属项目的
            findResult = defectDao.bugFindDataForSearchInfo(dataFlag, dataColumn);
        }
        if (findResult == null) {
            findResult = new ArrayList<Map<String, Object>>();
        }
        return Result.renderSuccess(findResult);
    }

    /**
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: bugFindModuleAndTradeBySystemRids
     * @Description: 根据被测系统级联查询模块和交易信息
     * <AUTHOR>
     */
    @Override
    public Result bugFindModuleAndTradeBySystemRids(String systemResourceIDs) {

        if (org.springframework.util.StringUtils.isEmpty(systemResourceIDs)) {
            return Result.renderError("被测系统参数不能为空!");
        }
        List<String> resourceIDs = Arrays.asList(systemResourceIDs.split(","));
        //根据被测系统查询下面的模块（缺陷表）
        List<Map<String, Object>> moduleResult = defectDao.bugFindModuleInfoBySystemRids(resourceIDs);
        //根据被测系统查询下面的交易（缺陷表）
        List<Map<String, Object>> tradeResult = defectDao.bugFindTradeInfoBySystemRids(resourceIDs);

        Map<String, Object> mapResult = new HashMap<String, Object>();
        mapResult.put("module", moduleResult == null ? new ArrayList<>() : moduleResult);
        mapResult.put("trade", tradeResult == null ? new ArrayList<>() : tradeResult);
        return Result.renderSuccess(mapResult);
    }

    /**
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: bugFindTradeByModuleRids
     * @Description: 根据模块级联查询交易信息(仅限于缺陷表中)
     * <AUTHOR>
     */
    @Override
    public Result bugFindTradeByModuleRids(String moduleResourceIDs) {

        List<Map<String, Object>> mapResult = defectDao.bugFindTradeByModuleRids(moduleResourceIDs);
        return Result.renderSuccess(mapResult);
    }

    /**
     * @Method: findByDemandNames
     * @Description: 所属需求名称查询
     * @Param: " [projectNameList] "
     * @return: java.util.List<com.jettech.model.Defect>
     * @Author: wws
     * @Date: 2020/11/11
     */
    @Override
    public List<Defect> findByDemandNames(List<String> projectNameList) {
        return defectDao.findByDemandNames(projectNameList);
    }

    /**
     * @Title: findExportDefect
     * @Description: 根据搜索条件查询导出数据
     * @Param: "[params]"
     * @Return: "java.util.List<com.jettech.model.Defect>"
     * @Author: li_yajiao
     * @Date: 2020/1/2
     */
    public List<Defect> findExportDefect(Map<String, Object> params) {
        return defectDao.findExportDefect(params);

    }


    /**
     * @Method: findDefectByPerformRids
     * @Description: 执行案例的rid查询
     * @Param: " [resourceIDs] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/11/18
     */
    @Override
    public Result findDefectByPerformRids(String resourceIDs) {
        List<Defect> list = defectDao.findDefectByPerformRids(resourceIDs);
        return Result.renderSuccess(list);
    }

    /**
     * @param defectResourceID
     * @return Result    返回类型
     * @throws
     * @Title: findDefaultTransferUser
     * @Description: 查询流转配置默认处理人
     * <AUTHOR>
     */
    @Override
    public Result findDefaultTransferUser(String defectResourceID, String transferStatusResourceID, String loginUserNumber) {
        //如果流转状态是空的
        if (org.springframework.util.StringUtils.isEmpty(transferStatusResourceID) || org.springframework.util.StringUtils.isEmpty(defectResourceID)) {
            return Result.renderSuccess(new ArrayList<Map<String, Object>>());
        }
        List<Map<String, Object>> finalResult = null;
        Defect currentDefect = this.findByResourceID(Long.valueOf(defectResourceID));
        //ProcessState currentStatus =processStateService.findByDefectByStateKeyAndProjectResourceID(currentDefect.getDefectState(),currentDefect.getSubordinateProjectResourceID().toString());
        Long processState = processStateService.findByDefectByStateKeyAndProjectResourceID2(currentDefect.getDefectState(), currentDefect.getSubordinateProjectResourceID().toString());
        //如果状态没变，就返回当前处理人数据
        if (Long.valueOf(transferStatusResourceID).equals(processState)) {
            String userKey = RedisCacheConfig.DEFECT_FIND_TRANSF_USERNUMBER + currentDefect.getHandleUserNumber();
            Object userDTO = this.jedisUtil.getObject(userKey);
            JettechUserDTO jettechUserDTO = null;
            if (userDTO == null) {
                jettechUserDTO = feignBugToBasic.findByNumber(currentDefect.getHandleUserNumber(), "");
                this.jedisUtil.setObject(userKey, jettechUserDTO, 60 * 30);
            } else {
                jettechUserDTO = (JettechUserDTO) userDTO;
            }
            if (jettechUserDTO != null) {
                finalResult = new ArrayList<>();
                Map<String, Object> mapObj = new HashMap<>();
                mapObj.put("userName", jettechUserDTO.getUserName());
                mapObj.put("number", jettechUserDTO.getNumber());
                mapObj.put("resourceID", jettechUserDTO.getResourceID());
                finalResult.add(mapObj);
                return Result.renderSuccess(finalResult);
            }
            return Result.renderError("数据有误！");
        }
        //ProcessConfigure processConfigure = processConfigureService.findCurrentToTransferorInfo(String.valueOf(processState),transferStatusResourceID);
        String defaultTransfer = this.processConfigureService.findCurrentToTransferorInfo2(String.valueOf(processState), transferStatusResourceID);
        if (defaultTransfer == null) {
            return Result.renderError("流程状态配置有误！");
        }
        if (!org.springframework.util.StringUtils.isEmpty(defaultTransfer)) {
            finalResult = new ArrayList<>();
            Map<String, Object> mapObj = new HashMap<>();
            String findNumber = "";
            if (CREATE_USER.equals(defaultTransfer)) {
                findNumber = currentDefect.getCreateUser();
            }
            if (UPDATE_USER.equals(defaultTransfer)) {
                findNumber = currentDefect.getEditUser();
            }
            if (HANDLE_USER.equals(defaultTransfer)) {
                findNumber = currentDefect.getHandleUserNumber();
            }
            if (LOGIN_USER.equals(defaultTransfer)) {
                findNumber = loginUserNumber;
            }

            String userKey = RedisCacheConfig.DEFECT_FIND_TRANSF_USERNUMBER + findNumber;
            Object userDTO = this.jedisUtil.getObject(userKey);
            JettechUserDTO jettechUserDTO = null;
            if (userDTO == null) {
                jettechUserDTO = feignBugToBasic.findByNumber(findNumber, "");
                this.jedisUtil.setObject(userKey, jettechUserDTO, 60 * 30);
            } else {
                jettechUserDTO = (JettechUserDTO) userDTO;
            }
            if (jettechUserDTO != null) {
                mapObj.put("userName", jettechUserDTO.getUserName());
                mapObj.put("number", jettechUserDTO.getNumber());
                mapObj.put("resourceID", jettechUserDTO.getResourceID());
                finalResult.add(mapObj);
            } else {
                return Result.renderError("数据有误！");
            }
        }
        if (finalResult == null) {
            finalResult = new ArrayList<>();
        }
        return Result.renderSuccess(finalResult);
    }

    /**
     * 查询缺陷描述富文本中包含图片的
     *
     * @return java.util.List<com.jettech.model.Defect>
     * <AUTHOR>
     * 15:21
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
//	@Override
//	public List<Integer> findByDefectDescription() {
//
//		return defectDao.findByDefectDescription();
//	}

    /**
     * @return com.jettech.dto.Result
     * <AUTHOR>
     * @description 根据所属项目resourceID查询项目先的缺陷
     * @date 2020年12月30日 17:05
     **/
    @Override
    public Result findBugsByTestProjectResourceID(String testProjectResourceID) {
        List<Defect> defectList = defectDao.findBysubordinateProjectResourceIDIn(Arrays.asList(testProjectResourceID));
        return Result.renderSuccess(defectList);
    }

    /**
     * 〈根据项目testProjectResourceID查询缺陷的创建人、负责人〉
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 17:47
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, String>> findbugsByTestProjectResourceID(String testProjectResourceID) {
        return defectDao.findbugsByTestProjectResourceID(testProjectResourceID);
    }

    /**
     * 缺陷导入查询单选
     *
     * @param singleSelect
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, String>> findExcelSingleSelectDic(List<String> singleSelect) {
        return defectDao.findExcelSingleSelectDic(singleSelect);
    }

    /**
     * 缺陷导入查询多选
     *
     * @param multiSelect
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, String>> findExcelSMultiSelectDic(List<String> multiSelect) {
        return defectDao.findExcelSMultiSelectDic(multiSelect);
    }

    /**
     * 缺陷导入查询缺陷大类
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, String>> findExcelParentDefectTypeDic() {
        return defectDao.findExcelParentDefectTypeDic();
    }

    /**
     * 查询项目和流程状态数据
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, Object>> findProjectAndStateData() {
        return defectDao.findProjectAndStateData();
    }

    /**
     * 查询需求数据
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, Object>> findDemandData() {
        return defectDao.findDemandData();
    }

    /**
     * 查询被测系统和交易数据数据
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, Object>> findSystemAndTradeData() {
        return defectDao.findSystemAndTradeData();
    }

    /**
     * 查询人员数据
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, Object>> findJettechUserData() {
        return defectDao.findJettechUserData();
    }

    /**
     * 查询重复的项目名称
     *
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * <AUTHOR>
     * 11:54
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<String> findDuplicationProject() {
        return defectDao.findDuplicationProject();
    }

    /**
     * @param userMap
     * @return tianliping
     * 2022年12月28日
     * @Description 批量修改 缺陷处理人姓名
     */
    @Override
    public Result<?> updateDefecthandleUserName(Map<String, String> userMap) {
        String userName = userMap.get("userName");
        String number = userMap.get("number");
        String loginUser = userMap.get("loginUserNumber");
        //根据人员编号查询信息
        List<Defect> defectList = defectDao.findByHandleUserNumber(number, "handle");
        defectList.stream().forEach(x -> x.setHandleUser(userName));
        if (defectList.size() > 0) {
            this.update(defectList, loginUser);
        }
        List<Defect> createdefectList = defectDao.findByHandleUserNumber(number, "create");
        createdefectList.stream().forEach(x -> x.setCreateUserName(userName));
        if (createdefectList.size() > 0) {
            this.update(createdefectList, loginUser);
        }
        return Result.renderSuccess();
    }


    @Override
    public Result<?> uploadDefectFilesTmp(String tmpDir, MultipartFile[] files, String userNumber) {
        // 文件输出的流
        FileOutputStream fos = null;
        try {

            // 存储路径
            String rootPath = DEFECT_FILE_TMP_DIR + File.separator + userNumber + File.separator + tmpDir;
            List<Map<String, String>> fileNames = new ArrayList<>();
            // 创建根的路径
            File dirFile = new File(rootPath);
            if (!dirFile.exists()) {
                dirFile.mkdirs();
            }
            for (MultipartFile caseResultRecordImage : files) {
                // 文件名称
                String fileName = caseResultRecordImage.getOriginalFilename();
                if (org.springframework.util.StringUtils.isEmpty(fileName) || "blob".equals(fileName)) {
                    fileName = "截图_" + DateUtil.getDateStr(new Date(), "yyyy-MM-dd_HH_mm_ss_S") + ".png";
                }
                Map<String, String> map = new HashMap<>();
                map.put("name", fileName);
                map.put("size", caseResultRecordImage.getSize() + "");
                fileNames.add(map);
                // 图片数据文件
                File imageDataFile = new File(dirFile, fileName);
                // 删除存在文件
                if (imageDataFile.exists()) {
                    imageDataFile.delete();
                }
                // 文件输出的流
                fos = new FileOutputStream(imageDataFile);
                fos.write(caseResultRecordImage.getBytes());
                fos.flush();
            }

            if (!fileNames.isEmpty()) {
                try {
                    BaseMessage message = new BaseMessage();
                    message.setToUser(userNumber);
                    message.setCmd(DEFECT_UPLOAD_IMAGE_CMD);
                    message.setMsg(JSON.toJSONString(fileNames));
                    feignBugToBasic.postMessageOne(message);
                } catch (Exception e) {
                }
            }

        } catch (Exception e) {
            return Result.renderError("上传缺陷图片出错:" + e.getMessage());
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.renderSuccess();
    }


    /**
     * 删除临时文件夹里的文件
     *
     * @param tmpDir
     * @param fileNames
     * @param userNumber
     * @return
     */
    @Override
    public Result<?> deleteDefectFilesTmp(String tmpDir, List<String> fileNames, String userNumber) {
        // 存储路径
        String rootPath = DEFECT_FILE_TMP_DIR + File.separator + userNumber + File.separator + tmpDir;
        // 创建根的路径
        File dirFile = new File(rootPath);
        if (dirFile.exists() && dirFile.isDirectory()) {
            File[] files = dirFile.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (fileNames.contains(file.getName())) {
                        file.delete();
                    }
                }
            }
        }
        return Result.renderSuccess();
    }


}
