package com.jettech.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Iterators;
import com.jettech.DTO.TestSystemDTO;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.*;
import com.jettech.common.util.DateUtil;
import com.jettech.dao.idao.ITestSystemDao;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToBugService;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import com.jettech.util.*;

import com.jettech.util.LongUtil;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.swing.text.html.HTMLDocument;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
public class TestSystemServiceImpl extends BaseHBServiceImpl<TestSystem> implements ITestSystemService {

    @Autowired
    private ITestSystemDao testSystemDao;
    @Autowired
    private ExecutorService executorService;
    @Autowired
    private ITradeService tradeService;
    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private ISystemModuleService systemModuleService;
    @Autowired
    private IUserinfoService userinfoService;
    @Autowired
    private IDemandTestSystemService demandTestSystemService;
    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasicService;
    @Autowired
    private ITestSystemUserService testSystemUserService;
    @Autowired
    private DataDicUtil dataDicUtil;
    @Autowired
    private IFeignDataDesignToBugService feignDataDesignToBugService;
    @Autowired
    private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;
    @Autowired
    private ILeftTreeConfigurationService leftTreeConfigurationService;
    @Autowired
    private IDemandService demandService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private JettoApiService jettoApiService;
    @Autowired
    private  ITradeTestManagerService iTradeTestManagerService;

    @PostConstruct
    public void postConstruct() {

        this.baseDao = testSystemDao;
    }

    /**
     * @return com.jettech.dto.Result<?>
     */
    @Override
    public Result<?> findSinglePointLeftTreeByProjectResourceID(String projectResourceID) {
        if (projectResourceID == null || "".equals(projectResourceID)) {
            return Result.renderError("查询参数为空!");
        }
        //projectResourceID为0，是从案例资产库请求，没有需求，值为null
        if ("0".equals(projectResourceID)) {
            projectResourceID = null;
        }

        //变更：系统创建后未关联需求也要在系统案例库中展示 pro-2759  2022/1107
        //List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceID(projectResourceID);
        List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceIDAndLeft(projectResourceID);
        //List<Map<String, Object>> leftTree = new ArrayList<Map<String, Object>>();
        //if (testSystemList != null && testSystemList.size() > 0) {
        getSinglePointLeftTree(testSystemList);
        //}
        return Result.renderSuccess(testSystemList);
    }

    @Override
    public List<Map<String, Object>> getSinglePointLeftTree(List<Map<String, Object>> testSystemList) {
        ArrayList<Map<String, Object>> results = new ArrayList<>(testSystemList.size());
        ArrayList<String> strings = new ArrayList<>(testSystemList.size());
        Map<Integer, Future<Map<String, Object>>> futures = new HashMap<>();
        for (Map<String, Object> map : testSystemList) {
            strings.add(map.get("resourceID") + "");
        }
        List<Map<String, Object>> moduleList = systemModuleService.findByTestSystemResourceIDs(strings);
        HashMap<String, List<Map<String, Object>>> parentRidKeyModule = new HashMap<>();
        for (Map<String, Object> map : moduleList) {
            List<Map<String, Object>> list;
            if (!parentRidKeyModule.containsKey(map.get("parentResourceID").toString())) {
                list = new ArrayList<>();
                parentRidKeyModule.put(map.get("parentResourceID").toString(), list);
            }
            parentRidKeyModule.get(map.get("parentResourceID").toString()).add(map);

        }
        List<Map<String, Object>> tradeList = tradeService.findByTestSystemResourceIDs(strings);
        HashMap<String, List<Map<String, Object>>> sysRidKeyTrade = new HashMap<>();
        HashMap<String, List<Map<String, Object>>> sysRidKeyAndMouleIsNullTrade = new HashMap<>();
        for (Map<String, Object> map : tradeList) {
            List<Map<String, Object>> list;
            if (map.get("moduleResourceID") == null || "".equals(map.get("moduleResourceID").toString())) {
                if (!sysRidKeyAndMouleIsNullTrade.containsKey(map.get("testSystemResourceID").toString())) {
                    list = new ArrayList<>();
                    sysRidKeyAndMouleIsNullTrade.put(map.get("testSystemResourceID").toString(), list);
                }
                sysRidKeyAndMouleIsNullTrade.get(map.get("testSystemResourceID").toString()).add(map);
            } else {
                if (!sysRidKeyTrade.containsKey(map.get("moduleResourceID").toString())) {
                    list = new ArrayList<>();
                    sysRidKeyTrade.put(map.get("moduleResourceID").toString(), list);
                }
                sysRidKeyTrade.get(map.get("moduleResourceID").toString()).add(map);
            }
        }
        int m= CheckUtil.checkLoop(testSystemList.size());
        for (int i = 0; i < m; i++) {
            Map<String, Object> map = testSystemList.get(i);
            List<Map<String, Object>> hashMaps = new ArrayList<>();
            map.put("children", hashMaps);
            List<Map<String, Object>> trade = sysRidKeyAndMouleIsNullTrade.get(map.get("resourceID").toString());
            List<Map<String, Object>> module = parentRidKeyModule.get(map.get("resourceID").toString());
            if (trade != null && trade.size() > 0) {
                hashMaps.addAll(trade);
            }
            if (module != null && module.size() > 0) {
                hashMaps.addAll(module);
                for (Map<String, Object> moduleMap : module) {
                    buildTree(moduleMap, parentRidKeyModule, sysRidKeyTrade);
                }
            }

        }
        return testSystemList;
    }

    @Override
    public List<TestSystem> findAllByDataAuth() {
        return testSystemDao.findAllByDataAuth();
    }

    @Override
    public List<Map<String, Object>> countQuoteRecord(List<Long> quoteResourceIDList, Date timeStart, Date timeEnd) {
        return testSystemDao.countQuoteRecord(quoteResourceIDList, timeStart, timeEnd);
    }

    @Override
    public List<Map<String, Object>> countCaseResultFile(List<Long> recordResourceIDList) {
        return testSystemDao.countCaseResultFile(recordResourceIDList);
    }

    private void buildTree(Map<String, Object> moduleMap, HashMap<String, List<Map<String, Object>>> parentRidKeyModule, HashMap<String, List<Map<String, Object>>> sysRidKeyTrade) {
        ArrayList<Map<String, Object>> childrens = new ArrayList<>();
        moduleMap.put("children", childrens);
        List<Map<String, Object>> moduleMaps = parentRidKeyModule.get(moduleMap.get("resourceID").toString());
        List<Map<String, Object>> trades = sysRidKeyTrade.get(moduleMap.get("resourceID").toString());
        if (trades != null && trades.size() > 0) {
            childrens.addAll(trades);
        }
        if (moduleMaps != null) {
            childrens.addAll(moduleMaps);
            for (Map<String, Object> childModule : moduleMaps) {
                buildTree(childModule, parentRidKeyModule, sysRidKeyTrade);
            }
        }
    }


    /**
     * @Title saveorUpdateTestSystem
     * @Description 新增或修改被测系统
     * @Params [request, paramsMap]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    @Override
    @Transactional
    public Result saveorUpdateTestSystem(TestSystem testSystem, String userNumber) {
        try {
            String token = HttpRequestUtils.getCurrentRequestToken();
            String name = testSystem.getName();
            String simpleName = testSystem.getSimpleName();
            Long resourceID = testSystem.getResourceID();
            if (name == null || "".equals(name)) {
                return new Result(false, 20001, "名称为必输项不能为空");
            }
            boolean result = this.verifySystemNameNotRepeated(name, resourceID);
            if (result) {
                return new Result(false, 20001, "系统名称已存在");
            }
            boolean b = this.verifySystemSimpleNameNotRepeated(simpleName, resourceID);
            if (b) {
                return new Result(false, 20001, "系统简称已存在");
            }
            if (null == testSystem.getManager() || null == testSystem.getProjectManager()) {
                return new Result(false, 20001, "系统人员必输项为空");
            }
            if(testSystem.getTestMode() == null){
                testSystem.setTestMode(0);
            }

            if (StringUtils.isEmpty(testSystem.getNumber())) {
                SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
                testSystem.setNumber(worker.genNextId() + "");
            }

            if (null == testSystem.getResourceID()) {
                // 新增被测系统
                testSystem = this.save(testSystem, userNumber);
            } else {
                //旧的系统信息中与人员的关系
                List<String> listAllold = new ArrayList<String>();
                TestSystem old = this.findByResourceID(resourceID);
                listAllold.add(old.getManager());
                listAllold.addAll(Arrays.asList(old.getProjectManager().split(",")));
                if (null != old.getSITTestManger()) {
                    listAllold.addAll(Arrays.asList(old.getSITTestManger().split(",")));
                }
                if (null != old.getSITTesters()) {
                    listAllold.addAll(Arrays.asList(old.getSITTesters().split(",")));
                }
                if (null != old.getDevelopPerson()) {
                    listAllold.addAll(Arrays.asList(old.getDevelopPerson().split(",")));
                }
                Set<String> userLists = new LinkedHashSet<>(listAllold);
                // 通过UserNumber获取人员resourceID
                List<JettechUserDTO> jettechUserDTOSold = feignDataDesignToBasicService.findByNumberIn(new ArrayList<>(userLists), token);
                List<String> userResourceIDListold = jettechUserDTOSold.stream().map(e -> e.getResourceID().toString()).collect(Collectors.toList());
                // 删除被测系统查询原先的关联关系
                List<TestSystemUser> testSystemUsersold = testSystemUserService.findbyTestSystemResourceID(String.valueOf(resourceID));
                List<TestSystemUser> deltsu = new ArrayList<>();
                // 删除原先的关联关系
                if (testSystemUsersold.size() > 0) {
                    for (String s : userResourceIDListold) {
                        for (TestSystemUser tsu : testSystemUsersold) {
                            if (s.equals(String.valueOf(tsu.getUserResourceID()))) {
                                deltsu.add(tsu);
                            }
                        }
                    }
                    if (deltsu.size() > 0) {
                        testSystemUserService.deleteInBatch(deltsu, userNumber);
                    }
                }
                // 修改被测系统的时候，同步更新defect表中的数据名称
                if (!old.getName().equals(testSystem.getName())) {
                    // 当名称不一致，调用同步方法
                    Result r = feignDataDesignToBugService.updateDefectSubordinateName("system", resourceID, testSystem.getName(), "", token);
                    if (!r.isSuccess()) {
                        return Result.renderError("同步更新defect表中的数据名称失败");
                    }
                    // 同步更改案例编写库的交易名称
                    Result testCaseResult = testCaseService.updateTestCaseSystemNameBySystemId(testSystem.getName(),testSystem.getResourceID());
                    if (!testCaseResult.isSuccess()) {
                        return Result.renderError("同步更新ds_testcase表中的数据名称失败");
                    }
                    // 同步更改案例执行中的交易名称
                    Result performcaseResult = feignDataDesignToManexecuteService.updatePerformcaseSystemNameBySystemId(testSystem.getName(),testSystem.getResourceID(),token);
                    if (!performcaseResult.isSuccess()) {
                        return Result.renderError("同步更新performcase表中的数据名称失败");
                    }
                }
                testSystem.setId(old.getId());
                testSystem.setCreateUser(old.getCreateUser());
                testSystem.setCreateTime(old.getCreateTime());
                testSystem = this.update(testSystem, userNumber);
            }
            //关联jettoApi的trade_folder表
            jettoApiService.saveOrUpdateSystemFolder(testSystem);

            // 将‘业务负责人’、‘行方项目经理’、‘SIT测试负责人’、‘SIT测试人员’ 、‘开发人员’添加到系统关联人员中
            List<String> listAll = new ArrayList<String>();
            listAll.add(testSystem.getManager());
            listAll.addAll(Arrays.asList(testSystem.getProjectManager().split(",")));
            if (null != testSystem.getSITTestManger()) {
                listAll.addAll(Arrays.asList(testSystem.getSITTestManger().split(",")));
            }
            if (null != testSystem.getSITTesters()) {
                listAll.addAll(Arrays.asList(testSystem.getSITTesters().split(",")));
            }
            if (null != testSystem.getDevelopPerson()) {
                listAll.addAll(Arrays.asList(testSystem.getDevelopPerson().split(",")));
            }
            Set<String> userList = new LinkedHashSet<>(listAll);
            // 通过UserNumber获取人员resourceID
            List<JettechUserDTO> jettechUserDTOS = feignDataDesignToBasicService.findByNumberIn(new ArrayList<>(userList), token);
            // 当新增、修改系统后，建立系统与人员关联关系
            Map<String, Object> params = new HashMap<String, Object>();
            List<String> userResourceIDList = jettechUserDTOS.stream().map(e -> e.getResourceID().toString()).collect(Collectors.toList());
            params.put("userResourceIDList", userResourceIDList);
            params.put("userNumber", userNumber);
            params.put("testSystemResourceID", testSystem.getResourceID());
            // 调用关联方法，内部已经将旧的关系删除了
            this.setSelectTestSystemUser(params);

            return Result.renderSuccess(testSystem);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("保存被测系统数据异常");
        }
    }


    /**
     * @Title deleteTestSystemByResourceID
     * @Description 删除被测系统
     * @Params [resourceID, userNumber]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    @Override
    @GlobalTransactional(name = "deleteTestSystemByResourceID", rollbackFor = Exception.class)
    public Result deleteTestSystemByResourceID(String resourceID, String userNumber) {
        return this.checkDeleteTestSystem(resourceID, userNumber);
    }

    /**
     * <AUTHOR>
     * @description 确认删除被测系统
     * @date 2020年11月25日 14:20
     * @param [resourceID, userNumber]
     * @return com.jettech.dto.Result
     **/
    @Override
    @GlobalTransactional(name = "deleteTestSystemByResourceID", rollbackFor = Exception.class)
    public Result confirmDeleteTestSystem(String resourceID, String userNumber) {
        Result result = this.checkDeleteTestSystem(resourceID, userNumber);
        if (!result.isSuccess()) {
            return result;
        }

        TestSystem testSystem = this.findByResourceID(LongUtil.parseLong(resourceID));
        // 查询被测系统与人员的关联关系
        List<TestSystemUser> testSystemUsers = testSystemUserService.findbyTestSystemResourceID(resourceID);
        // 删除系统与人员的关联关系
        if (!testSystemUsers.isEmpty() && testSystemUsers != null) {
            testSystemUserService.deleteInBatch(testSystemUsers, userNumber);
        }
        this.delete(testSystem, userNumber);

        //api联动删除trade_folder
        this.testSystemDao.deleteTradeFolderBySystem(resourceID);
        return Result.renderSuccess("删除成功");
    }

    /**
     * <AUTHOR>
     * @description 检验是否能删除被测系统
     * @date 2020年11月25日 14:23
     * @param [resourceID, userNumber]
     * @return com.jettech.dto.Result
     **/
    private Result checkDeleteTestSystem(String resourceID, String userNumber) {
        TestSystem testSystem = this.findByResourceID(LongUtil.parseLong(resourceID));
        List<DemandTestSystem> demandTestSystems = demandTestSystemService.findBytestSystemResourceID(testSystem.getResourceID());
        List<SystemModule> systemModules = systemModuleService.findbyTestSystemResourceID(resourceID);
        List<Trade> trades = tradeService.findbyTestSystemResourceID(resourceID, "", "", "");
        //判断系统下是否维护模块交易、需求
        if(!systemModules.isEmpty()||!trades.isEmpty()||!demandTestSystems.isEmpty()){
            return Result.renderError("当前系统下已经维护模块交易/关联需求不允许删除！");
        }
        return Result.renderSuccess();
    }

    @Override
    /**
     * @Title saveorUpdateTestSystem
     * @Description 查询当前被测系统下是否维护数据
     * @Params [request, paramsMap]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result findResourceBySystemResourceID(String resourceID) {
        List<SystemModule> systemModules = systemModuleService.findbyTestSystemResourceID(resourceID);
        List<Trade> trades = tradeService.findbyTestSystemResourceID(resourceID, "", "", "");
        if (systemModules.isEmpty() && trades.isEmpty()) {
            return Result.renderSuccess("当前被测系统下没有维护数据，请确认删除");
        }
        return Result.renderSuccess("当前被测系统下已维护数据，请确认删除");
    }


    /**
     * @return com.jettech.dto.Result
     */
    @Override
    public Result updateNodePosition(Map map) {
        String userNumber = String.valueOf(map.get("userNumber"));
        //被拖动节点的类型
        String type = String.valueOf(map.get("type"));
        //被拖动系统的新父节点Type
        String parentType = String.valueOf(map.get("parentType"));
        //被拖动节点的resourceID
        String resourceID = String.valueOf(map.get("resourceID"));
        //被拖动节点的新父节点resourceID
        String newParentResourceID = String.valueOf(map.get("newParentResourceID"));
        ArrayList<String> list = new ArrayList<>();
        List<Trade> tradeList = new ArrayList<>();
        try {
            switch (type) {
                case "module":
                    SystemModule sm = systemModuleService.findByResourceID(Long.valueOf(resourceID));
                    sm.setParentResourceID(Long.valueOf(newParentResourceID));
                    if ("system".equals(parentType)) {
                        sm.setTestSystemResourceID(Long.valueOf(newParentResourceID));
                    } else if ("module".equals(parentType)) {
                        SystemModule parentSM = systemModuleService.findByResourceID(Long.valueOf(resourceID));
                        sm.setTestSystemResourceID(parentSM.getTestSystemResourceID());
                    }
                    systemModuleService.update(sm, userNumber);

                    //模块下和交易关联的模块resourceID
                    findChildrenModule(sm.getResourceID(), list);
                    if (!list.isEmpty()) {
                        tradeList = tradeService.findBySystemModule(list);
                        tradeList.stream().forEach(e -> {
                            e.setTestSystemResourceID(Long.valueOf(newParentResourceID));
                        });
                        //更新交易
                        tradeService.update(tradeList, userNumber);
                    }
                    break;
                case "trade":
                    Trade trade = tradeService.findByResourceID(Long.valueOf(resourceID));
                    if ("system".equals(parentType)) {
                        trade.setTestSystemResourceID(Long.valueOf(newParentResourceID));
                    } else if ("module".equals(parentType)) {
                        SystemModule parentSM = systemModuleService.findByResourceID(Long.valueOf(newParentResourceID));
                        trade.setModuleResourceID(parentSM.getResourceID());
                        trade.setTestSystemResourceID(parentSM.getTestSystemResourceID());
                    }
                    tradeService.update(trade, userNumber);
                    break;
            }
            return Result.renderSuccess();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError();
        }
    }

    /**
     * 寻找交易
     */
    private void findChildrenModule(Long moduleResourceID, ArrayList<String> list) {
        List<SystemModule> sm_list = systemModuleService.findByParentResourceID(String.valueOf(moduleResourceID));
        if (sm_list.isEmpty()) {
            list.add(String.valueOf(moduleResourceID));
        }
        for (SystemModule sm : sm_list) {
            findChildrenModule(sm.getResourceID(), list);
        }
    }

    @Override
    /**
     * @Title findSystemManagement
     * @Description 查询系统负责人（下拉展示）
     * @Params [request]
     * @Return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @Date 2019/11/11
     */
    public Result<?> findSystemManagement() {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<JettechUserDTO> userList = feignDataDesignToBasicService.findAllPerson(token);
        List<Map<String, Object>> mapList = new ArrayList<>();
        userList.stream().forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("number", item.getNumber());
            map.put("userName", item.getUserName());
            map.put("resourceID", item.getResourceID());
            mapList.add(map);
        });
        return Result.renderSuccess(mapList);
    }

    /**
     * @Title verifySystemNameNotRepeated
     * @Description 校验项目下被测系统名称唯一
     * @Params
     * @Return
     * <AUTHOR>
     * @Date 2019/11/5
     */
    private boolean verifySystemNameNotRepeated(String name, Long nodeResourceID) {
        List<TestSystem> systemList = testSystemDao.verifySystemNameNotRepeated(name, nodeResourceID);
        if (systemList != null && systemList.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * @return com.jettech.dto.Result
     */
    @Override
    public Result whetherToRepeat(Map<String, String> paramsMap) {
        String name = paramsMap.get("name");
        if (StringUtils.isBlank(name)) {
            return Result.renderSuccess();
        }
        Long resourceID = LongUtil.parseLong(paramsMap.get("resourceID"));
        boolean result = this.verifySystemNameNotRepeated(name, resourceID);
        if (result) {
            return new Result(false, 20001, "系统名称已存在");
        }
        return Result.renderSuccess();
    }

    /**
     * @return com.jettech.dto.Result<?>
     */
    @Override
    public Result<?> SinglePointLeftTree(Object demandResourceIDObj, Object relationTradeMapObj, Object testPlanType) {
        if (demandResourceIDObj == null || "".equals(demandResourceIDObj)) {
            return Result.renderError("查询参数为空!");
        }
        List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceID(demandResourceIDObj.toString());
        List<Map<String, Object>> leftTree = new ArrayList<Map<String, Object>>();
        if ("UAT".equals(testPlanType)) {
            SystemModule systemModule = new SystemModule();
            Trade trade = new Trade();
            List<SystemModule> systemmodules = systemModuleService.findbySystemModuleName("UAT流程用例模块");
            List<Trade> trades = tradeService.findbyTradeName("UAT流程用例交易");
            if (null != systemmodules && systemmodules.size() > 0) {
                Optional<SystemModule> first1 = systemmodules
                        .stream()
                        .filter(s -> null == s.getTestSystemResourceID() && null == s.getParentResourceID())
                        .findFirst();
                if (first1.isPresent()) {
                    systemModule = first1.get();
                    List<Trade> trades1 = tradeService.findBySystemModuleResourceID(String.valueOf(systemModule.getResourceID()), null, null, null);
                    if (null != trades1 && trades1.size() > 0) {
                        Optional<Trade> first = trades1
                                .stream()
                                .filter(s -> null == s.getTestSystemResourceID() && null != s.getModuleResourceID())
                                .findFirst();
                        if (first.isPresent()) {
                            trade = first.get();
                        }
                    }
                }
            } else {
                // 树节点追加：UAT用例模块和UAT流程用例交易
                SystemModule s = new SystemModule();
                s.setName("UAT流程用例模块");
                systemModule = systemModuleService.save(s, "admin");
                Trade t = new Trade();
                t.setNumber("UAT");
                t.setName("UAT流程用例交易");
                t.setStatus("1");
                t.setComment("UAT流程用例交易");
                t.setModuleResourceID(systemModule.getResourceID());
                trade = tradeService.save(t, "admin");
            }
            Map<String, Object> uatMap = new HashMap<>();
            uatMap.put("name", trade.getName());
            uatMap.put("type", "trade");
            uatMap.put("resourceID", trade.getResourceID());
            uatMap.put("moduleResourceID", trade.getModuleResourceID());

            List<Map<String, Object>> children = new ArrayList<>();
            children.add(uatMap);
            Map<String, Object> uatMapParent = new HashMap<>();
            uatMapParent.put("name", systemModule.getName());
            uatMapParent.put("resourceID", systemModule.getResourceID());
            uatMapParent.put("type", "module");
            uatMapParent.put("children", children);
            testSystemList.add(0, uatMapParent);
        }
        if (testSystemList != null && testSystemList.size() > 0) {
            leftTree = getSinglePointLeftTreeAndCaseNumber(demandResourceIDObj.toString(), testSystemList, relationTradeMapObj, testPlanType);
        }
        return Result.renderSuccess(leftTree);
    }

    @Override
    public Result<?> findUserBySystem(String id) {

        if (id == null || "".equals(id)) {
            return Result.renderError("系统id为空！");
        }
        TestSystem testSystem = find(Long.parseLong(id));
        if (testSystem == null) {
            return Result.renderError("系统不存在！");
        }

        List<Map<String, Object>> mapList = testSystemDao.findUserBySystem(testSystem.getResourceID());
        return Result.renderSuccess(mapList);
    }

    /**
     * @Title: initializeTestSystemTable
     * @Description: 系统交易管理初始化被测系统table(分页 + 条件查询)
     * @Param: "[]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    @Override
    public Result initializeTestSystemTable(Map<String, String> params) {
        //测试方式数据处理
        String testMode = params.get("testMode");
        if(testMode == null || "".equals(testMode))
            testMode = "0";
        //测试方式数据处理
        testMode = BinaryDecimalUtil.DicValToBin(testMode);
        params.put("testMode", String.valueOf(Integer.parseInt(testMode,2)));
        return testSystemDao.initializeTestSystemTable(params);
    }

    /**
     * @Title: selectTestSystemUser
     * @Description: 返回已选和未选的人员列表
     * @Param: "[resourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    @Override
    public Result selectTestSystemUser(Long resourceID) {
        // 已选人员
        List<Map<String, Object>> selectUserMap = testSystemDao.selectTestSystemUser(resourceID);
        List<Object> selectUserIDs = selectUserMap.stream().map(s -> s.get("resourceID")).collect(Collectors.toList());
        String token = HttpRequestUtils.getCurrentRequestToken();
        //未选人员,需要排斥已选人员
        List<JettechUserDTO> allPerson = feignDataDesignToBasicService.findAllPerson(token);
        List<Map<String,Object>> unSelectUser = new ArrayList<>();
        List<Map<String,Object>> selectUser = new ArrayList<>();
        for (JettechUserDTO u : allPerson) {
            Map<String, Object> map = new HashMap<>();
            map.put("number", u.getNumber());
            map.put("userName", u.getUserName());
            map.put("resourceID", u.getResourceID());
            if (selectUserIDs.contains(u.getResourceID())) {
                selectUser.add(map);
            } else {
                unSelectUser.add(map);
            }
        }
        Map<String, List<Map<String,Object>>> maps = new HashMap<>();
        maps.put("selectUser", selectUser);
        maps.put("unSelectUser", unSelectUser);

        return Result.renderSuccess(maps);
    }

    private List<Map<String, Object>> getSinglePointLeftTreeAndCaseNumber(String demandResourceID, List<Map<String, Object>> testSystemList, Object relationTradeMapObj, Object testPlanType) {
        ArrayList<Map<String, Object>> results = new ArrayList<>(testSystemList.size());
        ArrayList<String> strings = new ArrayList<>(testSystemList.size());
        Map<Integer, Future<Map<String, Object>>> futures = new HashMap<>();
        for (Map<String, Object> map : testSystemList) {
            strings.add(map.get("resourceID") + "");
        }
        List<Map<String, Object>> moduleList = systemModuleService.findByTestSystemResourceIDs(strings);
        if ("UAT".equals(testPlanType)) {
            Map<String, Object> map = JSONObject.parseObject(JSON.toJSONString(testSystemList.get(0)));
            moduleList.add(0, map);
        }
        List<Map<String, Object>> tradeList = tradeService.findByTestSystemResourceIDs(strings);
        if ("UAT".equals(testPlanType)) {
            List<Map<String, Object>> children = (List<Map<String, Object>>) testSystemList.get(0).get("children");
            Map<String, Object> map = children.stream().map(Map::entrySet).flatMap(Set::stream).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (key, value) -> {
                return key + "," + value;
            }));
            tradeList.add(0, map);
        }
        if (relationTradeMapObj != null) {
            JSONArray relationTradeMapObj1 = (JSONArray) relationTradeMapObj;
            ArrayList arrayList = relationTradeMapObj1.toJavaObject(ArrayList.class);
            for (Object obj : arrayList) {
                HashMap hashMap = JSONObject.parseObject(obj.toString(), HashMap.class);
                Integer count = testCaseService.findCountNumberByTradeResourceID(hashMap.get("tradeResourceID").toString(), demandResourceID, String.valueOf(testPlanType));
                List<Map<String, Object>> testSytstemResourceID = tradeList.stream().filter(o -> hashMap.get("tradeResourceID").toString().equals(o.get("resourceID").toString())).collect(Collectors.toList());
                if (testSytstemResourceID == null || testSytstemResourceID.size() == 0) {
                    /*return new ArrayList<Map<String, Object>>();*/
                    continue;
                }
                testSytstemResourceID.get(0).put("relationTestCaseCount", hashMap.get("number"));
                testSytstemResourceID.get(0).put("allTestCaseCount", count);
            }
        }
        if ("UAT".equals(testPlanType)) {
            testSystemList.remove(0);
            testSystemList.add(0, moduleList.get(0));
        }
        int m=CheckUtil.checkLoop(testSystemList.size());
        for (int i = 0; i < m; i++) {
            Map<String, Object> map = testSystemList.get(i);
            List<Map<String, Object>> modulesInTestSystem = moduleList.stream().filter(o -> map.get("resourceID").equals(o.get("testSystemResourceID"))).collect(Collectors.toList());
            List<Map<String, Object>> tradesInTestSystem = tradeList.stream().filter(o -> map.get("resourceID").equals(o.get("testSystemResourceID"))).collect(Collectors.toList());
            if (modulesInTestSystem == null || modulesInTestSystem.size() == 0) {
                if (tradesInTestSystem == null || tradesInTestSystem.size() == 0) {
                    results.add(map);
                    continue;
                } else {
                    map.put("children", tradesInTestSystem);
                    results.add(map);
                    continue;
                }
            }
            Future<Map<String, Object>> future = executorService.submit(new LeftTreeCallable(map, modulesInTestSystem, tradesInTestSystem));
            futures.put(i, future);
            try {
                moduleList.removeAll(modulesInTestSystem);
                tradeList.removeAll(tradesInTestSystem);
            } catch (NullPointerException e) {

            }
        }
        for (Map.Entry<Integer, Future<Map<String, Object>>> entry : futures.entrySet()) {
            try {
                results.add(entry.getKey(), entry.getValue().get());
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
            }
        }
        return results;
    }

    /**
     * @param "[systemResourceID]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getTreeBySystemRid
     * @description: 单个被测系统的左侧树
     * <AUTHOR>
     * @date 2019/12/4 14:28
     */
    @Override
    public Result getTreeBySystemRid(String systemResourceID, String userNumber) {

        //查询该系统下关联的人员判断是否可以维护
//        Result result = selectTestSystemUser(Long.parseLong(systemResourceID));
//        HashMap<String, List<JettechUserDTO>> usersmap= (HashMap<String, List<JettechUserDTO>>) result.getObj();
//        List<JettechUserDTO> selectUser = usersmap.get("selectUser");
//        Boolean flag =false;
//        for (JettechUserDTO jettechUserDTO : selectUser) {
//            if (jettechUserDTO.getNumber().equals(userNumber)){
//                   flag=true;
//            }
//        }
//        if (!flag){
//            return Result.renderError("抱歉!你不是该系统下人员无法维护!");
//        }


        List<SystemModule> sm_list = systemModuleService.findbyTestSystemResourceID(systemResourceID);
        List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
        sm_list = sm_list.stream()
                .filter(e -> e.getParentResourceID().equals(e.getTestSystemResourceID())).collect(Collectors.toList());
        sm_list.forEach(e -> {
            HashMap<String, Object> map = new HashMap<>();
            map.put("id", e.getId());
            map.put("name", e.getName());
            map.put("simpleName", e.getSimpleName());
            map.put("resourceID", e.getResourceID());
            map.put("type", "module");
            map.put("isSystemChild", e.isSystemChild());
            map.put("childrenList", getChildren(e));
            map.put("parentID", e.getParentResourceID());
            listMap.add(map);
        });

        TestSystem ts = this.findByResourceID(Long.valueOf(systemResourceID));
        HashMap<String, Object> systemMap = new HashMap<>();
        systemMap.put("id", ts.getId());
        systemMap.put("name", ts.getName());
        systemMap.put("resourceID", ts.getResourceID());
        systemMap.put("type", "system");
        systemMap.put("childrenList", listMap);

        List<Map<String, Object>> maps = new ArrayList<>();
        maps.add(systemMap);
        return Result.renderSuccess(maps);
    }

    private List<Map<String, Object>> getChildren(SystemModule sm) {
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<SystemModule> sm_list = systemModuleService.findByParentResourceID(sm.getResourceID().toString());
        if (sm_list != null || !sm_list.isEmpty()) {
            sm_list.forEach(e -> {
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", e.getId());
                map.put("name", e.getName());
                map.put("resourceID", e.getResourceID());
                map.put("type", "module");
                map.put("childrenList", getChildren(e));
                map.put("parentID", e.getParentResourceID());
                listMap.add(map);
            });
        }
        return listMap;
    }

    @Override
    public String SavedemandUser(String userId, String systemModelresourceIDs) {

        String[] split = systemModelresourceIDs.split(",");
        List<String> arrayList = new ArrayList<>();
        for (String string : split) {
            arrayList.add(string);
        }
        User user = userinfoService.findById(Integer.valueOf(userId));
        if (user == null) {
            return "用户异常！";
        }
        //原来的
        List<TestSystem> findByusernumber = testSystemDao.findByusernumber(user.getNumber());
        //获取用户已经关联的系统
        Map<String, String> oldUser = new HashMap<>();
        for (TestSystem testSystem : findByusernumber) {
            oldUser.put(String.valueOf(testSystem.getResourceID()), "");
        }
        //新的
        List<TestSystem> findByResourceIDIn = testSystemDao.findByResourceIDIn(arrayList);
        //新增系统
        Map<String, String> newUser = new HashMap<>();
        for (TestSystem testSystem : findByResourceIDIn) {
            newUser.put(String.valueOf(testSystem.getResourceID()), "");
            if (oldUser.isEmpty() || !oldUser.isEmpty() && !oldUser.containsKey(String.valueOf(testSystem.getResourceID()))) {
                TestSystem testSystem2 = new TestSystem();
                testSystem2.setManager(user.getNumber());
                testSystem2.setResourceID(testSystem.getResourceID());
                testSystemDao.save(testSystem2);
            }
        }
        //删除取消关联的系统
        List<TestSystem> deleteList = new ArrayList<>();
        for (TestSystem testSystem : findByusernumber) {
            if (newUser.isEmpty() || !newUser.isEmpty() && !newUser.containsKey(String.valueOf(testSystem.getResourceID()))) {
                deleteList.add(testSystem);
            }
        }
        if (!deleteList.isEmpty()) {
            testSystemDao.deleteInBatch(deleteList);
        }
        return "删除成功";
    }

    /**
     * @Title: initializeTradeTable
     * @Description: 根据单个被测系统或模块查询关联的交易
     * @Param: "[map]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    @Override
    public Result initializeTradeTable(Map<String, String> map) {
        JSONObject result = new JSONObject();
        String type = map.get("type");
        String resourceID = map.get("resourceID");
        String name = map.get("name");
        String versionNumber = map.get("versionNumber");
        String executeMode = map.get("executeMode");
        Integer page = map.get("page") == null ? 1 : Integer.valueOf(map.get("page"));
        Integer pageSize = map.get("pageSize") == null ? 10 : Integer.valueOf(map.get("pageSize"));
        List<Trade> trades = null;
        switch (type) {
            case "system":
                PageHelper.startPage(page, pageSize);
                // 根据被测系统查询交易
                trades = tradeService.findbyTestSystemResourceID(resourceID, name, versionNumber, executeMode);
                break;
            case "module":
                // 根据当前模块查询交易
                List<SystemModule> modules = systemModuleService.findModuleAndSubModelByResId(resourceID);
                List<String> resIds = modules.stream().map(x -> x.getResourceID().toString()).collect(Collectors.toList());
                PageHelper.startPage(page, pageSize);
                trades = tradeService.findBySystemModuleResourceIDsIn(resIds, name, versionNumber, executeMode);
                break;
            default:
                break;
        }
        //查询交易类型
        List<Map<String, String>> tradeType = tradeService.findTradeDicsByDicName("TRADETYPE");
        Map<String, String> mapTradeType = new HashMap<>();
        if (!tradeType.isEmpty() && tradeType.size() > 0) {
            for (Map<String, String> mapType : tradeType) {
                mapTradeType.put(mapType.get("value"), mapType.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }
        List<Map<String, String>> executeModes = tradeService.findTradeDicsByDicName("executeMode");
        Map<String, String> mapExecuteMode = new HashMap<>();
        if (!executeModes.isEmpty() && executeModes.size() > 0) {
            for (Map<String, String> mapType : executeModes) {
                mapExecuteMode.put(mapType.get("value"), mapType.get("textName"));
            }
        } else {
            return Result.renderError("数据字典数据有误！");
        }
        String token = HttpRequestUtils.getCurrentRequestToken();
 //     Result r5 = feignDataDesignToBasicService.findTypeAllUser(token);
//        List<Map<String, String>> l5 = (List<Map<String, String>>) r5.getObj();
       List<TradeAndTestManager> findAll = iTradeTestManagerService.findAll();
        Map<Long, List<TradeAndTestManager>> tradeMap = findAll.stream()
                .collect(Collectors.groupingBy(s -> s.getTradeResourceID()));
        List<JettechUserDTO> allPerson = feignDataDesignToBasicService.findAllPerson(token);
        Map<String, String>    userMap = allPerson.stream().collect(Collectors.toMap(JettechUserDTO::getNumber, JettechUserDTO::getUserName, (entity1,entity2) -> entity1));

        if (trades != null) {
            for (Trade trade : trades) {
                trade.setType(mapTradeType.get(trade.getType()));
                trade.setExecuteMode(mapExecuteMode.get(trade.getExecuteMode()));
               // findAll
                List<TradeAndTestManager> list = tradeMap.get(trade.getResourceID()) ;
                if(list!=null) {
                	 List<String> mamangerss = list.stream().map(x->x.getBelongTestManager()).collect(Collectors.toList());
                	 List<String> userNameList=new ArrayList<>();
//                     for (Map<String, String> userMap : l5) {
//                         if (null!=mamangerss&&!mamangerss.isEmpty()) {
//                            if(mamangerss.contains(userMap.get("number"))) {
//                         	   String userName = userMap.get("userName");
//                                userNameList.add(userName);
//                            }
//                         }
//                     }

                     for(String mm:mamangerss) {
                    	 String userName = userMap.get(mm);
                    	 userNameList.add(userName);
                     }
                     String collect = userNameList.stream().collect(Collectors.joining(","));
                     trade.setBelongTestManagerString(collect);
                }

            }

            PageInfo<Trade> pageInfo = new PageInfo<Trade>(trades);
            // 当前页
            result.put("page", page);
            // 当前页的条数
            result.put("pageSize", pageSize);
            // 总的条数
            result.put("total", pageInfo.getTotal());
            // 总的页数
            result.put("totalPage", pageInfo.getPages());
            // 返回数据
            result.put("pageData", pageInfo.getList());
        }

        return Result.renderSuccess(result);
    }

    /**
     * @Title: setSelectTestSystemUser
     * @Description: 被测系统关联人员
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    @Override
    public Result setSelectTestSystemUser(Map<String, Object> params) {
        String testSystemResourceID = String.valueOf(params.get("testSystemResourceID"));
        List<String> userResourceIDList = (List<String>) params.get("userResourceIDList");
        String userNumber = String.valueOf(params.get("userNumber"));
//        // 根据被测系统查询原先的关联关系
//        List<TestSystemUser> testSystemUsers = testSystemUserService.findbyTestSystemResourceID(testSystemResourceID);
//        // 删除原先的关联关系
//        if (testSystemUsers.size() > 0) {
//            testSystemUserService.deleteInBatch(testSystemUsers, userNumber);
//        }
        // 保存被测系统新的关联关系
        List<TestSystemUser> list = new ArrayList<>();
        for (String s : userResourceIDList) {
            TestSystemUser testSystemUser = new TestSystemUser();
            testSystemUser.setTestSystemResourceID(Long.valueOf(testSystemResourceID));
            testSystemUser.setUserResourceID(Long.valueOf(s));
            list.add(testSystemUser);
        }
        List<TestSystemUser> save = null;
        if (list.size() > 0) {
            save = testSystemUserService.save(list, userNumber);
        }
        return Result.renderSuccess(save);
    }

    /**
     * @param "[dicName]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getDicValues
     * @description: 获取字典里的值
     * <AUTHOR>
     * @date 2019/12/6 11:13
     */
    @Override
    public Result getDicValues(String dicName) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        return feignDataDesignToBasicService.findByName(dicName, token);
    }

    /**
     * @Title: simpleNameWhetherToRepeat
     * @Description: 判断系统简称是否重复
     * @Param: "[paramsMap]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/10
     */
    @Override
    public Result simpleNameWhetherToRepeat(Map<String, String> paramsMap) {
        String simpleName = paramsMap.get("simpleName");
        if (StringUtils.isBlank(simpleName)) {
            return Result.renderSuccess();
        }
        Long resourceID = LongUtil.parseLong(paramsMap.get("resourceID"));
        boolean result = this.verifySystemSimpleNameNotRepeated(simpleName, resourceID);
        if (result) {
            return new Result(false, 20001, "系统简称已存在");
        }
        return Result.renderSuccess();
    }

    /**
     * @Title findDataDictionaryByDicName
     * @Description 数据字典查询
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/12/4
     */
    @Override
    public Result findDataDictionaryByDicName(String dicName) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        return feignDataDesignToBasicService.findByName(dicName, token);
    }

    /**
     * @param "[]"
     * @param page
     * @param pageSize
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getSITtestUser
     * @description: 返回所有的人员列表，也就是SIT测试人员和SIT测试负责人
     * <AUTHOR>
     * @date 2019/12/12 19:18
     */
    @Override
    public Result getSITtestUser(String name, String page, String pageSize) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        //未选人员,需要排斥已选人员
        Result<?> result = feignDataDesignToBasicService.findUserByName(page, pageSize, name, token);
        return result;
    }

    /**
     * @param "[]"
     * @param page
     * @param pageSize
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findTypeAllUser
     * @description: 返回内部机构下所有的人员列表，通过basic查询
     * <AUTHOR>
     * @date 2019/12/12 19:50
     */
    @Override
    public Result findTypeAllUser(String name, String page, String pageSize) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        return feignDataDesignToBasicService.findUserByDeptName(page, pageSize, name, token);
    }

    /**
     * @param testSystemResourceID
     * @return com.jettech.dto.Result<?>
     * @Description 查询被测系统下一级的模块和交易MAP
     * <AUTHOR>
     * @date 2019-12-12 20:42
     */
    @Override
    public Result<?> queryNextLowerLevelMap(Long testSystemResourceID) {
        List<HashMap<String, Object>> resultMap = systemModuleService.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);
        if (resultMap != null && resultMap.size() > 0) {
            List<HashMap<String, Object>> tradeMapList = tradeService.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);
            if (tradeMapList != null && tradeMapList.size() > 0) {
                resultMap.addAll(tradeMapList);
            }
        } else {
            resultMap = tradeService.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);
        }
        return Result.renderSuccess(resultMap);
    }

    /**
     * @param
     * @return
     * @Title: findRelevanceSystemID
     * @Description: 已关联系统ID
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @Override
    public List<String> findRelevanceSystemID(String resourceID) {
        return testSystemDao.findRelevanceSystemID(resourceID);
    }

    /**
     * @Title: findOneTestSystem
     * @Description: 根据id查询单个被测系统
     * @Param: "[systemResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/17
     */
    @Override
    public Result findOneTestSystem(Long systemResourceID) {
        TestSystem testSystem = testSystemDao.findByResourceID(systemResourceID);
        Integer testMode = testSystem.getTestMode();
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<JettechUserDTO> allPerson = feignDataDesignToBasicService.findAllPerson(token);
        Map<String, String> userMap = allPerson.stream().collect(Collectors.toMap(JettechUserDTO::getNumber, JettechUserDTO::getUserName));

        String manager = getUserNames(testSystem.getManager(), userMap);
        String pm = getUserNames(testSystem.getProjectManager(), userMap);
        String sitManager = getUserNames(testSystem.getSITTestManger(), userMap);
        String sitTester = getUserNames(testSystem.getSITTesters(), userMap);
        String devPerson = getUserNames(testSystem.getDevelopPerson(), userMap);

        testSystem.setManager(manager);
        testSystem.setProjectManager(pm);
        testSystem.setSITTestManger(sitManager);
        testSystem.setSITTesters(sitTester);
        testSystem.setDevelopPerson(devPerson);
        JSONObject json = JSONObject.parseObject(JSON.toJSONString(testSystem));
        json.put("testMode", BinaryDecimalUtil.TenToDicVal(testMode));
        return Result.renderSuccess(json);
    }

    public String getUserNames(String str, Map<String, String> userMap) {
        JSONArray array = new JSONArray();
        if (org.springframework.util.StringUtils.isEmpty(str)) {
            return array.toString();
        } else {
            String[] split = str.split(",");

            for (int i = 0; i < split.length; i++) {
                JSONObject json = new JSONObject();
                json.put("text", userMap.get(split[i]));
                json.put("number", split[i]);
                array.add(json);
            }
            return array.toString();
        }
    }


    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findOutDept
     * @description: 查询外部机构下所有的子机构
     * <AUTHOR>
     * @date 2019/12/12 20:11
     */
    @Override
    public Result findOutDept() {
        String token = HttpRequestUtils.getCurrentRequestToken();
        return feignDataDesignToBasicService.findOutDept(token);
    }

    /**
     * @Title: verifySystemSimpleNameNotRepeated
     * @Description: 判断系统简称是否重复
     * @Param: "[simpleName, resourceID]"
     * @Return: "boolean"
     * @Author: xpp
     * @Date: 2019/12/10
     */
    private boolean verifySystemSimpleNameNotRepeated(String simpleName, Long resourceID) {
        if (StringUtils.isBlank(simpleName)) {
            return false;
        }
        List<TestSystem> systemList = testSystemDao.verifySystemSimpleNameNotRepeated(simpleName, resourceID);
        if (systemList != null && systemList.size() > 0) {
            return true;
        }
        return false;
    }


    @Override
    /**
     * @Title findAllSystem
     * @Description 查询所有被测系统
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/12/13
     */
    public Result findAllSystem() {
        List<TestSystem> all = this.findAll();
        return Result.renderSuccess(all);
    }

    /**
     * @param "[agent, response]"
     * @return void
     * @throws
     * @Title: downloadModal
     * @description: 模板下载
     * <AUTHOR>
     * @date 2019/12/27 14:38
     */
    @Override
    public void downloadModal(String agent, HttpServletResponse response) {
        dataDicUtil.getDicMap("testSystem");
        //重要级别
        List<Map<String, Object>> important_list = dataDicUtil.getList("important");
        //业务归口部门
        List<Map<String, Object>> centralizedBusinessDepartment_list = dataDicUtil.getList("CentralizedBusinessDepartment");
        String[] centralizedBusinessDepartment_arr = centralizedBusinessDepartment_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);
        // 所属研发中心(数据字典)
        //List<Map<String, Object>> developmentCenter_list = dataDicUtil.getList("developmentCenter");
        //测试方式
        List<Map<String, Object>> testModeList = dataDicUtil.getList("testMode");
        String[] testMode_arr = testModeList.stream().map(x -> x.get("textName").toString()).toArray(String[]::new);

        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet();
        sheet.setColumnWidth(0, 100 * 35);
        sheet.setColumnWidth(1, 100 * 35);
        sheet.setColumnWidth(2, 100 * 35);
        sheet.setColumnWidth(3, 100 * 35);
        sheet.setColumnWidth(4, 100 * 35);
        sheet.setColumnWidth(5, 100 * 35);
        sheet.setColumnWidth(6, 100 * 35);
        sheet.setColumnWidth(7, 100 * 35);
        sheet.setColumnWidth(8, 100 * 35);
        sheet.setColumnWidth(9, 100 * 35);
        sheet.setColumnWidth(10, 100 * 35);
//        sheet.setColumnWidth(11, 100 * 35);
//        sheet.setColumnWidth(12, 100 * 35);

        sheet.setDefaultRowHeight((short) (22 * 20));

        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);

        XSSFCellStyle warnStyle = wb.createCellStyle();
        warnStyle.setAlignment(HorizontalAlignment.CENTER);
        warnStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直
        warnStyle.setBorderBottom(BorderStyle.THIN);
        warnStyle.setBorderLeft(BorderStyle.THIN);
        warnStyle.setBorderRight(BorderStyle.THIN);
        warnStyle.setBorderTop(BorderStyle.THIN);

        XSSFFont font = wb.createFont();
        font.setBold(true);
        font.setColor(XSSFFont.COLOR_RED);
        warnStyle.setFont(font);

        //模板列
        XSSFRow row = sheet.createRow(0);
        row.setHeight((short) (22 * 20));

        XSSFCell simpleName = row.createCell(0);
        XSSFRichTextString sNameStr = new XSSFRichTextString("*系统简称");
        sNameStr.applyFont(0, 1, font);
        simpleName.setCellValue(sNameStr);
        simpleName.setCellStyle(cellStyle);

        XSSFCell name = row.createCell(1);
        XSSFRichTextString nameStr = new XSSFRichTextString("*系统名称");
        nameStr.applyFont(0, 1, font);
        name.setCellValue(nameStr);
        name.setCellStyle(cellStyle);

        XSSFCell busPCentralizedDepartment = row.createCell(2);
        XSSFRichTextString bpdStr = new XSSFRichTextString("*业务归口部门");
        bpdStr.applyFont(0, 1, font);
        busPCentralizedDepartment.setCellValue(bpdStr);
        busPCentralizedDepartment.setCellStyle(cellStyle);

        addComboBox(wb,sheet,centralizedBusinessDepartment_arr,2,2);
//        addComboBox(wb,sheet,testMode_arr,10,10);


        XSSFCell manager = row.createCell(3);
        XSSFRichTextString mStr = new XSSFRichTextString("*业务负责人");
        mStr.applyFont(0, 1, font);
        manager.setCellValue(mStr);
        manager.setCellStyle(cellStyle);

        XSSFCell developCompany = row.createCell(4);
        developCompany.setCellValue("开发公司");
        developCompany.setCellStyle(cellStyle);

        XSSFCell projectManager = row.createCell(5);
        XSSFRichTextString pmStr = new XSSFRichTextString("*行方开发经理");
        pmStr.applyFont(0, 1, font);
        projectManager.setCellValue(pmStr);
        projectManager.setCellStyle(cellStyle);

        XSSFCell SITTestManger = row.createCell(6);
        SITTestManger.setCellValue("SIT测试负责人");
        SITTestManger.setCellStyle(cellStyle);

        XSSFCell SITTesters = row.createCell(7);
        SITTesters.setCellValue("UAT测试负责人");
        SITTesters.setCellStyle(cellStyle);

        XSSFCell important = row.createCell(8);
        XSSFRichTextString iStr = new XSSFRichTextString("*重要级别");
        iStr.applyFont(0, 1, font);
        important.setCellValue(iStr);
        important.setCellStyle(cellStyle);

        XSSFCell developPerson = row.createCell(9);
        developPerson.setCellValue("开发人员");
        developPerson.setCellStyle(cellStyle);
        //测试方式
        XSSFCell testmode = row.createCell(10);
        XSSFRichTextString tmStr = new XSSFRichTextString("*测试方式");
        tmStr.applyFont(0, 1, font);
        testmode.setCellValue(tmStr);
        testmode.setCellStyle(cellStyle);
        addComboBox(wb,sheet,testMode_arr,10,10);
        String token = HttpRequestUtils.getCurrentRequestToken();
        //查询开发公司
        Result result = feignDataDesignToBasicService.findOutDept(token);
        List<Map<String, Object>> company_list = (List<Map<String, Object>>) result.getObj();
        String[] company_arr = company_list.stream().map(e -> e.get("name").toString()).toArray(String[]::new);
        //查询所有人员(sit测试人,sit负责人,)
        List<JettechUserDTO> allPerson = feignDataDesignToBasicService.findAllPerson(token);
        //查询业务负责人，行方经理
        Result hfResult = feignDataDesignToBasicService.findTypeAllUser(token);
        List<Map<String, Object>> fh_List = (List<Map<String, Object>>) hfResult.getObj();
        String[] hf_arr=fh_List.stream().map(x->x.get("userName").toString()).toArray(String[]::new);

        String[] important_arr = important_list.stream().map(e -> e.get("textName").toString()).toArray(String[]::new);
        this.setValidationData(sheet, 1, 1000, 8, 8, important_arr);

        addComboBox(wb,sheet,hf_arr,3,3);
        addComboBox(wb,sheet,hf_arr,5,5);
        wb.setSheetHidden(2, true);
        wb.setSheetHidden(3, true);
        wb.setSheetHidden(4, true);


        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            response.setHeader("Content-Disposition", "attachment;filename=" + resFileUtils.encodeDownloadFilename("modal.xlsx", agent));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            wb.write(out);
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    /**
     * @Method: addComboBox
     * @Description: sheet列表框添加下拉框
     * @Param: " [centralizedBusinessDepartment_arr, i, i1] "
     * @return: void
     * @Author: wws
     * @Date: 2021/4/6
     */
    private void addComboBox(XSSFWorkbook wb, XSSFSheet sheet, String[] arr, int firstCol, int endCol) {
        // 解决 业务归口部门 下拉列表值超过255，导致Excel数据 错误
        // 思路：新建了一个sheet，在这个sheet中某列输入实际需要的下拉列表内容（可以动态插入），然后隐藏这个sheet即可
        String sheetName = "hiddenSheet" + firstCol;
        XSSFSheet hiddenSheet = wb.createSheet(sheetName);
        for (int i = 0; i < arr.length; i++) {
            hiddenSheet.createRow(i).createCell(0).setCellValue(arr[i]);
        }
        // 创建名称，可被其他单元格引用
        Name category1Name = wb.createName();
        category1Name.setNameName(sheetName);
        // 设置名称引用的公式
        // 使用像'A1：B1'这样的相对值会导致在Microsoft Excel中使用工作簿时名称所指向的单元格的意外移动，
        // 通常使用绝对引用，例如'$A$1:$B$1'可以避免这种情况。
        // 参考： http://poi.apache.org/apidocs/dev/org/apache/poi/ss/usermodel/Name.html
        category1Name.setRefersToFormula(sheetName+"!" + "$A$1:$A$" + arr.length);
        // 获取上文名称内数据
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(sheetName);
        // 设置下拉框位置
        CellRangeAddressList addressList = new CellRangeAddressList(0, 1000, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            // 数据校验
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        // 作用在目标sheet上
        sheet.addValidationData(dataValidation);
        // 设置hiddenSheet隐藏
        wb.setSheetHidden(1, true);


    }

    /**
     * 设置有效性
     *
     * @param offset 主影响单元格所在列，即此单元格由哪个单元格影响联动
     * @param sheet
     * @param rowNum 行数
     * @param colNum 列数
     */
    public static void setDataValidation(String offset, XSSFSheet sheet, int rowNum, int colNum) {
        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
        DataValidation data_validation_list;
        data_validation_list = getDataValidationByFormula(
                "INDIRECT($" + offset + (rowNum) + ")", rowNum, colNum, dvHelper);
        sheet.addValidationData(data_validation_list);
    }

    /**
     * 加载下拉列表内容
     *
     * @param formulaString
     * @param naturalRowIndex
     * @param naturalColumnIndex
     * @param dvHelper
     * @return
     */
    private static DataValidation getDataValidationByFormula(
            String formulaString, int naturalRowIndex, int naturalColumnIndex, XSSFDataValidationHelper dvHelper) {
        // 加载下拉列表内容
        // 举例：若formulaString = "INDIRECT($A$2)" 表示规则数据会从名称管理器中获取key与单元格 A2 值相同的数据，
        //如果A2是江苏省，那么此处就是江苏省下的市信息。
        XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper.createFormulaListConstraint(formulaString);
        // 设置数据有效性加载在哪个单元格上。
        // 四个参数分别是：起始行、终止行、起始列、终止列
        int firstRow = naturalRowIndex - 1;
        int lastRow = naturalRowIndex - 1;
        int firstCol = naturalColumnIndex - 1;
        int lastCol = naturalColumnIndex - 1;
        CellRangeAddressList regions = new CellRangeAddressList(firstRow,
                lastRow, firstCol, lastCol);
        // 数据有效性对象
        // 绑定
        XSSFDataValidation data_validation_list = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, regions);
        data_validation_list.setEmptyCellAllowed(false);
        if (data_validation_list instanceof XSSFDataValidation) {
            data_validation_list.setSuppressDropDownArrow(true);
            data_validation_list.setShowErrorBox(true);
        } else {
            data_validation_list.setSuppressDropDownArrow(false);
        }
        // 设置输入信息提示信息
        data_validation_list.createPromptBox("下拉选择提示", "请使用下拉方式选择合适的值！");
        // 设置输入错误提示信息
        //data_validation_list.createErrorBox("选择错误提示", "你输入的值未在备选列表中，请下拉选择合适的值！");
        return data_validation_list;
    }

    /**
     * 计算formula
     *
     * @param offset   偏移量，如果给0，表示从A列开始，1，就是从B列
     * @param rowId    第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     */
    public static String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        if (colCount <= 25) {
            char end = (char) (start + colCount - 1);
            return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
        } else {
            char endPrefix = 'A';
            char endSuffix = 'A';
            if ((colCount - 25) / 26 == 0 || colCount == 51) {// 26-51之间，包括边界（仅两次字母表计算）
                if ((colCount - 25) % 26 == 0) {// 边界值
                    endSuffix = (char) ('A' + 25);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                }
            } else {// 51以上
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26);
                }
            }
            return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
        }
    }

    /**
     * 添加数据有效性检查.
     *
     * @param sheet              要添加此检查的Sheet
     * @param firstRow           开始行
     * @param lastRow            结束行
     * @param firstCol           开始列
     * @param lastCol            结束列
     * @param explicitListValues 有效性检查的下拉列表
     * @throws IllegalArgumentException 如果传入的行或者列小于0(< 0)或者结束行/列比开始行/列小
     */
    public static void setValidationData(Sheet sheet, int firstRow, int lastRow,
                                         int firstCol, int lastCol, String[] explicitListValues) throws IllegalArgumentException {
        if (firstRow < 0 || lastRow < 0 || firstCol < 0 || lastCol < 0 || lastRow < firstRow || lastCol < firstCol) {
            throw new IllegalArgumentException("Wrong Row or Column index : " + firstRow + ":" + lastRow + ":" + firstCol + ":" + lastCol);
        }
        if (sheet instanceof XSSFSheet) {
            XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) sheet);
            XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
                    .createExplicitListConstraint(explicitListValues);
            CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
            XSSFDataValidation validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        } else if (sheet instanceof HSSFSheet) {
            CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
            DVConstraint dvConstraint = DVConstraint.createExplicitListConstraint(explicitListValues);
            DataValidation validation = new HSSFDataValidation(addressList, dvConstraint);
            validation.setSuppressDropDownArrow(true);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        }
    }


    /**
     * @param "[file, userNumber]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: importExcel
     * @description: 被测系统导入excel
     * <AUTHOR>
     * @date 2019/12/27 18:40
     */
    @Override
    @Transactional
    public Result importExcel(MultipartFile file, String userNumber) throws Exception {
//        String fileName = file.getOriginalFilename();// 获取文件名
        InputStream is=null;
        try {
            XSSFWorkbook wb = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = wb.getSheetAt(0);

            ArrayList<Map<String, Object>> dataList = new ArrayList<>();

            dataDicUtil.getDicMap("testSystem");
            // List<Map<String, Object>> hostTribe_list = dataDicUtil.getList("HostTribe");
            String token = HttpRequestUtils.getCurrentRequestToken();
//        List<Map<String, String>> tribeList = feignDataDesignToBasicService.findNameAndInfoNameByDirName("所属部落",token);

            List<Map<String, Object>> testModeList = dataDicUtil.getList("testMode");
            Map<String, String> testModeMap = testModeList.stream().collect(Collectors.toMap(x -> x.get("textName").toString(), x -> x.get("value").toString(), (k1, k2) -> k2));
            Result hfResult = feignDataDesignToBasicService.findTypeAllUser(token);
            List<Map<String, Object>> fh_List = (List<Map<String, Object>>) hfResult.getObj();
            //查询所有人员(sit测试人,sit负责人,)
            List<JettechUserDTO> allPerson = feignDataDesignToBasicService.findAllPerson(token);
            List<String> testSystemList = new ArrayList<String>();
            int rows = sheet.getPhysicalNumberOfRows();
            if (rows == 1) return Result.renderError("导入模板数据为空！");
            int m=CheckUtil.checkLoop(sheet.getLastRowNum());
            for (int i = 0; i <=m ; i++) {
                XSSFRow row = sheet.getRow(i + 1);
                if (row == null || isRowEmpty(row)) continue;
                HashMap<String, Object> map = new HashMap<>();
                for (int j = 0; j <= sheet.getRow(0).getPhysicalNumberOfCells(); j++) {
                    XSSFCell cell = row.getCell(j);
                    String cellContent;
                    if (cell == null) cellContent = null;
                    else cellContent = getCellValue(cell).trim();
                    switch (j) {
                        case 0:
                            boolean b = this.verifySystemSimpleNameNotRepeated(cellContent, null);
                            if(b) return Result.renderError("系统简称“"+cellContent+"”已存在！");
                            map.put("simpleName", cellContent == null ? "" : cellContent);
                            break;
                        case 1:
                            if (StringUtils.isBlank(cellContent))
                                return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在系统空名称！");
                            boolean f = this.verifySystemNameNotRepeated(cellContent, null);
                            if (f) return Result.renderError("系统名称“" + cellContent + "”已存在！");
                            if (testSystemList.contains(cellContent)) {
                                return Result.renderError("导入被测系统的模板中，功能名称“" + cellContent + "”存在相同数据。");
                            } else {
                                testSystemList.add(cellContent);
                            }
                            map.put("name", cellContent);
                            break;
//                    case 2:
//                        if (StringUtils.isBlank(cellContent))
//                            return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空所属开发中心！");
//                        map.put("developmentCenter", dataDicUtil.getValue("developmentCenter", cellContent));
//                        break;
                        case 2:
                            if (StringUtils.isBlank(cellContent))
                                return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空业务归口部门！");
                            map.put("busPCentralizedDepartment", dataDicUtil.getValue("CentralizedBusinessDepartment", cellContent));
                            break;
                        case 3:
                            if (StringUtils.isBlank(cellContent))
                                return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空业务负责人！");
//                        Result<?> result = feignDataDesignToBasicService.findUserByName(cellContent);
//                        List<Map<String,Object>> list = (List<Map<String, Object>>) result.getObj();
//                        if(list.isEmpty()) return  Result.renderError("负责人：“"+cellContent+"”不存在！");
//                        String text = list.get(0).get("text").toString();
                            String res = fh_List.stream().filter(e -> e.get("userName").toString().equals(cellContent))
                                    .map(e -> e.get("number").toString()).collect(Collectors.joining(","));
                            if (StringUtils.isBlank(res)) return Result.renderError("负责人：“" + cellContent + "”不存在！");
                            map.put("manager", res);
                            break;
//                    case 5:
//                        if (StringUtils.isBlank(cellContent))
//                            return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空所属部落！");
//                        String id = tribeList.stream()
//                                .filter(e -> cellContent.equals(e.get("text").toString()))
//                                .findFirst()
//                                .get()
//                                .get("id");
//                        if (StringUtils.isBlank(id)) return Result.renderError("所属部落:“" + cellContent + "”，不存在！");
//                        map.put("belongTribe", id);
//                        break;
//                    case 6:
//                        if (StringUtils.isBlank(cellContent))
//                            return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空所属小队！");
//                        XSSFCell cell1 = row.getCell(j - 1);
//                        String stringCellValue = cell1.getStringCellValue();
//                        List<Map<String, Object>> teamList = (List<Map<String, Object>>) hostTribe_list.get(0).get(stringCellValue);
//                        Optional<Map<String, Object>> textName = teamList.stream().filter(e -> cellContent.equals(e.get("text").toString())).findFirst();
//                        if (!textName.isPresent())
//                            return Result.renderError("所属小队“" + cellContent + "”，不属于“" + stringCellValue + "”!");
//                        Object HostTeam = textName.get().get("id");
//                        map.put("subordinatTeam", HostTeam);
//                        break;
                        case 4:
                            if (StringUtils.isNotBlank(cellContent)) {
                                String[] split = cellContent.split(",");
                                List<String> split_list = Arrays.asList(split);
                                //查询开发公司
                                Result companyRes = feignDataDesignToBasicService.findOutDept(token);
                                List<Map<String, Object>> company_list = (List<Map<String, Object>>) companyRes.getObj();
                                String developCompanys = company_list.stream()
                                        .filter(e -> split_list.contains(e.get("name").toString()))
                                        .map(e -> e.get("resourceID").toString()).collect(Collectors.joining(","));
                                if (StringUtils.isBlank(developCompanys))
                                    return Result.renderError("开发公司：“" + cellContent + "”不存在,或格式错误！");
                                map.put("developCompany", developCompanys);
                            }
                            break;
                        case 5:
                            if (StringUtils.isBlank(cellContent))
                                return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空行方经理！");
                            String[] pro_arr = cellContent.split(",");
                            List<String> pro_list = Arrays.asList(pro_arr);
                            String projectManagers = fh_List.stream().filter(e -> pro_list.contains(e.get("userName").toString()))
                                    .map(e -> e.get("number").toString()).collect(Collectors.joining(","));
                            if (StringUtils.isBlank(projectManagers))
                                return Result.renderError("行方项目经理：“" + cellContent + "”不存在,或格式错误！");
                            map.put("projectManager", projectManagers);
                            break;
                        case 6:
                            if (StringUtils.isNotBlank(cellContent)) {
                                String[] sitM_arr = cellContent.split(",");
                                List<String> sitM_list = Arrays.asList(sitM_arr);
                                String SITTestMangers = allPerson.stream().filter(e -> sitM_list.contains(e.getUserName()))
                                        .map(e -> e.getNumber()).collect(Collectors.joining(","));
                                if (StringUtils.isBlank(SITTestMangers))
                                    return Result.renderError("SIT负责人：“" + cellContent + "”不存在，或格式错误！");
                                map.put("SITTestManger", SITTestMangers);
                            }
                            break;
                        case 7:
                            if (StringUtils.isNotBlank(cellContent)) {
                                String[] sitT_arr = cellContent.split(",");
                                List<String> sitT_list = Arrays.asList(sitT_arr);
                                String SITTesterses = allPerson.stream().filter(e -> sitT_list.contains(e.getUserName()))
                                        .map(e -> e.getNumber()).collect(Collectors.joining(","));
                                if (StringUtils.isBlank(SITTesterses))
                                    return Result.renderError("UAT测试人：“" + cellContent + "”不存在,或格式错误！");
                                map.put("SITTesterses", SITTesterses);
                            }
                            break;
                        case 8:
                            if (StringUtils.isBlank(cellContent))
                                return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空重要级别！");
                            map.put("important", dataDicUtil.getValue("important", cellContent));
                            break;
                        case 9:
                            //开发人员
                            if (StringUtils.isNotBlank(cellContent)) {
                                String[] develop_arr = cellContent.split(",");
                                List<String> develop_list = Arrays.asList(develop_arr);
                                String developPerson = allPerson.stream().filter(e -> develop_list.contains(e.getUserName()))
                                        .map(e -> e.getNumber()).collect(Collectors.joining(","));
                                if (StringUtils.isBlank(developPerson))
                                    return Result.renderError("开发负责人：“" + cellContent + "”不存在,或格式错误！");
                                map.put("developPerson", developPerson);
                            }
                            break;
                        case 10:
                            //测试方式
                            if(StringUtils.isBlank(cellContent)) return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，存在空测试方式！");
                            String[] split = cellContent.split(",");
                            String dicValue = "";
                            for (String s : split)
                            {
                                if(testModeMap.get(s) == null) return Result.renderError("第" + (i + 1) + "行，第" + (j + 1) + "列，输入的测试方式内容错误！");
                                dicValue = dicValue + testModeMap.get(s);
                            }

                            String s = BinaryDecimalUtil.DicValToBin(dicValue);
                            map.put("testMode",BinaryDecimalUtil.BinToTen(s));

                            break;
                        default:
                            break;
                    }
                }
                dataList.add(map);
        }

        ArrayList<TestSystem> list = new ArrayList<>();
        dataList.forEach(e -> {
            TestSystem ts = new TestSystem();
            //名称
            ts.setName(e.get("name").toString());
            //简称
            ts.setSimpleName(e.get("simpleName") == null ? "" : e.get("simpleName").toString());
            //对口部门
            ts.setBusPCentralizedDepartment(e.get("busPCentralizedDepartment").toString());
            //负责人
            ts.setManager(e.get("manager").toString());
            //所属部落
            // ts.setBelongTribe(e.get("belongTribe").toString());
            //所属小队
            //  ts.setSubordinatTeam(e.get("subordinatTeam").toString());
            //开发公司
            ts.setDevelopCompany(e.get("developCompany") == null ? null : e.get("developCompany").toString());
            //行方项目经理
            ts.setProjectManager(e.get("projectManager").toString());
            //SIT负责人
            ts.setSITTestManger(e.get("SITTestManger") == null ? null : e.get("SITTestManger").toString());
            //sit测试人
            ts.setSITTesters(e.get("SITTesterses") == null ? null : e.get("SITTesterses").toString());
            //重要级别
            ts.setImportant(e.get("important").toString());
            //研发中心
            // ts.setDevelopmentCenter(e.get("developmentCenter").toString());
            //开发人员
            ts.setDevelopPerson(e.get("developPerson") == null ? null : e.get("developPerson").toString());
            //测试方式
            ts.setTestMode(Integer.valueOf(e.get("testMode").toString()));
            list.add(ts);
        });
        List<TestSystem> tsList = this.save(list, userNumber);

        tsList.forEach(e -> {
            // 将‘业务负责人’、‘行方项目经理’、‘SIT测试负责人’、‘SIT测试人员’ 、‘开发人员’添加到系统关联人员中
            List<String> listAll = new ArrayList<String>();
            if (e.getManager() != null) {
                listAll.add(e.getManager());
            }
            if (e.getProjectManager() != null || StringUtils.isNotBlank(e.getProjectManager())) {
                listAll.addAll(Arrays.asList(e.getProjectManager().split(",")));
            }
            if (e.getSITTestManger() != null || StringUtils.isNotBlank(e.getSITTestManger())) {
                listAll.addAll(Arrays.asList(e.getSITTestManger().split(",")));
            }
            if (e.getSITTesters() != null || StringUtils.isNotBlank(e.getSITTesters())) {
                listAll.addAll(Arrays.asList(e.getSITTesters().split(",")));
            }
            if (e.getDevelopPerson() != null || StringUtils.isNotBlank(e.getDevelopPerson())) {
                listAll.addAll(Arrays.asList(e.getDevelopPerson().split(",")));
            }

            Set<String> userList = new LinkedHashSet<>(listAll);
            // 通过UserNumber获取人员resourceID
            List<JettechUserDTO> jettechUserDTOS = feignDataDesignToBasicService.findByNumberIn(new ArrayList<>(userList), token);
            // 当新增、修改系统后，建立系统与人员关联关系
            Map<String, Object> params = new HashMap<String, Object>();
            List<String> userResourceIDList = jettechUserDTOS.stream().map(f -> f.getResourceID().toString()).collect(Collectors.toList());
            params.put("userResourceIDList", userResourceIDList);
            params.put("userNumber", userNumber);
            params.put("testSystemResourceID", e.getResourceID());
            // 调用关联方法，内部已经将旧的关系删除了
            this.setSelectTestSystemUser(params);
        });

        return Result.renderSuccess("导入成功!");
    }catch (Exception e){e.printStackTrace();
        return null;}
        finally {
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){
                return null;
            }
        }
        }

    public static boolean isRowEmpty(Row row) {
        int m=CheckUtil.checkLoop(row.getLastCellNum());
        for (int c = row.getFirstCellNum(); c < m; c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK)
                return false;
        }
        return true;
    }

    /**
     * @Title: getBelongTribe
     * @Description: 获取下拉框选项所属部落
     * @Param: "[]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/31
     */
    @Override
    public Result getBelongTribe() {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, String>> list = feignDataDesignToBasicService.findNameAndInfoNameByDirName("所属部落", token);
        return Result.renderSuccess(list);
    }

    /**
     * @Title: getSubordinatTeam
     * @Description: 根据选择的所属部落获取下拉框选项-->所属小队
     * @Param: "[id]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/31
     */
    @Override
    public Result getSubordinatTeam(String text) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, Object>> list = feignDataDesignToBasicService.findValueAndTextNameByInfoName(text, token);
        return Result.renderSuccess(list);
    }

    /**
     * @Title: findOneLeftTreeByTestSystemResourceID
     * @Description: 查询单个被测系统的左侧树
     * @Param: "[testSystemResourceID]"
     * @Return: "com.jettech.dto.Result<?>"
     * @Author: xpp
     * @Date: 2020/2/21
     */
    @Override
    public Result<?> findOneLeftTreeByTestSystemResourceID(String testSystemResourceID, String demandResourceID, String userNumber) {
        if (StringUtils.isBlank(demandResourceID) || StringUtils.isBlank(testSystemResourceID)) {
            return Result.renderError("缺少需求或被测系统id");
        }
        //查询该系统下关联的人员判断是否可以维护
//        Result result = selectTestSystemUser(Long.parseLong(testSystemResourceID));
//        HashMap<String, List<JettechUserDTO>> usersmap= (HashMap<String, List<JettechUserDTO>>) result.getObj();
//        List<JettechUserDTO> selectUser = usersmap.get("selectUser");
//        Boolean flag =false;
//        for (JettechUserDTO jettechUserDTO : selectUser) {
//            if (jettechUserDTO.getNumber().equals(userNumber)){
//                flag=true;
//            }
//        }
//        if (!flag){
//            return Result.renderError("抱歉!你不是该系统下人员无法维护!");
//        }
        TestSystem ts = this.findByResourceID(Long.valueOf(testSystemResourceID));
        HashMap<String, Object> systemMap = new HashMap<>();
        systemMap.put("resourceID", ts.getResourceID());
        systemMap.put("number", ts.getNumber());
        systemMap.put("name", ts.getName());
        systemMap.put("manager", ts.getManager());
        systemMap.put("describes", ts.getDescribes());
        systemMap.put("demandResourceID", demandResourceID);
        systemMap.put("type", "system");
        List<Map<String, Object>> testSystemList = new ArrayList<Map<String, Object>>();
        testSystemList.add(systemMap);
        List<Map<String, Object>> treeList = getSinglePointLeftTree(testSystemList);
        return Result.renderSuccess(treeList);
    }

    /**
     * @Title: findCheckedLeftTree
     * @Description: 页面回选已经自定义的左侧树节点
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/2/25
     */
    public Result findCheckedLeftTree(Map<String, String> params) {
        String demandResourceID = params.get("demandResourceID");
        String testSystemResourceID = params.get("testSystemResourceID");
        String leftTreeType = params.get("leftTreeType");
        if (StringUtils.isBlank(demandResourceID) || StringUtils.isBlank(testSystemResourceID) || StringUtils.isBlank(leftTreeType)) {
            return Result.renderError("缺少需求或被测系统id或案例类型字段");
        }
        LeftTreeConfiguration l = leftTreeConfigurationService.findLeftTreeConfigurationByDemandResrouceIDAndType(demandResourceID, leftTreeType);
        if (null != l && null != l.getConfiguration()) {
            List<Map> maps = JSONArray.parseArray(l.getConfiguration(), Map.class);
            List<Map> collect = maps.stream().filter(map -> String.valueOf(map.get("resourceID")).equals(testSystemResourceID)
                    && "system".equals(String.valueOf(map.get("type")))).collect(Collectors.toList());
            if (null != collect && collect.size() > 0) {
                // 勾选的被测系统
                Optional<Map> map3 = collect.stream()
                        .filter(s -> String.valueOf(s.get("resourceID")).equals(testSystemResourceID))
                        .findFirst();
                Map map = map3.get();
                if (null != map.get("children")) {
                    List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("children");
                    List<Map<String, Object>> modules = list.stream()
                            .filter(s -> "module".equals(s.get("type")))
                            .collect(Collectors.toList());

                    List<String> moduleIDs = modules.stream()
                            .map(s -> String.valueOf(s.get("resourceID")))
                            .collect(Collectors.toList());
                    List<String> tradeIDs1 = list.stream()
                            .filter(s -> "trade".equals(s.get("type")))
                            .map(s -> String.valueOf(s.get("resourceID")))
                            .collect(Collectors.toList());

                    List<Map<String, Object>> trade = modules.stream()
                            .filter(s -> null != s.get("children"))
                            .map(s -> {
                                List<Map<String, Object>> a = (List<Map<String, Object>>) s.get("children");
                                return a;
                            })
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    List<String> tradeIDs2 = trade.stream()
                            .filter(s -> "trade".equals(s.get("type")))
                            .map(s -> String.valueOf(s.get("resourceID")))
                            .collect(Collectors.toList());
                    List<String> collect1 = Stream.of(moduleIDs, tradeIDs1, tradeIDs2)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    return Result.renderSuccess(collect1);
                }
            }
        }
        return Result.renderSuccess(null);
    }

    /**
     * @Title: findLeftTreeBySIT
     * @Description: 通过SIT按钮初始化左侧树
     * @Param: "[testEnviroment]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    @Override
    public Result findLeftTreeBySIT(String demandResourceID) {
        if (demandResourceID == null || "".equals(demandResourceID)) {
            return Result.renderError("查询参数为空!");
        }
        List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceID(demandResourceID);
        LeftTreeConfiguration l = leftTreeConfigurationService.findLeftTreeConfigurationByDemandResrouceIDAndType(demandResourceID, "SIT");
        if (null != l) {
            String configuration = l.getConfiguration();
            List<Map> configMaps = JSONArray.parseArray(configuration, Map.class);
            List<String> resourceIDs = configMaps.stream().map(s -> String.valueOf(s.get("resourceID"))).collect(Collectors.toList());
            if (testSystemList.size() > 0) {
                for (Map<String, Object> map : testSystemList) {
                    if (!resourceIDs.contains(String.valueOf(map.get("resourceID")))) {
                        configMaps.add(map);
                    }
                }
            }
            return Result.renderSuccess(configMaps);
        }
        return Result.renderSuccess(testSystemList);
    }

    /**
     * @Title: findLeftTreeByUAT
     * @Description: 通过UAT按钮初始化左侧树，增加UAT树节点
     * @Param: "[testEnviroment]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    @Override
    public Result findLeftTreeByUAT(String demandResourceID) {
        if (demandResourceID == null || "".equals(demandResourceID)) {
            return Result.renderError("查询参数为空!");
        }
        Trade trade = null;
        SystemModule systemmodule = null;
        List<SystemModule> systemmodules = systemModuleService.findbySystemModuleName("UAT流程用例模块");
        List<Trade> trades = tradeService.findbyTradeName("UAT流程用例交易");
        if (null != systemmodules && systemmodules.size() > 0) {
            Optional<SystemModule> first1 = systemmodules
                    .stream()
                    .filter(s -> null == s.getTestSystemResourceID() && null == s.getParentResourceID())
                    .findFirst();
            if (first1.isPresent()) {
                systemmodule = first1.get();
                List<Trade> trades1 = tradeService.findBySystemModuleResourceID(String.valueOf(systemmodule.getResourceID()), null, null, null);
                if (null != trades1 && trades1.size() > 0) {
                    Optional<Trade> first = trades1
                            .stream()
                            .filter(s -> null == s.getTestSystemResourceID() && null != s.getModuleResourceID())
                            .findFirst();
                    if (first.isPresent()) {
                        trade = first.get();
                    }
                }
            }
        } else {
            //在独立的"UAT"模块之上增加一个默认的模块，模块名称叫"流程UAT用例模块"，
            // 同时独立交易的名称修改为“流程UAT用例交易”
            // 单独存在，无挂靠系统和父模块
            SystemModule s = new SystemModule();
            s.setName("UAT流程用例模块");
            systemmodule = systemModuleService.save(s, "admin");
            // 树节点追加：UAT用例模块和交易
            Trade t = new Trade();
            t.setNumber("UAT");
            t.setName("UAT流程用例交易");
            t.setStatus("1");
            t.setComment("UAT流程用例");
            t.setModuleResourceID(systemmodule.getResourceID());
            trade = tradeService.save(t, "admin");
        }
        Map<String, Object> uatMap = new HashMap<>();
        uatMap.put("name", trade.getName());
        uatMap.put("type", "trade");
        uatMap.put("resourceID", trade.getResourceID());
        uatMap.put("moduleResourceID", trade.getModuleResourceID());

        List<Map<String, Object>> children = new ArrayList<>();
        children.add(uatMap);
        Map<String, Object> uatMapParent = new HashMap<>();
        uatMapParent.put("name", systemmodule.getName());
        uatMapParent.put("resourceID", systemmodule.getResourceID());
        uatMapParent.put("type", "module");
        uatMapParent.put("children", children);

        List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceID(demandResourceID);
        LeftTreeConfiguration l = leftTreeConfigurationService.findLeftTreeConfigurationByDemandResrouceIDAndType(demandResourceID, "UAT");
        if (null != l) {
            String configuration = l.getConfiguration();
            List<Map> configMaps = JSONArray.parseArray(configuration, Map.class);
            List<String> resourceIDs = configMaps.stream().map(s -> String.valueOf(s.get("resourceID"))).collect(Collectors.toList());
            if (testSystemList.size() > 0) {
                for (Map<String, Object> map : testSystemList) {
                    if (!resourceIDs.contains(String.valueOf(map.get("resourceID")))) {
                        configMaps.add(map);
                    }
                }
            }
            configMaps.add(0, uatMapParent);
            return Result.renderSuccess(configMaps);
        }
        testSystemList.add(0, uatMapParent);
        return Result.renderSuccess(testSystemList);
    }

    private String getCellValue(Cell cell) {
        return cell.getStringCellValue();
    }


    /**
     * @param testSystemResourceID
     * @return Result<?>
     * @Title checkData
     * @Description 根据被测系统查询所有的模块和交易
     * <AUTHOR>
     * @data Jan 11, 20206:05:04 PM
     */
    @Override
    public Result<?> checkData(Long testSystemResourceID, Long resourceID, String type) {

        /**
         List<HashMap<String, Object>> resultMaps = new ArrayList<HashMap<String,Object>>();
         //查询出系统下的最外层模块
         resultMaps = systemModuleService.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);

         //		 List<Long> collect = resultMaps.stream().map(e ->Long.valueOf(e.get("resourceID").toString())).collect(Collectors.toList());

         ArrayList<String> arrayList = new ArrayList<String>();
         arrayList.add(testSystemResourceID.toString());
         //模块下的交易名称
         Map<String, List<String>> resultMap = new HashMap<String, List<String>>();

         //系统下所有的交易
         List<Map<String, Object>> maps = tradeService.findByTestSystemResourceIDs(arrayList);
         ArrayList<String> onlyTradeNameList = new ArrayList<String>();

         if(resultMaps != null && resultMaps.size() > 0) {
         for (HashMap<String, Object> hashMap : resultMaps) {
         ArrayList<String> tradeNameList = new ArrayList<String>();
         for (Map<String, Object> map : maps) {
         String name = map.get("name").toString();
         //若交易是在模块下
         if(map.get("moduleResourceID") != null && !"".equals(map.get("moduleResourceID").toString())) {
         String modelu_trade = findByModuleRid(Long.valueOf((map.get("moduleResourceID").toString())),name,(hashMap.get("resourceID").toString()));
         if(!"".equals(modelu_trade) && modelu_trade != null) {
         tradeNameList.add(modelu_trade);
         }
         //交易是直接在系统下
         }else {
         if(!onlyTradeNameList.contains(name)) {
         onlyTradeNameList.add(name);
         }
         }
         }
         if(tradeNameList.size()>0 && tradeNameList!=null) {
         resultMap.put(hashMap.get("name").toString(), tradeNameList);
         }
         //				resultMap.put("module", );
         }
         if(onlyTradeNameList.size()>0 && onlyTradeNameList!=null) {
         resultMap.put("onlyTrade", onlyTradeNameList);
         }
         }else {
         List<HashMap<String, Object>> tradeMapList = tradeService.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);
         List<String> tradeList = tradeMapList.stream().map(e -> e.get("name").toString()).collect(Collectors.toList());
         resultMap.put("onlyTrade", tradeList);
         }

         return Result.renderSuccess(resultMap);
         */


        if ("trade".equals(type)) {
            Trade trade = tradeService.findByResourceID(resourceID);
            return Result.renderSuccess(trade);
        }

        List<HashMap<String, Object>> resultMaps = new ArrayList<HashMap<String, Object>>();

        //查询出系统下的最外层模块
        resultMaps = systemModuleService.findNextLowerLevelMapByTestSystemResourceID(testSystemResourceID);

        ArrayList<String> arrayList = new ArrayList<String>();
        arrayList.add(testSystemResourceID.toString());
        //系统下所有的交易
        List<Map<String, Object>> maps = tradeService.findByTestSystemResourceIDs(arrayList);
        List<Map<String, Object>> deleteList = new ArrayList<Map<String, Object>>();
        if (resultMaps != null && resultMaps.size() > 0) {
            for (HashMap<String, Object> hashMap : resultMaps) {
                if (!hashMap.get("resourceID").toString().equals(resourceID.toString())) {
                    continue;
                }
                int m=CheckUtil.checkLoop(maps.size());
                for (int i = 0; i < m; i++) {
                    //				for (Map<String, Object> map : maps) {
                    String name = maps.get(i).get("name").toString();
                    //若交易是在模块下
                    if (maps.get(i).get("moduleResourceID") != null && !"".equals(maps.get(i).get("moduleResourceID").toString())) {
                        String modelu_trade = findByModuleRid(Long.valueOf((maps.get(i).get("moduleResourceID").toString())), name, resourceID.toString());
                        if (!"".equals(modelu_trade) && modelu_trade != null) {
                            maps.get(i).put("name", modelu_trade);
                            //去除不在一个模块下的交易
                        } else {
                            deleteList.add(maps.get(i));
                        }
                        //去除挂在系统上的交易
                    } else {
                        deleteList.add(maps.get(i));
                    }
                    //				}
                }
            }
        }
        maps.removeAll(deleteList);
        return Result.renderSuccess(maps);
    }

    //递归查询模块的方法
    private String findByModuleRid(Long moduleResourceId, String name, String resourceID) {
        //如果只有两层 则直接判断
        if (moduleResourceId.toString().equals(resourceID.toString())) {
            return name;
        }
        //查询模块
        SystemModule systemModule = systemModuleService.findByResourceID(moduleResourceId);
        //查询模块为空 则不属于当前最外层模块下的交易
        if (systemModule == null) {
            return "";
        }
        //如果查到了最外层
        if (!resourceID.equals((systemModule.getParentResourceID().toString()))) {
            name = systemModule.getName() + "-" + name;
            name = findByModuleRid(systemModule.getParentResourceID(), name, resourceID);
        } else {
            name = systemModule.getName() + "-" + name;
        }
        return name;
    }

    /**
     * @param "[demandResourceID]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findTestSystemUserByDemandResourceID
     * @description: 需求下被测系统
     * <AUTHOR>
     * @date 2020/2/12 13:29
     */
    @Override
    public Result findTestSystemByDemandResourceID(String demandResourceID) {
        List<DemandTestSystem> dts_list = demandTestSystemService.findBydemandResourceID(Long.valueOf(demandResourceID));
        return Result.renderSuccess(dts_list);
    }

    /**
     * @Title: customLeftTree
     * @Description: 通过勾选自定义显示案例编写左侧树
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/2/18
     */
    @Override
    public Result customLeftTree(Map<String, String> params, String userNumber) {
        String demandResourceID = params.get("demandResourceID");
        String testSystemResourceID = params.get("testSystemResourceID");
        String leftTreeType = params.get("leftTreeType");
        // 勾选的系统模块id集合
        String systemModuleStr = params.get("systemModuleList");
        // 勾选的交易id集合
        String tradeStr = params.get("tradeList");

        if (StringUtils.isBlank(demandResourceID) || StringUtils.isBlank(testSystemResourceID) || StringUtils.isBlank(leftTreeType)) {
            return Result.renderError("缺少需求或被测系统id或案例类型字段");
        }
        if (StringUtils.isBlank(systemModuleStr) && StringUtils.isBlank(tradeStr) &&
                !StringUtils.isBlank(testSystemResourceID)) {
            if ("UAT".equals(leftTreeType)) {
                return this.findLeftTreeByUAT(demandResourceID);
            }
            if ("SIT".equals(leftTreeType)) {
                return this.findLeftTreeBySIT(demandResourceID);
            }
        }

        List<String> systemModule1 = null;
        if (!StringUtils.isEmpty(systemModuleStr)) {
            String[] split1 = systemModuleStr.split(",");
            systemModule1 = Arrays.asList(split1);
        }

        List<String> trade1 = null;
        if (!StringUtils.isEmpty(tradeStr)) {
            String[] split = tradeStr.split(",");
            trade1 = Arrays.asList(split);
        }

        // 跟据交易重新组装左侧树
        // 树结构容器
        List<Map<String, Object>> treeList = new ArrayList<Map<String, Object>>();
        // 所有的交易对象
        List<Trade> tradeList = tradeService.findByResourceIDIn(trade1);
        // 交易节点(以及节点下的案例节点)
        List<Map<String, Object>> tradeNodes = tradeList.stream()
                .map(trade -> {
                    Map<String, Object> tradeMap = new HashMap<>();
                    tradeMap.put("id", trade.getId());
                    tradeMap.put("name", trade.getName());
                    tradeMap.put("resourceID", trade.getResourceID());
                    tradeMap.put("type", "trade");
                    tradeMap.put("number", trade.getNumber());
                    tradeMap.put("testSystemResourceID", trade.getTestSystemResourceID());
                    tradeMap.put("moduleResourceID", trade.getModuleResourceID());
                    return tradeMap;
                }).collect(Collectors.toList());
        // 根据交易节点的rid分组
        Map<String, List<Map<String, Object>>> rid = tradeNodes.stream().collect(Collectors.groupingBy(s -> String.valueOf(s.get("resourceID"))));
        // 根据是否在被测系统下分组
        Map<Boolean, List<Trade>> collect = tradeList.stream().collect(Collectors.partitioningBy(trade -> null == trade.getModuleResourceID()));
        // 直接在被测系统下的交易
        List<Trade> trades1 = collect.get(true);
        // 根据被测系统的rid对交易分组
        Map<Long, List<Trade>> collect1 = trades1.stream().collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 根据当前被测系统下交易对象组装下级节点
        for (Long s : collect1.keySet()) {
            // 当前交易的上一级被测系统
            TestSystem testSystem = this.findByResourceID(s);
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", testSystem.getId());
            map1.put("name", testSystem.getName());
            map1.put("resourceID", testSystem.getResourceID());
            map1.put("type", "system");
            map1.put("manager", testSystem.getManager());
            // 当前被测系统下的所有交易对象
            List<Trade> trades = collect1.get(testSystem.getResourceID());
            // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
            List<List<Map<String, Object>>> collect2 = trades.stream().map(t -> rid.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect2.stream().flatMap(Collection::stream).collect(Collectors.toList());
            map1.put("children", collect3);
            treeList.add(map1);
        }
        // 上一级是模块的交易对象
        List<Trade> trades2 = collect.get(false);
        if (trades2.size() > 0) {
            // 上一级是模块的交易节点
            List<Map<String, Object>> tradeNodes2 = trades2.stream().map(t -> rid.get(String.valueOf(t.getResourceID()))).flatMap(Collection::stream).collect(Collectors.toList());
            // 通过模块下的交易递归查询父节点
            List<Map<String, Object>> treeList2 = findTradeToSystemModule(trades2, rid, treeList, "");
            treeList = treeList2;
        }

        // 当前只勾选模块的数据组装树机构
        if (null != systemModule1 && systemModule1.size() > 0) {
            // 筛选出没有交易节点的模块
            List<Trade> trades = tradeService.findBySystemModule(systemModule1);
            Map<String, List<Trade>> map = trades.stream().collect(Collectors.groupingBy(s -> String.valueOf(s.getModuleResourceID())));
            Set<String> longs = map.keySet();
            List<String> collect2 = systemModule1.stream().filter(s -> !longs.contains(s)).collect(Collectors.toList());
            List<SystemModule> systemModuleList = systemModuleService.findByResourceIDIn(collect2);
            // 模块节点
            List<Map<String, Object>> systemModuleNodes = new ArrayList<>();
            systemModuleList.stream().forEach(s -> {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", s.getId());
                map1.put("name", s.getName());
                map1.put("resourceID", s.getResourceID());
                map1.put("type", "module");
                map1.put("parentResourceID", s.getParentResourceID());
                map1.put("testSystemResourceID", s.getTestSystemResourceID());
                systemModuleNodes.add(map1);
            });
            List<Map<String, Object>> treeList3 = findSystemModuleToSystemModule(systemModuleList, systemModuleNodes, treeList, "");
            treeList = treeList3;
        }

        // 通过维护人自定义维护左侧树之后，保存树结构到本地数据库中
        // 没有维护权限的人初始化左侧树都用此结构
        LeftTreeConfiguration leftTreeConfiguration = new LeftTreeConfiguration();
        leftTreeConfiguration.setDemandResourceID(Long.valueOf(demandResourceID));
        leftTreeConfiguration.setConfigurationType(leftTreeType);
        LeftTreeConfiguration l = leftTreeConfigurationService.findLeftTreeConfigurationByDemandResrouceIDAndType(demandResourceID, leftTreeType);
        if (null != l) {
            // 判断原有记录是要追加还是要更新
            List<Map> oldTree = JSONArray.parseArray(l.getConfiguration(), Map.class);
            // 筛选排除原来已经配置过得的被测系统
            List<Map> excludeTree = oldTree.stream()
                    .filter(s -> !String.valueOf(s.get("resourceID")).equals(testSystemResourceID))
                    .collect(Collectors.toList());
            // 然后再排除的基础上追加已经重新配置好的配置系统，更新配置
            excludeTree.addAll(treeList);
            leftTreeConfiguration.setConfiguration(JSONArray.toJSONString(excludeTree));
            leftTreeConfigurationService.delete(l, userNumber);
        } else {
            leftTreeConfiguration.setConfiguration(JSONArray.toJSONString(treeList));
        }
        LeftTreeConfiguration save = leftTreeConfigurationService.save(leftTreeConfiguration, userNumber);
        List<Map> jsonArray = JSONArray.parseArray(save.getConfiguration(), Map.class);
        List<String> resourceIDs = jsonArray.stream().map(s -> String.valueOf(s.get("resourceID"))).collect(Collectors.toList());
        List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceID(demandResourceID);
        if (testSystemList.size() > 0) {
            for (Map<String, Object> s : testSystemList) {
                if (!resourceIDs.contains(String.valueOf(s.get("resourceID")))) {
                    jsonArray.add(s);
                }
            }
        }
        if ("UAT".equals(leftTreeType)) {
            Trade trade = null;
            SystemModule systemmodule = null;

            List<SystemModule> systemmodules = systemModuleService.findbySystemModuleName("UAT流程用例模块");
            List<Trade> trades = tradeService.findbyTradeName("UAT流程用例交易");

            if (null != systemmodules && systemmodules.size() > 0) {
                Optional<SystemModule> first1 = systemmodules
                        .stream()
                        .filter(s -> null == s.getTestSystemResourceID() && null == s.getParentResourceID())
                        .findFirst();
                if (first1.isPresent()) {
                    systemmodule = first1.get();
                    List<Trade> trades8 = tradeService.findBySystemModuleResourceID(String.valueOf(systemmodule.getResourceID()), null, null, null);
                    if (null != trades8 && trades8.size() > 0) {
                        Optional<Trade> first = trades8
                                .stream()
                                .filter(s -> null == s.getTestSystemResourceID() && null != s.getModuleResourceID())
                                .findFirst();
                        if (first.isPresent()) {
                            trade = first.get();
                        }
                    }
                }
            } else {
                //在独立的"UAT"模块之上增加一个默认的模块，模块名称叫"流程UAT用例模块"，
                // 同时独立交易的名称修改为“流程UAT用例交易”
                // 单独存在，无挂靠系统和父模块
                SystemModule s = new SystemModule();
                s.setName("UAT流程用例模块");
                systemmodule = systemModuleService.save(s, "admin");
                // 树节点追加：UAT用例模块和交易
                Trade t = new Trade();
                t.setNumber("UAT");
                t.setName("UAT流程用例交易");
                t.setStatus("1");
                t.setComment("UAT流程用例");
                t.setModuleResourceID(systemmodule.getResourceID());
                trade = tradeService.save(t, "admin");
            }
            Map<String, Object> uatMap = new HashMap<>();
            uatMap.put("name", trade.getName());
            uatMap.put("type", "trade");
            uatMap.put("resourceID", trade.getResourceID());
            uatMap.put("moduleResourceID", trade.getModuleResourceID());

            List<Map<String, Object>> children = new ArrayList<>();
            children.add(uatMap);
            Map<String, Object> uatMapParent = new HashMap<>();
            uatMapParent.put("name", systemmodule.getName());
            uatMapParent.put("resourceID", systemmodule.getResourceID());
            uatMapParent.put("type", "module");
            uatMapParent.put("children", children);
            jsonArray.add(0, uatMapParent);
        }
        return Result.renderSuccess(jsonArray);
    }

    // 通过单独勾选的模块组装树结构数据
    private List<Map<String, Object>> findOnlySystemModuleToSystemModuleOrTestSystem(List<SystemModule> systemModuleList, List<Map<String, Object>> systemModuleNodes, List<Map<String, Object>> treeList, String testSystemResourceID) {
        // 根据模块rid对模块节点分组
        Map<String, List<Map<String, Object>>> moduleGroup = systemModuleNodes.stream().collect(Collectors.groupingBy(s -> String.valueOf(s.get("resourceID"))));
        // 根据是否是被测系统下模块对象分组
        Map<Boolean, List<SystemModule>> collect1 = systemModuleList.stream().collect(Collectors.partitioningBy(s -> null != s.getParentResourceID() && s.getParentResourceID().equals(s.getTestSystemResourceID())));
        // 直接在被测系统下的模块对象
        List<SystemModule> systemModules1 = collect1.get(true);
        // 根据被测系统的rid进行分组（直接在被测系统下的模块对象）
        Map<Long, List<SystemModule>> collect2 = systemModules1.stream().collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 上一级还是模块的或者只到模块的当前模块对象,或者没有上一级节点只是模块
        List<SystemModule> systemModules2 = collect1.get(false);
        // 筛选出没有上一级只是模块的模块对象
        Map<Boolean, List<SystemModule>> collect7 = systemModules2.stream().collect(Collectors.groupingBy(s -> Objects.isNull(s.getParentResourceID()) && Objects.isNull(s.getTestSystemResourceID())));
        List<SystemModule> systemModules3 = collect7.get(true);
        // 根据上一级还是模块的模块父rid对进行分组
        List<SystemModule> systemModules4 = systemModules2.stream().filter(l -> !Objects.isNull(l.getParentResourceID()) && !Objects.isNull(l.getTestSystemResourceID())).collect(Collectors.toList());
        // 没有上一级，只是到模块截至的模块对象，UAT
        if (systemModules3.size() > 0) {
            systemModules3.stream().forEach(s -> {
                // 模块下所有的模块节点
                List<Map<String, Object>> collect3 = moduleGroup.get(String.valueOf(s.getResourceID()));
                treeList.addAll(0, collect3);
            });
        }

        // 直接在被测系统下的模块对象,直接循环被测系统
        Set<Long> longs = collect2.keySet();
        longs.stream().forEach(s -> {
            // 查询上一级被测系统
            TestSystem testSystem = this.findByResourceID(s);
            // 在当前被测系统下的所有模块对象
            List<SystemModule> systemModules = collect2.get(testSystem.getResourceID());
            // 在当前被测系统下的所有模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream().map(module -> moduleGroup.get(String.valueOf(module.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream).collect(Collectors.toList());

            Optional<Map<String, Object>> ifExist = treeList.stream().filter(x -> x.get("resourceID").equals(testSystem.getResourceID())).findFirst();
            // 存在为true
            if (ifExist.isPresent()) {
                // 说明顶级被测系统节点重复,找出重复节点添加
                Map<String, Object> testSystemMap = ifExist.get();
                List<Map<String, Object>> list = (List<Map<String, Object>>) testSystemMap.get("children");
                // 合并两个children元素
                List<Map<String, Object>> collect6 = Stream.of(list, collect3).flatMap(Collection::stream).collect(Collectors.toList());
                testSystemMap.put("children", collect6);
                // 排除treeList原有集合中的重复元素
                treeList.removeIf(x -> x.get("resourceID").equals(testSystemMap.get("resourceID")));
                treeList.add(testSystemMap);
            } else {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", testSystem.getId());
                map1.put("name", testSystem.getName());
                map1.put("resourceID", testSystem.getResourceID());
                map1.put("type", "system");
                map1.put("manager", testSystem.getManager());
                map1.put("children", collect3);
                treeList.add(map1);
            }
        });

        // 上一级还是模块的节点收集器
        //List<Map<String, Object>> systemModules3 = new ArrayList<>();
        systemModuleNodes.clear();
        // 上一级还是模块下的模块对象
        List<SystemModule> systemModulesParent = null;
        if (systemModules4.size() > 0) {
            Map<Long, List<SystemModule>> collect4 = systemModules4.stream().collect(Collectors.groupingBy(s -> s.getParentResourceID()));
            // 根据当前模块查询得到的还是模块上一级的模块对象
            List<String> collect5 = systemModules2.stream().map(s -> String.valueOf(s.getParentResourceID())).collect(Collectors.toList());
            List<SystemModule> systemModulesParentA = systemModuleService.findByResourceIDIn(collect5);
            // 对查询得到的上一级模块去重
            systemModulesParent = systemModulesParentA.stream().distinct().collect(Collectors.toList());
            systemModulesParent.stream().forEach(parentModule -> {
                // 当前模块上一级还是模块,组装父节点返回
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", parentModule.getId());
                map1.put("name", parentModule.getName());
                map1.put("resourceID", parentModule.getResourceID());
                map1.put("type", "module");
                map1.put("parentResourceID", parentModule.getParentResourceID());
                map1.put("testSystemResourceID", parentModule.getTestSystemResourceID());
                // 父模块下所有的模块对象
                List<SystemModule> systemModules = collect4.get(parentModule.getResourceID());
                // 父模块下所有的模块节点
                List<List<Map<String, Object>>> collect = systemModules.stream().map(module -> moduleGroup.get(String.valueOf(module.getResourceID()))).collect(Collectors.toList());
                List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream).collect(Collectors.toList());
                map1.put("children", collect3);
                systemModuleNodes.add(map1);
            });
        }

        if (systemModuleNodes.size() > 0 && null != systemModulesParent) {
            List<Map<String, Object>> treeList2 = findSystemModuleToSystemModule(systemModulesParent, systemModuleNodes, treeList, testSystemResourceID);
            return treeList2;
        } else {
            return treeList;
        }
    }

    // 通过模块下的交易递归查询父节点
    private List<Map<String, Object>> findTradeToSystemModule(List<Trade> trades2, Map<String, List<Map<String, Object>>> tradeNodesGroup, List<Map<String, Object>> treeList, String testSystemResourceID) {
        // 上一级是模块的所有交易id
        List<String> collect2 = trades2.stream().map(s -> String.valueOf(s.getModuleResourceID())).collect(Collectors.toList());
        // 根据交易查询到所有的上一级模块
        List<SystemModule> systemModulesA = systemModuleService.findByResourceIDIn(collect2);
        // 对上一级的模块进行去重
        List<SystemModule> systemModules = systemModulesA.stream().distinct().collect(Collectors.toList());
        // 根据模块rid对交易分组
        Map<Long, List<Trade>> moduleToTrades = trades2.stream().collect(Collectors.groupingBy(s -> s.getModuleResourceID()));
        // 交易上一级所有的模块节点
        List<Map<String, Object>> systemModuleNodes = new ArrayList<>();
        systemModules.stream().forEach(s -> {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", s.getId());
            map1.put("name", s.getName());
            map1.put("resourceID", s.getResourceID());
            map1.put("type", "module");
            map1.put("parentResourceID", s.getParentResourceID());
            map1.put("testSystemResourceID", s.getTestSystemResourceID());
            // 当前模块下的所有交易对象
            List<Trade> trades = moduleToTrades.get(s.getResourceID());
            // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
            List<List<Map<String, Object>>> collect = trades.stream().map(t -> tradeNodesGroup.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream).collect(Collectors.toList());
            map1.put("children", collect3);
            systemModuleNodes.add(map1);
        });

        if (systemModuleNodes.size() > 0) {
            // 通过交易的上一级模块递归向上查询父节点
            List<Map<String, Object>> treelist2 = findSystemModuleToSystemModule(systemModules, systemModuleNodes, treeList, testSystemResourceID);
            return treelist2;
        } else {
            return null;
        }
    }

    // 通过交易的上一级模块向上查询父节点
    private List<Map<String, Object>> findSystemModuleToSystemModule(List<SystemModule> systemModuleList, List<Map<String, Object>> systemModuleNodes, List<Map<String, Object>> treeList, String testSystemResourceID) {
        // 根据模块rid对模块节点分组
        Map<String, List<Map<String, Object>>> moduleGroup = systemModuleNodes.stream().collect(Collectors.groupingBy(s -> String.valueOf(s.get("resourceID"))));
        // 根据是否是被测系统下模块对象分组
        Map<Boolean, List<SystemModule>> collect1 = systemModuleList.stream().collect(Collectors.partitioningBy(s -> null != s.getParentResourceID() && s.getParentResourceID().equals(s.getTestSystemResourceID())));
        // 直接在被测系统下的模块对象
        List<SystemModule> systemModules1 = collect1.get(true);
        // 根据被测系统的rid进行分组（直接在被测系统下的模块对象）
        Map<Long, List<SystemModule>> collect2 = systemModules1.stream().collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 上一级还是模块的或者只到模块的当前模块对象,或者没有上一级节点只是模块
        List<SystemModule> systemModules2 = collect1.get(false);
        // 筛选出没有上一级只是模块的模块对象
        Map<Boolean, List<SystemModule>> collect7 = systemModules2.stream().collect(Collectors.groupingBy(s -> Objects.isNull(s.getParentResourceID()) && Objects.isNull(s.getTestSystemResourceID())));
        List<SystemModule> systemModules3 = collect7.get(true);
        // 根据上一级还是模块的模块父rid对进行分组
        List<SystemModule> systemModules4 = systemModules2.stream().filter(l -> !Objects.isNull(l.getParentResourceID()) && !Objects.isNull(l.getTestSystemResourceID())).collect(Collectors.toList());
        // 没有上一级，只是到模块截至的模块对象，UAT
        if (null != systemModules3 && systemModules3.size() > 0) {
            systemModules3.stream().forEach(s -> {
                // 模块下所有的模块节点
                List<Map<String, Object>> collect3 = moduleGroup.get(String.valueOf(s.getResourceID()));
                treeList.addAll(0, collect3);
            });
        }

        // 直接在被测系统下的模块对象,直接循环被测系统
        Set<Long> longs = collect2.keySet();
        longs.stream().forEach(s -> {
            // 查询上一级被测系统
            TestSystem testSystem = this.findByResourceID(s);
            // 在当前被测系统下的所有模块对象
            List<SystemModule> systemModules = collect2.get(testSystem.getResourceID());
            // 在当前被测系统下的所有模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream().map(module -> moduleGroup.get(String.valueOf(module.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream).collect(Collectors.toList());

            Optional<Map<String, Object>> ifExist = treeList.stream().filter(x -> x.get("resourceID").equals(testSystem.getResourceID())).findFirst();
            // 存在为true
            if (ifExist.isPresent()) {
                // 说明顶级被测系统节点重复,找出重复节点添加
                Map<String, Object> testSystemMap = ifExist.get();
                List<Map<String, Object>> list = (List<Map<String, Object>>) testSystemMap.get("children");
                // 合并两个children元素
                List<Map<String, Object>> collect6 = Stream.of(list, collect3).flatMap(Collection::stream).collect(Collectors.toList());
                testSystemMap.put("children", collect6);
                // 排除treeList原有集合中的重复元素
                treeList.removeIf(x -> x.get("resourceID").equals(testSystemMap.get("resourceID")));
                treeList.add(testSystemMap);
            } else {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", testSystem.getId());
                map1.put("name", testSystem.getName());
                map1.put("resourceID", testSystem.getResourceID());
                map1.put("type", "system");
                map1.put("manager", testSystem.getManager());
                map1.put("children", collect3);
                treeList.add(map1);
            }
        });

        // 上一级还是模块的节点收集器
        //List<Map<String, Object>> systemModules3 = new ArrayList<>();
        systemModuleNodes.clear();
        // 上一级还是模块下的模块对象
        List<SystemModule> systemModulesParent = null;
        if (null != systemModules4 && systemModules4.size() > 0) {
            Map<Long, List<SystemModule>> collect4 = systemModules4.stream().collect(Collectors.groupingBy(s -> s.getParentResourceID()));
            // 根据当前模块查询得到的还是模块上一级的模块对象
            List<String> collect5 = systemModules2.stream().map(s -> String.valueOf(s.getParentResourceID())).collect(Collectors.toList());
            List<SystemModule> systemModulesParentA = systemModuleService.findByResourceIDIn(collect5);
            // 对查询得到的上一级模块去重
            systemModulesParent = systemModulesParentA.stream().distinct().collect(Collectors.toList());
            systemModulesParent.stream().forEach(parentModule -> {
                // 当前模块上一级还是模块,组装父节点返回
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", parentModule.getId());
                map1.put("name", parentModule.getName());
                map1.put("resourceID", parentModule.getResourceID());
                map1.put("type", "module");
                map1.put("parentResourceID", parentModule.getParentResourceID());
                map1.put("testSystemResourceID", parentModule.getTestSystemResourceID());
                // 父模块下所有的模块对象
                List<SystemModule> systemModules = collect4.get(parentModule.getResourceID());
                // 父模块下所有的模块节点
                List<List<Map<String, Object>>> collect = systemModules.stream().map(module -> moduleGroup.get(String.valueOf(module.getResourceID()))).collect(Collectors.toList());
                List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream).collect(Collectors.toList());
                map1.put("children", collect3);
                systemModuleNodes.add(map1);
            });
        }

        if (systemModuleNodes.size() > 0 && null != systemModulesParent && systemModulesParent.size() > 0) {
            List<Map<String, Object>> treeList2 = findSystemModuleToSystemModule(systemModulesParent, systemModuleNodes, treeList, testSystemResourceID);
            return treeList2;
        } else {
            return treeList;
        }
    }

    /**
     * @param "[map]"
     * @return void
     * @throws
     * @Title: exportTestSystem
     * @description: 导出被测系统
     * <AUTHOR>
     * @date 2020/2/20 17:03
     */
    @Override
    public void exportTestSystem(Map map) {
        HttpServletRequest request = (HttpServletRequest) map.get("request");
        HttpServletResponse response = (HttpServletResponse) map.get("response");
        String agent = (String) map.get("Agent");
        //测试方式数据处理
        String testMode = (String) map.get("testMode");
        if(testMode == null || "".equals(testMode))
            testMode = "0";
        testMode = BinaryDecimalUtil.DicValToBin(testMode);
        map.put("testMode",testMode);

        //筛选勾选的系统
        String testSystemRids = map.get("testSystemRids").toString();
        List<TestSystemDTO> list = new ArrayList<>();
        if(testSystemRids!=null&&testSystemRids!= ""){
            list = testSystemDao.findByTestSystemAndSearch(map).stream().filter(item->testSystemRids.contains(item.getResourceID().toString())).collect(Collectors.toList());
        }else{
            list = testSystemDao.findByTestSystemAndSearch(map);
        }
        try {
            XSSFWorkbook wb = createExcel(list);

            response.reset();
            OutputStream out = null;
            out = response.getOutputStream();
            String fileName = "testSystem" + DateUtil.getDateStr(new Date()) + System.currentTimeMillis() + ".xlsx";
            String mimeType = request.getSession().getServletContext().getMimeType(fileName);
            response.setHeader("content-type", mimeType + ";charset=UTF-8");
            String downloadName = DownLoadUtils.getName(agent, fileName);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(downloadName,"utf-8"));
//            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("utf-8");

            wb.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }


    }

    /**
     * @param "[list]"
     * @return org.apache.poi.xssf.usermodel.XSSFWorkbook
     * @throws
     * @Title: createExcel
     * @description: 创建excel
     * <AUTHOR>
     * @date 2020/2/21 11:18
     */
    private XSSFWorkbook createExcel(List<TestSystemDTO> list) {
        XSSFWorkbook wb = new XSSFWorkbook();
        String[] title = new String[]{"系统简称", "系统名称",/* "描述",*/ "业务归口部门", "业务负责人", "开发公司",
                "行方开发经理", "SIT测试负责人", "UAT测试负责人", "重要级", "开发人员","测试方式"};
        XSSFSheet sheet = wb.createSheet();
        XSSFRow titleRow = sheet.createRow(0);

        sheet.setColumnWidth(0, 100 * 55);
        sheet.setColumnWidth(1, 100 * 55);
        sheet.setColumnWidth(2, 100 * 40);
        sheet.setColumnWidth(3, 100 * 35);
        sheet.setColumnWidth(4, 100 * 35);
        sheet.setColumnWidth(5, 100 * 35);
        sheet.setColumnWidth(6, 100 * 35);
        sheet.setColumnWidth(7, 100 * 35);
        sheet.setColumnWidth(8, 100 * 35);
        sheet.setColumnWidth(9, 100 * 35);
        sheet.setColumnWidth(10, 100 * 80);
        //sheet.setColumnWidth(10, 100 * 35);
        //sheet.setColumnWidth(11, 100 * 35);
//        sheet.setColumnWidth(11,100 * 35);
//        sheet.setColumnWidth(12,100 * 35);
//        sheet.setColumnWidth(13,100 * 35);

        sheet.setDefaultRowHeight((short) (22 * 20));

        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);//垂直
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);

        for (int i = 0; i < title.length; i++) {
            XSSFCell cell = titleRow.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(cellStyle);
        }
        dataDicUtil.getDicMap("testSystem");
        //  List<Map<String, Object>> hostTribe_list = dataDicUtil.getList("HostTribe");
        String token = HttpRequestUtils.getCurrentRequestToken();
        //  List<Map<String, String>> tribeList = feignDataDesignToBasicService.findNameAndInfoNameByDirName("所属部落",token);

        Result hfResult = feignDataDesignToBasicService.findTypeAllUser(token);
        List<Map<String, Object>> fh_List = (List<Map<String, Object>>) hfResult.getObj();

        //查询所有人员(sit测试人,sit负责人,)
        List<JettechUserDTO> allPerson = feignDataDesignToBasicService.findAllPerson(token);
        List<Map<String, Object>> testModeList = dataDicUtil.getList("testMode");
        Map<String, String> tmMap = testModeList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));
        int m=CheckUtil.checkLoop(list.size());
        for (int i = 0; i <m ; i++) {
            TestSystemDTO ts = list.get(i);
            XSSFRow row = sheet.createRow(i + 1);
            for (int j = 0; j < title.length; j++) {
                XSSFCell cell = row.createCell(j);
                switch (j) {
                    case 0:
                        //系统简称
                        cell.setCellValue(ts.getSimpleName());
                        break;
                    case 1:
                        //系统名称
                        cell.setCellValue(ts.getName());
                        break;
                    case 2:
                        //业务归口部门
                        String bpd = ts.getBusPCentralizedDepartment();
                        if (StringUtils.isBlank(bpd)) continue;
                        bpd = dataDicUtil.getTextName("CentralizedBusinessDepartment", bpd);
                        cell.setCellValue(bpd);
                        break;
                    /*case 2:
                        //描述
                        cell.setCellValue(ts.getDescribes());
                        break;*/
                    case 3:
                        //业务负责人
                        String manager = ts.getManager();
                        if (StringUtils.isBlank(manager)) continue;
                        String[] manager_arr = manager.split(",");
                        List<String> manager_list = Arrays.asList(manager_arr);
                        String res = fh_List.stream().filter(e -> manager_list.contains(e.get("number").toString()))
                                .map(e -> e.get("userName").toString()).collect(Collectors.joining(","));
                        cell.setCellValue(res);
                        break;
//                    case 4:
//                        //所属部落
//                        String bt = ts.getBelongTribe();
//                        if(StringUtils.isBlank(bt)) continue;
//                       /* String id = tribeList.stream()
//                                .filter(e -> bt.equals(e.get("id").toString()))
//                                .findFirst()
//                                .get()
//                                .get("text");*/
//                        String id="";
//                        Optional<Map<String, String>> op= tribeList.stream()
//                        .filter(e -> bt.equals(e.get("id").toString()))
//                        .findFirst();
//                        if(op!=null && op.isPresent()) {
//                        	id=op.get().get("text");
//                        }
//                        cell.setCellValue(id);
//                        break;

//                    case 6:
//                        //所属小队
//                        String sbt = ts.getSubordinatTeam();
//                        if(StringUtils.isBlank(sbt)) continue;
//                        XSSFCell cell1 = row.getCell(j - 2);
//                        String stringCellValue = cell1.getStringCellValue();
//                        List<Map<String, Object>> teamList = (List<Map<String, Object>>) hostTribe_list.get(0).get(stringCellValue);
//                        String hostTeam="";
//                        if(teamList!=null && !teamList.isEmpty()) {
//                        	Optional<Map<String, Object>> textName = teamList.stream().filter(e -> sbt.equals(e.get("id").toString())).findFirst();
//                        	if(textName!=null && textName.isPresent()) {
//                        		 hostTeam = textName.get().get("text").toString();
//                        	}
//                        }
//                        cell.setCellValue(hostTeam);
//                        break;
                    case 4:
                        //开发公司
                        String company = ts.getDevelopCompany();
                        if (StringUtils.isBlank(company)) continue;
                        String[] split = company.split(",");
                        List<String> split_list = Arrays.asList(split);
                        //查询开发公司
                        Result companyRes = feignDataDesignToBasicService.findOutDept(token);
                        List<Map<String, Object>> company_list = (List<Map<String, Object>>) companyRes.getObj();
                        String developCompanys = company_list.stream()
                                .filter(e -> split_list.contains(e.get("resourceID").toString()))
                                .map(e -> e.get("name").toString()).collect(Collectors.joining(","));
                        cell.setCellValue(developCompanys);
                        break;
                    case 5:
                        //行方项目经理
                        String pm = ts.getProjectManager();
                        if (StringUtils.isBlank(pm)) continue;
                        String[] pro_arr = pm.split(",");
                        List<String> pro_list = Arrays.asList(pro_arr);
                        String projectManagers = fh_List.stream().filter(e -> pro_list.contains(e.get("number").toString()))
                                .map(e -> e.get("userName").toString()).collect(Collectors.joining(","));
                        cell.setCellValue(projectManagers);
                        break;
                    case 6:
                        //SIT测试负责人
                        String sitTestManger = ts.getSITTestManger();
                        if (StringUtils.isBlank(sitTestManger)) continue;
                        String[] sitM_arr = sitTestManger.split(",");
                        List<String> sitM_list = Arrays.asList(sitM_arr);
                        String SITTestMangers = allPerson.stream().filter(e -> sitM_list.contains(e.getNumber()))
                                .map(e -> e.getUserName()).collect(Collectors.joining(","));
                        cell.setCellValue(SITTestMangers);
                        break;
                    case 7:
                        //SIT测试人员
                        String sitTesters = ts.getSITTesters();
                        if (StringUtils.isBlank(sitTesters)) continue;
                        String[] sitT_arr = sitTesters.split(",");
                        List<String> sitT_list = Arrays.asList(sitT_arr);
                        String SITTesterses = allPerson.stream().filter(e -> sitT_list.contains(e.getNumber()))
                                .map(e -> e.getUserName()).collect(Collectors.joining(","));
                        cell.setCellValue(SITTesterses);
                        break;
                    case 8:
                        //重要级
                        String important = ts.getImportant();
                        if (StringUtils.isBlank(important)) continue;
                        String name = dataDicUtil.getTextName("important", important);
                        cell.setCellValue(name);
                        break;
//                    case 12:
//                        //所属研发中心
//                        String developmentCenter = ts.getDevelopmentCenter();
//                        if(StringUtils.isBlank(developmentCenter)) continue;
//                        String textname = dataDicUtil.getTextName("developmentCenter", developmentCenter);
//                        cell.setCellValue(textname);
//                        break;
                    case 9:
                        //开发人员
                        String dp = ts.getDevelopPerson();
                        if (StringUtils.isBlank(dp)) continue;
                        String[] dp_arr = dp.split(",");
                        List<String> dpList = Arrays.asList(dp_arr);
                        String developPersons = allPerson.stream().filter(e -> dpList.contains(e.getNumber()))
                                .map(e -> e.getUserName()).collect(Collectors.joining(","));
                        cell.setCellValue(developPersons);
                        break;
                    case 10:
                        //测试方式
                        if (ts.getTestMode()!=null) {
                            List<String> strs = BinaryDecimalUtil.TenToDicVal(ts.getTestMode());
                            String testModeValue = "";
                            for (String s : strs) {
                                testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) +",";
                            }
                            cell.setCellValue("".equals(testModeValue) ? "" : testModeValue.substring(0,testModeValue.length() - 1));
                        }
                        break;
                    default:
                        break;

                }
            }
        }
        return wb;
    }

    /**
     * @return
     * @Title: searchTestSystem
     * @Description: 查询系统
     * <AUTHOR>
     * @date 2020年2月23日 上午11:20:51
     */
    public List<Map<String, Object>> searchTestSystem() {
        List<Map<String, Object>> list = new ArrayList<>();
        List<TestSystem> testSystems = findAll();
        for (TestSystem testSystem : testSystems) {
            Map<String, Object> map = new HashMap<>();
            map.put("resourceID", testSystem.getResourceID());
            map.put("name", testSystem.getName());
            list.add(map);
        }
        return list;
    }

    /**
     * @param userResourceID
     * @return
     * @Title: findTestSystemByUserResourceID
     * @Description: 查询人员所属系统
     * <AUTHOR>
     * @date 2020年3月3日 下午9:02:30
     */
    @Override
    public String findTestSystemByUserResourceID(Long userResourceID) {
        return testSystemDao.findTestSystemByUserResourceID(userResourceID);
    }

    /**
     * @Title findAllSubordinateSystemMap
     * @Description 缺陷归属系统初始化查所有
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2020/3/6
     */
    @Override
    public Result findSubordinateSystemMap() {
        List<TestSystem> subordinateSystemMap = testSystemDao.findSubordinateSystemMap();
        return Result.renderSuccess(subordinateSystemMap);
    }

    /**
     * @Title: findTradeByResourceID
     * @Description: 根据交易id查询系统
     * @Param: "[tradeResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/3/7
     */
    @Override
    public Result findTestSystembyTradeByResourceID(Long tradeResourceID) {
        TestSystem testSystem = null;
        Trade trade = tradeService.findByResourceID(tradeResourceID);
        if (null != trade.getTestSystemResourceID()) {
            testSystem = this.findByResourceID(trade.getTestSystemResourceID());
        }
        return Result.renderSuccess(testSystem);
    }

    /**
     * @Title findTestSystemMap
     * @Description 查询被测系统返回Map
     * @Params [systemRID]
     * @Return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Date 2020/3/7
     */
    @Override
    public Map<String, String> findTestSystemMap(Long systemRID) {
        return testSystemDao.findTestSystemMap(systemRID);
    }

    /**
     * @param
     * @return Result
     * @Title:findLeftTreeByTestStage
     * @Description:通过测试阶段初始化左侧树
     * @author: wu_yancheng
     * @date 2020年4月1日下午3:04:10
     */
    @Override
    public Result findLeftTreeByTestStage(String demandResourceID, String testStage) {
        if (testStage.equals("SIT"))
            return this.findLeftTreeBySIT(demandResourceID);
        if (testStage.equals("UAT"))
            return this.findLeftTreeByUAT(demandResourceID);
        else {
            if (demandResourceID == null || "".equals(demandResourceID)) {
                return Result.renderError("查询参数为空!");
            }
            List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceID(demandResourceID);
            LeftTreeConfiguration l = leftTreeConfigurationService.findLeftTreeConfigurationByDemandResrouceIDAndType(demandResourceID, testStage);
            if (null != l) {
                String configuration = l.getConfiguration();
                List<Map> configMaps = JSONArray.parseArray(configuration, Map.class);
                List<String> resourceIDs = configMaps.stream().map(s -> String.valueOf(s.get("resourceID"))).collect(Collectors.toList());
                if (testSystemList.size() > 0) {
                    for (Map<String, Object> map : testSystemList) {
                        if (!resourceIDs.contains(String.valueOf(map.get("resourceID")))) {
                            configMaps.add(map);
                        }
                    }
                }
                return Result.renderSuccess(configMaps);
            }
            return Result.renderSuccess(testSystemList);
        }
    }

    /**
     * 当任务类型是案例设计时，任务范围的左侧系统模块树
     *
     * @param @param  systemResourceID
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: getTestTaskTradeTreeBySystemResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @SuppressWarnings({"unchecked", "unlikely-arg-type"})
    @Override
    public Result<?> getTestTaskTradeTreeBySystemResourceID(Map<String, String> params) {
        String systemResourceID = params.get("testSystemResourceID");
        String userNumber = params.get("userNumber");
        String taskResourceID = params.get("taskResourceID");
        Map<Long, String> relationModuleMap = new HashMap<Long, String>();
        Map<Long, String> allModuleMap = new HashMap<Long, String>();
        List<Map<Long, String>> relationModuleListMap = this.findUserUnderSystemORModuleTaskTradeCount(userNumber, taskResourceID, "module");
        if (!relationModuleListMap.isEmpty() && relationModuleListMap.size() > 0) {
            for (Map<Long, String> map : relationModuleListMap) {
                Object obj1 = map.get("systemOrModuleResourceID");
                Object obj2 = map.get("quoteSum");

                relationModuleMap.put(Long.valueOf(obj1.toString()), String.valueOf(obj2.toString()));
            }
        }
        List<Map<Long, String>> allModuleListMap = this.findUnderSystemORModuleTradeCount("module");
        if (!allModuleListMap.isEmpty() && allModuleListMap.size() > 0) {
            for (Map<Long, String> map : allModuleListMap) {
                Object obj1 = map.get("systemOrModuleResourceID");
                Object obj2 = map.get("sumCount");
                allModuleMap.put(Long.valueOf(obj1.toString()), String.valueOf(obj2.toString()));
            }
        }
        System.out.println(relationModuleListMap.toString() + allModuleListMap.toString());
        List<SystemModule> sm_list = systemModuleService.findbyTestSystemResourceID(systemResourceID);
        List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();
        sm_list = sm_list.stream()
                .filter(e -> e.getParentResourceID().equals(e.getTestSystemResourceID())).collect(Collectors.toList());
        sm_list.forEach(e -> {
            HashMap<String, Object> map = new HashMap<>();
            map.put("id", e.getId());
            map.put("name", e.getName());
            map.put("resourceID", e.getResourceID());
            map.put("type", "module");
            map.put("childrenList", getTestTaskMuduleChildren(e, relationModuleMap, allModuleMap));
            map.put("parentID", e.getParentResourceID());
            if (relationModuleMap.containsKey(e.getResourceID())) {
                map.put("relationTradeSum", relationModuleMap.get(e.getResourceID()));
                map.put("allTradeSum", allModuleMap.get(e.getResourceID()));
            }
            listMap.add(map);
        });

        TestSystem ts = this.findByResourceID(Long.valueOf(systemResourceID));
        Map<Long, String> relationSystemMap = new HashMap<Long, String>();
        Map<Long, String> allSystemMap = new HashMap<Long, String>();
        List<Map<Long, String>> relationSystemListMap = this.findUserUnderSystemORModuleTaskTradeCount(userNumber, taskResourceID, "system");
        if (!relationSystemListMap.isEmpty() && relationSystemListMap.size() > 0) {
            for (Map<Long, String> map : relationSystemListMap) {
                Object obj1 = map.get("systemOrModuleResourceID");
                Object obj2 = map.get("quoteSum");

                relationSystemMap.put(Long.valueOf(obj1.toString()), String.valueOf(obj2.toString()));
            }
        }
        List<Map<Long, String>> allSystemListMap = this.findUnderSystemORModuleTradeCount("system");
        if (!allSystemListMap.isEmpty() && allSystemListMap.size() > 0) {
            for (Map<Long, String> map : allSystemListMap) {
                Object obj1 = map.get("systemOrModuleResourceID");
                Object obj2 = map.get("sumCount");
                allSystemMap.put(Long.valueOf(obj1.toString()), String.valueOf(obj2.toString()));
            }
        }
        HashMap<String, Object> systemMap = new HashMap<>();
        systemMap.put("id", ts.getId());
        systemMap.put("name", ts.getName());
        systemMap.put("resourceID", ts.getResourceID());
        systemMap.put("type", "system");
        systemMap.put("childrenList", listMap);
        if (relationSystemMap.containsKey(ts.getResourceID())) {
            systemMap.put("relationTradeSum", relationSystemMap.get(ts.getResourceID()));
            systemMap.put("allTradeSum", allSystemMap.get(ts.getResourceID()));
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        maps.add(systemMap);
        return Result.renderSuccess(maps);
    }

    /**
     * 当任务类型是案例设计时，任务范围的左侧系统模块树,当点击系统或者模块时展示右侧的交易列表
     *
     * @param @param  params
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: initTestTaskTradeTable
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public Result<?> initTestTaskTradeTable(Map<String, String> params) {
        JSONObject result = new JSONObject();
        String type = params.get("type");
        String resourceID = params.get("resourceID");
        String name = params.get("name");
        String userNumber = params.get("userNumber");
        String taskResourceID = params.get("taskResourceID");
        Integer page = params.get("page") == null ? 1 : Integer.valueOf(params.get("page"));
        Integer pageSize = params.get("pageSize") == null ? 10 : Integer.valueOf(params.get("pageSize"));
        PageHelper.startPage(page, pageSize);
        List<Trade> trades = null;
        switch (type) {
            case "system":
                // 根据被测系统查询交易
//            	List<Trade> tradeTmp = tradeService.findbyTestSystemResourceID(resourceID, name);
                trades = tradeService.findOnlyTradebyTestSystemResourceID(resourceID, name);
//                trades = tradeTmp.stream().filter(tra ->tra.getModuleResourceID() == null).collect(Collectors.toList());
                break;
            case "module":
                // 根据当前模块查询交易
                trades = tradeService.findBySystemModuleResourceID(resourceID, name, null, null);
                break;
            default:
                break;
        }
        if (!trades.isEmpty() && trades.size() > 0) {
            List<String> tradeRids = trades.stream().map(x -> x.getResourceID().toString()).collect(Collectors.toList());
            //查找当前人当前任务i下勾选的案例rid
            List<String> hasRidTestCases = testSystemDao.findTestTaskCaseResourceIDsInTrades(tradeRids, userNumber, taskResourceID);
            if (!hasRidTestCases.isEmpty() && hasRidTestCases.size() > 0) {
                trades.stream().forEach(x -> {
                    if (hasRidTestCases.contains(x.getResourceID().toString())) {
                        x.setComment("1");//选中
                    } else {
                        x.setComment("0");//未选中
                    }
                });

            } else {
                trades.stream().forEach(x -> x.setComment("0"));
            }

        }
        PageInfo<Trade> pageInfo = new PageInfo<Trade>(trades);
        // 当前页
        result.put("page", page);
        // 当前页的条数
        result.put("pageSize", pageSize);
        // 总的条数
        result.put("total", pageInfo.getTotal());
        // 总的页数
        result.put("totalPage", pageInfo.getPages());
        // 返回数据
        result.put("pageData", pageInfo.getList());

        return Result.renderSuccess(result);
    }

    private List<Map<String, Object>> getTestTaskMuduleChildren(SystemModule sm, Map<Long, String> relationModuleMap, Map<Long, String> allModuleMap) {
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<SystemModule> sm_list = systemModuleService.findByParentResourceID(sm.getResourceID().toString());
        if (sm_list != null || !sm_list.isEmpty()) {
            sm_list.forEach(e -> {
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", e.getId());
                map.put("name", e.getName());
                map.put("resourceID", e.getResourceID());
                map.put("type", "module");
                map.put("childrenList", getTestTaskMuduleChildren(e, relationModuleMap, allModuleMap));
                map.put("parentID", e.getParentResourceID());
                if (relationModuleMap.containsKey(e.getResourceID())) {
                    map.put("relationTradeSum", relationModuleMap.get(e.getResourceID()));
                    map.put("allTradeSum", allModuleMap.get(e.getResourceID()));
                }
                listMap.add(map);
            });
        }
        return listMap;
    }

    /**
     * 查询当前人在当前任务下,任务交易已关联的系统或者模块中的任务交易表中引用的交易(flag-module模块/flag-system系统)
     *
     * @param @return 参数
     * @return Map<Long, String>    返回类型
     * @throws
     * @Title: findUserUnderModuleTaskTradeCount
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<Map<Long, String>> findUserUnderSystemORModuleTaskTradeCount(String userNumber, String taskResourceID, String Flag) {

        return systemModuleService.findUserUnderModuleTaskTradeCount(userNumber, taskResourceID, Flag);
    }

    ;

    /**
     * 查询交易列表系统或者模块下面的交易总数（flag-system系统/flag-module-模块）
     *
     * @param @param  flag
     * @param @return 参数
     * @return List<Map < Long, String>>    返回类型
     * @throws
     * @Title: findUnderSystemORModuleTradeCount
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<Map<Long, String>> findUnderSystemORModuleTradeCount(String flag) {

        return systemModuleService.findUnderSystemORModuleTradeCount(flag);
    }

    /**
     * 根据任务的rid查询该任务维护的系统模块交易范围，展示为左侧树
     *
     * @param @param  params
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findLeftTreeByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> findLeftTreeByTestTaskResourceID(Map<String, String> params) {
        String taskResourceID = params.get("taskResourceID");
        if (StringUtils.isEmpty(taskResourceID)) {
            return Result.renderError("taskResourceID参数为空！");
        }
        Map<String, String> taskMap = this.findByTestTaskResourceID(taskResourceID);
        if (taskMap.isEmpty() || taskMap == null) {
            return Result.renderError("当前任务不存在或已被删除！");
        }
        List<Trade> tradesFind = this.findSelectedTradeByTaskResourceID(taskResourceID);
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", "0");
        map.put("name", "系统交易");
        map.put("type", "root");
        map.put("environment", taskMap.get("testStage"));
        map.put("testStageResourceID", String.valueOf(taskMap.get("testStageResourceID")));
        //如果有保存的交易数据
        if (!tradesFind.isEmpty() && tradesFind.size() > 0) {
            Map<String, Object> systemMap = (Map<String, Object>) this.buildTreeOne(tradesFind, "").getObj();
            List<Map<String, Object>> maps = new ArrayList<>();
            List<Map<String, Object>> listSys = new ArrayList<Map<String, Object>>();
            listSys.add(systemMap);
            map.put("children", listSys);
            maps.add(map);
            return Result.renderSuccess(maps);
        } else {//没有保存数据
            map.put("children", new ArrayList<List<Map<String, Object>>>());
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        maps.add(map);
        return Result.renderSuccess(maps);
    }

    ;

    private Result<?> buildTreeOne(List<Trade> tradesFind, String taskResourceID) {
        //被测系统和交易
        Map<String, List<Trade>> mapTestSystemTrade = new HashMap<>();
        //模块和交易
        Map<String, List<Trade>> mapModuleTrade = new HashMap<>();
        String testSystemResourceID = "";
        for (Trade trade : tradesFind) {
            if (org.springframework.util.StringUtils.isEmpty(trade.getTestSystemResourceID())) {//UAT流程用例
                continue;
            }
            if (!org.springframework.util.StringUtils.isEmpty(trade.getModuleResourceID())) {//模块不空，交易挂载模块下
                if (!mapModuleTrade.isEmpty() && mapModuleTrade.containsKey(trade.getModuleResourceID().toString())) {
                    mapModuleTrade.get(trade.getModuleResourceID().toString()).add(trade);
                } else {
                    List<Trade> listTrade = new ArrayList<Trade>();
                    listTrade.add(trade);
                    mapModuleTrade.put(trade.getModuleResourceID().toString(), listTrade);
                }
            } else {//否则直接挂在被测系统下
                if (!mapTestSystemTrade.isEmpty() && mapTestSystemTrade.containsKey(trade.getTestSystemResourceID().toString())) {
                    mapTestSystemTrade.get(trade.getTestSystemResourceID().toString()).add(trade);
                } else {
                    List<Trade> listTrade = new ArrayList<Trade>();
                    listTrade.add(trade);
                    mapTestSystemTrade.put(trade.getTestSystemResourceID().toString(), listTrade);
                }
            }
            testSystemResourceID = trade.getTestSystemResourceID().toString();
        }
        Map<String, Object> systemMap = new HashMap<>();
        if (org.springframework.util.StringUtils.isEmpty(testSystemResourceID)) {
            return Result.renderError("当前交易数据信息有误！");
        } else {
            TestSystem testSystem = this.findByResourceID(Long.valueOf(testSystemResourceID));
            if (testSystem == null) {
                return Result.renderError("当前树结构被测系统不存在或已被删除！");
            }
            systemMap.put("id", testSystem.getId());
            systemMap.put("name", testSystem.getName());
            systemMap.put("resourceID", testSystem.getResourceID());
            systemMap.put("type", "system");
            systemMap.put("children", new ArrayList<>());
            systemMap.put("parentID", "0");
            for (Map.Entry<String, List<Trade>> entry : mapTestSystemTrade.entrySet()) {
                List<Trade> trades = entry.getValue();
                List<Map<String, Object>> childrenTradeList = new ArrayList<>();
                for (Trade trade : trades) {
                    Map<String, Object> tradeMap = new HashMap<>();
                    tradeMap.put("id", trade.getId());
                    tradeMap.put("name", trade.getName());
                    tradeMap.put("resourceID", trade.getResourceID());
                    tradeMap.put("type", "trade");
                    tradeMap.put("parentID", entry.getKey());
//	    		        if(!org.springframework.util.StringUtils.isEmpty(taskResourceID)) {
//	    		        	tradeMap.put("children", this.buildTestCaseTreeList(trade.getResourceID().toString(), taskResourceID));
//	    		        }else {
                    tradeMap.put("children", new ArrayList<>());
//	    		        }
                    childrenTradeList.add(tradeMap);
                }
                if (!org.springframework.util.StringUtils.isEmpty(childrenTradeList) && childrenTradeList.size() > 0) {
                    systemMap.put("children", childrenTradeList);
                }
            }
        }

        //查询当前被测系统下所有的模块（包含子模块）
        List<SystemModule> allModules = systemModuleService.findbyTestSystemResourceID(testSystemResourceID);
        Map<String, SystemModule> mapAllModule = new HashMap<>();
        allModules.stream().forEach(x -> {
            mapAllModule.put(x.getResourceID().toString(), x);
        });
        //用来存储所有已存在树模块节点
        Set<String> moduleSet = new HashSet<>();
        Map<String, Map<String, Object>> moduleMapMap = new HashMap<>();
        moduleMapMap.put(testSystemResourceID, systemMap);
        //建立交易和直属模块的关系
        for (Map.Entry<String, List<Trade>> entry : mapModuleTrade.entrySet()) {
            String moduleRid = entry.getKey();
            SystemModule systemModule = mapAllModule.get(moduleRid);
            Map<String, Object> moduleMap = new HashMap<>();
            moduleMap.put("id", systemModule.getId());
            moduleMap.put("name", systemModule.getName());
            moduleMap.put("resourceID", systemModule.getResourceID());
            moduleMap.put("type", "module");
            moduleMap.put("parentID", systemModule.getParentResourceID().toString());
            moduleMap.put("children", new ArrayList<>());
            List<Trade> trades = entry.getValue();
            List<Map<String, Object>> childrenTradeList = new ArrayList<>();
            for (Trade trade : trades) {
                Map<String, Object> tradeMap = new HashMap<>();
                tradeMap.put("id", trade.getId());
                tradeMap.put("name", trade.getName());
                tradeMap.put("resourceID", trade.getResourceID());
                tradeMap.put("type", "trade");
                tradeMap.put("parentID", moduleRid);
//		        if(!org.springframework.util.StringUtils.isEmpty(taskResourceID)) {
//		        	tradeMap.put("children", this.buildTestCaseTreeList(trade.getResourceID().toString(), taskResourceID));
//		        }else {
                tradeMap.put("children", new ArrayList<>());
//		        }
//		        tradeMap.put("children", new ArrayList<>());
                childrenTradeList.add(tradeMap);
            }
            if (!org.springframework.util.StringUtils.isEmpty(childrenTradeList) && childrenTradeList.size() > 0) {
                moduleMap.put("children", childrenTradeList);
            }
            //如果这个模块的父级为被测系统
            if (systemModule.getParentResourceID().equals(systemModule.getTestSystemResourceID())) {
                List<Map<String, Object>> systemChilds = (List<Map<String, Object>>) systemMap.get("children");
                if (!systemChilds.contains(moduleMap)) {
                    systemChilds.add(moduleMap);
                    moduleSet.add(moduleRid);
                    moduleMapMap.put(moduleRid, moduleMap);
                }
                continue;
            }
            moduleSet.add(moduleRid);
            moduleMapMap.put(moduleRid, moduleMap);
            systemMap = this.buildTree(moduleSet, mapAllModule, systemModule, systemMap, moduleMapMap);

        }
        return Result.renderSuccess(systemMap);
    }

    /**
     * 查询任务和测试计划信息
     *
     * @param @param  taskResourceID
     * @param @return 参数
     * @return Map<String, Object>    返回类型
     * @throws
     * @Title: findByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private Map<String, String> findByTestTaskResourceID(String taskResourceID) {

        return testSystemDao.findByTestTaskResourceID(taskResourceID);
    }

    ;

    /**
     * 查询任务下选中的交易
     *
     * @param @return 参数
     * @return List<Trade>    返回类型
     * @throws
     * @Title: findSelectedTradeByTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<Trade> findSelectedTradeByTaskResourceID(String taskResourceID) {

        return tradeService.findSelectedTradeByTaskResourceID(taskResourceID);

    }

    /**
     * 构造树结构
     *
     * @param @param allModules 系统下所有模块
     * @param @param module 当前模块
     * @param @param systemResourceID    参数 被测系统rid
     * @return void    返回类型
     * @throws
     * @Title: buildTree
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> buildTree(Set<String> moduleSet, Map<String, SystemModule> mapAllModule, SystemModule module,
                                          Map<String, Object> systemMap, Map<String, Map<String, Object>> moduleMapMap) {
        //父节点和被测系统节点一样的话，该模块直接挂在被测系统下
        if (module.getParentResourceID().equals(module.getTestSystemResourceID())) {
            List<Map<String, Object>> systemChilds = (List<Map<String, Object>>) systemMap.get("children");
            if (!systemChilds.contains(moduleMapMap.get(module.getResourceID().toString()))) {
                systemChilds.add(moduleMapMap.get(module.getResourceID().toString()));
            }
            return systemMap;
        }
        //模块父模块
        SystemModule systemModuleParent = mapAllModule.get(module.getParentResourceID().toString());
        if (moduleMapMap.get(systemModuleParent.getResourceID().toString()) != null) {
            Map<String, Object> parentMap = moduleMapMap.get(systemModuleParent.getResourceID().toString());
            List<Map<String, Object>> parentChilds = (List<Map<String, Object>>) parentMap.get("children");
            if (!parentChilds.contains(moduleMapMap.get(module.getResourceID().toString()))) {
                parentChilds.add(moduleMapMap.get(module.getResourceID().toString()));
            }
        } else {
            Map<String, Object> moduleMap = new HashMap<>();
            moduleMap.put("id", systemModuleParent.getId());
            moduleMap.put("name", systemModuleParent.getName());
            moduleMap.put("resourceID", systemModuleParent.getResourceID());
            moduleMap.put("type", "module");
            moduleMap.put("parentID", systemModuleParent.getParentResourceID().toString());
            List<Map<String, Object>> chlds = new ArrayList<>();
            chlds.add(moduleMapMap.get(module.getResourceID().toString()));
            moduleMap.put("children", chlds);
            if (!moduleSet.contains(systemModuleParent.getResourceID().toString())) {//如果这个模块没有被处理过
                moduleSet.add(systemModuleParent.getResourceID().toString());
            }
            moduleMapMap.put(systemModuleParent.getResourceID().toString(), moduleMap);

        }
        return this.buildTree(moduleSet, mapAllModule, systemModuleParent, systemMap, moduleMapMap);

    }

    /**
     * 根据任务的rid查询该任务维护的系统模块交易和手工编写案例，展示为左侧树（手工执行用例范围树结构-来源为任务的勾选保存的案例）
     *
     * @param @param  params
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findManExecuteLeftTreeByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> findManExecuteLeftTreeByTestTaskResourceID(Map<String, String> params) {
        String taskResourceID = params.get("taskResourceID");
        if (StringUtils.isEmpty(taskResourceID)) {
            return Result.renderError("taskResourceID参数为空！");
        }
        Map<String, String> taskMap = this.findByTestTaskResourceID(taskResourceID);
        if (taskMap == null || taskMap.isEmpty()) {
            return Result.renderError("当前任务不存在或已被删除！");
        }
        String userNumber = params.get("userNumber");

        Date startTimeda = null;
        Date endTimeda = null;
        if (!org.springframework.util.StringUtils.isEmpty(params.get("startTime"))) {
            startTimeda = DateUtil.getDate(params.get("startTime"), "yyyy-MM-dd HH:mm:ss");
        }
        if (!org.springframework.util.StringUtils.isEmpty(params.get("endTime"))) {
            endTimeda = DateUtil.getDate(params.get("endTime"), "yyyy-MM-dd HH:mm:ss");
        }
        //案例的状态
        String testCaseState = "";
        if (!org.springframework.util.StringUtils.isEmpty(params.get("testCaseState"))) {
            testCaseState = String.valueOf(params.get("testCaseState"));
        }
        Map<String, String> mapForCaseRunResult = new HashMap<>();
        List<String> hasQuoteSearchCaseRids = new ArrayList<>();
        //查询当前任务下引用人在引用表中的案例引用
        String testPlanResourceID = String.valueOf(taskMap.get("testPlanResourceID"));
        List<Map<String, String>> mapForCaseQuote = this.findCaseQuoteListByInfomation(taskResourceID, userNumber, startTimeda, endTimeda, testCaseState);
        if (!mapForCaseQuote.isEmpty() && mapForCaseQuote.size() > 0) {
            for (Map<String, String> map : mapForCaseQuote) {
                Object obj = map.get("testCaseResourceID");
                mapForCaseRunResult.put(String.valueOf(obj), map.get("caseResult"));
                hasQuoteSearchCaseRids.add(String.valueOf(obj));
            }
        } else {//没有引用案例数据
            HashMap<String, Object> map = new HashMap<>();
//	       	map.put("id", "0");
//	       	map.put("name", "系统交易");
//	       	map.put("type", "root");
            map.put("environment", taskMap.get("planName"));
            map.put("testPlanResourceID", String.valueOf(taskMap.get("testPlanResourceID")));
            map.put("id", "0");
            map.put("label", "系统交易");
            map.put("rid", "0");
            map.put("nodeType", "root");
            map.put("intent", "系统交易");
            map.put("children", new ArrayList<List<Map<String, Object>>>());
            List<Map<String, Object>> maps = new ArrayList<>();
            maps.add(map);
            return Result.renderSuccess(maps);
        }

        List<String> tradeRids = this.findTradeRidsBySelectedTestCaseByTaskResourceID(taskResourceID, userNumber, hasQuoteSearchCaseRids);
        HashMap<String, Object> map = new HashMap<>();
//    	map.put("id", "0");
//    	map.put("name", "系统交易");
//    	map.put("type", "root");
        map.put("id", "0");
        map.put("label", "系统交易");
        map.put("rid", "0");
        map.put("nodeType", "root");
        map.put("intent", "系统交易");
        map.put("environment", taskMap.get("planName"));
        map.put("testPlanResourceID", String.valueOf(taskMap.get("testPlanResourceID")));
        //如果有保存的交易数据
        if (!tradeRids.isEmpty() && tradeRids.size() > 0) {
            List<Trade> tradesFind = tradeService.findByResourceIDIn(tradeRids);
            Map<String, Object> systemMap = (Map<String, Object>) this.buildTreeOneForTestCase(tradesFind, taskResourceID, mapForCaseRunResult, userNumber, hasQuoteSearchCaseRids).getObj();
            List<Map<String, Object>> maps = new ArrayList<>();
            List<Map<String, Object>> listSys = new ArrayList<Map<String, Object>>();
            listSys.add(systemMap);
            map.put("children", listSys);
            maps.add(map);
            return Result.renderSuccess(maps);
        } else {//没有保存数据
            map.put("children", new ArrayList<List<Map<String, Object>>>());
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        maps.add(map);
        return Result.renderSuccess(maps);
    }

    /**
     * 根据任务id获取被测系统
     *
     * @param taskRid
     * @return 测试系统
     * <AUTHOR>
     */
    @Override
    public Result<List<TestSystem>> findTestSystemsByTestTaskResourceId(String taskRid) {
        Result<List<TestSystem>> res = new Result<>();
        List<Trade> trades = this.findSelectedTradeByTaskResourceID(taskRid);
        if (trades != null && trades.size() > 0) {
            List<TestSystem> testSystems = testSystemDao.findByResourceIDIn(trades.stream().map(
                    x -> x.getTestSystemResourceID().toString()).collect(Collectors.toList()));
            res.setSuccess(true);
            res.setObj(testSystems);
        }
        return res;
    }

    /**
     * 查询当前任务下引用人在引用表中的案例引用
     *
     * @param "testPlanResourceID"
     * @param @param               taskResourceID
     * @param @param               userNumber
     * @param @param               startTimeda
     * @param @param               endTimeda
     * @param @param               testCaseState
     * @param @return              参数
     * @return List<Map < String, String>>    返回类型
     * @throws
     * @Title: findCaseQuoteListByInfomation
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<Map<String, String>> findCaseQuoteListByInfomation(String taskResourceID, String userNumber,
                                                                    Date startTimeda, Date endTimeda, String testCaseState) {

        return testSystemDao.findCaseQuoteListByInfomation(taskResourceID, userNumber, startTimeda, endTimeda, testCaseState);
    }

    /**
     * 查询手工执行任务下选中保存的案例所属交易Rid
     *
     * @param userNumber
     * @param hasQuoteSearchCaseRids
     * @param @return                参数
     * @return List<Trade>    返回类型
     * @throws
     * @Title: findSelectedTestCaseByTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<String> findTradeRidsBySelectedTestCaseByTaskResourceID(String taskResourceID, String userNumber, List<String> hasQuoteSearchCaseRids) {

        return testCaseService.findTradeRidsBySelectedTestCaseByTaskResourceID(taskResourceID, userNumber, hasQuoteSearchCaseRids);

    }

    /**
     * 通过交易和任务查询保存的案例
     *
     * @param userNumber
     * @param hasQuoteSearchCaseRids
     * @param @param                 trades
     * @param @param                 taskResourceID
     * @param @return                参数
     * @return List<TestCase>    返回类型
     * @throws
     * @Title: findTestCasesByTaskAndTrades
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<TestCase> findTestCasesByTaskAndTrades(String tradeResourceID, String taskResourceID, String userNumber, List<String> hasQuoteSearchCaseRids) {

        return testCaseService.findTestCasesByTaskAndTrades(tradeResourceID, taskResourceID, userNumber, hasQuoteSearchCaseRids);
    }

    /**
     * 构建交易下案例树节点
     *
     * @param @param  tradeRid
     * @param @param  taskRid
     * @param @return 参数
     * @return List<Map < String, Object>>    返回类型
     * @throws
     * @Title: buildTestCaseTreeList
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private List<Map<String, Object>> buildTestCaseTreeList(String tradeRid, String taskRid, Map<String, String> mapForCaseRunResult, String userNumber, List<String> hasQuoteSearchCaseRids) {
        List<TestCase> testCases = this.findTestCasesByTaskAndTrades(tradeRid, taskRid, userNumber, hasQuoteSearchCaseRids);
        List<Map<String, Object>> listResult = new ArrayList<>();
        for (TestCase testCase : testCases) {
            Map<String, Object> caseMap = new HashMap<>();
            caseMap.put("id", testCase.getId());
            caseMap.put("label", testCase.getCaseId());
            caseMap.put("intent", testCase.getCaseId() + "  " + testCase.getIntent());
            caseMap.put("rid", testCase.getResourceID());
            caseMap.put("nodeType", "testCase");

//			caseMap.put("id", testCase.getId());
//			caseMap.put("caseID", testCase.getCaseId());
//			caseMap.put("intent", testCase.getIntent());
//			caseMap.put("resourceID", testCase.getResourceID());
//			caseMap.put("type", "testcase");
            caseMap.put("parentID", tradeRid);

            // 添加执行结果
            caseMap.put("caseResult", mapForCaseRunResult.get(testCase.getResourceID().toString()));
            listResult.add(caseMap);
        }
        return listResult;
    }

    /**
     * 案例用
     *
     * @param @param  tradesFind
     * @param @param  taskResourceID
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: buildTreeOne
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private Result<?> buildTreeOneForTestCase(List<Trade> tradesFind, String taskResourceID, Map<String, String> mapForCaseRunResult, String userNumber, List<String> hasQuoteSearchCaseRids) {
        //被测系统和交易
        Map<String, List<Trade>> mapTestSystemTrade = new HashMap<>();
        //模块和交易
        Map<String, List<Trade>> mapModuleTrade = new HashMap<>();
        String testSystemResourceID = "";
        for (Trade trade : tradesFind) {
            if (org.springframework.util.StringUtils.isEmpty(trade.getTestSystemResourceID())) {//UAT流程用例
                continue;
            }
            if (!org.springframework.util.StringUtils.isEmpty(trade.getModuleResourceID())) {//模块不空，交易挂载模块下
                if (!mapModuleTrade.isEmpty() && mapModuleTrade.containsKey(trade.getModuleResourceID().toString())) {
                    mapModuleTrade.get(trade.getModuleResourceID().toString()).add(trade);
                } else {
                    List<Trade> listTrade = new ArrayList<Trade>();
                    listTrade.add(trade);
                    mapModuleTrade.put(trade.getModuleResourceID().toString(), listTrade);
                }
            } else {//否则直接挂在被测系统下
                if (!mapTestSystemTrade.isEmpty() && mapTestSystemTrade.containsKey(trade.getTestSystemResourceID().toString())) {
                    mapTestSystemTrade.get(trade.getTestSystemResourceID().toString()).add(trade);
                } else {
                    List<Trade> listTrade = new ArrayList<Trade>();
                    listTrade.add(trade);
                    mapTestSystemTrade.put(trade.getTestSystemResourceID().toString(), listTrade);
                }
            }
            testSystemResourceID = trade.getTestSystemResourceID().toString();
        }
        Map<String, Object> systemMap = new HashMap<>();
        if (org.springframework.util.StringUtils.isEmpty(testSystemResourceID)) {
            return Result.renderError("当前交易数据信息有误！");
        } else {
            TestSystem testSystem = this.findByResourceID(Long.valueOf(testSystemResourceID));
            if (testSystem == null) {
                return Result.renderError("当前树结构被测系统不存在或已被删除！");
            }
            systemMap.put("id", testSystem.getId());
            systemMap.put("label", testSystem.getName());
            systemMap.put("rid", testSystem.getResourceID());
            systemMap.put("nodeType", "testSystem");
            systemMap.put("intent", testSystem.getName());


//			systemMap.put("name", testSystem.getName());
//			systemMap.put("resourceID", testSystem.getResourceID());
//			systemMap.put("type", "system");
            systemMap.put("children", new ArrayList<>());
            systemMap.put("parentID", "0");
            for (Map.Entry<String, List<Trade>> entry : mapTestSystemTrade.entrySet()) {
                List<Trade> trades = entry.getValue();
                List<Map<String, Object>> childrenTradeList = new ArrayList<>();
                for (Trade trade : trades) {
                    Map<String, Object> tradeMap = new HashMap<>();
                    tradeMap.put("id", trade.getId());
                    tradeMap.put("label", trade.getName());
                    tradeMap.put("rid", trade.getResourceID());
                    tradeMap.put("nodeType", "trade");
                    tradeMap.put("intent", trade.getName());

//	    		        tradeMap.put("id", trade.getId());
//	    		        tradeMap.put("name", trade.getName());
//	    		        tradeMap.put("resourceID", trade.getResourceID());
//	    		        tradeMap.put("type", "trade");
                    tradeMap.put("parentID", entry.getKey());
                    if (!org.springframework.util.StringUtils.isEmpty(taskResourceID)) {
                        tradeMap.put("children", new ArrayList<>());
                        //需求变更，不再在交易下下挂案例节点，而是点击交易返回交易下案例信息
//	    		        	tradeMap.put("children", this.buildTestCaseTreeList(trade.getResourceID().toString(), taskResourceID,mapForCaseRunResult,userNumber,hasQuoteSearchCaseRids,testPlanResourceID));
                    } else {
                        tradeMap.put("children", new ArrayList<>());
                    }
                    childrenTradeList.add(tradeMap);
                }
                if (!org.springframework.util.StringUtils.isEmpty(childrenTradeList) && childrenTradeList.size() > 0) {
                    systemMap.put("children", childrenTradeList);
                }
            }
        }

        //查询当前被测系统下所有的模块（包含子模块）
        List<SystemModule> allModules = systemModuleService.findbyTestSystemResourceID(testSystemResourceID);
        Map<String, SystemModule> mapAllModule = new HashMap<>();
        allModules.stream().forEach(x -> {
            mapAllModule.put(x.getResourceID().toString(), x);
        });
        //用来存储所有已存在树模块节点
        Set<String> moduleSet = new HashSet<>();
        Map<String, Map<String, Object>> moduleMapMap = new HashMap<>();
        moduleMapMap.put(testSystemResourceID, systemMap);
        //建立交易和直属模块的关系
        for (Map.Entry<String, List<Trade>> entry : mapModuleTrade.entrySet()) {
            String moduleRid = entry.getKey();
            SystemModule systemModule = mapAllModule.get(moduleRid);
            Map<String, Object> moduleMap = new HashMap<>();
            moduleMap.put("id", systemModule.getId());
            moduleMap.put("label", systemModule.getName());
            moduleMap.put("rid", systemModule.getResourceID());
            moduleMap.put("nodeType", "systemModule");
            moduleMap.put("intent", systemModule.getName());


//			moduleMap.put("id", systemModule.getId());
//			moduleMap.put("name", systemModule.getName());
//			moduleMap.put("resourceID", systemModule.getResourceID());
//			moduleMap.put("type", "module");
            moduleMap.put("parentID", systemModule.getParentResourceID().toString());
            moduleMap.put("children", new ArrayList<>());
            List<Trade> trades = entry.getValue();
            List<Map<String, Object>> childrenTradeList = new ArrayList<>();
            for (Trade trade : trades) {
                Map<String, Object> tradeMap = new HashMap<>();
                tradeMap.put("id", trade.getId());
                tradeMap.put("label", trade.getName());
                tradeMap.put("rid", trade.getResourceID());
                tradeMap.put("nodeType", "trade");
                tradeMap.put("intent", trade.getName());

//		        tradeMap.put("id", trade.getId());
//		        tradeMap.put("name", trade.getName());
//		        tradeMap.put("resourceID", trade.getResourceID());
//		        tradeMap.put("type", "trade");
                tradeMap.put("parentID", moduleRid);
                if (!org.springframework.util.StringUtils.isEmpty(taskResourceID)) {
                    tradeMap.put("children", new ArrayList<>());
                    //需求变更，不再在交易下下挂案例节点，而是点击交易返回交易下案例信息
//		        	tradeMap.put("children", this.buildTestCaseTreeList(trade.getResourceID().toString(), taskResourceID, mapForCaseRunResult,userNumber,hasQuoteSearchCaseRids,testPlanResourceID));
                } else {
                    tradeMap.put("children", new ArrayList<>());
                }
//		        tradeMap.put("children", new ArrayList<>());
                childrenTradeList.add(tradeMap);
            }
            if (!org.springframework.util.StringUtils.isEmpty(childrenTradeList) && childrenTradeList.size() > 0) {
                moduleMap.put("children", childrenTradeList);
            }
            //如果这个模块的父级为被测系统
            if (systemModule.getParentResourceID().equals(systemModule.getTestSystemResourceID())) {
                List<Map<String, Object>> systemChilds = (List<Map<String, Object>>) systemMap.get("children");
                if (!systemChilds.contains(moduleMap)) {
                    systemChilds.add(moduleMap);
                    moduleSet.add(moduleRid);
                    moduleMapMap.put(moduleRid, moduleMap);
                }
                continue;
            }
            moduleSet.add(moduleRid);
            moduleMapMap.put(moduleRid, moduleMap);
            systemMap = this.buildTreeForTestCase(moduleSet, mapAllModule, systemModule, systemMap, moduleMapMap);

        }
        return Result.renderSuccess(systemMap);
    }

    /**
     * 构造树结构（带案例的用）
     *
     * @param @param allModules 系统下所有模块
     * @param @param module 当前模块
     * @param @param systemResourceID    参数 被测系统rid
     * @return void    返回类型
     * @throws
     * @Title: buildTree
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> buildTreeForTestCase(Set<String> moduleSet, Map<String, SystemModule> mapAllModule, SystemModule module,
                                                     Map<String, Object> systemMap, Map<String, Map<String, Object>> moduleMapMap) {
        //父节点和被测系统节点一样的话，该模块直接挂在被测系统下
        if (module.getParentResourceID().equals(module.getTestSystemResourceID())) {
            List<Map<String, Object>> systemChilds = (List<Map<String, Object>>) systemMap.get("children");
            if (!systemChilds.contains(moduleMapMap.get(module.getResourceID().toString()))) {
                systemChilds.add(moduleMapMap.get(module.getResourceID().toString()));
            }
            return systemMap;
        }
        //模块父模块
        SystemModule systemModuleParent = mapAllModule.get(module.getParentResourceID().toString());
        if (moduleMapMap.get(systemModuleParent.getResourceID().toString()) != null) {
            Map<String, Object> parentMap = moduleMapMap.get(systemModuleParent.getResourceID().toString());
            List<Map<String, Object>> parentChilds = (List<Map<String, Object>>) parentMap.get("children");
            if (!parentChilds.contains(moduleMapMap.get(module.getResourceID().toString()))) {
                parentChilds.add(moduleMapMap.get(module.getResourceID().toString()));
            }
        } else {
            Map<String, Object> moduleMap = new HashMap<>();
            moduleMap.put("id", systemModuleParent.getId());
            moduleMap.put("label", systemModuleParent.getName());
            moduleMap.put("rid", systemModuleParent.getResourceID());
            moduleMap.put("nodeType", "systemModule");
            moduleMap.put("intent", systemModuleParent.getName());

//			moduleMap.put("id", systemModuleParent.getId());
//			moduleMap.put("name", systemModuleParent.getName());
//			moduleMap.put("resourceID", systemModuleParent.getResourceID());
//			moduleMap.put("type", "module");
            moduleMap.put("parentID", systemModuleParent.getParentResourceID().toString());
            List<Map<String, Object>> chlds = new ArrayList<>();
            chlds.add(moduleMapMap.get(module.getResourceID().toString()));
            moduleMap.put("children", chlds);
            if (!moduleSet.contains(systemModuleParent.getResourceID().toString())) {//如果这个模块没有被处理过
                moduleSet.add(systemModuleParent.getResourceID().toString());
            }
            moduleMapMap.put(systemModuleParent.getResourceID().toString(), moduleMap);

        }
        return this.buildTree(moduleSet, mapAllModule, systemModuleParent, systemMap, moduleMapMap);

    }
    /**
     * 构建交易下案例树节点
     * @Title: buildTestCaseTreeList
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param @param tradeRid
     * @param @param taskRid
     * @param @return    参数
     * @return List<Map < String, Object>>    返回类型
     * @throws
     * <AUTHOR>
     */
//	private List<Map<String,Object>> findExecuteCaseListInfo(String tradeRid,String taskRid,Map<String,String> mapForCaseRunResult,String userNumber,List<String> hasQuoteSearchCaseRids,String testPlanResourceID){
//		List<TestCase>  testCases = this.findTestCasesByTaskAndTrades(tradeRid, taskRid,userNumber,hasQuoteSearchCaseRids,testPlanResourceID);
//		List<Map<String,Object>> listResult = new ArrayList<>();
//		for (TestCase testCase : testCases) {
//			Map<String, Object> caseMap = new HashMap<>();
//			 caseMap.put("id", testCase.getId());
//			 caseMap.put("label", testCase.getCaseId());
//			 caseMap.put("intent", testCase.getCaseId() + "  " +testCase.getIntent());
//			 caseMap.put("rid", testCase.getResourceID());
//			 caseMap.put("nodeType", "testCase");
//
////			caseMap.put("id", testCase.getId());
////			caseMap.put("caseID", testCase.getCaseId());
////			caseMap.put("intent", testCase.getIntent());
////			caseMap.put("resourceID", testCase.getResourceID());
////			caseMap.put("type", "testcase");
//			caseMap.put("parentID",tradeRid);
//
//			// 添加执行结果
//			caseMap.put("caseResult", mapForCaseRunResult.get(testCase.getResourceID().toString()));
//			listResult.add(caseMap);
//		}
//		return listResult;
//	}

    /**
     * 根据交易和任务查询手工执行引用案例列表信息
     *
     * @param @return 参数
     * @return Result<?>    返回类型
     * @throws
     * @Title: findExecuteCaseListInfo
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @Override
    public Result<?> findExecuteCaseListInfo(Map<String, String> params, PageRequest pageRequest) {
        String taskResourceID = params.get("taskResourceID");

        Long timeStart = Long.valueOf(params.get("timeStart") == null ? "0" : params.get("timeStart"));
        Long timeEnd = Long.valueOf(params.get("timeEnd") == null ? "0" : params.get("timeEnd"));
        String resourceID = String.valueOf(params.get("resourceID") == null ? "0" : params.get("resourceID")).trim();
        String nodeType = String.valueOf(params.get("type") == null ? "root" : params.get("type")).trim();
        String caseName = String.valueOf(params.get("caseName") == null ? "" : params.get("caseName")).trim();
        String caseId = String.valueOf(params.get("caseId") == null ? "" : params.get("caseId")).trim();

        if (StringUtils.isEmpty(taskResourceID)) {
            return Result.renderError("taskResourceID参数为空！");
        }
        String tagResourceIDs = "";
        if(!StringUtils.isEmpty(params.get("tagResourceIDs"))) {
            tagResourceIDs = String.valueOf(params.get("tagResourceIDs"));
        }
        Map<String, String> taskMap = this.findByTestTaskResourceID(taskResourceID);
        if (taskMap == null || taskMap.isEmpty()) {
            return Result.renderError("当前任务不存在或已被删除！");
        }
        //案例的状态
        String testCaseState = "";
        if (!org.springframework.util.StringUtils.isEmpty(params.get("testCaseState"))) {
            testCaseState = String.valueOf(params.get("testCaseState"));
        }

        List<String> caseTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(params.get("caseType"))) {
            caseTypeList = Arrays.asList(String.valueOf(params.get("caseType")).split(","));
        }

        List<String> tradeResIDs;
        if (nodeType.equals("trade")) {
            tradeResIDs = new ArrayList<>();
            tradeResIDs.add(resourceID);
        } else {
            List<Map<String, Object>> trades = tradeService.findAllTradeByParentResIDAndTaskResID(taskResourceID, nodeType, resourceID);
            tradeResIDs = trades.stream().map(x -> x.get("resourceID").toString())
                    .collect(Collectors.toList());
        }

        // 根据taskResourceID在at_defect表中查询testcaseresourceId,即为关联了缺陷的案例
        Result bugsByTestCaseResourceID = feignDataDesignToBugService.findBugsByTestTaskResourceID(taskResourceID, null);
        List<String> bugsByTestCaseResourceIDList = (List<String>) bugsByTestCaseResourceID.getObj();

        PageImpl<Map<String, Object>> res = this.findCaseQuoteListByTradeInformation(taskResourceID, tradeResIDs,
                testCaseState, caseTypeList, timeStart, timeEnd,tagResourceIDs, pageRequest,caseName, caseId);

        List<Map<String, Object>> recordCountList = new ArrayList<>();
        List<Map<String, Object>> fileCountList = new ArrayList<>();


        //redis缓存中查询案例类型
        Map<Object, Object> dic = redisUtils.getHashEntries("案例类型");
        List<Map<String, Object>> caseList = res.getContent();
        //标签
        Map<String,List<Map<String, Object>>> tagMap = new HashMap<>();
        if(!caseList.isEmpty()){
            //recordCount
            //查询执行记录数
            List<Long> quoteResourceIDList = caseList.stream().map(item -> Long.parseLong(item.get("quoteCaseResourceID").toString())).collect(Collectors.toList());
            recordCountList = this.countQuoteRecord(quoteResourceIDList, timeStart > 0 ? new Date(timeStart) : null, timeStart > 0 ? new Date(timeEnd) : null);

            //newRecordFilesCount
            //查询最后一次执行附件数
            List<Long> recordResourceIDList = caseList.stream().filter(item -> item.get("newResultRecordResourceID") != null).map(item -> Long.parseLong(item.get("newResultRecordResourceID").toString())).collect(Collectors.toList());
            if (!recordResourceIDList.isEmpty()) {
                fileCountList = this.countCaseResultFile(recordResourceIDList);
            }

            //案例的rid集合
            List<String> relationResourceIDs = caseList.stream().map(e->String.valueOf(e.get("resourceID"))).collect(Collectors.toList());
            //查询案例相关的标签
            if(!relationResourceIDs.isEmpty()){
                List<Map<String, Object>> relationData = testCaseService.findRelationResourceTags(relationResourceIDs, 2);
                if(!relationData.isEmpty()){
                    tagMap = relationData.stream().collect(Collectors.groupingBy(e ->String.valueOf(e.get("relationResourceID"))));
                }
            }
        }
        for (Map<String, Object> map : caseList) {
            map.put("recordCount", 0);
            map.put("newRecordFilesCount", 0);
            map.put("newRecordFilesCount", Integer.parseInt(map.get("newFilesCount").toString()) > 0  ? Integer.parseInt(map.get("newFilesCount").toString()) : Integer.parseInt(map.get("oldFilesCount").toString()));
            if (!recordCountList.isEmpty()) {
                String quoteCaseResourceID = String.valueOf(map.get("quoteCaseResourceID") == null ? "" : map.get("quoteCaseResourceID"));
                for(Map<String, Object> rcMap : recordCountList)
                {
                    String testCaseQuoteResourceID = String.valueOf(rcMap.get("testCaseQuoteResourceID"));
                    if(testCaseQuoteResourceID.equals(quoteCaseResourceID))
                    {
                        map.put("recordCount", rcMap.get("recordCount"));
                        break;
                    }
                }
                /*
                Optional<Map<String, Object>> optional = recordCountList.stream().filter(item -> item.get("testCaseQuoteResourceID").toString().equals(map.get("quoteCaseResourceID").toString())).findFirst();
                if (optional.isPresent()) {
                    map.put("recordCount", optional.get().get("recordCount"));
                }*/
            }

            if (!fileCountList.isEmpty()) {
                String newResultRecordResourceID = String.valueOf(map.get("newResultRecordResourceID") == null ? "" : map.get("newResultRecordResourceID"));
                for(Map<String, Object> fcMap : fileCountList)
                {
                    String caseResultRecordResourceID = String.valueOf(fcMap.get("caseResultRecordResourceID"));
                    if(newResultRecordResourceID.equals(caseResultRecordResourceID))
                    {
                        map.put("newRecordFilesCount", fcMap.get("newRecordFilesCount"));
                        break;
                    }
                }
                /*
                Optional<Map<String, Object>> optional = fileCountList.stream().filter(item -> item.get("caseResultRecordResourceID").toString().equals(map.get("newResultRecordResourceID") != null ? map.get("newResultRecordResourceID").toString() : "")).findFirst();
                if (optional.isPresent()) {
                    map.put("newRecordFilesCount", optional.get().get("newRecordFilesCount"));
                }*/
            }

//            int recordCount = Objects.equals(map.get("recordCount"), null)
//                    || Objects.equals(String.valueOf(map.get("recordCount")), "null") ? 0 : Integer.parseInt(map.get("recordCount").toString());
//            map.put("recordCount", recordCount);
//            int fileCount = Objects.equals(map.get("newRecordFilesCount"), null)
//                    || Objects.equals(String.valueOf(map.get("newRecordFilesCount")), "null") ? 0 : Integer.parseInt(map.get("newRecordFilesCount").toString());
//            map.put("newRecordFilesCount", fileCount);
            map.put("caseType", dic.isEmpty() ? map.get("caseType") : dic.get(map.get("caseType")));
            if (bugsByTestCaseResourceIDList != null && bugsByTestCaseResourceIDList.contains(map.get("resourceID").toString())) {
                map.put("hasBug", true);
            } else {
                map.put("hasBug", false);
            }
            //添加标签list
            map.put("tags", tagMap.get(String.valueOf(map.get("resourceID"))) == null?new ArrayList<>():tagMap.get(String.valueOf(map.get("resourceID"))));
        }
        return Result.renderSuccess(res);

    }

    /**
     * 查找当前交易下有引用关系的引用案例
     *
     * @param @param  taskResourceID
     * @param @param  userNumber
     * @param @param  object
     * @param @param  object2
     * @param @param  testCaseState
     * @param @param  testPlanResourceID
     * @param @param  tradeResourceID
     * @param @return 参数
     * @param caseTypeList
     * @return List<Map < String, String>>    返回类型
     * @throws
     * @Title: findCaseQuoteListByTradeAndTaskPage
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     * @param caseName
     */
    private PageImpl<Map<String, Object>> findCaseQuoteListByTradeInformation(String taskResourceID,
                                                                              List<String> tradeResIDs, String testCaseState,
                                                                              List<String> caseTypeList, Long timeStart, Long timeEnd,String tagResourceIDs, PageRequest pageRequest, String caseName, String caseId) {
        List<String> testCaseStates = new ArrayList<>();
        if(!"".equals(testCaseState)){
            testCaseStates = Arrays.asList(testCaseState.split(",")) ;
        }
        return testSystemDao.findCaseQuoteListByTradeAndTaskPage(taskResourceID, tradeResIDs,
                testCaseStates, caseTypeList, timeStart, timeEnd,tagResourceIDs, pageRequest,caseName, caseId);
    }

    /**
     * 查看当前案例下是否有缺陷
     *
     * @param @param  caseRid
     * @param @return 参数
     * @return int    返回类型
     * @throws
     * @Title: findBugsByCaseResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    private int findBugsByCaseResourceID(String caseRid) {

        return testSystemDao.findBugsByCaseResourceID(caseRid);
    }

    /**
     * @param name
     * @return
     * @Title: findByName
     * @Description: 根据名称查询系统
     * <AUTHOR>
     * @date 2020年5月27日 上午10:42:57
     */
    @Override
    public TestSystem findByName(String name) {
        return testSystemDao.findByName(name);
    }

    /**
     * @param demandResourceID : 需求RID(null时查所有)
     * @param keyWord          : 关键字
     * @return : com.jettech.dto.Result<?>
     * @Method : findSinglePointLeftTreeByProjectResourceIDAndKeyWord
     * @Description : 通过需求rid和关键字查询系统模块交易树
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-06-24 周三 11:54:42
     */
    @Override
    public Result<?> findSinglePointLeftTreeByDemandResourceIDAndKeyWord(String demandResourceID, String keyWord) {
        if (demandResourceID == null || "".equals(demandResourceID) || keyWord == null || "".equals(keyWord)) {
            return Result.renderError("查询参数为空!");
        }
        //projectResourceID为0，是从案例资产库请求，没有需求，值为null
        if ("0".equals(demandResourceID)) {
            demandResourceID = null;
        }
        ArrayList<TreeNode> treeList = new ArrayList<>();
        List<Map<String, Object>> testSystemList = testSystemDao.findByTestProjectResourceID(demandResourceID);
        List<TreeNode> moduleList = systemModuleService.findByDemandResourceID(demandResourceID);
        if (testSystemList == null || moduleList == null) {
            return Result.renderError("暂无数据!");
        }
        HashMap<String, List<TreeNode>> moduleMaps = new HashMap<>();
        for (TreeNode moduleNode : moduleList) {
            if (moduleMaps.get(moduleNode.getParentResourceID()) == null) {
                moduleMaps.put(moduleNode.getParentResourceID(), new ArrayList<>());
            }
            moduleMaps.get(moduleNode.getParentResourceID()).add(moduleNode);
        }
        ArrayList<String> enableSystemResourceID = new ArrayList<>();
        ArrayList<String> enableModuleResourceID = new ArrayList<>();
        for (Map<String, Object> map : testSystemList) {
            TreeNode systemNode = new TreeNode();
            systemNode.setName(map.get("name").toString());
            systemNode.setNumber(map.get("number") == null || "".equals(map.get("number")) ? null : map.get("number").toString());
            systemNode.setResourceID(map.get("resourceID").toString());
            systemNode.setType(map.get("type").toString());
            List<TreeNode> children = moduleMaps.get(map.get("resourceID").toString());
            if (children != null) {
                for (TreeNode treeNode : children) {
                    treeNode.setParentTreeNode(systemNode);
                    buildSystemAndModuleTreeNode(treeNode, moduleMaps, keyWord, enableModuleResourceID);
                }
                systemNode.setChildren(children);
            }
            if (systemNode.getName().contains(keyWord)) {
                systemNode.setEnable(true);
                enableSystemResourceID.add(systemNode.getResourceID());
            }
            treeList.add(systemNode);
        }
        List<TreeNode> tradeList = tradeService.findBySystemResourceIDsOrModuleResourceIDsOrKeyWord(enableSystemResourceID, enableModuleResourceID, keyWord);
        HashMap<String, List<TreeNode>> parentRidKeyTradeNodes = new HashMap<>();
        for (TreeNode tradeNode : tradeList) {
            if (!parentRidKeyTradeNodes.containsKey(tradeNode.getParentResourceID())) {
                parentRidKeyTradeNodes.put(tradeNode.getParentResourceID(), new ArrayList<>());
            }
            parentRidKeyTradeNodes.get(tradeNode.getParentResourceID()).add(tradeNode);
        }
        for (TreeNode treeNode : treeList) {
            buildTree(treeNode, parentRidKeyTradeNodes);
        }
        removeNotEnableNode(treeList);
        return Result.renderSuccess(treeList);
    }

    private void removeNotEnableNode(List<TreeNode> tradeList) {
        if (tradeList == null) {
            return;
        }
        Iterator<TreeNode> iterator = tradeList.iterator();
        int m=CheckUtil.checkLoop(Iterators.size(iterator));
        if(m==CheckUtil.MAX_LOOPS){
            return;
        }
        iterator = tradeList.iterator();
        while (iterator.hasNext()) {
            TreeNode treeNode = iterator.next();
            if (!treeNode.isEnable()) {
                iterator.remove();
            } else {
                removeNotEnableNode(treeNode.getChildren());
            }
        }
    }

    private void buildTree(TreeNode treeNode, HashMap<String, List<TreeNode>> parentRidKeyTradeNodes) {
        List<TreeNode> children = parentRidKeyTradeNodes.get(treeNode.getResourceID());
        if (children != null) {
            setEnableLinkedNodes(treeNode);
            if (treeNode.getChildren() == null) {
                treeNode.setChildren(children);
            } else {
                treeNode.getChildren().addAll(children);
            }
        }
        if (treeNode.getChildren() != null) {
            for (TreeNode childNode : treeNode.getChildren()) {
                buildTree(childNode, parentRidKeyTradeNodes);
            }
        }

    }

    private void buildSystemAndModuleTreeNode(TreeNode treeNode, HashMap<String, List<TreeNode>> moduleMaps, String keyWord, List<String> enableModuleResourceID) {
        List<TreeNode> children = moduleMaps.get(treeNode.getResourceID());
        if (treeNode.getName().contains(keyWord)) {
            setEnableLinkedNodes(treeNode);
            enableModuleResourceID.add(treeNode.getResourceID());
        }
        if (children != null) {
            treeNode.setChildren(children);
            for (TreeNode childNode : children) {
                childNode.setParentTreeNode(treeNode);
                buildSystemAndModuleTreeNode(childNode, moduleMaps, keyWord, enableModuleResourceID);
            }
        }

    }

    private void setEnableLinkedNodes(TreeNode treeNode) {
        treeNode.setEnable(true);
        if (treeNode.getParentTreeNode() != null && !treeNode.getParentTreeNode().isEnable()) {
            setEnableLinkedNodes(treeNode.getParentTreeNode());
        }
    }


    /**
     * @param file
     * @param userNumber
     * @return
     * @Title: importTrade
     * @Description:交易导入
     * <AUTHOR>
     * @date 2020年5月26日 下午2:38:24
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result<?> importTrade(MultipartFile file, String userNumber) {

        //数据字典查询交易状态
        List<Map<String, String>> tradeStatus = tradeService.findTradeDicsByDicName("STATUS");
        if (tradeStatus.isEmpty()) {
            return Result.renderError("请在数据字典配置交易状态！");
        }
        Map<String, String> mapTradeStatus = new HashMap<>();
        for (Map<String, String> map : tradeStatus) {
            mapTradeStatus.put(map.get("textName"), map.get("value"));
        }

        //数据字典查询交易类型
        List<Map<String, String>> tradeType = tradeService.findTradeDicsByDicName("TRADETYPE");
        if (tradeType.isEmpty()) {
            return Result.renderError("请在数据字典配置交易类型！");
        }
        Map<String, String> mapTradeType = new HashMap<>();
        for (Map<String, String> map : tradeType) {
            mapTradeType.put(map.get("textName"), map.get("value"));
        }
        //数据字典查询执行方式
        List<Map<String, String>> executeModes = tradeService.findTradeDicsByDicName("executeMode");
        if (executeModes.isEmpty()) {
            return Result.renderError("请在数据字典配置执行方式！");
        }
        Map<String, String> mapExecuteModes = new HashMap<>();
        for (Map<String, String> map : executeModes) {
            mapExecuteModes.put(map.get("textName"), map.get("value"));
        }

        //读取excel内容
        ReadExcel readExcel = new ReadExcel();
        List<Map<String, String>> dataList = readExcel.getExcelData(file, mapTradeStatus, mapTradeType, mapExecuteModes);
        System.out.println("读取excel内容如下：" + dataList);

        if (20000 != readExcel.getCode()) {
            return Result.renderError(readExcel.getErrorInfo()+"可能存在空行数据或数据不规范，请检查导入表格");
        }if( dataList.size()== 0 ){
            return Result.renderError("导入列表为空，请重新编辑导入表格");
        }

        List<Trade> tradeAdd = new ArrayList<>();
        List<Trade> tradeUpdate = new ArrayList<>();
        Map<Long, Object> modeuleAndTrade = new HashMap<>();
        Map<Long, Object> systemAndTrade = new HashMap<>();
        //系统和交易编号验证
        Map<Long, Object> systemAndTradeNumberVerify = new HashMap<>();
        //系统和交易名称验证
        Map<Long, Object> systemAndTradeNameVerify = new HashMap<>();
        Map<String, Long> testSystemMap = new HashMap<>();
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, String> map = dataList.get(i);
            String testSystemName = map.get("testSystem");
            String tradeName = map.get("name");
            String number = map.get("number");
            String type = map.get("type");
            String status = map.get("status");
            String comment = map.get("comment");
            String executeMode = map.get("executeMode") == null ? "" : map.get("executeMode");
            //获取模块
            List<String> moduleNames = new ArrayList<>();
            int moduleNum = 1;
            while (map.containsKey("module" + moduleNum)) {
                moduleNames.add(map.get("module" + moduleNum++));
            }
            //所属系统
            Long testSystemRid = null;
            if (testSystemMap.isEmpty() || !testSystemMap.containsKey(testSystemName)) {
                TestSystem testSystem = findByName(testSystemName);
                if (testSystem == null) {
                    return Result.renderError("第" + (i + 2) + "行被测系统不存在，请先维护被测系统!");
                }
                testSystemRid = testSystem.getResourceID();
                testSystemMap.put(testSystemName, testSystem.getResourceID());
            } else {
                testSystemRid = testSystemMap.get(testSystemName);
            }

            if (!moduleNames.isEmpty()) {
                //交易挂在模块下
                //查询或构建模块结构，返回最底层模块
                String[] result = validataAndGetModule(testSystemRid, testSystemRid, moduleNames, moduleNames.size() - 1, modeuleAndTrade, userNumber);
                if ("false".equals(result[0])) {
                    return Result.renderError("第" + (i + 2) + "行" + result[1]);
                }
                Long modeleResourceID = Long.valueOf(result[1]);
                //验证excel中模块下的交易名称是否重复
//                if (!modeuleAndTrade.isEmpty() && modeuleAndTrade.containsKey(modeleResourceID)) {
//                    Map<String, String> tradeStr = (Map<String, String>) modeuleAndTrade.get(modeleResourceID);
//                    if (tradeStr.containsKey(tradeName)) {
//                        return Result.renderError("第" + (i + 2) + "行交易名称重复!");
//                    }
//                }

                //需求变更，现在是整个被测系统下的交易名称和编号都不重复（由于编号不是必输的，有编号就校验，没有就不校验）
                if(number != null){
                    if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                        Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                        if (tradeStr.containsKey(number)) {
                            return Result.renderError("第" + (i + 2) + "行交易编号重复!");
                        }
                    }
                    //根据被测系统和交易编号查询交易
                    Trade trade = tradeService.findByTestSystemResourceAndNumber(testSystemRid,number);
                    if(trade != null){
                        return Result.renderError("第" + (i + 2) + "行交易编号已存在!");
                    }
                }

                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
                    Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
                    if (tradeStr.containsKey(tradeName)) {
                        return Result.renderError("第" + (i + 2) + "行交易名称重复!");
                    }
                }

                Trade trade = tradeService.findByTestSystemResourceAndName(testSystemRid, tradeName);
                if (trade == null) {
                    //新建交易
                    trade = new Trade();
                    trade.setName(tradeName);
                    trade.setNumber(number);
                    trade.setType(mapTradeType.get(type));
                    trade.setStatus(mapTradeStatus.get(status));
                    trade.setComment(comment);
                    trade.setModuleResourceID(modeleResourceID);
                    trade.setTestSystemResourceID(testSystemRid);
                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
                    tradeAdd.add(trade);

                } else {
                    //修改交易
//                    trade.setNumber(number);
//                    trade.setType(mapTradeType.get(type));
//                    trade.setStatus(mapTradeStatus.get(status));
//                    trade.setComment(comment);
//                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
//                    tradeUpdate.add(trade);
                    return Result.renderError("第" + (i + 2) + "行交易名称已存在!");

                }

                Map<String, String> valueNumber = new HashMap<>();
                if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                    valueNumber = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                }
                valueNumber.put(number, number);
                systemAndTradeNumberVerify.put(testSystemRid, valueNumber);

                Map<String, String> valueName = new HashMap<>();
                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
                    valueName = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
                }
                valueName.put(tradeName, tradeName);
                systemAndTradeNameVerify.put(testSystemRid, valueName);


                Map<String, String> value = new HashMap<>();
                if (!modeuleAndTrade.isEmpty() && modeuleAndTrade.containsKey(modeleResourceID)) {
                    value = (Map<String, String>) modeuleAndTrade.get(modeleResourceID);
                }
                value.put(tradeName, tradeName);
                modeuleAndTrade.put(modeleResourceID, value);

            } else {
                //交易直接挂在系统下
                ////验证excel中系统下的交易名称是否重复
//                if (!systemAndTrade.isEmpty() && systemAndTrade.containsKey(testSystemRid)) {
//                    Map<String, String> tradeStr = (Map<String, String>) systemAndTrade.get(testSystemRid);
//                    if (tradeStr.containsKey(tradeName)) {
//                        return Result.renderError("第" + (i + 2) + "行交易名称重复!");
//                    }
//                }
                //需求变更，现在是整个被测系统下的交易名称和编号都不重复（由于编号不是必输的，有编号就校验，没有就不校验）
                if(number != null){
                    if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                        Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                        if (tradeStr.containsKey(number)) {
                            return Result.renderError("第" + (i + 2) + "行交易编号重复!");
                        }
                    }
                    //根据被测系统和交易编号查询交易
                    Trade trade = tradeService.findByTestSystemResourceAndNumber(testSystemRid,number);
                    if(trade != null){
                        return Result.renderError("第" + (i + 2) + "行交易编号已存在!");
                    }
                }

                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
                    Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
                    if (tradeStr.containsKey(tradeName)) {
                        return Result.renderError("第" + (i + 2) + "行交易名称重复!");
                    }
                }

                Trade trade = tradeService.findByTestSystemResourceAndName(testSystemRid, tradeName);
                if (trade == null) {
                    //新建交易
                    trade = new Trade();
                    trade.setName(tradeName);
                    trade.setNumber(number);
                    trade.setType(mapTradeType.get(type));
                    trade.setStatus(mapTradeStatus.get(status));
                    trade.setComment(comment);
                    trade.setTestSystemResourceID(testSystemRid);
                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
                    tradeAdd.add(trade);


                } else {
                    //修改交易
//                    trade.setNumber(number);
//                    trade.setType(mapTradeType.get(type));
//                    trade.setStatus(mapTradeStatus.get(status));
//                    trade.setComment(comment);
//                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
//                    tradeUpdate.add(trade);
                    return Result.renderError("第" + (i + 2) + "行交易名称已存在!");
                }
                Map<String, String> valueNumber = new HashMap<>();
                if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                    valueNumber = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                }
                valueNumber.put(number, number);
                systemAndTradeNumberVerify.put(testSystemRid, valueNumber);

                Map<String, String> valueName = new HashMap<>();
                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
                    valueName = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
                }
                valueName.put(tradeName, tradeName);
                systemAndTradeNameVerify.put(testSystemRid, valueName);


                Map<String, String> value = new HashMap<>();
                if (!systemAndTrade.isEmpty() && systemAndTrade.containsKey(testSystemRid)) {
                    value = (Map<String, String>) systemAndTrade.get(testSystemRid);
                }
                value.put(tradeName, tradeName);
                systemAndTrade.put(testSystemRid, value);
            }
        }

        if (!tradeAdd.isEmpty()) {
            tradeService.save(tradeAdd, userNumber);
        }
        if (!tradeUpdate.isEmpty()) {
            tradeService.update(tradeUpdate, userNumber);
        }

        return Result.renderSuccess();
    }


    /**
     * @param “testSystem”
     * @param parentResourceID
     * @param moduleNames
     * @param num
     * @param modeuleAndTrade
     * @param userNumber
     * @return
     * @Title: validataAndGetModule
     * @Description: 验证和创建模块
     * <AUTHOR>
     * @date 2020年5月27日 下午4:10:44
     */
    private String[] validataAndGetModule(Long testSystemRid, Long parentResourceID, List<String> moduleNames, int num, Map<Long, Object> modeuleAndTrade, String userNumber) {

        String[] result = new String[2];
        SystemModule systemModule = systemModuleService.findByNameAndTestSystemResourceIDAndParentResourceID(moduleNames.get(num), testSystemRid, parentResourceID);
        if (systemModule == null) {
            //新增模块
            systemModule = new SystemModule();
            systemModule.setName(moduleNames.get(num--));
            systemModule.setTestSystemResourceID(testSystemRid);
            systemModule.setParentResourceID(parentResourceID);
            systemModule = systemModuleService.save(systemModule, userNumber);

            if (num > -1) {
                result = validataAndGetModule(testSystemRid, systemModule.getResourceID(), moduleNames, num, modeuleAndTrade, userNumber);
            } else {
                result[0] = "true";
                result[1] = systemModule.getResourceID().toString();
            }

            return result;
        }

        if (num == 0) {
            //最底层模块
            List<SystemModule> childModules = systemModuleService.findByParentResourceID(systemModule.getResourceID().toString());
            if (!childModules.isEmpty()) {
                result[0] = "false";
                result[1] = systemModule.getName() + "模块下已有模块，不允许新建交易！";
                return result;
            }
            result[0] = "true";
            result[1] = systemModule.getResourceID().toString();
            return result;
        } else {
            //上级模块
            List<Trade> trades = tradeService.findByModuleResourceID(systemModule.getResourceID());
            if (!trades.isEmpty() || modeuleAndTrade.containsKey(systemModule.getResourceID())) {
                result[0] = "false";
                result[1] = systemModule.getName() + "模块下已经存在交易,不允许再维护下一级模块！";
                return result;
            }
            result = validataAndGetModule(testSystemRid, systemModule.getResourceID(), moduleNames, --num, modeuleAndTrade, userNumber);
            return result;
        }
    }

    private class LeftTreeCallable implements Callable<Map<String, Object>> {
        private Map<String, Object> projectMap;
        private List<Map<String, Object>> moduleMap;
        private List<Map<String, Object>> tradeMap;

        public LeftTreeCallable(Map<String, Object> projectMap, List<Map<String, Object>> moduleMap, List<Map<String, Object>> tradeMap) {
            this.projectMap = projectMap;
            this.moduleMap = moduleMap;
            this.tradeMap = tradeMap;
        }

        @Override
        public Map<String, Object> call() {
            List<Map<String, Object>> tradesTestSystem = null;
            try {
                tradesTestSystem = tradeMap.stream().filter(o -> o.get("moduleResourceID") == null && o.get("moduleResourceID") == null).collect(Collectors.toList());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (tradesTestSystem == null || tradesTestSystem.size() == 0) {
                projectMap.put("children", new ArrayList<Map<String, Object>>());
            } else {
                projectMap.put("children", tradesTestSystem);
                tradeMap.removeAll(tradesTestSystem);
            }
            projectMap = findByRecursion(projectMap, moduleMap, tradeMap);
            return projectMap;
        }

        private Map<String, Object> findByRecursion(Map<String, Object> mainMap, List<Map<String, Object>> moduleMap, List<Map<String, Object>> tradeMap) {
            if (mainMap.get("parentResourceID") == null && mainMap.get("demandResourceID") == null) {
                return mainMap;
            }

            List<Map<String, Object>> modulesInMain = moduleMap.stream().filter(o -> mainMap.get("resourceID").equals(o.get("parentResourceID"))).collect(Collectors.toList());
            if (modulesInMain == null || modulesInMain.size() == 0) {
                List<Map<String, Object>> tradesInMain = tradeMap.stream().filter(o -> mainMap.get("resourceID").equals(o.get("moduleResourceID"))).collect(Collectors.toList());
                if (tradesInMain == null || tradesInMain.size() == 0) {
                    return mainMap;
                } else {
                    //mainMap.merge("children",tradesInMain, (v1, v2) -> new ArrayList<Map<String,Object>>((Collection<? extends Map<String, Object>>) v1).addAll((Collection<? extends Map<String, Object>>) v2));
                    if (mainMap.get("children") == null) {
                        mainMap.put("children", tradesInMain);
                    } else {
                        List<Map<String, Object>> children = (List<Map<String, Object>>) mainMap.get("children");
                        children.addAll(tradesInMain);
                        mainMap.put("children", children);
                    }
                    return mainMap;
                }
            } else {
                //moduleMap.removeAll(modulesInMain);
                for (Map<String, Object> map : modulesInMain) {
                    findByRecursion(map, moduleMap, tradeMap);
                }
                //mainMap.merge("children",modulesInMain, (v1, v2) -> new ArrayList<Map<String,Object>>((Collection<? extends Map<String, Object>>) v1).addAll((Collection<? extends Map<String, Object>>) v2));
                if (mainMap.get("children") == null) {
                    mainMap.put("children", modulesInMain);
                } else {
                    List<Map<String, Object>> children = (List<Map<String, Object>>) mainMap.get("children");
                    children.addAll(modulesInMain);
                    mainMap.put("children", children);
                }
                return mainMap;
            }
        }
    }

	@Override
	public Result<?> importTrade2(MultipartFile file, String userNumber) {

        //数据字典查询交易状态
        List<Map<String, String>> tradeStatus = tradeService.findTradeDicsByDicName("STATUS");
        if (tradeStatus.isEmpty()) {
            return Result.renderError("请在数据字典配置交易状态！");
        }
        Map<String, String> mapTradeStatus = new HashMap<>();
        for (Map<String, String> map : tradeStatus) {
            mapTradeStatus.put(map.get("textName"), map.get("value"));
        }

        //数据字典查询交易类型
        List<Map<String, String>> tradeType = tradeService.findTradeDicsByDicName("TRADETYPE");
        if (tradeType.isEmpty()) {
            return Result.renderError("请在数据字典配置交易类型！");
        }
        Map<String, String> mapTradeType = new HashMap<>();
        for (Map<String, String> map : tradeType) {
            mapTradeType.put(map.get("textName"), map.get("value"));
        }
        //数据字典查询执行方式
        List<Map<String, String>> executeModes = tradeService.findTradeDicsByDicName("executeMode");
        if (executeModes.isEmpty()) {
            return Result.renderError("请在数据字典配置执行方式！");
        }
        Map<String, String> mapExecuteModes = new HashMap<>();
        for (Map<String, String> map : executeModes) {
            mapExecuteModes.put(map.get("textName"), map.get("value"));
        }

        //查询人员
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result r5 = feignDataDesignToBasicService.findTypeAllUser(token);
        List<Map<String, String>> peopleList = (List<Map<String, String>>) r5.getObj();
        Map<String, String> peopleMap = new HashMap<>();
        for (Map<String, String> userMap : peopleList) {
        	peopleMap.put(userMap.get("userName"), userMap.get("number"));
        }

        //读取excel内容
        Result<?> readResult = this.getExcelData2(file, mapTradeStatus, mapTradeType, mapExecuteModes,peopleMap);
        System.out.println("读取excel内容如下：" + readResult);

        if (20000 != readResult.getCode()) {
            return readResult;
        }
        List<Map<String, String>> dataList = (List<Map<String, String>>)readResult.getObj();
        if( dataList.isEmpty()){
            return Result.renderError("导入列表为空，请重新编辑导入表格");
        }


        List<Trade> tradeAdd = new ArrayList<>();
        List<TradeAndTestManager> newList=new ArrayList<TradeAndTestManager>();
        List<Trade> tradeUpdate = new ArrayList<>();
        Map<Long, Object> modeuleAndTrade = new HashMap<>();
        Map<Long, Object> systemAndTrade = new HashMap<>();
        //系统和交易编号验证
        Map<Long, Object> systemAndTradeNumberVerify = new HashMap<>();
        //系统和交易名称验证
        Map<Long, Object> systemAndTradeNameVerify = new HashMap<>();
        Map<String, Long> testSystemMap = new HashMap<>();
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, String> map = dataList.get(i);
            String testSystemName = map.get("testSystem");
            String tradeName = map.get("name");
            String number = map.get("number");
            String type = map.get("type");
            String status = map.get("status");
            String comment = map.get("comment");
            String executeMode = map.get("executeMode") == null ? "" : map.get("executeMode");
            String belongMananger = map.get("belongManager") == null ? "" : map.get("belongManager");//测试经理
            //获取模块
            List<String> moduleNames = new ArrayList<>();
            int moduleNum = 1;
            while (map.containsKey("module" + moduleNum)) {
                moduleNames.add(map.get("module" + moduleNum++));
            }
            //所属系统
            Long testSystemRid = null;
            if (testSystemMap.isEmpty() || !testSystemMap.containsKey(testSystemName)) {
                TestSystem testSystem = findByName(testSystemName);
                if (testSystem == null) {
                    return Result.renderError("第" + (i + 2) + "行「*所属系统」对应列的值不存在，请先维护被测系统!");
                }
                testSystemRid = testSystem.getResourceID();
                testSystemMap.put(testSystemName, testSystem.getResourceID());
            } else {
                testSystemRid = testSystemMap.get(testSystemName);
            }

            if (!moduleNames.isEmpty()) {
                //交易挂在模块下
                //查询或构建模块结构，返回最底层模块
                String[] result = validataAndGetModule(testSystemRid, testSystemRid, moduleNames, moduleNames.size() - 1, modeuleAndTrade, userNumber);
                if ("false".equals(result[0])) {
                    return Result.renderError("第" + (i + 2) + "行" + result[1]);
                }
                Long modeleResourceID = Long.valueOf(result[1]);
                //验证excel中模块下的交易名称是否重复
//                if (!modeuleAndTrade.isEmpty() && modeuleAndTrade.containsKey(modeleResourceID)) {
//                    Map<String, String> tradeStr = (Map<String, String>) modeuleAndTrade.get(modeleResourceID);
//                    if (tradeStr.containsKey(tradeName)) {
//                        return Result.renderError("第" + (i + 2) + "行交易名称重复!");
//                    }
//                }

                //需求变更，现在是整个被测系统下的交易名称和编号都不重复（由于编号不是必输的，有编号就校验，没有就不校验）
                if(number != null && !"".equals(number)){
                    if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                        Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                        if (tradeStr.containsKey(number)) {
                            return Result.renderError("第" + (i + 2) + "行「交易编码」列对应的值重复!");
                        }
                    }
//                    //根据被测系统和交易编号查询交易
//                    Trade trade = tradeService.findByTestSystemResourceAndNumber(testSystemRid,number);
//                    if(trade != null){
//                        return Result.renderError("第" + (i + 2) + "行「交易编码」列对应的值已存在!");
//                    }
                }

//                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
//                    Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
//                    if (tradeStr.containsKey(tradeName)) {
//                        return Result.renderError("第" + (i + 2) + "行「*交易名称」列对应的值重复!");
//                    }
//                }

                Trade trade = tradeService.findByTestSystemResourceAndName(testSystemRid, tradeName);
                if (trade == null) {
                    //新建交易
                    trade = new Trade();
                    trade.setName(tradeName);
                    trade.setNumber(number);
                    trade.setType(mapTradeType.get(type));
                    trade.setStatus(mapTradeStatus.get(status));
                    trade.setComment(comment);
                    trade.setModuleResourceID(modeleResourceID);
                    trade.setTestSystemResourceID(testSystemRid);
                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
                    trade.setResourceID(generateResourceID());
                    tradeAdd.add(trade);
                    if(belongMananger != null && !belongMananger.equals("")){
                        String[] split = belongMananger.split(",");
                        //  List<TradeAndTestManager> newList=new ArrayList<TradeAndTestManager>();
                        for(String s :split) {
                            TradeAndTestManager tt=new TradeAndTestManager();
                            tt.setBelongTestManager(s);
                            tt.setTradeResourceID(trade.getResourceID());
                            newList.add(tt);
                        }
                    }


                } else {
                    //修改交易
                    trade.setNumber(number);
                    trade.setType(mapTradeType.get(type));
                    trade.setStatus(mapTradeStatus.get(status));
                    trade.setComment(comment);
                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
                    tradeUpdate.add(trade);
                 //   return Result.renderError("第" + (i + 2) + "行「*交易名称」列对应的值已存在!");

                }

                Map<String, String> valueNumber = new HashMap<>();
                if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                    valueNumber = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                }
                valueNumber.put(number, number);
                systemAndTradeNumberVerify.put(testSystemRid, valueNumber);

                Map<String, String> valueName = new HashMap<>();
                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
                    valueName = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
                }
                valueName.put(tradeName, tradeName);
                systemAndTradeNameVerify.put(testSystemRid, valueName);


                Map<String, String> value = new HashMap<>();
                if (!modeuleAndTrade.isEmpty() && modeuleAndTrade.containsKey(modeleResourceID)) {
                    value = (Map<String, String>) modeuleAndTrade.get(modeleResourceID);
                }
                value.put(tradeName, tradeName);
                modeuleAndTrade.put(modeleResourceID, value);

            } else {
                //交易直接挂在系统下
                ////验证excel中系统下的交易名称是否重复
//                if (!systemAndTrade.isEmpty() && systemAndTrade.containsKey(testSystemRid)) {
//                    Map<String, String> tradeStr = (Map<String, String>) systemAndTrade.get(testSystemRid);
//                    if (tradeStr.containsKey(tradeName)) {
//                        return Result.renderError("第" + (i + 2) + "行交易名称重复!");
//                    }
//                }
                //需求变更，现在是整个被测系统下的交易名称和编号都不重复（由于编号不是必输的，有编号就校验，没有就不校验）
                if(number != null && !"".equals(number)){
                    if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                        Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                        if (tradeStr.containsKey(number)) {
                            return Result.renderError("第" + (i + 2) + "行「交易编码」重复!");
                        }
                    }
                    //根据被测系统和交易编号查询交易
                    Trade trade = tradeService.findByTestSystemResourceAndNumber(testSystemRid,number);
                    if(trade != null){
                        return Result.renderError("第" + (i + 2) + "行「交易编码」已存在!");
                    }
                }

                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
                    Map<String, String> tradeStr = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
                    if (tradeStr.containsKey(tradeName)) {
                        return Result.renderError("第" + (i + 2) + "行「*交易名称」重复!");
                    }
                }

                Trade trade = tradeService.findByTestSystemResourceAndName(testSystemRid, tradeName);
                if (trade == null) {
                    //新建交易
                    trade = new Trade();
                    trade.setName(tradeName);
                    trade.setNumber(number);
                    trade.setType(mapTradeType.get(type));
                    trade.setStatus(mapTradeStatus.get(status));
                    trade.setComment(comment);
                    trade.setTestSystemResourceID(testSystemRid);
                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
                    tradeAdd.add(trade);
                    trade.setResourceID(generateResourceID());
                    //处理测试经理数据
                    if(belongMananger != null && !belongMananger.equals("")){
                        String[] split = belongMananger.split(",");
                        for(String s :split) {
                            TradeAndTestManager tt=new TradeAndTestManager();
                            tt.setBelongTestManager(s);
                            tt.setTradeResourceID(trade.getResourceID());
                            newList.add(tt);
                        }
                    }
                } else {
                    //修改交易
                    trade.setNumber(number);
                    trade.setType(mapTradeType.get(type));
                    trade.setStatus(mapTradeStatus.get(status));
                    trade.setComment(comment);
                    trade.setExecuteMode(mapExecuteModes.get(executeMode) == null ? "" : mapExecuteModes.get(executeMode));
                    tradeUpdate.add(trade);
                   // return Result.renderError("第" + (i + 2) + "行「*交易名称」对应列的值已存在!");
                }
                Map<String, String> valueNumber = new HashMap<>();
                if (!systemAndTradeNumberVerify.isEmpty() && systemAndTradeNumberVerify.containsKey(testSystemRid)) {
                    valueNumber = (Map<String, String>) systemAndTradeNumberVerify.get(testSystemRid);
                }
                valueNumber.put(number, number);
                systemAndTradeNumberVerify.put(testSystemRid, valueNumber);

                Map<String, String> valueName = new HashMap<>();
                if (!systemAndTradeNameVerify.isEmpty() && systemAndTradeNameVerify.containsKey(testSystemRid)) {
                    valueName = (Map<String, String>) systemAndTradeNameVerify.get(testSystemRid);
                }
                valueName.put(tradeName, tradeName);
                systemAndTradeNameVerify.put(testSystemRid, valueName);


                Map<String, String> value = new HashMap<>();
                if (!systemAndTrade.isEmpty() && systemAndTrade.containsKey(testSystemRid)) {
                    value = (Map<String, String>) systemAndTrade.get(testSystemRid);
                }
                value.put(tradeName, tradeName);
                systemAndTrade.put(testSystemRid, value);
            }
        }

        if (!tradeAdd.isEmpty()) {
            tradeService.save(tradeAdd, userNumber);
        }
        if (!tradeUpdate.isEmpty()) {
            tradeService.update(tradeUpdate, userNumber);
        }
        if(!newList.isEmpty()) {
     	   iTradeTestManagerService.save(newList, userNumber);
        }

        return Result.renderSuccess(dataList);
	}
	private long generateResourceID() {
		 SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
	     return worker.genNextId();
}
	/**
     *
     * @param peopleMap
	 * @Description:读取交易excel数据（表头不固定）
     *
     */
    public Result<?> getExcelData2(MultipartFile mFile,Map<String,String> mapTradeStatus,Map<String,String> mapTradeType,Map<String,String> mapExecuteModes, Map<String, String> peopleMap) {
        String fileName = mFile.getOriginalFilename();// 获取文件名
        InputStream is=null;
        try {
            if (!validateExcel(fileName)) {// 验证文件名是否合格
                return Result.renderError("请选择Excel文件导入");
            }
             is=mFile.getInputStream();
            Workbook wb = null;
            if (isExcel2007(fileName)) {
            	 wb = new XSSFWorkbook(is);// 当excel是2007时,创建excel2007
            }else {
            	 wb = new HSSFWorkbook(is);// 当excel是2003时,创建excel2003
            }
            return this.readTradeExcelData2(wb,mapTradeStatus,mapTradeType,mapExecuteModes,peopleMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("读取文件异常:"+e.getMessage());
        }finally {
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){

            }
        }
    }

    /**
     * 验证EXCEL文件
     *
     * @param filePath
     * @return
     */
    public boolean validateExcel(String filePath) {
        if (filePath == null || !(isExcel2003(filePath) || isExcel2007(filePath))) {
            return false;
        }
        return true;
    }

    // @描述：是否是2003的excel，返回true是2003
    public static boolean isExcel2003(String filePath) {
        return filePath.matches("^.+\\.(?i)(xls)$");
    }

    // @描述：是否是2007的excel，返回true是2007
    public static boolean isExcel2007(String filePath) {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }

    private Result<?> readTradeExcelData2(Workbook wb,Map<String,String> mapTradeStatus,Map<String,String> mapTradeType,Map<String,String> mapExecuteModes, Map<String, String> peopleMap){
    	List<Map<String, String>> resultList=new ArrayList<>();
        // 得到第一个shell
        Sheet sheet = wb.getSheetAt(0);

        //验证并获取Excel表头
        Map<String, Object> ob = getSheetHead(sheet);
		if (ob.get("error") != null) {
			return Result.renderError(ob.get("").toString());
		}
		List<String> excelColumnList = (List<String>) ob.get("columnList");

        // 得到Excel的行数
        int totalRows = sheet.getLastRowNum();
        // 得到Excel的列数(前提是有行数)
        int totalCells =0;

        Row titleRow = sheet.getRow(0);

        if (titleRow != null) {
            totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
        }
        //校验必填项（excel表头名称以*开始视为必填项）
        totalRows=CheckUtil.checkLoop(totalRows);
        for(int i = 1; i <= totalRows; i++){
            Row row = sheet.getRow(i);
            if(isRowEmpty(row)) continue;
            for (int j = 0; j < totalCells; j++) {
            	Cell titleCell =  titleRow.getCell(j);
            	if(!titleCell.toString().startsWith("*")) {
            		continue;
            	}
            	Cell nameCell = row.getCell(j);
            	String nameCellValue = cellToString(nameCell);
            	if("".equals(nameCellValue)){
            		return Result.renderError("第"+(i+1)+"行「"+titleCell.toString().substring(1)+"」列对应值不能为空！");
            	}
			}
        }

        Map<Integer, String> numMap = new HashMap<>();
        numMap.put(1, "一");
        numMap.put(2, "二");
        numMap.put(3, "三");
        numMap.put(4, "四");
        numMap.put(5, "五");
        numMap.put(6, "六");
        numMap.put(7, "七");
        numMap.put(8, "八");
        numMap.put(9, "九");
        numMap.put(10, "十");

//        List<TestSystem> testSystemList = testSystemDao.findAll();
//        List<SystemModule> systemModuleList = systemModuleService.findAll();

        // 循环Excel行数
        totalRows=CheckUtil.checkLoop(totalRows);
        for (int r = 1; r <= totalRows; r++) {
            Row row = sheet.getRow(r);
            if (row == null || isRowEmpty(row)) {
                continue;
            }
            // 循环Excel的列
            Map<String, String> map = new HashMap<String, String>();
            int moduleNum=1;//上级模块
            totalCells=CheckUtil.checkLoop(totalCells);
            for (int c = 0; c <= totalCells; c++) {
            	//获取当前列的标题名称
            	Cell titleCell =  titleRow.getCell(c);
                String titleCellValue = cellToString(titleCell);
                //获取当前行、对应列的值
                Cell cell = row.getCell(c);
                String cellValue = cellToString(cell);

            	if ("*所属系统".equals(titleCellValue)) {
            		map.put("testSystem",cellValue);
            		continue;
				}
            	if (titleCellValue.equals(numMap.get(c)+"级模块") && !"".equals(cellValue)) {
					map.put("module"+c,cellValue);
            		/*
            		 * 2023-01-07东莞银行定制化：去掉导入时对模块的校验，改为模块直接导入
            		 *
            		//验证交易所属系统、模块层级关系是否正确
            		//验证当前行「*所属系统」列的数据
            		Cell systemCell = row.getCell(0);
                    String systemCellValue = cellToString(systemCell);
                    TestSystem testSystem = testSystemList.stream().filter(x -> systemCellValue.equals(x.getName())).findFirst().orElse(null);
            		if ( testSystem == null) {
            			return Result.renderError("第"+(r+1)+"行「*所属系统」列的值不存在！");
					}
            		//当是一级模块时
            		if (c == 1) {
                		SystemModule systemModule = systemModuleList.stream().filter(x ->
									    					testSystem.getResourceID().equals(x.getTestSystemResourceID())
									    					&& testSystem.getResourceID().equals(x.getParentResourceID())
									    					&& cellValue.equals(x.getName())
													).findFirst().orElse(null);
                		if (systemModule ==null) {
                			return Result.renderError("第"+(r+1)+"行「"+titleCellValue+"」列的值不存在！");
						}
                		map.put("module"+c,cellValue);
                		continue;
					}
            		if (c>1) {
                		//获取前一列的值
            			Cell beforeCell = row.getCell(c-1);
                        String beforeCellValue = cellToString(beforeCell);
                        //验证当前行前一列的模块名称
                        SystemModule beforeystemModule = systemModuleList.stream().filter(x ->
									    					testSystem.getResourceID().equals(x.getTestSystemResourceID())
									    					&& beforeCellValue.equals(x.getName())
													).findFirst().orElse(null);
                        if (beforeystemModule == null) {
                        	//获取前一列的标题名称
                        	Cell beforeTitleCell =  titleRow.getCell(c-1);
                            String beforeTitleCellValue = cellToString(beforeTitleCell);
                            return Result.renderError("第"+(r+1)+"行「"+beforeTitleCellValue+"」列的值不存在！");
						}
                        //验证当前行当前列的模块名称
                        SystemModule systemModule = systemModuleList.stream().filter(x ->
									    					testSystem.getResourceID().equals(x.getTestSystemResourceID())
									    					&& beforeystemModule.getResourceID().equals(x.getParentResourceID())
									    					&& cellValue.equals(x.getName())
													).findFirst().orElse(null);
                        if (systemModule == null) {
                            return Result.renderError("第"+(r+1)+"行「"+titleCellValue+"」列的值不存在！");
						}
                        map.put("module"+c,cellValue);
					}
            		*/
				}
            	if ("交易编码".equals(titleCellValue)) {
            		map.put("number",cellValue);
            		continue;
				}
            	if ("*交易名称".equals(titleCellValue)) {
            		map.put("name",cellValue);
            		continue;
				}
            	if ("*交易类型".equals(titleCellValue)) {
            		if(!mapTradeType.containsKey(cellValue)){
                        return Result.renderError("第"+(r+1)+"行「"+titleCellValue+"」列的值错误！");
                    }
            		map.put("type",cellValue);
            		continue;
				}
            	if ("*交易状态".equals(titleCellValue)) {
            		if(!mapTradeStatus.containsKey(cellValue)){
                        return Result.renderError("第"+(r+1)+"行「"+titleCellValue+"」列的值错误！");
                    }
            		map.put("status",cellValue);
            		continue;
				}
            	if ("交易描述".equals(titleCellValue)) {
            		map.put("comment",cellValue);
            		continue;
				}
            	if ("执行方式".equals(titleCellValue)) {
            		if(!"".equals(cellValue)) {
            			if(!mapExecuteModes.containsKey(cellValue)){
                            return Result.renderError("第"+(r+1)+"行「"+titleCellValue+"」列的值错误！");
                		}
            		}
            		map.put("executeMode",cellValue);
            		continue;
				}

            	if ("归属测试经理".equals(titleCellValue)) {
//            		if(!"".equals(cellValue)) {
//            			if(!peopleMap.containsKey(cellValue)){
//                            return Result.renderError("第"+(r+1)+"行「"+titleCellValue+"」列的值错误！");
//                		}
//            		}
                    List<String> numberList = new ArrayList<>();
                    if (StringUtils.isNotEmpty(cellValue)) {
                        String[] split = cellValue.split(",");
                        for (String s : split) {
                            if (!peopleMap.containsKey(s)) {
                                return Result.renderError("第" + (r + 1) + "行「" + titleCellValue + "」列的值错误！");
                            }
                            String number = peopleMap.get(s);
                            numberList.add(number);
                        }
                    }
                    String collect = numberList.stream().collect(Collectors.joining(","));
                    map.put("belongManager", collect);
                    continue;
				}

            	/*
        		//只有在大于7列时，才会有系统模块需要处理
        		if(excelColumnList.size()>7) {
        			//系统模块列的值不为空时，才会进行处理
        			if (!"".equals(cellValue)) {
            			//获取当前行「所属系统」列对应的系统名称，根据系统名称查询系统数据
            			TestSystem testSystem = testSystemList.stream().filter(x -> cellValue.equals(x.getName())).findFirst().orElse(null);
                		if ( testSystem == null) {
                			return Result.renderError("第"+r+"行「"+titleCellValue+"」列对应值不存在！");
						}
                		if ( c==1 && titleCell.equals(numMap.get(c)+"级模块")) {
                			//根据系统模块所属系统rid、父级rid、模块名称查询
                			SystemModule systemModule = systemModuleList.stream().filter(x ->
		                					testSystem.getResourceID().equals(x.getTestSystemResourceID())
		                					&& testSystem.getResourceID().equals(x.getParentResourceID())
		                					&& cellValue.equals(x.getName())
                					).findFirst().orElse(null);
                			if (systemModule != null) {
                				Cell nextitleCell = titleRow.getCell(c+1);
                				String nextitleCellValue = cellToString(nextitleCell);
                				//如果一级模块下一列是：「交易编码」时，表示交易直接挂在一级模块下
                				if ("交易编码".equals(nextitleCellValue)) {
                					map.put("module"+c,cellValue);
								}
                				//如果下一级也是系统模块，但取值为空，同样表示交易直接挂在一级模块下
                				else if(nextitleCellValue.equals(numMap.get(c+1)+"级模块") &&  "".equals(cellValue) ) {
                					map.put("module"+c,cellValue);
                				}
							} else {
                				return Result.renderError("第"+r+"行「"+titleCell.toString()+"」列对应值不存在！");
							}
                		}
                		//其他层级模块
                		//验证当前系统模块层级和系统、上级系统模块关系
                		else if ( c>1 && titleCell.equals(numMap.get(c)+"级模块")) {
                			//由于第一层级模块已校验过系统，此处不再校验
                			//获取上级系统模块
                			SystemModule parentSystemModule = systemModuleList.stream().filter(x -> testSystem.getResourceID().equals(x.getTestSystemResourceID())
                					&& cell.toString().equals(x.getName()) ).findFirst().orElse(null);
                			SystemModule systemModule = systemModuleList.stream().filter(x -> testSystem.getResourceID().equals(x.getTestSystemResourceID())
                					&& parentSystemModule.getResourceID().equals(x.getParentResourceID()) ).findFirst().orElse(null);
                			if (systemModule != null) {

							} else {
								return Result.renderError("第"+r+"行「"+titleCell.toString()+"」列对应值不存在！");
							}
                		}


            		}
            	}
                */
            }
    		if (map.size()>8) {
                Map<String, String> resultMap = new HashMap<>();
                //将模块层级重新排序
                resultMap.put("testSystem",map.get("testSystem"));
                resultMap.put("number",map.get("number"));
                resultMap.put("name",map.get("name"));
                resultMap.put("type",map.get("type"));
                resultMap.put("status",map.get("status"));
                resultMap.put("comment",map.get("comment"));
                resultMap.put("executeMode",map.get("executeMode"));
                resultMap.put("belongManager",map.get("belongManager"));
    			//需要排序的模块个数
    			int n = map.size()-8;
    			for (int i = 1; i <= map.size()-8; i++) {
    				String moduleName = map.get("module"+i);
    				resultMap.put("module"+n,moduleName);
    				n--;
    				if (n==0) {
    					break;
    				}
    			}
    			resultList.add(resultMap);
    		}else {
            	resultList.add(map);
    		}
        }

//                        	//模块
//                        	String module=getCellValue(cell);
//                        	if(module!=null && !"".equals(module)) {
//                        		map.put("module"+moduleNum++,getCellValue(cell));
//                        	}


    	return Result.renderSuccess(resultList);
    }

	/**
	 * 验证并获取excel表头
	 * @param sheet
	 * @return
	 */
	private Map<String, Object> getSheetHead(Sheet sheet) {
		Map<String, Object> ob = new HashMap<String, Object>();

		List<String> titleList = new ArrayList<String>();
		Row headRow = sheet.getRow(0);// 表头
		if (headRow != null) {
			int headNum = headRow.getPhysicalNumberOfCells();// 表头个数
            headNum=CheckUtil.checkLoop(headNum);
            for (int j = 0; j < headNum; j++) {
				Cell cell = headRow.getCell(j);
				String cellValue = cellToString(cell);
				// 包含特殊字符直接返回错误信息
				if (isSpecialChar(cellValue)) {
					ob.put("error", "表头不能包含<>、空格、换行等特殊字符");
					return ob;
				}
				// 单sheet里，有重名表头，直接返回错误信息
				if (titleList.contains(cellValue)) {
					ob.put("error", "「交易列表」sheet存在重名表头:" + cellValue);
					return ob;
				}
				if (StringUtils.isNotEmpty(cellValue)) {
					titleList.add(cellValue);
				}
			}

			// 表头校验
			// 至少要有：「*所属系统、交易编码、*交易名称、*交易类型、*交易状态、交易描述、执行方式」这7列
			List<String> validateList = new ArrayList<String>();
			//String [] titileArray = {"*所属系统","交易编码","*交易名称","*交易类型","*交易状态","交易描述","执行方式"};
			String [] titileArray = {"*所属系统","交易编码","*交易名称","*交易类型","*交易状态","交易描述","执行方式","归属测试经理"};
			for (int i = 0; i < titileArray.length; i++) {
				if (!titleList.contains(titileArray[i])) {
					validateList.add(titileArray[i]);
				}
			}
			if (!validateList.isEmpty()) {
				ob.put("error", "缺少"+validateList.toString()+validateList.size()+"列");
				return ob;
			}

			// 限制最多只能10级模块
			if (titleList.size()>17) {
				ob.put("error", "最多只支持十级系统模块下的交易导入！");
				return ob;
			}

			Map<Integer, String> numMap = new LinkedHashMap<Integer, String>();
	        numMap.put(1, "一");
	        numMap.put(2, "二");
	        numMap.put(3, "三");
	        numMap.put(4, "四");
	        numMap.put(5, "五");
	        numMap.put(6, "六");
	        numMap.put(7, "七");
	        numMap.put(8, "八");
	        numMap.put(9, "九");
	        numMap.put(10, "十");

			// 第1列固定：*所属系统
			// 最后6列固定：交易编码、*交易名称、*交易类型、*交易状态、交易描述、执行方式
			// 中间系统模块列：从第一级开始，向右逐列加一级
			if (!"*所属系统".equals(titleList.get(0))) {
				ob.put("error", "第1列必须为「*所属系统」");
				return ob;
			}

			// 表头大于7列才会有系统模块层级
			if (titleList.size()>8) {
				for( int i=1 ; i< (titleList.size()-7) ; i++) {
					if ( !titleList.get(i).equals(numMap.get(i)+"级模块") ) {
						ob.put("error", "按照模块层级排序，第"+(i+1)+"列名称必须为「"+numMap.get(i)+"级模块」");
						return ob;
					}
				}
			}

			//交易编码
			if(!"交易编码".equals(titleList.get(titleList.size()-7))) {
				if(titleList.size()>8) {
					ob.put("error", "「"+numMap.get(titleList.size()-7)+"级模块」列后必须为「交易编码」列");
					return ob;
				}else {
					ob.put("error", "「*所属系统」列后必须为「交易编码」列");
					return ob;
				}
			}
			//*交易名称
			if(!"*交易名称".equals(titleList.get(titleList.size()-6))) {
				ob.put("error", "「交易编码」列后必须为「*交易名称」列");
				return ob;
			}
			//*交易类型
			if(!"*交易类型".equals(titleList.get(titleList.size()-5))) {
				ob.put("error", "「*交易名称」列后必须为「*交易类型」列");
				return ob;
			}
			//*交易状态
			if(!"*交易状态".equals(titleList.get(titleList.size()-4))) {
				ob.put("error", "「*交易类型」列后必须为「*交易状态」列");
				return ob;
			}
			//交易描述
			if(!"交易描述".equals(titleList.get(titleList.size()-3))) {
				ob.put("error", "「*交易状态」列后必须为「交易描述」列");
				return ob;
			}
			//执行方式
			if(!"执行方式".equals(titleList.get(titleList.size()-2))) {
				ob.put("error", "「交易描述」列后必须为「执行方式」列");
				return ob;
			}
			if(!"归属测试经理".equals(titleList.get(titleList.size()-1))) {
				ob.put("error", "「执行方式」列后必须为「归属测试经理」列");
				return ob;
			}
			ob.put("columnList", titleList);
		}
		return ob;
	}

	/**
	 * 转字符串格式
	 */
	private String cellToString(Cell cell) {
		if (cell != null) {
			cell.setCellType(CellType.STRING);
			String value = cell.getStringCellValue();
			for (int i = 10; i < 14; i++) {
				value = value.replaceAll(String.valueOf((char) i), "");
			}
			return value.trim();
		} else {
			return "";
		}
	}

	/**
	 * 判断是否含有特殊字符
	 */
	private boolean isSpecialChar(String str) {
		// String regEx = "[
		// `~!@#$%^&*()+={}`:;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】’；：“
		// ”‘。，、？]|\n|\r|\t";
		// String regEx =
		// "[`~!@#$^&()+={}`:;',\\[\\].<>/?~！@#￥……&（）+|{}【】’；：“”‘。，、？]|\n|\r|\t";
		// String regEx = "[
		// `~!@#$^&*()+={}`:;',\\[\\].<>/?~！@#￥……&*（）——+|{}【】’；：“
		// ”‘。，、？]|\n|\r|\t";
		String regEx = "<>\n|\r|\t";
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(str);
		return m.find();

	}

}
