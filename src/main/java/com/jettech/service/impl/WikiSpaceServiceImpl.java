package com.jettech.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.common.dto.assets.WikiSpacePageDTO;
import com.jettech.common.page.PageResult;
import com.jettech.common.page.PageUtil;
import com.jettech.dao.idao.IWikiSpaceDao;
import com.jettech.model.WikiSpace;
import com.jettech.service.iservice.IWikiSpaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WikiSpaceServiceImpl extends BaseServiceImpl<WikiSpace> implements IWikiSpaceService {

    @Autowired
    private IWikiSpaceDao wikiSpaceDao;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = wikiSpaceDao;
    }


    @Override
    public PageResult getSpacePage(WikiSpacePageDTO dto) {
        PageHelper.startPage(dto.getPageNumber(), dto.getPageSize());
        return PageUtil.getPageResult(new PageInfo<>(this.getSpaceList(dto)));
    }

    @Override
    public List<WikiSpace> getSpaceList(WikiSpacePageDTO dto) {
        return this.wikiSpaceDao.getSpaceList(dto);
    }

    @Override
    public String addOrUpdateSpace(WikiSpace wikiSpace, String loginUserNumber) {
        //校验是否存在同名空间
        boolean check = this.checkSpaceName(wikiSpace.getSpaceName());
        if (wikiSpace.getResourceID() != null) {
            WikiSpace space = this.findByResourceID(wikiSpace.getResourceID());
            if (space != null) {
                if(!space.getSpaceName().equals(wikiSpace.getSpaceName())&&!check){
                    return "修改失败，不可修改为其他已存在空间名！";
                }else{
                    wikiSpace.setId(space.getId());
                    this.update(wikiSpace, loginUserNumber);
                }
            }else{
                return "操作失败";
            }
        } else {
            if(check){
                this.save(wikiSpace, loginUserNumber);
            }else{
                return "保存失败，不可保存为已存在空间名！";
            }
        }
        return "操作成功！";
    }

    //检查是否存在重名空间
    private boolean checkSpaceName(String spaceName){
        List<WikiSpace> spaceList = wikiSpaceDao.findAll().stream().filter(item->item.getSpaceName().equals(spaceName)).collect(Collectors.toList());
        if(spaceList.size()>0){
            return false;
        }else {
            return true;
        }
    }
}

