package com.jettech.service.impl;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jettech.dao.idao.IDemandChangeNodesDao;
import com.jettech.model.DemandChangeNodes;
import com.jettech.service.iservice.IDemandChangeNodesService;


/**
 * @ClassName DemandChangeNodesServiceImpl
 * @description 需求变更记录表Service实现类
 * <AUTHOR>
 * @create 20200217
 */
@Service
@Transactional
public class DemandChangeNodesServiceImpl extends BaseServiceImpl<DemandChangeNodes> implements IDemandChangeNodesService {
	@Autowired
	private IDemandChangeNodesDao iDemandChangeNodesDao;
	
	@PostConstruct
	private void postConstruct() {
		baseDao = this.iDemandChangeNodesDao;
	}
}
