package com.jettech.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jettech.DTO.SceneTaskInfoDTO;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.DataDictionaryDTO;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.dao.idao.ISceneCaseContainDao;
import com.jettech.dao.idao.ISceneTaskInfoDao;
import com.jettech.enums.TaskStatus;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.model.SceneTaskInfo;
import com.jettech.service.iservice.ISceneTaskInfoService;
import com.jettech.util.LoginUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Service
public class SceneTaskInfoServiceImpl  extends BaseHBServiceImpl<SceneTaskInfo> implements ISceneTaskInfoService {
    private static final Logger logger = LoggerFactory.getLogger(SceneTaskInfoServiceImpl.class);

    @Autowired
    private ISceneTaskInfoDao sceneTaskInfoDao;
    @Autowired
    private IFeignDataDesignToBasicService basicFeign;

    @PostConstruct
    public void init() {
        this.baseDao = sceneTaskInfoDao;
    }

    @Override
    public List<SceneTaskInfo> findByTestTaskResourceID(Long testTaskResourceID) {
        SceneTaskInfo query = new SceneTaskInfo();
        query.setTestTaskResourceID(testTaskResourceID);
        QueryWrapper<SceneTaskInfo> queryWrapper = Wrappers.query(query);
        return sceneTaskInfoDao.selectList(queryWrapper);
    }

    @Override
    public SceneTaskInfo saveSceneTask(SceneTaskInfoDTO sceneTaskInfoDTO) {
        sceneTaskInfoDTO.setStatus(TaskStatus.UNDO.getCode());
        SceneTaskInfo result = save(sceneTaskInfoDTO, LoginUtils.getUserNumber());
        return result;
    }

    @Override
    public SceneTaskInfo updateSceneTask(SceneTaskInfo sceneTaskInfo) {
        Assert.notNull(sceneTaskInfo.getId(), "ID不能为空");
        SceneTaskInfo result = update(sceneTaskInfo, LoginUtils.getUserNumber());
        return result;
    }

    @Override
    public void deleteById(Long id) {
        Assert.notNull(id, "ID不能为空");
        SceneTaskInfo sceneTaskInfo = new SceneTaskInfo();
        sceneTaskInfo.setId(id);
        delete(sceneTaskInfo, LoginUtils.getUserNumber());
    }

    @Override
    protected void preDelete(SceneTaskInfo model, String userNumber) {
        //通知UI和API删除对于的数据
    }
}
