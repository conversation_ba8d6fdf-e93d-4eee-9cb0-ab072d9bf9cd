package com.jettech.service.impl;

import com.jettech.dao.idao.IPerFormCaseDao;
import com.jettech.model.PerFormCase;
import com.jettech.service.iservice.IPerFormCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-10-22 17:00
 */
@Service
@Transactional
public class PerFormCaseServiceImpl extends BaseServiceImpl<PerFormCase> implements IPerFormCaseService {

    @Autowired
    private IPerFormCaseDao perFormCaseDao;

    @PostConstruct
    public void postConstruct() {

        this.baseDao = perFormCaseDao;
    }


    @Override
    public List<PerFormCase> findByTestcaseResourceID(String resourceID) {

        return perFormCaseDao.findByTestcaseResourceID(resourceID);
    }

    @Override
    public List<PerFormCase> findByGroupResourceIDs(List<String> projectGroups) {
        return perFormCaseDao.findByGroupResourceIDs(projectGroups);
    }
}
