package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.feign.IFeignAssetsToTrp;
import com.jettech.service.iservice.IProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class ProjectServiceImpl implements IProjectService {
    @Autowired
    private IFeignAssetsToTrp feignAssetsToTrp;
    /**
     * @Description 查询用户拥有的项目
     * <AUTHOR>
     * @date 2019-11-06 10:15
     * @param userNumber
     * @return com.jettech.dto.Result<?>
     */
    @Override
    public Result<?> findTestProjectByUserNumber(String userNumber) {
    	String token = HttpRequestUtils.getCurrentRequestToken();
        return feignAssetsToTrp.findTestProjectByUserNumber(userNumber,token);
    }
}
