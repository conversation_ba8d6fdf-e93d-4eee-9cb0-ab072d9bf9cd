package com.jettech.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jettech.common.dto.Result;
import com.jettech.dao.idao.ITestSystemApplyDao;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.mapper.ITestSystemApplyMapper;
import com.jettech.model.ApplyRround;
import com.jettech.model.TestSystem;
import com.jettech.model.TestSystemApply;
import com.jettech.service.iservice.ITestSystemApplyService;
import com.jettech.service.iservice.ITestSystemService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
@Service
public class TestSystemApplyImpl extends BaseHBServiceImpl<TestSystemApply> implements ITestSystemApplyService {
    private static final Logger logger = LoggerFactory.getLogger(TestSystemApplyImpl.class);

    @Autowired
    private ITestSystemApplyDao testSystemApplyDao;

    @Autowired
    private com.jettech.mapper.IApplyRroundMapper IApplyRroundMapper;

    @Autowired
    private ITestSystemService testSystemService;

    @Autowired
    private ITestSystemApplyMapper testSystemApplyMapper;

    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasicService;

    private static final String TESTSYSTEM_PATH = "/jettomanager-datadesign/tmp/testsystem/";

    private ReentrantLock lock = new ReentrantLock();


    @PostConstruct
    public void postConstruct() {

        this.baseDao = testSystemApplyDao;
    }

    /**
     * @Title: findTestSystemApply
     * @Description: 系统应用(分页 + 条件查询)
     * @Param: params
     * @Author: dwl
     * @Date: 2024/7/15
     */
    @Override
    public Result findTestSystemApply(Map<String, String> params) {
        return testSystemApplyDao.findTestSystemApply(params);
    }

    /**
     * @Title: addTestSystemApply
     * @Description: 新增系统应用
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @Override
    public Result<?> addTestSystemApply(Map<String, String> params, String userNumber) {
        try {
            String applyNumber = params.get("applyNumber");
            String applyName = params.get("applyName");
            String applyType = params.get("applyType");
            String applyDescribes = params.get("applyDescribes");
            String systemResourceID = params.get("systemResourceID");

            //查询下一个应用编号
            Long sysResourceID = Long.valueOf(systemResourceID);
            String nextApplyNumber = generateNextNumber(sysResourceID);

            //新增
            TestSystemApply tsa = new TestSystemApply();
            tsa.setApplyNumber(nextApplyNumber);
            tsa.setApplyName(applyName);
            tsa.setApplyType(applyType);
            tsa.setApplyDescribes(applyDescribes);
            tsa.setSystemResourceID(sysResourceID);
            this.save(tsa, userNumber);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("系统应用创建异常, 异常信息: " +e.getMessage());
        }
        return Result.renderSuccess("应用创建成功");
    }

    public String generateNextNumber(Long sysResourceID) {
        lock.lock();
        TestSystem ts = testSystemService.findByResourceID(sysResourceID);
        String systemName = ts.getName();
        String maxNumber = testSystemApplyDao.findMaxApplyNumber(sysResourceID, systemName);
        try {
            String nextNumber = "";
            if (StringUtils.isNotBlank(maxNumber)) {
                int currentNumber;
                if (maxNumber.contains(systemName)) {
                    currentNumber = Integer.parseInt(maxNumber.substring(systemName.length()));
                } else {
                    currentNumber = Integer.parseInt(maxNumber.substring(maxNumber.length() - 3));
                }
                nextNumber = systemName + String.format("%03d", currentNumber + 1);
            } else {
                nextNumber = systemName + "001";
            }
            return nextNumber;
        } finally {
            lock.unlock();
        }
    }

    /**
     * @Title: updateTestSystemApply
     * @Description: 修改系统应用
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @Override
    public Result<?> updateTestSystemApply(Map<String, String> params, String userNumber) {
        String resourceID = params.get("resourceID");
        String applyNumber = params.get("applyNumber");
        String applyName = params.get("applyName");
        String applyType = params.get("applyType");
        String applyDescribes = params.get("applyDescribes");
//        String systemResourceID = params.get("systemResourceID");

        try {
            TestSystemApply tsa = this.findByResourceID(Long.valueOf(resourceID));
            tsa.setApplyNumber(applyNumber);
            tsa.setApplyName(applyName);
            tsa.setApplyType(applyType);
            tsa.setApplyDescribes(applyDescribes);
            tsa.setEditUser(userNumber);
            tsa.setEditTime(new Date());
            this.update(tsa, userNumber);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("系统应用修改异常, 异常信息: " +e.getMessage());
        }
        return Result.renderSuccess("应用修改成功");
    }

    /**
     * @Title: deleteTestSystemApply
     * @Description: 删除系统应用
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @Override
    public Result<?> deleteTestSystemApply(String resourceID, String userNumber) {
        try {
            Long systemApplyResourceID = Long.valueOf(resourceID);
            //查询应用下是否关联轮次
            QueryWrapper<ApplyRround> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("applyResourceID", systemApplyResourceID);
            List<ApplyRround> rList = IApplyRroundMapper.selectList(queryWrapper);
            if (null != rList && rList.size() > 0) {
                return Result.renderError("应用下关联轮次不能删除");
            }

            //删除应用
            TestSystemApply tsa = this.findByResourceID(systemApplyResourceID);
            if (null != tsa) {
                this.delete(tsa, userNumber);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("系统应用删除异常, 异常信息: " +e.getMessage());
        }
        return Result.renderSuccess("应用删除成功");
    }

    /**
     * @Title: findTestSystemApplyBySystemResourceID
     * @Description: 通过系统resourceID查询应用
     * @Param: params
     * @Author: dwl
     * @Date: 2024/7/16
     */
    @Override
    public Result findTestSystemApplyBySystemResourceID(String systemResourceID) {
        QueryWrapper<TestSystemApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("systemResourceID", Long.valueOf(systemResourceID));
        List<TestSystemApply> rList = testSystemApplyMapper.selectList(queryWrapper);
        return Result.renderSuccess(rList);
    }

    /**
     * @Title: findApplyRoundByApplyResourceID
     * @Description: 通过应用resourceID查询应用
     * @Author: dwl
     * @Date: 2024/7/16
     */
    @Override
    public Result findApplyRoundByApplyResourceID(String applyResourceID) {
        QueryWrapper<ApplyRround> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("applyResourceID", applyResourceID);
        List<ApplyRround> rList = IApplyRroundMapper.selectList(queryWrapper);

        if (!CollectionUtils.isEmpty(rList)) {
            List<Map<String,String>> dic4 = (List<Map<String,String>>) feignDataDesignToBasicService.findDictionaryByInfoName("测试轮次").getObj();
            Map<String,String> typeMap =  new HashMap<>(0);
            if (dic4 != null){
                typeMap =  dic4.stream().collect(Collectors.toMap(e-> e.get("value"), e -> e.get("textName")));
            }
            // 根据数据字典进行数据库内容转换
            for (ApplyRround t : rList) {
                t.setRoundValue(null == t.getRound() || "".equals(t.getRound()) ? "" : typeMap.get(t.getRound()));
            }
        }
        return Result.renderSuccess(rList);
    }

    /**
     * @Title: findRootDirectoryPath
     * @Description: 返回根目录路径
     * @Author: dwl
     * @Date: 2024/7/16
     */
    @Override
    public Map<String, Object> findRootDirectoryPath() {
        Map<String, Object> map = new HashMap<>();
        String rootPath = System.getProperty("user.dir");
        String path = rootPath + TESTSYSTEM_PATH;
        File file = new File(path);
        if (!file.exists()) {
            if (file.mkdirs()) {
                logger.info("创建路径成功: " + path);
            } else {
                logger.info("创建路径失败: " + path);
            }
        }

        map.put("rootPath", file.getPath());
        logger.info("当前项目根目录：" + file.getPath());
        return map;
    }
}
