package com.jettech.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.DTO.TaskTestCaseDTO;
import com.jettech.DTO.TaskTradeCaseDto;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.StatusCode;
import com.jettech.common.dto.assets.GenerageCaseDto;
import com.jettech.common.dto.datadesign.SystemModuleTradeDTO;
import com.jettech.common.dto.datadesign.TestCaseDTO;
import com.jettech.common.dto.datadesign.TestCaseInfoDTO;
import com.jettech.common.dto.trp.DataDictionaryDTO;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.enums.AttachmentStoreTypeEnums;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.redis.JedisClient;
import com.jettech.common.util.*;
import com.jettech.common.util.constants.testcase.TestCaseConstant;
import com.jettech.common.vo.xmind.TopicText;
import com.jettech.dao.idao.ITestCaseDao;
import com.jettech.feign.*;
import com.jettech.mapper.ITestCaseMapper;
import com.jettech.mapper.ITradeMapper;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import com.jettech.util.CompareTestCase;
import com.jettech.util.LongUtil;
import com.jettech.util.PageUtil;
import com.jettech.view.TaskTradeCaseView;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.*;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
//@Transactional
public class TestCaseServiceImpl extends BaseHBServiceImpl<TestCase> implements ITestCaseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TestCaseServiceImpl.class);

    public static final String Redis_Trade_Max_Case_Id_Key = "trade_max_case_id_";
    /**
     * 案例
     */
    @Autowired
    private ITestCaseDao testCaseDao;
    /**
     * 交易
     */
    @Autowired
    private ITradeService tradeService;
    /**
     * 模块
     */
    @Autowired
    private ISystemModuleService systemModuleService;

    /**
     * 被测系统
     */
    @Autowired
    private ITestSystemService testSystemService;

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 需求
     */
    @Autowired
    private IDemandService demandService;

    @Autowired
    private IDemandChangeNodesService iDemandChangeNodesService;

    @Autowired
    private ITestProjectService testProjectService;

    @Autowired
    private ParamConfig paramConfig;

    @Autowired
    private ITestCaseFileService testCaseFileService;

    @Autowired
    private FtpUtil ftpUtil;
    /**
     * fiegn调用缺陷
     */
    @Autowired
    private IFeignDataDesignToBugService bugService;
    /**
     * fiegn调用案例引用
     */
    @Autowired
    private IFeignDataDesignToManexecuteService manexecuteService;

    @Autowired
    private IFeignDataDesignToBasicService basicService;
    @Autowired
    private IFeignDataDesignToAssets feignDataDesignToAssets;
    @Autowired
    private ITestCaseRecycleBinService iTestCaseRecycleBinService;

    @Autowired
    private IProjectGroupService projectGroupService;

    @Autowired
    private ITestCasequoteService testCasequoteService;

    @Autowired
    private IPerFormCaseService perFormCaseService;

    @Autowired
    private IUserinfoService userinfoService;

    @Autowired
    private ITestCaseVersionService testCaseVersionService;
    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;

    @Autowired
    private ITradeMapper tradeMapper;
    @Autowired
    private JedisClient jedisClient;

    @Value("${use_file_service}")
    private boolean useFileService;
    @Autowired
    private ITestCaseMapper testCaseMapper;
    @Autowired
    private IFeignJettoUiMauto feignJettoUIiMauto;
    @PostConstruct
    public void postConstruct() {

        this.baseDao = testCaseDao;
    }


    /**
     * @Title: addTestCase
     * @Description: 新增案例
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @Override
    public Result addTestCase(TestCase testCase, MultipartFile[] files, String userNumber) {
        // 必输字段非空校验
        // *案例编号、*测试意图、*正反例、*案例级别、*测试步骤、*预期结果
        /*if (testCase.getIntent() == null || testCase.getIsNegative() == null || testCase.getCasetLevel() == null
                || testCase.getTestStep() == null || testCase.getExpectedResult() == null) {
            return Result.renderError("必输项参数为空");
        }*/
//        if (testCase.getTesttaskResourceID() == null) {
//            return Result.renderError("测试任务参数为空！");
//        }
        String token = HttpRequestUtils.getCurrentRequestToken();
        Map<String, String> testPlan = null;
        if (testCase.getTesttaskResourceID() != null) {
            testPlan = manexecuteService.findTestPlanByTestTaskResourceID(testCase.getTesttaskResourceID(), token);
        }
//        if (testPlan == null || testPlan.isEmpty()) {
//            return Result.renderError("测试任务或测试计划异常！");
//        }
        TestCase save = null;
        try {
            // 纯数字递增的id
            String testCaseCaseId = this.getNewTestCaseCaseId(testCase.getTradeResourceID());
            testCase.setCaseId(testCaseCaseId);
            testCase.setCaseEditId(testCaseCaseId);
            testCase.setDemandResourceID(testPlan == null || testPlan.isEmpty() ? null : Long.valueOf(testPlan.get("demandResourceID")));
            testCase.setTestEnviroment(testPlan == null || testPlan.isEmpty() ? null : testPlan.get("testStage"));
            JettechUserDTO byNumber = basicService.findByNumber(userNumber, token);
            testCase.setMaintainer(byNumber.getUserName());
            // 维护时间
            testCase.setMaintenanceTime(new Date());
            testCase.setLeadsource(0);
            //添加系统模块交易落库
            if(testCase.getTradeResourceID()!=null){
                TestSystem testSystem = (TestSystem)testSystemService.findTestSystembyTradeByResourceID(testCase.getTradeResourceID()).getObj();
                testCase.setTestsystem(testSystem.getName());
                testCase.setTestsystemResourceID(testSystem.getResourceID());
                Trade trade = tradeService.findByResourceID(testCase.getTradeResourceID());
                testCase.setTrade(trade.getName());
                SystemModule systemModule = systemModuleService.findByResourceID(trade.getModuleResourceID());
                if(systemModule!=null){
                    testCase.setSystemmodule(systemModule.getName());
                    testCase.setSystemmoduleResourceID(systemModule.getResourceID());
                }
            }
            if (StringUtils.isEmpty(testCase.getName())) {
                //api案例名称不能为空
                SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
                testCase.setName(worker.genNextId() + "");
            }

            //api案例显示  需要设置tradeFlowCaseFolderId,testProjectId
            if (testCase.getDemandResourceID() != null) {
                Demand demand = demandService.findByResourceID(testCase.getDemandResourceID());
                if (demand != null) {
                    testCase.setTestProjectId(demand.getTestProjectResourceID());
                    List<Map<String, Object>> folderList = testProjectService.findTradeFlowCaseFolder(demand.getTestProjectResourceID());
                    if (folderList != null && folderList.size() > 0) {
                        testCase.setTradeFlowCaseFolderId((long) folderList.get(0).get("id"));
                    }
                }
            }

        	//案例字段-评审状态未启用时新增案例的评审状态默认为"已评审",20211008dingwl
    		this.reviewStatusUsingEnable(testCase);

            save = this.save(testCase, userNumber);
            if (testCase.getTesttaskResourceID() != null) {
                Result<?> result = manexecuteService.updateTestTaskStatus(String.valueOf(testCase.getTesttaskResourceID()), "2", userNumber);
                if (!result.isSuccess()) {
                    return Result.renderError("更新任务状态失败！");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("新增异常:" + e.getMessage());
        }
       /* if (save != null) {
            try {
                upload(files, save.getResourceID(), userNumber);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }*/

        if (save != null) {
            try {
                if(useFileService){
                    feignDataDesignToFileService.upload(files,save.getResourceID(), ObjectTypeEnum.SCRIPT.getValue(),true);
                }else{
                    upload(files, save.getResourceID(), userNumber);
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return Result.renderSuccess(save);
    }
    /**
     * @Title: reviewStatusUsingEnable
     * @Description: 案例字段-评审状态未启用时新增案例的评审状态默认为"已评审"
     * @author: dingwenlong
     * @date: 2021年10月8日
     * @param testCase
     */
    @SuppressWarnings("unchecked")
	private void reviewStatusUsingEnable(TestCase testCase) {
    	LOGGER.info("-- start reviewStatusUsingEnable -- ");
    	if (StringUtils.isEmpty(testCase.getReviewStatus()) || !TestCaseConstant.REVIEW_STATUS_Y.equals(testCase.getReviewStatus())) {
        	//查询案例字段配置信息
    		LOGGER.info("   1.查询案例字段配置信息 -- ");
        	Map<String, Object> fieldConfigMap;
        	Result<Map<String, Object>> fieldConfigResult = basicService.initTestCaseConfigData();
        	if (!fieldConfigResult.isSuccess()) {
        		LOGGER.error("查询案例字段配置信息异常,异常信息: {}", fieldConfigResult.getMsg());
    			throw new RuntimeException(fieldConfigResult.getMsg());
    		} else {
    			List<Map<String, Object>> fieldConfigMapList = (List<Map<String, Object>>) fieldConfigResult.getObj();
    			if (fieldConfigMapList.isEmpty()) {
    				LOGGER.error("案例字段配置为空！");
    				throw new RuntimeException("案例字段配置为空！");
    			}
    			fieldConfigMap = fieldConfigMapList.stream().collect(Collectors.toMap(x -> x.get("name").toString(), y -> y.get("usingenable").toString()));
    		}
        	//评审状态是否启用,未启用时设置为"已评审"
        	LOGGER.info("   2.查询案评审状态是否启用 -- ");
        	if (!Boolean.parseBoolean(fieldConfigMap.get(TestCaseConstant.REVIEW_STATUS_FIELD).toString())) {
        		testCase.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
        	}
        }
    	LOGGER.info("-- end reviewStatusUsingEnable -- ");
    }

    /**
     * 案例附件上传
     *
     * @param files
     * @param resourceID
     * @param userNumber
     */
    private void upload(MultipartFile[] files, Long resourceID, String userNumber) throws IOException {
        List<TestCaseFile> testCaseFileList = new ArrayList<>();
        String filepath = AttachmentPathGenerator.getDefectAttachmentPath(AttachmentStoreTypeEnums.FTP, Long.valueOf(resourceID));
        Map<String, TestCaseFile> caseFileMap = new HashMap<>();
        Map<String, MultipartFile> fileMap = new HashMap<>();
        String newPath = paramConfig.getFtpPath() + filepath;

        //得到存储路径
        Arrays.stream(files).forEach(file -> {
            TestCaseFile testCaseFile = new TestCaseFile();
            testCaseFile.settestCaseresourceID(resourceID);
            testCaseFile.setName(file.getOriginalFilename());
            testCaseFile.setSize(String.valueOf(file.getSize()));
            testCaseFile.setUploadUserName(userNumber);
            String fileName = file.getOriginalFilename().trim();
            String[] str = fileName.split("\\.");
            String fileType = str[str.length - 1];
            testCaseFile.setExtname("." + fileType);
            testCaseFile.setResourceID(generateResourceID());
            testCaseFile.setPath(newPath);
            fileMap.put(String.valueOf(testCaseFile.getResourceID()), file);
            caseFileMap.put(String.valueOf(testCaseFile.getResourceID()), testCaseFile);
            testCaseFileList.add(testCaseFile);
        });

        if (!testCaseFileList.isEmpty()) {
            testCaseFileService.save(testCaseFileList, userNumber);
        }
        if (!fileMap.isEmpty()) {
            //保存修改附件
            this.saveAndUpdateAttachment(fileMap, caseFileMap);
        }
    }

    private long generateResourceID() {
        SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
        return worker.genNextId();
    }

    private void saveAndUpdateAttachment(Map<String, MultipartFile> fileMap, Map<String, TestCaseFile> caseFileMap) throws IOException {

        Set<String> resourceIDs = fileMap.keySet();
        InputStream inputStream=null;
        for (String resourceID : resourceIDs) {

             inputStream = fileMap.get(resourceID).getInputStream();
            String folderPath = "";
            String fileName = "";
            ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
            byte[] buff = new byte[100]; //buff用于存放循环读取的临时数据
            int rc = 0;
            FileOutputStream outPutStream=null;
            try {
                while ((rc = inputStream.read(buff, 0, 100)) > 0) {
                    byteStram.write(buff, 0, rc);
                }
                byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果
                fileName = resourceID + caseFileMap.get(resourceID).getExtname();
                if (paramConfig.getIsFtpOn()) {//判断ftp是否打开
                    String ftpPath = paramConfig.getFtpPath();
                    System.out.println(ftpPath);
                    folderPath = paramConfig.getFtpPath() + caseFileMap.get(resourceID).getPath();

                    //上传FTP
                    String upload = ftpUtil.upload(folderPath, fileName, bytes);
                    System.out.println(upload);

                } else {
                    folderPath = paramConfig.getAttachmentPath();

                    File dir = new File(folderPath);
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }
                    File file = new File(folderPath + File.separator + fileName);

                     outPutStream = new FileOutputStream(file);

                    outPutStream.write(bytes);

                    outPutStream.flush();

                    outPutStream.close();

                }
            } catch (Exception e1) {

            }finally {
                try {
                    if(inputStream!=null){
                        inputStream.close();
                    }
                    if(outPutStream!=null){
                        outPutStream.close();
                    }
                }catch (Exception e){

                }
            }
        }
    }

    /**
     * 需求状态由‘未开始’转为‘准备中’ QiaoHongju
     *
     * @param demandResourceID
     * @param userNumber
     */
    private void alterStatus2Readying(Long demandResourceID, String userNumber) {
        Demand demand = demandService.findByResourceID(demandResourceID);
        if ("未开始".equals(demand.getTypename())) {
            String token = HttpRequestUtils.getCurrentRequestToken();
            DataDictionaryDTO dic = basicService.findByNameAndTextNameFromDataDesign("DemandState", "准备中", token);
            DataDictionaryDTO dic1 = basicService.findByNameAndTextNameFromDataDesign("DemandState", "未开始", token);
            demand.setTypename("准备中");
            demand.setType(dic.getValue());
            demandService.update(demand, userNumber);
            DemandChangeNodes demandChangeNodes = new DemandChangeNodes();
            demandChangeNodes.setDemandResourceID(demand.getResourceID());
            demandChangeNodes.setBeforeChangeType(dic1.getValue());
            demandChangeNodes.setAfterChangeType(dic.getValue());
            iDemandChangeNodesService.save(demandChangeNodes, userNumber);
        }
    }

    /**
     * @Title: updateTestCase
     * @Description: 修改案例
     * @Param: "[testCase, userNumber]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @Override
    public Result updateTestCase(TestCase testCase, MultipartFile[] files, String userNumber) {
        // 必输字段非空校验
        // *案例编号、*测试意图、*正反例、*案例级别、*测试步骤、*预期结果
//        if (testCase.getCaseId() == null || testCase.getIntent() == null || testCase.getIsNegative() == null
//                || testCase.getCasetLevel() == null || testCase.getTestStep() == null
//                || testCase.getExpectedResult() == null) {
//            return Result.renderError("必输项参数为空");
//        }
        TestCase update = null;
        try {
            TestCase byResourceID = this.findByResourceID(testCase.getResourceID());
            testCase.setId(byResourceID.getId());
            //任务相关的案例信息在修改时不变更
            testCase.setTesttaskResourceID(byResourceID.getTesttaskResourceID());
            testCase.setDemandResourceID(byResourceID.getDemandResourceID());
            testCase.setTestEnviroment(byResourceID.getTestEnviroment());
            if(testCase.getCommitted()==null){
                testCase.setCommitted(byResourceID.getCommitted());
            }
            //判断版本是否变更
            if ((testCase.getVersionResourceID() != null && !testCase.getVersionResourceID().equals(byResourceID.getVersionResourceID()))
                    || (byResourceID.getVersionResourceID() != null && !byResourceID.getVersionResourceID().equals(testCase.getVersionResourceID()))) {

                TestCaseVersion testCaseVersion = new TestCaseVersion();
                BeanUtils.copyProperties(byResourceID, testCaseVersion);
                testCaseVersion.setId(null);
                testCaseVersion.setCreateUser("");
                testCaseVersion.setCreateTime(null);
                testCaseVersion.setEditUser("");
                testCaseVersion.setEditTime(null);
                testCaseVersion.setResourceID(null);
                testCaseVersion.setVersion(0);
                testCaseVersion.setTestCaseResourceID(testCase.getResourceID());
                this.testCaseVersionService.save(testCaseVersion, userNumber);
            }

            update = this.update(testCase, userNumber);
        } catch (Exception e) {
            return Result.renderError("修改异常");
        }
        try {
            if(useFileService){
                feignDataDesignToFileService.upload(files,testCase.getResourceID(), ObjectTypeEnum.SCRIPT.getValue(),true);
            }else{
                upload(files, testCase.getResourceID(), userNumber);
            }
           // this.upload(files,testCase.getResourceID(), userNumber);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.renderSuccess(update);
    }

    /**
     * @Title: isNotRepeat
     * @Description: 判断当前交易下案例的案例编号是否重复
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/5
     */
    @Override
    public Result isNotRepeat(TestCase testCase) {
        // 定义查询变量
        boolean anyMatch;
        // 查询当前交易下的所有案例
        List<TestCase> testCaseList1 = this.findbyTradeResourceID(testCase.getTradeResourceID());
        // 查询当前需求下的所有案例
        // List<TestCase> testCaseList1 =
        // this.findBydemandResourceID(testCase.getDemandResourceID());
        // 判断是新建还是修改
        if (testCase.getResourceID() != null) {
            // 有业务主键存在是修改
            // 修改判断案例编号是否重复排除修改前的案例编号
            anyMatch = testCaseList1.stream().filter(s -> !s.getResourceID().equals(testCase.getResourceID()))
                    .anyMatch(s -> s.getCaseId().equals(testCase.getCaseId()));
        } else {
            // 新增时还未生成业务主键
            anyMatch = testCaseList1.stream().anyMatch(s -> s.getCaseId().equals(testCase.getCaseId()));
        }

        if (anyMatch) {
            return Result.renderError("案例编号重复");
        } else {
            return Result.renderSuccess();
        }
    }

    /**
     * @return java.lang.Integer
     */
    @Override
    public Integer findCountNumberByTradeResourceID(String t, String demandResourceID, String type) {
        return testCaseDao.findCountNumberByTradeResourceID(t, demandResourceID, type);
    }

    @Override
    /**
     * @Title findCaseTotalBySelectedCase
     * @Description 根据选中的案例查询所属交易下的案例总数
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/28
     */
    public Result findCaseTotalBySelectedCase(String caseResourceID) {
        TestCase byResourceID = this.findByResourceID(LongUtil.parseLong(caseResourceID));
        Integer total = testCaseDao.findCountNumberByTradeResourceID(String.valueOf(byResourceID.getTradeResourceID()),
                "", "");
        return Result.renderSuccess(total);
    }

    @Override
    /**
     * @Title findTestCaseMapByResourceID
     * @Description 查询单个案例
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @Date 2019/11/29
     */
    public Result<?> findTestCaseMapByResourceID(String resourceID) {
        if (null == resourceID || "".equals(resourceID)) {
            return Result.renderError("数据异常！");
        }
        TestCase byResourceID = this.findByResourceID(LongUtil.parseLong(resourceID));
        if (byResourceID == null) {
            return Result.renderError("数据异常！");
        }
        JSONObject caseMap = JSONObject.parseObject(JSONObject.toJSON(byResourceID).toString());
        String testPhase = testCaseDao.findTestPhaseByTestTaskResourceID(byResourceID.getTesttaskResourceID());
        caseMap.put("testPhase", testPhase);
        Trade trade = tradeService.findByResourceID(byResourceID.getTradeResourceID());
        // UAT用例交易节点
        if (trade.getTestSystemResourceID() == null) {
            /*
             * caseMap.put("systemName", trade.getName()); caseMap.put("systemRID",
             * trade.getResourceID()); caseMap.put("moduleName", trade.getName());
             * caseMap.put("moduleRID", trade.getResourceID()); //添加所属交易名称
             * caseMap.put("tradeName", trade.getName());
             */
            SystemModule module = systemModuleService.findByResourceID(trade.getModuleResourceID());
            caseMap.put("moduleName", module.getName());
            caseMap.put("moduleRID", module.getResourceID());
            caseMap.put("tradeName", trade.getName());
            caseMap.put("tradeType", "UATTRADE");
            return Result.renderSuccess(caseMap);
        }
        TestSystem ts = testSystemService.findByResourceID(trade.getTestSystemResourceID());
        caseMap.put("systemName", ts.getName());
        caseMap.put("systemRID", ts.getResourceID());
        if (trade.getModuleResourceID() != null) {
            SystemModule module = systemModuleService.findByResourceID(trade.getModuleResourceID());
            SystemModule parentNode = getParentNode(module);
            caseMap.put("moduleName", parentNode.getName());
            caseMap.put("moduleRID", parentNode.getResourceID());
            // 增加所属交易
//			String tradeName = this.getTradeName(module, trade.getName());
            caseMap.put("tradeName", trade.getName());
        } else {
            caseMap.put("moduleName", trade.getName());
            caseMap.put("moduleRID", trade.getResourceID());
            // 添加所属交易名称
            caseMap.put("tradeName", trade.getName());
        }
        return Result.renderSuccess(caseMap);
    }

    /**
     * @param systemModule
     * @param tradeName
     * @return String
     * @Title getTradeName
     * @Description 拼接所属交易的名称
     * <AUTHOR>
     * @data Jan 19, 20203:43:15 PM
     */
    private String getTradeName(SystemModule systemModule, String tradeName) {
        if (systemModule.getParentResourceID().equals(systemModule.getTestSystemResourceID())) {
            return tradeName;
        } else {
            // 查询父级对象
            tradeName = systemModule.getName() + "-" + tradeName;
            systemModule = systemModuleService.findByResourceID(systemModule.getParentResourceID());
            tradeName = getTradeName(systemModule, tradeName);
        }
        return tradeName;
    }

    /**
     * @Title: findTestPlanTreebyTestCaseResourceID
     * @Description: 根据案例查询组装测试计划左侧树结构
     * @Param: "[testCaseResouceIDList]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/27
     */
    @Override
    public Result findTestPlanTreebyTestCaseResourceID(List<Map<Long, String>> maps) {
        // 树结构容器
        List<Map<String, Object>> treeList = new ArrayList<Map<String, Object>>();
        // 案例业务主键
        List<Long> testCaseResouceIDList = maps.stream().map(s -> s.keySet()).flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<String> testCaseResourceIDs = testCaseResouceIDList.stream().map(s -> String.valueOf(s))
                .collect(Collectors.toList());
        // 所有的案例对象
        List<TestCase> testCaseList = this.findByResourceIDIn(testCaseResourceIDs);
        // 根据交易对案例对象分组
        Map<Long, List<TestCase>> testCaseToTrade = testCaseList.stream()
                .collect(Collectors.groupingBy(testCase -> testCase.getTradeResourceID()));
        // 所有的交易对象
        List<Trade> tradeList = tradeService.findByResourceIDIn(new ArrayList<>(testCaseToTrade.keySet()).stream()
                .map(s -> String.valueOf(s)).collect(Collectors.toList()));
        // 交易节点(以及节点下的案例节点)
        List<Map<String, Object>> tradeNodes = tradeList.stream().map(trade -> {
            Map<String, Object> tradeMap = new HashMap<>();
            tradeMap.put("id", trade.getId());
            tradeMap.put("label", trade.getName());
            tradeMap.put("rid", trade.getResourceID());
            tradeMap.put("nodeType", "trade");
            tradeMap.put("intent", trade.getName());
            // 当前交易节点下的所有案例对象
            List<TestCase> testCaseList1 = testCaseToTrade.get(trade.getResourceID());
            // 案例节点
            List<Map<String, Object>> collect2 = testCaseList1.stream().map(testcase -> {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", testcase.getId());
                map1.put("label", testcase.getCaseId());
                map1.put("intent", testcase.getCaseId() + "  " + testcase.getIntent());
                map1.put("rid", testcase.getResourceID());
                map1.put("nodeType", "testCase");
                String val = "";
                for (Map<Long, String> m : maps) {
                    val = m.get(testcase.getResourceID());
                    if (val != null) {
                        break;
                    }
                }
                if (!StringUtils.isBlank(val)) {
                    // 添加执行结果
                    map1.put("caseResult", val);
                }
                return map1;
            }).collect(Collectors.toList());
            // 当前交易节点下的案例节点
            tradeMap.put("children", collect2);
            return tradeMap;
        }).collect(Collectors.toList());
        // 根据交易节点的rid分组
        Map<String, List<Map<String, Object>>> rid = tradeNodes.stream()
                .collect(Collectors.groupingBy(s -> String.valueOf(s.get("rid"))));
        // 根据是否在被测系统下分组
        Map<Boolean, List<Trade>> collect = tradeList.stream()
                .collect(Collectors.partitioningBy(trade -> null == trade.getModuleResourceID()));
        // 直接在被测系统下的交易
        List<Trade> trades1 = collect.get(true);
        List<Trade> UATTradeList = trades1.stream().filter(item -> (item.getTestSystemResourceID() == null))
                .collect(Collectors.toList());
        if (UATTradeList != null && UATTradeList.size() > 0) {
            trades1.removeAll(UATTradeList);
        }

        // 根据被测系统的rid对交易分组
        Map<Long, List<Trade>> collect1 = trades1.stream()
                .collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        /*
         * if(UATTradeList != null && UATTradeList.size() > 0){ Map<String, Object>
         * tradeMap = new HashMap<>(); tradeMap.put("id",0); tradeMap.put("label",
         * UATTradeList.get(0).getName()); tradeMap.put("rid",
         * UATTradeList.get(0).getResourceID()); tradeMap.put("nodeType", "trade");
         * tradeMap.put("intent", UATTradeList.get(0).getName()); // 当前被测系统下的所有交易对象
         * List<Trade> trades = UATTradeList; // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
         * List<List<Map<String, Object>>> collect2 = trades.stream().map(t ->
         * rid.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
         * List<Map<String, Object>> collect3 =
         * collect2.stream().flatMap(Collection::stream).collect(Collectors.toList());
         * tradeMap.put("children", collect3); treeList.add(tradeMap); }
         */
        // 根据当前被测系统下交易对象组装下级节点
        for (Long s : collect1.keySet()) {
            // 当前交易的上一级被测系统
            TestSystem testSystem = testSystemService.findByResourceID(s);
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", testSystem.getId());
            map1.put("label", testSystem.getName());
            map1.put("rid", testSystem.getResourceID());
            map1.put("nodeType", "testSystem");
            map1.put("intent", testSystem.getName());
            // 当前被测系统下的所有交易对象
            List<Trade> trades = collect1.get(testSystem.getResourceID());
            // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
            List<List<Map<String, Object>>> collect2 = trades.stream()
                    .map(t -> rid.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect2.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            treeList.add(map1);
        }
        // 上一级是模块的交易对象
        List<Trade> trades2 = collect.get(false);
        if (trades2.size() > 0) {
            // 上一级是模块的交易节点
            List<Map<String, Object>> tradeNodes2 = trades2.stream()
                    .map(t -> rid.get(String.valueOf(t.getResourceID()))).flatMap(Collection::stream)
                    .collect(Collectors.toList());
            // 通过模块下的交易递归查询父节点
            List<Map<String, Object>> treeList2 = findTradeToSystemModule(trades2, rid, treeList, "");
            // 按照每条案例向上级查询的线路，定义的被测系统会多次重复，需要进行去重合并
            List<Map<String, Object>> collect2 = treeList2.stream().distinct().collect(Collectors.toList());

            return Result.renderSuccess(treeList2);
        }
        return Result.renderSuccess(treeList);
    }

    // 通过模块下的交易递归查询父节点
    private List<Map<String, Object>> findTradeToSystemModule(List<Trade> trades2,
                                                              Map<String, List<Map<String, Object>>> tradeNodesGroup, List<Map<String, Object>> treeList,
                                                              String testSystemResourceID) {
        // 上一级是模块的所有交易id
        List<String> collect2 = trades2.stream().map(s -> String.valueOf(s.getModuleResourceID()))
                .collect(Collectors.toList());
        // 根据交易查询到所有的上一级模块
        List<SystemModule> systemModulesA = systemModuleService.findByResourceIDIn(collect2);
        // 对上一级的模块进行去重
        List<SystemModule> systemModules = systemModulesA.stream().distinct().collect(Collectors.toList());
        // 根据模块rid对交易分组
        Map<Long, List<Trade>> moduleToTrades = trades2.stream()
                .collect(Collectors.groupingBy(s -> s.getModuleResourceID()));
        // 筛选出uat模块
        List<SystemModule> uatModules = systemModules.stream()
                .filter(uatModule -> uatModule.getTestSystemResourceID() == null).collect(Collectors.toList());
        if (!uatModules.isEmpty()) {
            systemModules.removeAll(uatModules);
        }
        List<Map<String, Object>> uatTree = new ArrayList<Map<String, Object>>();
        if (!uatModules.isEmpty()) {
            // 组装uat模块树
            Map<String, Object> uatModuleMap = new HashMap<>();
            uatModuleMap.put("id", 0);
            uatModuleMap.put("label", uatModules.get(0).getName());
            uatModuleMap.put("rid", uatModules.get(0).getResourceID());
            uatModuleMap.put("nodeType", "systemModule");
            uatModuleMap.put("intent", uatModules.get(0).getName());
            List<Trade> trades = moduleToTrades.get(uatModules.get(0).getResourceID());
            List<List<Map<String, Object>>> collect = trades.stream()
                    .map(t -> tradeNodesGroup.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> children = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            uatModuleMap.put("children", children);
            uatTree.add(uatModuleMap);
        }

        // 交易上一级所有的模块节点
        List<Map<String, Object>> systemModuleNodes = new ArrayList<>();
        systemModules.stream().forEach(s -> {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", s.getId());
            map1.put("label", s.getName());
            map1.put("rid", s.getResourceID());
            map1.put("nodeType", "systemModule");
            map1.put("intent", s.getName());
            // 当前模块下的所有交易对象
            List<Trade> trades = moduleToTrades.get(s.getResourceID());
            // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
            List<List<Map<String, Object>>> collect = trades.stream()
                    .map(t -> tradeNodesGroup.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            systemModuleNodes.add(map1);
        });
        List<Map<String, Object>> treelist2 = new ArrayList<>();
        if (systemModuleNodes.size() > 0) {
            // 通过交易的上一级模块递归向上查询父节点
            treelist2 = findSystemModuleToSystemModule(systemModules, systemModuleNodes, treeList,
                    testSystemResourceID);
        } else {
            treelist2 = new ArrayList<>();
        }
        if (!uatTree.isEmpty()) {
            treelist2.addAll(0, uatTree);
        }
        return treelist2;
    }

    // 通过交易的上一级模块向上查询父节点
    private List<Map<String, Object>> findSystemModuleToSystemModule(List<SystemModule> systemModuleList,
                                                                     List<Map<String, Object>> systemModuleNodes, List<Map<String, Object>> treeList,
                                                                     String testSystemResourceID) {
        // 根据模块rid对模块节点分组
        Map<String, List<Map<String, Object>>> moduleGroup = systemModuleNodes.stream()
                .collect(Collectors.groupingBy(s -> String.valueOf(s.get("rid"))));
        // 根据是否是被测系统下模块对象分组
        Map<Boolean, List<SystemModule>> collect1 = systemModuleList.stream()
                .collect(Collectors.partitioningBy(s -> s.getParentResourceID().equals(s.getTestSystemResourceID())));
        // 直接在被测系统下的模块对象
        List<SystemModule> systemModules1 = collect1.get(true);
        // 根据被测系统的rid进行分组（直接在被测系统下的模块对象）
        Map<Long, List<SystemModule>> collect2 = systemModules1.stream()
                .collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 上一级还是模块的当前模块对象
        List<SystemModule> systemModules2 = collect1.get(false);
        // 根据模块父rid对进行分组
        Map<Long, List<SystemModule>> collect4 = systemModules2.stream()
                .collect(Collectors.groupingBy(s -> s.getParentResourceID()));
        // 根据当前模块查询得到的还是模块上一级的模块对象
        List<String> collect5 = systemModules2.stream().map(s -> String.valueOf(s.getParentResourceID()))
                .collect(Collectors.toList());
        List<SystemModule> systemModulesParentA = systemModuleService.findByResourceIDIn(collect5);
        // 对查询得到的上一级模块去重
        List<SystemModule> systemModulesParent = systemModulesParentA.stream().distinct().collect(Collectors.toList());

        // 直接在被测系统下的模块对象,直接循环被测系统
        Set<Long> longs = collect2.keySet();
        longs.stream().forEach(s -> {
            // 查询上一级被测系统
            TestSystem testSystem = testSystemService.findByResourceID(s);
            // 在当前被测系统下的所有模块对象
            List<SystemModule> systemModules = collect2.get(testSystem.getResourceID());
            // 在当前被测系统下的所有模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream()
                    .map(module -> moduleGroup.get(String.valueOf(module.getResourceID())))
                    .collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());

            Optional<Map<String, Object>> ifExist = treeList.stream()
                    .filter(x -> x.get("rid").equals(testSystem.getResourceID())).findFirst();
            // 存在为true
            if (ifExist.isPresent()) {
                // 说明顶级被测系统节点重复,找出重复节点添加
                Map<String, Object> testSystemMap = ifExist.get();
                List<Map<String, Object>> list = (List<Map<String, Object>>) testSystemMap.get("children");
                // 合并两个children元素
                List<Map<String, Object>> collect6 = Stream.of(list, collect3).flatMap(Collection::stream)
                        .collect(Collectors.toList());
                testSystemMap.put("children", collect6);
                // 排除treeList原有集合中的重复元素
                treeList.removeIf(x -> x.get("rid").equals(testSystemMap.get("rid")));
                treeList.add(testSystemMap);
            } else {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", testSystem.getId());
                map1.put("label", testSystem.getName());
                map1.put("rid", testSystem.getResourceID());
                map1.put("children", collect3);
                map1.put("nodeType", "testSystem");
                map1.put("intent", testSystem.getName());
                map1.put("isOpen", "false");
                if (!org.springframework.util.StringUtils.isEmpty(testSystemResourceID)
                        && String.valueOf(s).equals(testSystemResourceID)) {
                    map1.put("isOpen", "true");
                }
                treeList.add(map1);
            }
        });

        // 上一级还是模块的节点收集器
        // List<Map<String, Object>> systemModules3 = new ArrayList<>();
        systemModuleNodes.clear();
        // 上一级还是模块下的模块对象
        systemModulesParent.stream().forEach(parentModule -> {
            // 当前模块上一级还是模块,组装父节点返回
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", parentModule.getId());
            map1.put("label", parentModule.getName());
            map1.put("rid", parentModule.getResourceID());
            map1.put("nodeType", "systemModule");
            map1.put("intent", parentModule.getName());
            // 父模块下所有的模块对象
            List<SystemModule> systemModules = collect4.get(parentModule.getResourceID());
            // 父模块下所有的模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream()
                    .map(module -> moduleGroup.get(String.valueOf(module.getResourceID())))
                    .collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            systemModuleNodes.add(map1);
        });

        if (systemModuleNodes.size() > 0) {
            List<Map<String, Object>> treeList2 = findSystemModuleToSystemModule(systemModulesParent, systemModuleNodes,
                    treeList, testSystemResourceID);
            return treeList2;
        } else {
            return treeList;
        }
    }

    /**
     * @Title: findbyTradeResourceID
     * @Description: 根据当前交易查询其下的所有案例
     * @Param: "[tradeResourceID]"
     * @Return: "java.util.List<TestCase>"
     * @Author: xpp
     * @Date: 2019/11/6
     */
    @Override
    public List<TestCase> findbyTradeResourceID(Long tradeResourceID) {
        return testCaseDao.findbyTradeResourceID(tradeResourceID);
    }

    /**
     * @Title findbyTradeResourceIDList
     * @Description 根据交易集合查询案例
     * @Params [collect]
     * @Return [List<TestCase>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    @Override
    public List<TestCase> findbyTradeResourceIDList(List<Long> collect) {
        return testCaseDao.findbyTradeResourceIDList(collect);
    }

    /**
     * @return com.jettech.dto.Result
     */
	/*@Override
	public Result findByTradeAndOptions(Map paramMap) {
		String tradeResourceID = String
				.valueOf(paramMap.get("tradeResourceID") == null ? "" : paramMap.get("tradeResourceID")).trim();
		String demandResourceID = String
				.valueOf(paramMap.get("demandResourceID") == null ? "" : paramMap.get("demandResourceID")).trim();
		String testEnviroment = String
				.valueOf(paramMap.get("testEnviroment") == null ? "" : paramMap.get("testEnviroment"));
		if ("".equals(tradeResourceID) || "".equals(demandResourceID) || "".equals(testEnviroment)) {
			Result.renderError("参数非法!");
			return null;
		}
		String userNumber = String.valueOf(paramMap.get("userNumber"));
		String createUser = paramMap.get("createUser") == null ? "" : String.valueOf(paramMap.get("createUser")).trim();
		String isNegative = paramMap.get("isNegative") == null ? "" : String.valueOf(paramMap.get("isNegative")).trim();
		String caseType = paramMap.get("caseType") == null ? "" : String.valueOf(paramMap.get("caseType")).trim();
		String caseId = paramMap.get("caseId") == null ? "" : String.valueOf(paramMap.get("caseId")).trim();// 案例编号、导入案例编号或测试意图
		int pageInt = paramMap.get("page") == null ? 1 : Integer.parseInt(paramMap.get("page").toString());
		int pageSizeInt = paramMap.get("pageSize") == null ? 10 : Integer.parseInt(paramMap.get("pageSize").toString());
		int total = testCaseDao.findCountByTradeResourceID(tradeResourceID, createUser, isNegative, caseType,
				demandResourceID, caseId, testEnviroment, userNumber);
		if (total == 0) {
			return Result.renderSuccess();
		}
		double totalPage = Math.ceil((double) total / pageSizeInt);
		int startIndex = (pageInt - 1) * pageSizeInt;
		List<TestCase> list = testCaseDao.findByTradeAndOptions(tradeResourceID, startIndex, pageSizeInt, createUser,
				isNegative, caseType, demandResourceID, caseId, testEnviroment, userNumber);

		// 案例类型
		String token = HttpRequestUtils.getCurrentRequestToken();
		Result r = basicService.findByName("CASETYPE", token);
		List<Map<String, String>> l = (List<Map<String, String>>) r.getObj();
		// 根据数据字典进行数据内容转换
		for (TestCase t : list) {
			String s1 = l.stream().filter(s -> s.get("value").equals(t.getCaseType())).findFirst()
					.map(s -> s.get("textName")).orElse("");
			t.setCaseType(s1);
		}

		HashMap<String, Object> resultMap = new HashMap<>();
		resultMap.put("total", total);
		resultMap.put("pageSize", pageSizeInt);
		resultMap.put("totalPage", totalPage);
		resultMap.put("page", pageInt);
		resultMap.put("pageData", list);
		return Result.renderSuccess(resultMap);
	}*/
    @SuppressWarnings("unchecked")
    @Override
    public Result findByTradeAndOptions(Map paramMap) {
        String resourceID = String.valueOf(paramMap.get("resourceID") == null ? "0" : paramMap.get("resourceID")).trim();
        String nodeType = String.valueOf(paramMap.get("type") == null ? "root" : paramMap.get("type")).trim();
        String testTaskResourceID = String.valueOf(paramMap.get("testTaskResourceID") == null ? "" : paramMap.get("testTaskResourceID")).trim();
//        if (StringUtils.isEmpty(testTaskResourceID)) {
//            return Result.renderError("参数非法!");
//        }
        String userNumber = String.valueOf(paramMap.get("userNumber"));
        String createUser = paramMap.get("createUser") == null ? "" : String.valueOf(paramMap.get("createUser")).trim();
        String isNegative = paramMap.get("isNegative") == null ? "" : String.valueOf(paramMap.get("isNegative")).trim();
        String caseType = paramMap.get("caseType") == null ? "" : String.valueOf(paramMap.get("caseType")).trim();
        String caseId = paramMap.get("caseId") == null ? "" : String.valueOf(paramMap.get("caseId")).trim();// 案例编号、导入案例编号或测试意图
        String caseName = paramMap.get("caseName") == null ? "" : String.valueOf(paramMap.get("caseName")).trim();
        String intent = paramMap.get("intent") == null ? "" : String.valueOf(paramMap.get("intent")).trim();
        String testMode = paramMap.get("testMode") == null ? "" : String.valueOf(paramMap.get("testMode")).trim();// 测试方式
        String tagResourceIDs = paramMap.get("tagResourceIDs") == null ? "" : String.valueOf(paramMap.get("tagResourceIDs")).trim();
        String coverage = paramMap.get("coverage") == null ? "" : String.valueOf(paramMap.get("coverage")).trim();
        int pageInt = paramMap.get("page") == null ? 1 : Integer.parseInt(paramMap.get("page").toString());
        int pageSizeInt = paramMap.get("pageSize") == null ? 10 : Integer.parseInt(paramMap.get("pageSize").toString());
        if (testMode == null || "".equals(testMode)) testMode = "0";
        int total = 0;
        double totalPage = 0;
        List<Map<String, Object>> list = new ArrayList<>();
//        List<Trade> trades = tradeService.findSelectedTradeByTaskResourceID(testTaskResourceID);

        List<Map<String, Object>> tradeNodes = tradeService.findAllTradeByParentResIDAndTaskResID
                (testTaskResourceID, nodeType, resourceID);//根据父级节点及类型查询下级及子级节点所有交易

        List<Long> tradeResIds = tradeNodes.stream().map(x -> Long.valueOf(x.get("resourceID").toString()))
                .collect(Collectors.toList());

        if (tradeResIds.isEmpty()) {
            return Result.renderError("没有交易数据！");
        }
        //处理测试方式数据
        testMode = BinaryDecimalUtil.DicValToBin(testMode);

		list = testCaseDao.findByTradeResIdsAndTaskResIdPage(tradeResIds, testTaskResourceID,
                pageInt, pageSizeInt, true, createUser, isNegative, caseType, caseId, caseName, intent, testMode,
                tagResourceIDs, userNumber, coverage);

		total = (int) ((Page)list).getTotal();
		totalPage = Math.ceil(1.0 * total / pageSizeInt);

		if (total == 0) {
            return Result.renderSuccess();
        }

        Map<String, List<Map<String, Object>>> tagMap = new HashMap<>();
        if (!list.isEmpty()) {
            //案例的rid集合
            List<String> relationResourceIDs = list.stream().map(e -> String.valueOf(e.get("resourceID"))).collect(Collectors.toList());
            //查询案例相关的标签
            if (!relationResourceIDs.isEmpty()) {
                List<Map<String, Object>> relationData = testCaseDao.findRelationResourceTags(relationResourceIDs, 1);
                if (!relationData.isEmpty()) {
                    tagMap = relationData.stream().collect(Collectors.groupingBy(e -> String.valueOf(e.get("relationResourceID"))));
                }
            }
        }

        List<Map<String, Object>> resultList = new ArrayList<>();
        if (!list.isEmpty()) {
            String token = HttpRequestUtils.getCurrentRequestToken();
            Result<?> result = basicService.findFieldDataByFieldType(token);
            Map<String, Object> map = (Map<String, Object>) result.getObj();
            Result<?> resultDateType = basicService.findFieldTypeIsDate(token);
            List<String> resultDateTypeMap = (List<String>) resultDateType.getObj();
            //查询案例编写评审状态字典值
            Map<Object,Object> reviewStatusDics = redisUtils.getHashEntries("评审状态");
            Map<String, String> reviewStatusMap = new HashMap<>();
            if (reviewStatusDics != null){
                reviewStatusMap = reviewStatusDics.entrySet().stream()
                        .collect(Collectors.toMap(x-> x.getKey().toString(), x->x.getValue().toString()));
            }
            Map<Object,Object> tmDics = redisUtils.getHashEntries("测试方式");
            Map<String, String> tmMap = new HashMap<>();
            if (tmDics != null){
                tmMap = tmDics.entrySet().stream()
                        .collect(Collectors.toMap(x-> x.getKey().toString(), x->x.getValue().toString()));
            }
            //20211009dingwl
            //查询案例加入的执行范围的案例的resourceid集合,通过交易resourceID查询
            //List<String> testCaseResourceIdList = testCaseDao.queryExecuteScopeResourceIdList(tradeResIds);
            //查询案例是否关联ui脚本,通过ui脚本ids
            //Map<String, List<String>> scriptMap = this.getApiAndUiScriptId(list);

            for (Map<String, Object> tc : list) {
                Long tradeResId = (Long) tc.get("tradeResourceID");
                Map<String, Object> trade = tradeNodes.stream().filter(x -> Long.valueOf(x.get("resourceID").toString()).equals(tradeResId))
                        .findAny().orElse(null);
                String location = null;
                if (trade != null) {
                    location = trade.get("path").toString();
                }
                tc.put("location", location);
                tc.put("tags", tagMap.get(String.valueOf(tc.get("resourceID"))) == null ? new ArrayList<>() : tagMap.get(String.valueOf(tc.get("resourceID"))));
                for (Map.Entry<String, Object> entry : tc.entrySet()) {
                    if ("maintenanceTime".equals(entry.getKey())) {
                        if (!org.springframework.util.StringUtils.isEmpty(entry.getValue())) {
                            String date = String.valueOf(entry.getValue());
                            date = date.substring(0, 10);
                            tc.put(entry.getKey(), date);
                        }
                    }

                    //如果是日期字段，转换成字符串日期格式前端列表展示，不可用时间戳
                    if (!resultDateTypeMap.isEmpty() && resultDateTypeMap.size() > 0 && resultDateTypeMap.contains(entry.getKey())) {
                        if (!org.springframework.util.StringUtils.isEmpty(entry.getValue())) {
                            try {
                                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                                String format = df.format(new Date(Long.valueOf(entry.getValue().toString())));
                                tc.put(entry.getKey(), format);
                            } catch (Exception e) {
                                tc.put(entry.getKey(), entry.getValue().toString());
                            }
                        }
                    }
                    if (!map.isEmpty() && map.containsKey(entry.getKey())) {
                    	if(entry.getKey().toString().equals("testMode")){//测试方式的存储是使用二进制，不适用以下方法拆分转换 2022/1025
                        	continue;
                        }
                        Map<String, String> values = (Map<String, String>) map.get(entry.getKey());
                        String names = null;
                        Object flag = entry.getValue();
                        if (flag != null) {
                            names = flag.toString();
                        }
                        String resultValue = "";
                        if (names != null && !"".equals(names)) {
                            String[] arr = names.split(",");
                            for (String str : arr) {
                                resultValue = "".equals(resultValue) ? values.get(str) : resultValue + "," + values.get(str);
                            }
                        }
                        tc.put(entry.getKey(), resultValue);
                    }

                }
                //评审状态转换字典值
                if (!org.springframework.util.StringUtils.isEmpty(tc.get("reviewStatus"))) {
                    if (isNumeric(tc.get("reviewStatus").toString())) {
                        tc.put("reviewStatus", reviewStatusMap.get(tc.get("reviewStatus") == null ? null : tc.get("reviewStatus").toString()));
                    }
                }
                try {
                    //测试方式
                    List<String> testModeList = BinaryDecimalUtil.TenToDicVal(Integer.valueOf(tc.get("testMode").toString()));
                    String testModeValue = "";
                    for (String s : testModeList) {
                        testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) + ",";
                    }

                    tc.put("testModeKey", testModeList);
                    tc.put("testModeValue", "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));

                }catch (Exception ex){
                    ex.printStackTrace();
                }
                //测试覆盖情况(1-手工测试-TM, 2-接口自动化-API, 3-UI自动化-UI),20211009dingwl
                //String testCoverage = this.getTestCoverage(testCaseResourceIdList, scriptMap, tc);
                //tc.put("testCoverage", testCoverage);

                resultList.add(tc);
            }
        }


        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", total);
        resultMap.put("pageSize", pageSizeInt);
        resultMap.put("totalPage", totalPage);
        resultMap.put("page", pageInt);
        resultMap.put("pageData", resultList);
        return Result.renderSuccess(resultMap);
    }

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }
    /**
     * @Title: getApiAndUiScriptId
     * @Description: 获取api和ui的脚本id集合
     * @author: dingwenlong
     * @date: 2021年10月11日
     * @param list
     * @return
     */
	private Map<String, List<String>> getApiAndUiScriptId(List<Map<String, Object>> list) {
		Map<String, List<String>> map = new HashMap<>();
		// 查询案例是否关联ui脚本,通过ui脚本ids
		List<String> uiScriptIds = new ArrayList<>();
		List<String> scriptIdList = list.stream().map(tc -> tc.get("scriptInfoID").toString())
				.collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(scriptIdList)) {
			List<Map<String, Object>> uiScriptList = testCaseDao.queryUiScriptByIds(scriptIdList);
			if (!CollectionUtils.isEmpty(uiScriptList)) {
				// 查询出ui存在的脚本id集合
				uiScriptIds = uiScriptList.stream().map(s -> s.get("id").toString()).collect(Collectors.toList());
			}
		}
		// 查询案例是否关联api脚本,通过api脚本ids
		List<String> apiScriptIds = new ArrayList<>();
		List<String> tradeFlowIdList = list.stream().map(tc -> tc.get("tradeFlowId").toString())
				.collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(tradeFlowIdList)) {
			List<Map<String, Object>> apiScriptList = testCaseDao.queryApiScriptByIds(tradeFlowIdList);
			if (!CollectionUtils.isEmpty(apiScriptList)) {
				// 查询出api存在的脚本id集合
				apiScriptIds = apiScriptList.stream().map(s -> s.get("id").toString()).collect(Collectors.toList());
			}
		}
		map.put("uiScriptIds", uiScriptIds);
		map.put("apiScriptIds", apiScriptIds);
		return map;
	}

	/**
	 * @Title: getTestCoverage
	 * @Description: 测试覆盖情况(1-手工测试-TM, 2-接口自动化-API, 3-UI自动化-UI)
	 * @author: dingwenlong
	 * @date: 2021年10月11日
	 * @param testCaseResourceIdList
	 * @param scriptMap
	 * @param tc
	 * @return
	 */
	private String getTestCoverage(List<String> testCaseResourceIdList, Map<String, List<String>> scriptMap,
			Map<String, Object> tc) {
		List<String> uiScriptIds = scriptMap.get("uiScriptIds");
		List<String> apiScriptIds = scriptMap.get("apiScriptIds");
		StringBuilder sb = new StringBuilder();
		//手工测试
		if (testCaseResourceIdList.contains(tc.get("resourceID").toString())) {
			sb.append("1");
		}
		//api
		Object tradeFlowIdObj = tc.get("tradeFlowId");// api脚本id
		if (!CollectionUtils.isEmpty(apiScriptIds) && tradeFlowIdObj != null
				&& apiScriptIds.contains(tradeFlowIdObj.toString())) {
			if (sb.length() > 0) {
				sb.append(",2");
			} else {
				sb.append("2");
			}
		}
		//ui
		Object scriptIdObj = tc.get("scriptInfoID");// ui脚本id
		if (!CollectionUtils.isEmpty(uiScriptIds) && scriptIdObj != null
				&& uiScriptIds.contains(scriptIdObj.toString())) {
			if (sb.length() > 0) {
				sb.append(",3");
			} else {
				sb.append("3");
			}
		}
		return sb.toString();
	}

    /**
     * @Title: findByTestCase
     * @Description: 查询案例详情
     * @Param: "[currentTestCaseResourceID, userNumber]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @SuppressWarnings("unchecked")
    @Override
    public Result findByTestCase(String currentTestCaseResourceID) {

        if (currentTestCaseResourceID == null || "".equals(currentTestCaseResourceID)) {
            return Result.renderError("参数为空！");
        }
        Map<String, Object> map = testCaseDao.findByCaseResourceID(Long.valueOf(currentTestCaseResourceID));
        //查询多选下拉框的字段
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result<?> result = basicService.findFieldsByFieldType(token);
        Map<String, String> fields = (Map<String, String>) result.getObj();
        if (!fields.isEmpty()) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (fields.containsKey(entry.getKey())) {
                    List<String> valueList = new ArrayList<>();
                    String value = String.valueOf(entry.getValue());
                    if (value != null && !"".equals(value) && !"null".equals(value)) {
                        String[] arr = value.split(",");
                        for (String str : arr) {
                            if (str != null && !"".equals(str) && !"null".equals(str)) {
                                valueList.add(str);
                            }
                        }
                    }
                    map.put(entry.getKey(), valueList);
                }
            }
        }
        String testMode = map.get("testMode") != null ? map.get("testMode").toString().replace("[","").replace("]","") : null ;
        if (StringUtils.isNotEmpty(testMode)){
            //测试方式
            List<String> testModeList = BinaryDecimalUtil.TenToDicVal(Integer.valueOf(testMode));
            Result tmRes = basicService.findByName("TESTMODE", null);
            List<Map<String, Object>> tmList = (List<Map<String, Object>>) tmRes.getObj();
            Map<String, String> tmMap = tmList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));
            String testModeValue = "";
            for (String s : testModeList) {
                testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) + ",";
            }

            map.put("testModeKey", testModeList);
            map.put("testModeValue", "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
        }
        return Result.renderSuccess(map);
    }

    /**
     * @Title: deleteTestCase
     * @Description: 删除案例
     * @Param: "[iDList, userNumber]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @Override
    public Result deleteTestCase(List<String> iDList, String userNumber) {
        try {
            List<TestCase> byTestCaseList = this.findByResourceIDIn(iDList);
            String token = HttpRequestUtils.getCurrentRequestToken();
            Long demandRid = byTestCaseList.get(0).getDemandResourceID();
            List<Long> testcaseResIds = iDList.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            // 查询已经加入到手工执行任务的案例
            List<Map<String, Object>> caseQuoteListByTastCaseResId = manexecuteService.findCaseQuoteByTestCaseResourceIDs(testcaseResIds, token);
            List<Long> testcaseResIdsJoinCaseQuote = caseQuoteListByTastCaseResId.stream().map(m -> {
                if (null != m.get("testCaseResourceID") && !"".equals(m.get("testCaseResourceID").toString())) {
                    return Long.valueOf(m.get("testCaseResourceID").toString());
                }
                return null;
            }).collect(Collectors.toList());
            // 未加入到手工执行任务的案例
            List<Long> deleTestCaseResIdList = testcaseResIds.stream().filter(t -> !testcaseResIdsJoinCaseQuote.contains(t)).collect(Collectors.toList());
            List<TestCase> deleTestCaseList = byTestCaseList.stream().filter(t -> deleTestCaseResIdList.contains(t.getResourceID())).collect(Collectors.toList());

            /*// 删除案例下关联的缺陷、案例引用表(执行结果)中关联数据
            List<Long> collect = byTestCaseList.stream().map(s -> s.getResourceID()).collect(Collectors.toList());

            // 删除缺陷
            bugService.deleteDefectByTestCaseResourceIDList(collect, token);
            // 删除案例引用和执行结果
            manexecuteService.deleteTestCaseQuoteByTestCaseResourceIDList(collect, token);*/
            if (!CollectionUtils.isEmpty(deleTestCaseList)) {
                for (TestCase testCase : deleTestCaseList) {
                    HashMap map=new HashMap();
                    map.put("id",testCase.getResourceID());
                    try {
                        feignJettoUIiMauto.deleteCaseAndVariable(map,token,userNumber);
                    } catch (Exception e) {
                       e.printStackTrace();
                    }
                }
                this.deleteInBatch(deleTestCaseList, userNumber);
            }
            testCaseAddToRecycleBin(deleTestCaseList, userNumber);

            if (CollectionUtils.isEmpty(caseQuoteListByTastCaseResId)) {
                return Result.renderSuccess("当前删除案例" + iDList.size() + "条");
            }
            if (CollectionUtils.isEmpty(deleTestCaseResIdList)) {
                return Result.renderSuccess("当前案例已加入到任务中，不允许删除");
            }
            return Result.renderSuccess("当前勾选案例" + iDList.size() + "条，其中删除执行案例" + deleTestCaseResIdList.size() + "条， " + caseQuoteListByTastCaseResId.size() + "条案例已加入到手工执行任务不允许删除");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("删除异常");
        }
    }

    private void testCaseAddToRecycleBin(List<TestCase> list, String userNumber) {
        LOGGER.info("案例加入回收站数量：" + list.size());
        List<TestCaseRecycleBin> addCaseRecycleBin = new ArrayList<TestCaseRecycleBin>();
        for (TestCase testCase : list) {
            TestCaseRecycleBin testCaseRecycleBin = new TestCaseRecycleBin();
            BeanUtils.copyProperties(testCase, testCaseRecycleBin);
            addCaseRecycleBin.add(testCaseRecycleBin);
        }
        if (addCaseRecycleBin.size() != 0) {
            LOGGER.info("开始移入回收站");
            iTestCaseRecycleBinService.save(addCaseRecycleBin, userNumber);
            LOGGER.info("移入回收站结束");
        }
    }

    /**
     * @return java.util.List<TestCase>
     */
    @Override
    public List<TestCase> findByCaseIdsAndTradeResourceID(List<String> caseIds, String tradeResourceID,
                                                          String demandResourceID) {
        return testCaseDao.findByCaseIdsAndTradeResourceID(caseIds, tradeResourceID, demandResourceID);
    }

    @Override
    public List<TestCase> findBydemandResourceID(Long ResourceID) {
        return testCaseDao.findBydemandResourceID(ResourceID);
    }

    /**
     * @Title: getTestCaseCaseId
     * @Description: 新增案例时回显的案例编号
     * @Param: "[map]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    @Override
    public Result getTestCaseCaseId(Map<String, String> map) {
        String tradeResourceID = map.get("tradeResourceID");
        String demandResourceID = map.get("demandResourceID");
        String format = "";
        // 当前需求
        Demand demand = null;
        if (!StringUtils.isBlank(demandResourceID)) {
            demand = demandService.findByResourceID(Long.valueOf(demandResourceID));
        }
        // 查询交易
        Trade trade = tradeService.findByResourceID(Long.valueOf(tradeResourceID));
        format = this.getNewTestCaseCaseId(trade.getResourceID());

        StringBuilder builder = new StringBuilder();
        // 案例编号：需求编号+测试系统+所属模块+所属交易+编号<000001开始
        if (trade.getModuleResourceID() != null) {
            // 上一级是模块
            SystemModule systemModule = systemModuleService.findByResourceID(trade.getModuleResourceID());
            SystemModule parentsystemModule = getParentNode(systemModule);
            TestSystem testSystem = testSystemService.findByResourceID(parentsystemModule.getTestSystemResourceID());
            if (demand != null) {
                builder.append(demand.getName()).append("-");
            }
            if (testSystem != null) {
                builder.append(testSystem.getName()).append("-");
            }
            if (parentsystemModule != null) {
                builder.append(parentsystemModule.getName()).append("-");
            }
            if (trade != null) {
                builder.append(trade.getName()).append("-");
            }
        } else {
            // 直接在被测系统下挂的交易
            TestSystem testSystem = testSystemService.findByResourceID(trade.getTestSystemResourceID());
            if (demand != null) {
                builder.append(demand.getName()).append("-");
            }
            if (testSystem != null) {
                builder.append(testSystem.getName()).append("-");
            }
            if (trade != null) {
                builder.append(trade.getName()).append("-");
            }
        }
        if (!StringUtils.isBlank(demandResourceID)) {
            builder.append("编号").append(format);
            return Result.renderSuccess(builder.toString());
        } else {
            return Result.renderSuccess(builder.toString());
        }
    }

    /**
     * 加锁
     *
     * @param key   交易id
     * @param value 当前时间+超时时间
     * @return
     */
//    public boolean lock(String key, String value) {
//        if (redisTemplate.opsForValue().setIfAbsent(key, value)) {
//            //这个其实就是setnx命令，只不过在java这边稍有变化，返回的是boolea
//            return true;
//        }
//        //避免死锁，且只让一个线程拿到锁
//        String currentValue = redisTemplate.opsForValue().get(key);
//        //如果锁过期了
//        if (!StringUtils.isEmpty(currentValue) && Long.parseLong(currentValue) < System.currentTimeMillis()) {
//            //获取上一个锁的时间
//            String oldValues = redisTemplate.opsForValue().getAndSet(key, value);
//
//            /*
//               只会让一个线程拿到锁
//               如果旧的value和currentValue相等，只会有一个线程达成条件，
//               因为第二个线程拿到的oldValue已经和currentValue不一样了
//             */
//            if (!StringUtils.isEmpty(oldValues) && oldValues.equals(currentValue)) {
//                return true;
//            }
//        }
//        return false;
//    }

    /**
     * 解锁
     *
     * @param key   交易id
     * @param value 当前时间+超时时间
     */
//    public void unlock(String key, String value) {
//        try {
//            String currentValue = redisTemplate.opsForValue().get(key);
//            if (!StringUtils.isEmpty(currentValue) && currentValue.equals(value)) {
//                redisTemplate.opsForValue().getOperations().delete(key);
//            }
//        } catch (Exception e) {
//            logger.error("『redis分布式锁』解锁异常，{}", e);
//        }
//    }

    /**
     * @Title: getNewTestCaseCaseId
     * @Description: 根据最新要求返回案例编号
     * @Param: "[map]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/24
     */
    @Override
    public String getNewTestCaseCaseId(Long tradeResourceID) {
        // 加锁
//        long time = System.currentTimeMillis() + 1000*10;  //超时时间：10秒，最好设为常量
//        boolean isLock = this.lock(String.valueOf(tradeResourceID), String.valueOf(time));
//        if(!isLock){
//            throw new RuntimeException("系统在忙...");
//        }
        //String format;

        String prefix = "";
        long suffix = 0;
        int suffixLen = 7;
        String reg = "(\\D*)(\\d+)$";
        Pattern p = Pattern.compile(reg);

        //加了个60秒的redis缓存  UI API批量保存时获取案例编号最大值能够递增
        //不完成使用redis缓存是因为案例编号无固定规则  有可能存在不同的前缀  获取到的最大值不固定
        if(!jedisClient.existsStringKey(Redis_Trade_Max_Case_Id_Key + tradeResourceID)) {
            // 查询交易
            // 根据当前交易查询案例，当前交易下案例编号最大的案例
            List<TestCase> maxTestCase = testCaseDao.findMaxTestCasebyTradeResourceID(tradeResourceID);

            TestCaseRecycleBin maxTestCaseRecycleBin = iTestCaseRecycleBinService.findMaxTestCasebyTradeResourceID(tradeResourceID);
            // 根据当前交易查询案例资产库中的案例，交易下案例编号最大的案例
            String assetsCaseId = this.getAssetMaxTestCaseCaseId(tradeResourceID);
            String maxCaseID = "";
            String maxRecycle = "";

            if (maxTestCase.size() > 0){
                maxCaseID = maxTestCase.get(0).getCaseId();
            }
            if (maxTestCaseRecycleBin != null){
                maxRecycle = maxTestCaseRecycleBin.getCaseId();
            }

            String prefix1 = "";
            long suffix1 = 0;

            String prefix2 = "";
            long suffix2 = 0;

            String prefix3 = "";
            long suffix3 = 0;

            if (StringUtils.isNotEmpty(maxCaseID)) {
                Matcher m1 = p.matcher(maxCaseID);
                prefix1 = maxCaseID;
                if (m1.find()) {
                    int groupCount = m1.groupCount();
                    String suffixStr1 = m1.group(groupCount);
                    suffixLen = suffixStr1.length();
                    suffix1 = Long.parseLong(suffixStr1);
                    prefix1 = maxCaseID.substring(0, maxCaseID.length() - suffixStr1.length());
                }
            }

            if (StringUtils.isNotEmpty(assetsCaseId)) {
                Matcher m2 = p.matcher(assetsCaseId);
                prefix2 = assetsCaseId;
                if (m2.find()) {
                    int groupCount = m2.groupCount();
                    String suffixStr2 = m2.group(groupCount);
                    suffix2 = Long.parseLong(suffixStr2);
                    prefix2 = assetsCaseId.substring(0, assetsCaseId.length() - suffixStr2.length());
                }
            }

            if (StringUtils.isNotEmpty(maxRecycle)) {
                Matcher m3 = p.matcher(maxRecycle);
                prefix3 = maxRecycle;
                if (m3.find()) {
                    int groupCount = m3.groupCount();
                    String suffixStr3 = m3.group(groupCount);
                    suffix3 = Long.parseLong(suffixStr3);
                    prefix3 = maxRecycle.substring(0, maxRecycle.length() - suffixStr3.length());
                }
            }
            prefix = prefix1;
            suffix = suffix1;
            if ((Objects.equals(prefix, prefix2) && suffix2 > suffix)) {
                suffix = suffix2;
            }
            if (Objects.equals(prefix, prefix3) && suffix3 > suffix) {
                suffix = suffix3;
            }
        } else {
            String maxCaseID = jedisClient.getString(Redis_Trade_Max_Case_Id_Key + tradeResourceID);
            if (StringUtils.isNotEmpty(maxCaseID)) {
                Matcher m1 = p.matcher(maxCaseID);
                prefix = maxCaseID;
                if (m1.find()) {
                    int groupCount = m1.groupCount();
                    String suffixStr1 = m1.group(groupCount);
                    suffixLen = suffixStr1.length();
                    suffix = Integer.parseInt(suffixStr1);
                    prefix = maxCaseID.substring(0, maxCaseID.length() - suffixStr1.length());
                }
            }
        }
        String maxCaseId = prefix + String.format("%0" + suffixLen + "d", (suffix + 1));
        jedisClient.setString(Redis_Trade_Max_Case_Id_Key + tradeResourceID, maxCaseId, 60);
        return  maxCaseId;
    }
    @Override
    public String getNewTestCaseCaseId(Long tradeResourceID, String maxCaseId1, String maxRecycleBinCaseId, String maxAssetCaseId) {
        // 加锁
//        long time = System.currentTimeMillis() + 1000*10;  //超时时间：10秒，最好设为常量
//        boolean isLock = this.lock(String.valueOf(tradeResourceID), String.valueOf(time));
//        if(!isLock){
//            throw new RuntimeException("系统在忙...");
//        }
        //String format;

        String prefix = "";
        long suffix = 0;
        int suffixLen = 7;
        String reg = "(\\D*)(\\d+)$";
        Pattern p = Pattern.compile(reg);

        //加了个60秒的redis缓存  UI API批量保存时获取案例编号最大值能够递增
        //不完成使用redis缓存是因为案例编号无固定规则  有可能存在不同的前缀  获取到的最大值不固定
        if(!jedisClient.existsStringKey(Redis_Trade_Max_Case_Id_Key + tradeResourceID)) {
            // 查询交易
            // 根据当前交易查询案例，当前交易下案例编号最大的案例
//            List<TestCase> maxTestCase = testCaseDao.findMaxTestCasebyTradeResourceID(tradeResourceID);
//            TestCaseRecycleBin maxTestCaseRecycleBin = iTestCaseRecycleBinService.findMaxTestCasebyTradeResourceID(tradeResourceID);
            // 根据当前交易查询案例资产库中的案例，交易下案例编号最大的案例
//            String assetsCaseId = this.getAssetMaxTestCaseCaseId(tradeResourceID);
            String assetsCaseId = this.getAssetMaxTestCaseCaseId(maxAssetCaseId);
            String maxCaseID = "";
            String maxRecycle = "";

//            if (maxTestCase.size() > 0){
//                maxCaseID = maxTestCase.get(0).getCaseId();
//            }
//            if (maxTestCaseRecycleBin != null){
//                maxRecycle = maxTestCaseRecycleBin.getCaseId();
//            }
                maxCaseID = null != maxCaseId1 ? maxCaseId1 : "";
                maxRecycle = null != maxRecycleBinCaseId ? maxRecycleBinCaseId : "";

            String prefix1 = "";
            long suffix1 = 0;

            String prefix2 = "";
            long suffix2 = 0;

            String prefix3 = "";
            long suffix3 = 0;

            if (StringUtils.isNotEmpty(maxCaseID)) {
                Matcher m1 = p.matcher(maxCaseID);
                prefix1 = maxCaseID;
                if (m1.find()) {
                    int groupCount = m1.groupCount();
                    String suffixStr1 = m1.group(groupCount);
                    suffixLen = suffixStr1.length();
                    suffix1 = Long.parseLong(suffixStr1);
                    prefix1 = maxCaseID.substring(0, maxCaseID.length() - suffixStr1.length());
                }
            }

            if (StringUtils.isNotEmpty(assetsCaseId)) {
                Matcher m2 = p.matcher(assetsCaseId);
                prefix2 = assetsCaseId;
                if (m2.find()) {
                    int groupCount = m2.groupCount();
                    String suffixStr2 = m2.group(groupCount);
                    suffix2 = Long.parseLong(suffixStr2);
                    prefix2 = assetsCaseId.substring(0, assetsCaseId.length() - suffixStr2.length());
                }
            }

            if (StringUtils.isNotEmpty(maxRecycle)) {
                Matcher m3 = p.matcher(maxRecycle);
                prefix3 = maxRecycle;
                if (m3.find()) {
                    int groupCount = m3.groupCount();
                    String suffixStr3 = m3.group(groupCount);
                    suffix3 = Long.parseLong(suffixStr3);
                    prefix3 = maxRecycle.substring(0, maxRecycle.length() - suffixStr3.length());
                }
            }
            prefix = prefix1;
            suffix = suffix1;
            if ((Objects.equals(prefix, prefix2) && suffix2 > suffix)) {
                suffix = suffix2;
            }
            if (Objects.equals(prefix, prefix3) && suffix3 > suffix) {
                suffix = suffix3;
            }
        } else {
            String maxCaseID = jedisClient.getString(Redis_Trade_Max_Case_Id_Key + tradeResourceID);
            if (StringUtils.isNotEmpty(maxCaseID)) {
                Matcher m1 = p.matcher(maxCaseID);
                prefix = maxCaseID;
                if (m1.find()) {
                    int groupCount = m1.groupCount();
                    String suffixStr1 = m1.group(groupCount);
                    suffixLen = suffixStr1.length();
                    suffix = Integer.parseInt(suffixStr1);
                    prefix = maxCaseID.substring(0, maxCaseID.length() - suffixStr1.length());
                }
            }
        }
        String maxCaseId = prefix + String.format("%0" + suffixLen + "d", (suffix + 1));
        jedisClient.setString(Redis_Trade_Max_Case_Id_Key + tradeResourceID, maxCaseId, 60);
        return  maxCaseId;
    }

    @Override
    public Result findTradeTreebyTestCaseResourceID(List<Map<Long, String>> maps, String testSystemResourceID) {
        // 树结构容器
        List<Map<String, Object>> treeList = new ArrayList<Map<String, Object>>();
        // 案例业务主键
        List<Long> testCaseResouceIDList = maps.stream().map(s -> s.keySet()).flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<String> testCaseResourceIDs = testCaseResouceIDList.stream().map(s -> String.valueOf(s))
                .collect(Collectors.toList());
        // 所有的案例对象
        List<TestCase> testCaseList = this.findByResourceIDIn(testCaseResourceIDs);
        // 根据交易对案例对象分组
        Map<Long, List<TestCase>> testCaseToTrade = testCaseList.stream()
                .collect(Collectors.groupingBy(testCase -> testCase.getTradeResourceID()));
        // 所有的交易对象
        List<Trade> tradeList = tradeService.findByResourceIDIn(new ArrayList<>(testCaseToTrade.keySet()).stream()
                .map(s -> String.valueOf(s)).collect(Collectors.toList()));
        // 交易节点(以及节点下的案例节点)
        List<Map<String, Object>> tradeNodes = tradeList.stream().map(trade -> {
            Map<String, Object> tradeMap = new HashMap<>();
            tradeMap.put("id", trade.getId());
            tradeMap.put("label", trade.getName());
            tradeMap.put("rid", trade.getResourceID());
            tradeMap.put("nodeType", "trade");
            return tradeMap;
        }).collect(Collectors.toList());
        // 根据交易节点的rid分组
        Map<String, List<Map<String, Object>>> rid = tradeNodes.stream()
                .collect(Collectors.groupingBy(s -> String.valueOf(s.get("rid"))));
        // 根据是否在被测系统下分组
        Map<Boolean, List<Trade>> collect = tradeList.stream()
                .collect(Collectors.partitioningBy(trade -> null == trade.getModuleResourceID()));
        // 直接在被测系统下的交易
        List<Trade> trades1 = collect.get(true);
        // 根据被测系统的rid对交易分组
        Map<Long, List<Trade>> collect1 = trades1.stream()
                .collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 根据当前被测系统下交易对象组装下级节点
        for (Long s : collect1.keySet()) {
            // 当前交易的上一级被测系统
            TestSystem testSystem = testSystemService.findByResourceID(s);
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", testSystem.getId());
            map1.put("label", testSystem.getName());
            map1.put("rid", testSystem.getResourceID());
            map1.put("nodeType", "testSystem");
            map1.put("isOpen", "false");
            if (!org.springframework.util.StringUtils.isEmpty(testSystemResourceID)
                    && String.valueOf(s).equals(testSystemResourceID)) {
                map1.put("isOpen", "true");
            }
            // 当前被测系统下的所有交易对象
            List<Trade> trades = collect1.get(testSystem.getResourceID());
            // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
            List<List<Map<String, Object>>> collect2 = trades.stream()
                    .map(t -> rid.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect2.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            treeList.add(map1);
        }
        // 上一级是模块的交易对象
        List<Trade> trades2 = collect.get(false);
        if (trades2.size() > 0) {
            // 上一级是模块的交易节点
            List<Map<String, Object>> tradeNodes2 = trades2.stream()
                    .map(t -> rid.get(String.valueOf(t.getResourceID()))).flatMap(Collection::stream)
                    .collect(Collectors.toList());
            // 通过模块下的交易递归查询父节点
            List<Map<String, Object>> treeList2 = findTradeToSystemModule(trades2, rid, treeList, testSystemResourceID);
            // 按照每条案例向上级查询的线路，定义的被测系统会多次重复，需要进行去重合并
            List<Map<String, Object>> collect2 = treeList2.stream().distinct().collect(Collectors.toList());

            return Result.renderSuccess(treeList2);
        }
        return Result.renderSuccess(treeList);
    }

    @Override
    public Result updateTestCaseByTrade() throws Exception {
        // 查询所有的交易
        List<Trade> all = tradeService.findAll();
        // 根据每个交易查询其下的案例
        for (Trade t : all) {
            List<TestCase> testCaseList = this.findbyTradeResourceID(t.getResourceID());
            // 递增改变每个案例的编号
            if (testCaseList.size() > 0) {
                int i = 0000001;
                for (TestCase c : testCaseList) {
                    c.setCaseId(String.format("%07d", i++));
                }
                testCaseDao.update(testCaseList);
            }
        }
        return Result.renderSuccess();
    }

    /**
     * @Title: getParentNode
     * @Description: 递归向上查询最顶级模块
     * @Param: "[systemModule]"
     * @Return: "SystemModule"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    public SystemModule getParentNode(SystemModule systemModule) {
        // 递归查询上级模块
        if (systemModule.getParentResourceID().equals(systemModule.getTestSystemResourceID())) {
            return systemModule;
        } else {
            // 查询父级对象
            systemModule = systemModuleService.findByResourceID(systemModule.getParentResourceID());
            return getParentNode(systemModule);
        }
    }

    /**
     * @param @param  demandResourceIDs
     * @param @return 参数
     * @return List<Map < String, Object>> 返回类型
     * @throws @Title: findCountNumberByDemandRids
     * @Description: 根据需求查询需求下手工编写案例数
     */
    @Override
    public List<Map<String, Object>> findCountNumberByDemandRids(List<String> demandResourceIDs, String userNumber) {

        return testCaseDao.findCountNumberByDemandRids(demandResourceIDs, userNumber);
    }

    @Override
    public IPage<TestCase> findBySystemModule(Long moduleResourceID, Long demandResourceID,
                                              IPage<TestCase> testCasePage) {
        return testCaseDao.findBySystemModule(moduleResourceID, demandResourceID, testCasePage);
    }

    /**
     * @param demandResourceIDs
     * @param "userNumber"
     * @return List<Map < String, Object>>
     * @Title findTotalNumberByDemandRids
     * @Description 查询需求下的所有案例总数
     * <AUTHOR>
     * @data Jan 10, 20205:44:48 PM
     */
    @Override
    public List<Map<String, Object>> findTotalNumberByDemandRids(List<String> demandResourceIDs) {

        return testCaseDao.findTotalNumberByDemandRids(demandResourceIDs);
    }

    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws <AUTHOR>
     * @Title: InitAssetsLeftTree
     * @description: 引用案例资产库左侧树
     * @date 2020/1/13 10:50
     */
    @Override
    public Result InitAssetsLeftTree() {
        return testSystemService.findSinglePointLeftTreeByProjectResourceID(String.valueOf(0));
    }

    /**
     * @param "[map]"
     * @return com.jettech.dto.Result
     * @throws <AUTHOR>
     * @Title: InitAssetsTestCaseTables
     * @description: 初始化资产库案例列表
     * @date 2020/1/13 10:59
     */
    @Override
    public Result InitAssetsTestCaseTables(HashMap map) {
        String taskResourceID = String.valueOf(map.get("taskResourceID"));
        if (!StringUtils.isEmpty(taskResourceID)) {
            String token = HttpRequestUtils.getCurrentRequestToken();
            Map<String, String> testPlan = manexecuteService.findTestPlanByTestTaskResourceID(Long.valueOf(taskResourceID), token);
            map.put("testEnviroment", testPlan.get("testStage"));
        }
        return feignDataDesignToAssets.findByTradeAndOptions(map);
    }

    /**
     * @param "[map]"
     * @param "userNumber"
     * @return com.jettech.dto.Result
     * @throws <AUTHOR>
     * @Title: QuoteTestCase
     * @description: 引用案例
     * @date 2020/1/13 13:56
     */
    @Override
    public Result QuoteTestCase(Map map) throws Exception {
        String userNumber = (String) map.get("userNumber");
        String testtaskResourceID = (String) map.get("testtaskResourceID");
//        if (org.springframework.util.StringUtils.isEmpty(testtaskResourceID)) {
//            return Result.renderError("任务的testtaskResourceID为空！");
//        }
        String demandResourceID = "";
        if (!StringUtils.isEmpty(testtaskResourceID)) {
            Map<String, Object> testtaskFind = tradeService.findTestTaskByTestTaskResourceID(testtaskResourceID);
            if (testtaskFind != null) {
                demandResourceID = String.valueOf(testtaskFind.get("demandResourceID"));
            }
        }

        String testEnviroment = (String) map.get("testEnviroment");
        List<Map<String, Object>> listMap = (List<Map<String, Object>>) map.get("list");

        List<String> testCaseRids = new ArrayList<String>();
        for (Map<String, Object> maps : listMap) {
            // 案例rid
            testCaseRids.addAll((List<String>) maps.get("testCaseResourceIDs"));
        }
        String token = HttpRequestUtils.getCurrentRequestToken();
        JettechUserDTO user = basicService.findByNumber(userNumber, token);
        Result result = feignDataDesignToAssets.findByTestCaseResourceIDs(testCaseRids, token);
        if (result.getCode() != 20000)
            return result;
        List<Map<String, Object>> testCaseList = (List<Map<String, Object>>) result.getObj();
        if (testCaseList.isEmpty())
            return Result.renderError("案例不存在！");

        Map<String, List<Map<String, Object>>> tradeGroupMap = testCaseList.stream()
                .collect(Collectors.groupingBy(e -> e.get("tradeResourceID").toString()));

        Set<String> keySet = tradeGroupMap.keySet();
        Iterator<String> iterator = keySet.iterator();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ArrayList<TestCase> updateList = new ArrayList<>();
        ArrayList<TestCase> saveList = new ArrayList<>();
        Map<String, String> testCaseCaseIdMap = new HashMap<>();
        while (iterator.hasNext()) {
            String tradeResourceID = iterator.next();
            List<Map<String, Object>> tradeCaseList = tradeGroupMap.get(tradeResourceID);
            List<String> caseIds = tradeCaseList.stream().map(e -> e.get("caseId").toString())
                    .collect(Collectors.toList());
            List<TestCase> hasTcList = this.findByCaseIdsAndTradeResourceID(caseIds, tradeResourceID, demandResourceID);
            List<String> list = hasTcList.stream().map(e -> e.getCaseId()).collect(Collectors.toList());
            Map<Boolean, List<Map<String, Object>>> col = tradeCaseList.stream()
                    .collect(Collectors.groupingBy(e -> list.contains(e.get("caseId").toString())));
            List<Map<String, Object>> trueList = col.get(true);
            List<Map<String, Object>> falseList = col.get(false);

            if (trueList != null) {
                for (Map<String, Object> e : trueList) {
                    TestCase tc = hasTcList.stream().filter(f -> f.getCaseId().equals(e.get("caseId").toString()))
                            .findFirst().get();
                    updateFillTestCase(tradeResourceID, sdf, e, tc, demandResourceID, testEnviroment, true, testtaskResourceID, testCaseCaseIdMap);
                    updateList.add(tc);
                }
            }
            if (falseList != null) {
                for (Map<String, Object> e : falseList) {
                    TestCase tc = new TestCase();
                    FillTestCase(tradeResourceID, sdf, e, tc, demandResourceID, testEnviroment, false,
                            user.getUserName(), testtaskResourceID, testCaseCaseIdMap);
                    //引用的案例重置评审状态
                    tc.setReviewStatus("");
                    saveList.add(tc);
                }
            }
        }

        this.save(saveList, userNumber);
        this.update(updateList, userNumber);
        return Result.renderSuccess();
    }

    private void FillTestCase(String tradeResourceID, SimpleDateFormat sdf, Map<String, Object> map, TestCase tc,
                              String demandResourceID, String testEnviroment, boolean isUpdate, String userName, String testtaskResourceID, Map<String, String> testCaseCaseIdMap) throws ParseException {
        // 纯数字递增的id
        if (testCaseCaseIdMap.get(tradeResourceID) == null) {
            String testCaseCaseId = this.getNewTestCaseCaseId(Long.valueOf(tradeResourceID));
            testCaseCaseIdMap.put(tradeResourceID, testCaseCaseId);
        }
        if (isUpdate) {
            tc.setCaseId(map.get("caseId").toString());
        } else {
            tc.setCaseId(testCaseCaseIdMap.get(tradeResourceID));
        }
        tc.setName(String.valueOf(map.get("name")));
        tc.setCaseEditId(testCaseCaseIdMap.get(tradeResourceID));
        tc.setCasetLevel(map.get("casetLevel")==null?null:String.valueOf(map.get("casetLevel")));
        tc.setCaseType(map.get("caseType")==null?null:String.valueOf(map.get("caseType")));
        tc.setComment(String.valueOf(map.get("comment") == null ? "" : map.get("comment")));
        tc.setExpectedResult(map.get("expectedResult")==null?null:String.valueOf(map.get("expectedResult")));
        tc.setIntent(map.get("intent")==null?null:String.valueOf(map.get("intent")));
        tc.setIsNegative(map.get("isNegative")==null?null:String.valueOf(map.get("isNegative")));
        tc.setPreconditions(String.valueOf(map.get("preconditions") == null ? "" : map.get("preconditions")));
        tc.setTestStep(map.get("testStep")==null?null:String.valueOf(map.get("testStep")));
        tc.setTradeResourceID(Long.valueOf(tradeResourceID));
        tc.setTimingName(String.valueOf(map.get("timingName") == null ? "" : map.get("timingName")));
        // tc.setMaintainer(String.valueOf(map.get("maintainer")));
        tc.setMaintainer(userName);
        // tc.setMaintenanceTime(map.get("maintenanceTime") == null ? new Date() :
        // sdf.parse(String.valueOf(map.get("maintenanceTime"))));
        tc.setMaintenanceTime(new Date());
        tc.setReviewStatus(map.get("reviewStatus")==null?null:String.valueOf(map.get("reviewStatus")));
        tc.setDemandResourceID(StringUtils.isEmpty(demandResourceID) ? null : Long.valueOf(demandResourceID));
        tc.setTestEnviroment(String.valueOf(map.get("testEnviroment") == null ? "" : map.get("testEnviroment")));
        tc.setTesttaskResourceID(StringUtils.isEmpty(testtaskResourceID) ? null : Long.parseLong(testtaskResourceID));
        tc.setComments1(String.valueOf(map.get("comments1") == null ? "" : map.get("comments1")));
        tc.setComments2(String.valueOf(map.get("comments2") == null ? "" : map.get("comments2")));
        tc.setComments3(String.valueOf(map.get("comments3") == null ? "" : map.get("comments3")));
        tc.setComments4(String.valueOf(map.get("comments4") == null ? "" : map.get("comments4")));
        tc.setComments5(String.valueOf(map.get("comments5") == null ? "" : map.get("comments5")));
        tc.setComments6(String.valueOf(map.get("comments6") == null ? "" : map.get("comments6")));
        tc.setComments7(String.valueOf(map.get("comments7") == null ? "" : map.get("comments7")));
        tc.setComments8(String.valueOf(map.get("comments8") == null ? "" : map.get("comments8")));
        tc.setComments9(String.valueOf(map.get("comments9") == null ? "" : map.get("comments9")));
        tc.setComments10(String.valueOf(map.get("comments10") == null ? "" : map.get("comments10")));
        tc.setTestMode(map.get("testMode") == null ? 1 : Integer.valueOf(String.valueOf(map.get("testMode"))));
        //案例引用系统案例库系统、交易、模块入库
        tc.setTestsystemResourceID(map.get("testsystemResourceID")==null ? null:Long.valueOf(map.get("testsystemResourceID").toString()));
        tc.setTestsystem(map.get("testsystem")==null ? "":map.get("testsystem").toString());
        tc.setTrade(map.get("trade")==null ? "":map.get("trade").toString());
        tc.setSystemmodule(map.get("systemmodule")==null ? "":map.get("systemmodule").toString());
        tc.setSystemmoduleResourceID(map.get("systemmoduleResourceID")==null ? null:Long.valueOf(map.get("systemmoduleResourceID").toString()));
        testCaseCaseIdMap.put(tradeResourceID, String.format("%07d", Integer.valueOf(testCaseCaseIdMap.get(tradeResourceID)) + 1));
    }

    /**
     * @param "[valueOf,       testEnviroment]"
     * @param demandResourceID
     * @return java.util.List<TestCase>
     * @throws <AUTHOR>
     * @Title: findByTradeRidAndTestEnviroment
     * @description: 通过交易rid和测试环境查询案例
     * @date 2020/2/11 11:15
     */
    @Override
    public List<TestCase> findByTradeRidAndTestEnviroment(Long tradeResourceID, String demandResourceID,
                                                          String testEnviroment, String userNumber) {
        return testCaseDao.findByTradeRidAndTestEnviroment(tradeResourceID, demandResourceID, testEnviroment,
                userNumber);
    }

    /**
     * @param "[tradeResourceID, demandResourceID]"
     * @return java.util.List<TestCase>
     * @throws <AUTHOR>
     * @Title: findByTradeResourceIDAndDemadnResourceID
     * @description: 交易rid和需求rid查询
     * @date 2020/2/25 10:47
     */
    @Override
    public List<TestCase> findByTradeResourceIDAndDemandResourceID(Long tradeResourceID, String demandResourceID) {
        return testCaseDao.findByTradeResourceIDAndDemandResourceID(tradeResourceID, demandResourceID);
    }

    /**
     * @param map
     * @return
     * @Title: findTestcasequoteExportedWord
     * @Description: 查询导出引用执行案例到word文档需要数据
     * <AUTHOR>
     * @date 2020年3月13日下午4:30:53
     */
    @Override
    public Result<?> findTestcasequoteExportedWord(Map<String, Object> map) {
        List<Map<String, String>> caseResultMapList = (List<Map<String, String>>) map.get("caseResultMapList");
        List<Map<String, String>> resourceidMapList = (List<Map<String, String>>) map.get("resourceidMapList");
        // 树结构容器
        List<Map<String, Object>> treeList = new ArrayList<Map<String, Object>>();
        // 案例业务主键
        List<String> testCaseResourceIDs = caseResultMapList.stream().map(s -> s.keySet()).flatMap(Collection::stream)
                .collect(Collectors.toList());
        // 所有的案例对象
        List<TestCase> testCaseList = this.findByResourceIDIn(testCaseResourceIDs);
        // 根据交易对案例对象分组
        Map<Long, List<TestCase>> testCaseToTrade = testCaseList.stream()
                .collect(Collectors.groupingBy(testCase -> testCase.getTradeResourceID()));
        // 所有的交易对象
        List<Trade> tradeList = tradeService.findByResourceIDIn(new ArrayList<>(testCaseToTrade.keySet()).stream()
                .map(s -> String.valueOf(s)).collect(Collectors.toList()));
        // 交易节点(以及节点下的案例节点)
        List<Map<String, Object>> tradeNodes = tradeList.stream().map(trade -> {
            Map<String, Object> tradeMap = new HashMap<>();
            tradeMap.put("id", trade.getId());
            tradeMap.put("label", trade.getName());
            tradeMap.put("rid", trade.getResourceID());
            tradeMap.put("nodeType", "trade");
            // 当前交易节点下的所有案例对象
            List<TestCase> testCaseList1 = testCaseToTrade.get(trade.getResourceID());
            // 案例节点
            List<Map<String, Object>> collect2 = testCaseList1.stream().map(testcase -> {
                Map<String, Object> map1 = new HashMap<>();
                // map1.put("id", testcase.getId());
                map1.put("label", testcase.getCaseId());// 案例编号
                map1.put("rid", String.valueOf(testcase.getResourceID()));
                map1.put("nodeType", "testCase");
                String isNegative = testcase.getIsNegative();
                if ("0".equals(isNegative)) {
                    map1.put("isNegative", "反例");// 反例
                } else if ("1".equals(isNegative)) {
                    map1.put("isNegative", "正例");// 正例
                }
                map1.put("intent", testcase.getIntent());// 测试意图
                map1.put("preconditions", testcase.getPreconditions());// 预置条件
                map1.put("testStep", testcase.getTestStep());// 测试步骤
                map1.put("expectedResult", testcase.getExpectedResult());// 预期结果
                String val = "";
                for (Map<String, String> m : caseResultMapList) {
                    val = m.get(String.valueOf(testcase.getResourceID()));
                    if (val != null) {
                        break;
                    }
                }
                if (!StringUtils.isBlank(val)) {
                    // 添加执行结果
                    if ("待执行".equals(val)) {
                        map1.put("caseResult", "");// 执行结果
                    } else {
                        map1.put("caseResult", val);// 执行结果
                    }
                }
                String resourceidVal = "";
                for (Map<String, String> m1 : resourceidMapList) {
                    resourceidVal = m1.get(String.valueOf(testcase.getResourceID()));
                    if (resourceidVal != null) {
                        break;
                    }
                }
                if (!StringUtils.isBlank(resourceidVal)) {
                    // 引用案例RID
                    map1.put("testcasequoteRID", resourceidVal);
                }
                return map1;
            }).collect(Collectors.toList());
            // 当前交易节点下的案例节点
            tradeMap.put("children", collect2);
            return tradeMap;
        }).collect(Collectors.toList());
        // 根据交易节点的rid分组
        Map<String, List<Map<String, Object>>> rid = tradeNodes.stream()
                .collect(Collectors.groupingBy(s -> String.valueOf(s.get("rid"))));
        // 根据是否在被测系统下分组
        Map<Boolean, List<Trade>> collect = tradeList.stream()
                .collect(Collectors.partitioningBy(trade -> null == trade.getModuleResourceID()));
        // 直接在被测系统下的交易
        List<Trade> trades1 = collect.get(true);
        // 根据被测系统的rid对交易分组
        Map<Long, List<Trade>> collect1 = trades1.stream()
                .collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 根据当前被测系统下交易对象组装下级节点
        for (Long s : collect1.keySet()) {
            // 当前交易的上一级被测系统
            TestSystem testSystem = testSystemService.findByResourceID(s);
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", testSystem.getId());
            map1.put("label", testSystem.getName());
            map1.put("rid", testSystem.getResourceID());
            map1.put("nodeType", "testSystem");
            // 当前被测系统下的所有交易对象
            List<Trade> trades = collect1.get(testSystem.getResourceID());
            // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
            List<List<Map<String, Object>>> collect2 = trades.stream()
                    .map(t -> rid.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect2.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            treeList.add(map1);
        }
        // 上一级是模块的交易对象
        List<Trade> trades2 = collect.get(false);
        if (trades2.size() > 0) {
            // 上一级是模块的交易节点
            List<Map<String, Object>> tradeNodes2 = trades2.stream()
                    .map(t -> rid.get(String.valueOf(t.getResourceID()))).flatMap(Collection::stream)
                    .collect(Collectors.toList());
            // 通过模块下的交易递归查询父节点
            List<Map<String, Object>> treeList2 = findTradeToSystemModuleOfExportedWord(trades2, rid, treeList);
            // 按照每条案例向上级查询的线路，定义的被测系统会多次重复，需要进行去重合并
            List<Map<String, Object>> collect2 = treeList2.stream().distinct().collect(Collectors.toList());

            return Result.renderSuccess(treeList2);
        }
        return Result.renderSuccess(treeList);
    }

    private void updateFillTestCase(String tradeResourceID, SimpleDateFormat sdf, Map<String, Object> map, TestCase tc,
                                    String demandResourceID, String testEnviroment, boolean isUpdate, String testtaskResourceID, Map<String, String> testCaseCaseIdMap) throws ParseException {
        // 纯数字递增的id
        if (testCaseCaseIdMap.get(tradeResourceID) == null) {
            String testCaseCaseId = this.getNewTestCaseCaseId(Long.valueOf(tradeResourceID));
            testCaseCaseIdMap.put(tradeResourceID, testCaseCaseId);
        }
        if (isUpdate) {
            tc.setCaseId(map.get("caseId").toString());
        } else {
            tc.setCaseId(testCaseCaseIdMap.get(tradeResourceID));
        }
        tc.setName(String.valueOf(map.get("name")));
        tc.setCaseEditId(testCaseCaseIdMap.get(tradeResourceID));
        tc.setCasetLevel(map.get("casetLevel")==null?null:String.valueOf(map.get("casetLevel")));
        tc.setCaseType(map.get("caseType")==null?null:String.valueOf(map.get("caseType")));
        tc.setComment(String.valueOf(map.get("comment") == null ? "" : map.get("comment")));
        tc.setExpectedResult(map.get("expectedResult")==null?null:String.valueOf(map.get("expectedResult")));
        tc.setIntent(map.get("intent")==null?null:String.valueOf(map.get("intent")));
        tc.setIsNegative(map.get("isNegative")==null?null:String.valueOf(map.get("isNegative")));
        tc.setPreconditions(String.valueOf(map.get("preconditions") == null ? "" : map.get("preconditions")));
        tc.setTestStep(map.get("testStep")==null?null:String.valueOf(map.get("testStep")));
        tc.setTradeResourceID(Long.valueOf(tradeResourceID));
        tc.setTimingName(String.valueOf(map.get("timingName") == null ? "" : map.get("timingName")));
        tc.setMaintainer(map.get("maintainer")==null?null:String.valueOf(map.get("maintainer")));
        tc.setMaintenanceTime(map.get("maintenanceTime") == null ? new Date()
                : sdf.parse(String.valueOf(map.get("maintenanceTime"))));
        tc.setReviewStatus(map.get("intent")==null?null:String.valueOf(map.get("reviewStatus")));
        tc.setDemandResourceID(StringUtils.isEmpty(demandResourceID) ? null : Long.valueOf(demandResourceID));
        tc.setTestEnviroment(String.valueOf(map.get("testEnviroment") == null ? "" : map.get("testEnviroment")));
        tc.setTesttaskResourceID(StringUtils.isEmpty(testtaskResourceID) ? null : Long.parseLong(testtaskResourceID));
        tc.setComments1(String.valueOf(map.get("comments1") == null ? "" : map.get("comments1")));
        tc.setComments2(String.valueOf(map.get("comments2") == null ? "" : map.get("comments2")));
        tc.setComments3(String.valueOf(map.get("comments3") == null ? "" : map.get("comments3")));
        tc.setComments4(String.valueOf(map.get("comments4") == null ? "" : map.get("comments4")));
        tc.setComments5(String.valueOf(map.get("comments5") == null ? "" : map.get("comments5")));
        tc.setComments6(String.valueOf(map.get("comments6") == null ? "" : map.get("comments6")));
        tc.setComments7(String.valueOf(map.get("comments7") == null ? "" : map.get("comments7")));
        tc.setComments8(String.valueOf(map.get("comments8") == null ? "" : map.get("comments8")));
        tc.setComments9(String.valueOf(map.get("comments9") == null ? "" : map.get("comments9")));
        tc.setComments10(String.valueOf(map.get("comments10") == null ? "" : map.get("comments10")));
        //案例引用系统案例库系统、交易、模块入库
        tc.setTestsystemResourceID(map.get("testsystemResourceID")==null ? null:Long.valueOf(map.get("testsystemResourceID").toString()));
        tc.setTestsystem(map.get("testsystem")==null ? "":map.get("testsystem").toString());
        tc.setTrade(map.get("trade")==null ? "":map.get("trade").toString());
        tc.setSystemmodule(map.get("systemmodule")==null ? "":map.get("systemmodule").toString());
        tc.setSystemmoduleResourceID(map.get("systemmoduleResourceID")==null ? null:Long.valueOf(map.get("systemmoduleResourceID").toString()));
        testCaseCaseIdMap.put(tradeResourceID, String.format("%07d", Integer.valueOf(testCaseCaseIdMap.get(tradeResourceID)) + 1));
    }

    /**
     * 通过TradeResources查询 QiaoHongju
     *
     * @param tradeResourceIDList
     * @param testCasePage
     * @return
     */
    @Override
    public IPage<TestCase> findTestCaseByTradeResourceIDsAndDemandResourceIDPage(List<String> tradeResourceIDList,
                                                                                 String demandResourceID, String testEnviroment, com.baomidou.mybatisplus.extension.plugins.pagination.Page<TestCase> testCasePage) {
        return testCaseDao.findTestCaseByTradeResourceIDsAndDemandResourceIDPage(tradeResourceIDList, demandResourceID,
                testEnviroment, testCasePage);
    }

    /**
     * @param "[addCase, userNumber]"
     * @return java.util.List<TestCase>
     * @throws <AUTHOR>
     * @Title: saveImportList
     * @description: 批量导入案例
     * @date 2020/3/4 10:44
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TestCase> saveImportList(ArrayList<TestCase> addCase, String userNumber) {
        for (TestCase model : addCase) {
            if (model.getCreateUser() == null || StringUtils.isBlank(model.getCreateUser())) {
                model.setCreateUser(userNumber);
            }
            if (model.getResourceID() == null) {
                SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
                model.setResourceID(worker.genNextId());
            }
        }
        return testCaseDao.save(addCase);
    }

    // 通过模块下的交易递归查询父节点
    private List<Map<String, Object>> findTradeToSystemModuleOfExportedWord(List<Trade> trades2,
                                                                            Map<String, List<Map<String, Object>>> tradeNodesGroup, List<Map<String, Object>> treeList) {
        // 上一级是模块的所有交易id
        List<String> collect2 = trades2.stream().map(s -> String.valueOf(s.getModuleResourceID()))
                .collect(Collectors.toList());
        // 根据交易查询到所有的上一级模块
        List<SystemModule> systemModulesA = systemModuleService.findByResourceIDIn(collect2);
        // 对上一级的模块进行去重
        List<SystemModule> systemModules = systemModulesA.stream().distinct().collect(Collectors.toList());
        // 根据模块rid对交易分组
        Map<Long, List<Trade>> moduleToTrades = trades2.stream()
                .collect(Collectors.groupingBy(s -> s.getModuleResourceID()));
        // 交易上一级所有的模块节点
        List<Map<String, Object>> systemModuleNodes = new ArrayList<>();
        systemModules.stream().forEach(s -> {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", s.getId());
            map1.put("label", s.getName());
            map1.put("rid", s.getResourceID());
            map1.put("nodeType", "systemModule");
            // 当前模块下的所有交易对象
            List<Trade> trades = moduleToTrades.get(s.getResourceID());
            // 当前被测系统下的交易节点(根据交易的rid从交易节点分组中获取)
            List<List<Map<String, Object>>> collect = trades.stream()
                    .map(t -> tradeNodesGroup.get(String.valueOf(t.getResourceID()))).collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            systemModuleNodes.add(map1);
        });

        if (systemModuleNodes.size() > 0) {
            // 通过交易的上一级模块递归向上查询父节点
            List<Map<String, Object>> treelist2 = findSystemModuleToSystemModuleOfExportedWord(systemModules,
                    systemModuleNodes, treeList);
            return treelist2;
        } else {
            return null;
        }
    }

    // 通过交易的上一级模块向上查询父节点
    private List<Map<String, Object>> findSystemModuleToSystemModuleOfExportedWord(List<SystemModule> systemModuleList,
                                                                                   List<Map<String, Object>> systemModuleNodes, List<Map<String, Object>> treeList) {
        // 根据模块rid对模块节点分组
        Map<String, List<Map<String, Object>>> moduleGroup = systemModuleNodes.stream()
                .collect(Collectors.groupingBy(s -> String.valueOf(s.get("rid"))));
        // 根据是否是被测系统下模块对象分组
        Map<Boolean, List<SystemModule>> collect1 = systemModuleList.stream()
                .collect(Collectors.partitioningBy(s -> s.getParentResourceID().equals(s.getTestSystemResourceID())));
        // 直接在被测系统下的模块对象
        List<SystemModule> systemModules1 = collect1.get(true);
        // 根据被测系统的rid进行分组（直接在被测系统下的模块对象）
        Map<Long, List<SystemModule>> collect2 = systemModules1.stream()
                .collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 上一级还是模块的当前模块对象
        List<SystemModule> systemModules2 = collect1.get(false);
        // 根据模块父rid对进行分组
        Map<Long, List<SystemModule>> collect4 = systemModules2.stream()
                .collect(Collectors.groupingBy(s -> s.getParentResourceID()));
        // 根据当前模块查询得到的还是模块上一级的模块对象
        List<String> collect5 = systemModules2.stream().map(s -> String.valueOf(s.getParentResourceID()))
                .collect(Collectors.toList());
        List<SystemModule> systemModulesParentA = systemModuleService.findByResourceIDIn(collect5);
        // 对查询得到的上一级模块去重
        List<SystemModule> systemModulesParent = systemModulesParentA.stream().distinct().collect(Collectors.toList());

        // 直接在被测系统下的模块对象,直接循环被测系统
        Set<Long> longs = collect2.keySet();
        longs.stream().forEach(s -> {
            // 查询上一级被测系统
            TestSystem testSystem = testSystemService.findByResourceID(s);
            // 在当前被测系统下的所有模块对象
            List<SystemModule> systemModules = collect2.get(testSystem.getResourceID());
            // 在当前被测系统下的所有模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream()
                    .map(module -> moduleGroup.get(String.valueOf(module.getResourceID())))
                    .collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());

            Optional<Map<String, Object>> ifExist = treeList.stream()
                    .filter(x -> x.get("rid").equals(testSystem.getResourceID())).findFirst();
            // 存在为true
            if (ifExist.isPresent()) {
                // 说明顶级被测系统节点重复,找出重复节点添加
                Map<String, Object> testSystemMap = ifExist.get();
                List<Map<String, Object>> list = (List<Map<String, Object>>) testSystemMap.get("children");
                // 合并两个children元素
                List<Map<String, Object>> collect6 = Stream.of(list, collect3).flatMap(Collection::stream)
                        .collect(Collectors.toList());
                testSystemMap.put("children", collect6);
                // 排除treeList原有集合中的重复元素
                treeList.removeIf(x -> x.get("rid").equals(testSystemMap.get("rid")));
                treeList.add(testSystemMap);
            } else {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", testSystem.getId());
                map1.put("label", testSystem.getName());
                map1.put("rid", testSystem.getResourceID());
                map1.put("children", collect3);
                map1.put("nodeType", "testSystem");
                treeList.add(map1);
            }
        });

        // 上一级还是模块的节点收集器
        // List<Map<String, Object>> systemModules3 = new ArrayList<>();
        systemModuleNodes.clear();
        // 上一级还是模块下的模块对象
        systemModulesParent.stream().forEach(parentModule -> {
            // 当前模块上一级还是模块,组装父节点返回
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", parentModule.getId());
            map1.put("label", parentModule.getName());
            map1.put("rid", parentModule.getResourceID());
            map1.put("nodeType", "systemModule");
            // 父模块下所有的模块对象
            List<SystemModule> systemModules = collect4.get(parentModule.getResourceID());
            // 父模块下所有的模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream()
                    .map(module -> moduleGroup.get(String.valueOf(module.getResourceID())))
                    .collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            systemModuleNodes.add(map1);
        });

        if (systemModuleNodes.size() > 0) {
            List<Map<String, Object>> treeList2 = findSystemModuleToSystemModule(systemModulesParent, systemModuleNodes,
                    treeList);
            return treeList2;
        } else {
            return treeList;
        }
    }

    // 通过交易的上一级模块向上查询父节点
    private List<Map<String, Object>> findSystemModuleToSystemModule(List<SystemModule> systemModuleList,
                                                                     List<Map<String, Object>> systemModuleNodes, List<Map<String, Object>> treeList) {
        // 根据模块rid对模块节点分组
        Map<String, List<Map<String, Object>>> moduleGroup = systemModuleNodes.stream()
                .collect(Collectors.groupingBy(s -> String.valueOf(s.get("rid"))));
        // 根据是否是被测系统下模块对象分组
        Map<Boolean, List<SystemModule>> collect1 = systemModuleList.stream()
                .collect(Collectors.partitioningBy(s -> s.getParentResourceID().equals(s.getTestSystemResourceID())));
        // 直接在被测系统下的模块对象
        List<SystemModule> systemModules1 = collect1.get(true);
        // 根据被测系统的rid进行分组（直接在被测系统下的模块对象）
        Map<Long, List<SystemModule>> collect2 = systemModules1.stream()
                .collect(Collectors.groupingBy(s -> s.getTestSystemResourceID()));
        // 上一级还是模块的当前模块对象
        List<SystemModule> systemModules2 = collect1.get(false);
        // 根据模块父rid对进行分组
        Map<Long, List<SystemModule>> collect4 = systemModules2.stream()
                .collect(Collectors.groupingBy(s -> s.getParentResourceID()));
        // 根据当前模块查询得到的还是模块上一级的模块对象
        List<String> collect5 = systemModules2.stream().map(s -> String.valueOf(s.getParentResourceID()))
                .collect(Collectors.toList());
        List<SystemModule> systemModulesParentA = systemModuleService.findByResourceIDIn(collect5);
        // 对查询得到的上一级模块去重
        List<SystemModule> systemModulesParent = systemModulesParentA.stream().distinct().collect(Collectors.toList());

        // 直接在被测系统下的模块对象,直接循环被测系统
        Set<Long> longs = collect2.keySet();
        longs.stream().forEach(s -> {
            // 查询上一级被测系统
            TestSystem testSystem = testSystemService.findByResourceID(s);
            // 在当前被测系统下的所有模块对象
            List<SystemModule> systemModules = collect2.get(testSystem.getResourceID());
            // 在当前被测系统下的所有模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream()
                    .map(module -> moduleGroup.get(String.valueOf(module.getResourceID())))
                    .collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());

            Optional<Map<String, Object>> ifExist = treeList.stream()
                    .filter(x -> x.get("rid").equals(testSystem.getResourceID())).findFirst();
            // 存在为true
            if (ifExist.isPresent()) {
                // 说明顶级被测系统节点重复,找出重复节点添加
                Map<String, Object> testSystemMap = ifExist.get();
                List<Map<String, Object>> list = (List<Map<String, Object>>) testSystemMap.get("children");
                // 合并两个children元素
                List<Map<String, Object>> collect6 = Stream.of(list, collect3).flatMap(Collection::stream)
                        .collect(Collectors.toList());
                testSystemMap.put("children", collect6);
                // 排除treeList原有集合中的重复元素
                treeList.removeIf(x -> x.get("rid").equals(testSystemMap.get("rid")));
                treeList.add(testSystemMap);
            } else {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("id", testSystem.getId());
                map1.put("label", testSystem.getName());
                map1.put("rid", testSystem.getResourceID());
                map1.put("children", collect3);
                map1.put("nodeType", "testSystem");
                treeList.add(map1);
            }
        });

        // 上一级还是模块的节点收集器
        // List<Map<String, Object>> systemModules3 = new ArrayList<>();
        systemModuleNodes.clear();
        // 上一级还是模块下的模块对象
        systemModulesParent.stream().forEach(parentModule -> {
            // 当前模块上一级还是模块,组装父节点返回
            Map<String, Object> map1 = new HashMap<>();
            map1.put("id", parentModule.getId());
            map1.put("label", parentModule.getName());
            map1.put("rid", parentModule.getResourceID());
            map1.put("nodeType", "systemModule");
            // 父模块下所有的模块对象
            List<SystemModule> systemModules = collect4.get(parentModule.getResourceID());
            // 父模块下所有的模块节点
            List<List<Map<String, Object>>> collect = systemModules.stream()
                    .map(module -> moduleGroup.get(String.valueOf(module.getResourceID())))
                    .collect(Collectors.toList());
            List<Map<String, Object>> collect3 = collect.stream().flatMap(Collection::stream)
                    .collect(Collectors.toList());
            map1.put("children", collect3);
            systemModuleNodes.add(map1);
        });

        if (systemModuleNodes.size() > 0) {
            List<Map<String, Object>> treeList2 = findSystemModuleToSystemModule(systemModulesParent, systemModuleNodes,
                    treeList);
            return treeList2;
        } else {
            return treeList;
        }
    }

    /**
     * 查询手工执行任务下选中保存的案例所属交易Rid
     *
     * @param @return 参数
     * @return List<Trade> 返回类型
     * @throws <AUTHOR>
     * @Title: findSelectedTestCaseByTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    @Override
    public List<String> findTradeRidsBySelectedTestCaseByTaskResourceID(String taskResourceID, String userNumber,
                                                                        List<String> hasQuoteSearchCaseRids, String testPlanResourceID) {

        return testCaseDao.findTradeRidsBySelectedTestCaseByTaskResourceID(taskResourceID, userNumber,
                hasQuoteSearchCaseRids, testPlanResourceID);
    }

    /**
     * 查询手工执行任务下选中保存的案例所属交易Rid
     *
     * @param @return 参数
     * @return List<Trade> 返回类型
     * @throws <AUTHOR>
     * @Title: findSelectedTestCaseByTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    @Override
    public List<String> findTradeRidsBySelectedTestCaseByTaskResourceID(String taskResourceID, String userNumber,
                                                                        List<String> hasQuoteSearchCaseRids) {

        return testCaseDao.findTradeRidsBySelectedTestCaseByTaskResourceID(taskResourceID, userNumber,
                hasQuoteSearchCaseRids);
    }

    /**
     * 根据交易和任务的rid查询当前任务当前交易下的手工编写案例
     *
     * @param @param  map
     * @param @return 参数
     * @return List<TestCase> 返回类型
     * @throws <AUTHOR>
     * @Title: findTestCaseByTaskAndTradeResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    @Override
    public Result<?> findTestCaseByTaskAndTradeResourceID(HashMap<String, Object> map) {
        String taskResourceID = "";
        if (!org.springframework.util.StringUtils.isEmpty(map.get("taskResourceID"))) {
            taskResourceID = String.valueOf(map.get("taskResourceID"));
        } else {
            Result.renderError("参数有误！taskResourceID为空");
        }
        String tradeResourceID = "";
        if (!org.springframework.util.StringUtils.isEmpty(map.get("tradeResourceID"))) {
            tradeResourceID = String.valueOf(map.get("tradeResourceID"));
        } else {
            Result.renderError("参数有误！tradeResourceID为空");
        }
        String rows = "";// 默认为10
        if (!org.springframework.util.StringUtils.isEmpty(map.get("rows"))) {
            rows = map.get("rows").toString();// 默认为10
            if (rows == null || "".equals(rows))
                rows = "10";
        }
        String page = "";// 默认为1
        if (!org.springframework.util.StringUtils.isEmpty(map.get("page"))) {
            page = map.get("page").toString();// 默认为10
            if (page == null || "".equals(page))
                page = "1";
        }
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        PageImpl<Map<String, Object>> data = testCaseDao.findTestCaseByTaskAndTradeResourceID(pageRequest,
                taskResourceID, tradeResourceID);
        return Result.renderSuccess(data);
    }

    /**
     * 通过交易和任务查询保存的案例
     *
     * @param @param  trades
     * @param @param  taskResourceID
     * @param @return 参数
     * @return List<TestCase> 返回类型
     * @throws <AUTHOR>
     * @Title: findTestCasesByTaskAndTrades
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    @Override
    public List<TestCase> findTestCasesByTaskAndTrades(String tradeResourceID, String taskResourceID, String userNumber,
                                                       List<String> hasQuoteSearchCaseRids) {
        return testCaseDao.findTestCasesByTaskAndTrades(tradeResourceID, taskResourceID, userNumber,
                hasQuoteSearchCaseRids);
    }

    /**
     * 通过字典的name查询案例的字典数据
     *
     * @param @param  dicName
     * @param @return 参数
     * @return List<Map < String, Object>> 返回类型
     * @throws <AUTHOR>
     * @Title: findTestCaseDictionaryData
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    @Override
    public List<Map<String, Object>> findTestCaseDictionaryData(String dicName) {

        return testCaseDao.findTestCaseDictionaryData(dicName);
    }

    /**
     * 查询所有人员的name和number
     *
     * @param @return 参数
     * @return List<Map < String, String>> 返回类型
     * @throws <AUTHOR>
     * @Title: findAllUsersNameAndNumber
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    @Override
    public List<Map<String, String>> findAllUsersNameAndNumber() {

        return testCaseDao.findAllUsersNameAndNumber();
    }

    /**
     * 案例资产库：根据最新要求返回最大的案例编号(at_testCase表)
     *
     * @param @return 参数
     * @return String 返回类型
     * @throws <AUTHOR>
     * @Title: getAssetMaxTestCaseCaseId
     * @Description: TODO(这里用一句话描述这个方法的作用)
     */
    public String getAssetMaxTestCaseCaseId(Long tradeResourceID) {
        String format;
        String maxTestCaseCaseID = "";
        // 查询交易
        // 根据当前交易查询案例，当前交易下案例编号最大的案例(案例资产库中)
        maxTestCaseCaseID = testCaseDao.getAssetMaxTestCaseCaseId(tradeResourceID);
        if (!org.springframework.util.StringUtils.isEmpty(maxTestCaseCaseID)) {
            // 从案例编号的最大值
            format = maxTestCaseCaseID;
        } else {
            // 当前交易下还没有案例
            format = "0000000";
        }
        return format;
    }
    public String getAssetMaxTestCaseCaseId(String maxAssetCaseId) {
        String format;
        // 查询交易
        // 根据当前交易查询案例，当前交易下案例编号最大的案例(案例资产库中)
        String maxTestCaseCaseID = null != maxAssetCaseId ? maxAssetCaseId : "";
        if (!org.springframework.util.StringUtils.isEmpty(maxTestCaseCaseID)) {
            // 从案例编号的最大值
            format = maxTestCaseCaseID;
        } else {
            // 当前交易下还没有案例
            format = "0000000";
        }
        return format;
    }

    @Override
    public PageImpl<Map<String, Object>> findCaseInfoPage(PageRequest pageRequest, List<Long> caseResourceID) {
        return testCaseDao.findCaseInfoPage(pageRequest, caseResourceID);
    }

    /**
     * @param demandResourceIDs
     * @return List<Map < String, Object>>
     * @Title findTotalZXNumberByDemandRids
     * @Description 查询需求下的所有执行案例
     * <AUTHOR>
     */
    @Override
    public List<Map<String, Object>> findTotalZXNumberByDemandRids(List<String> demandResourceIDs) {
        return testCaseDao.findTotalZXNumberByDemandRids(demandResourceIDs);
    }

    /**
     * 批量保存案例
     * @return
     */
    @Override
    public Result<?> batchesSaveTestCaseByLimit(List<TestCase> testCaseList, String userNumber) {
        int dataLimit = 1000;
        if (!testCaseList.isEmpty()) {
            int size = testCaseList.size();
            if (dataLimit < size) {
                int part = size % dataLimit == 0 ? size / dataLimit : size / dataLimit + 1;
                int tmpData = 0;
                part=CheckUtil.checkLoop(part);
                for (int i = 0; i < part; i++) {
                    //最后一页可能不足1000条，特殊处理，防止超出边界
                    List<TestCase> list;
                    if (i == part - 1) {
                        list = testCaseList.subList(tmpData, testCaseList.size());
                    } else {
                        list = testCaseList.subList(tmpData, tmpData + 1000);
                        tmpData += 1000;
                    }
                    try{
                        save(list, userNumber);
                    }catch (Exception e){
                        return Result.renderError("案例名称超出长度限制。");
                    }
                }
            } else {
                if (!testCaseList.isEmpty()) {
                    try{
                        save(testCaseList, userNumber);
                    }catch (Exception e){
                        return Result.renderError("案例名称超出长度限制。");
                    }

                }
            }
        }
        return Result.renderSuccess();
    }

    /**
     * 批量更新案例
     */
    @Override
    public void batchesUpdateTestCaseByLimit(List<TestCase> testCaseList, String userNumber) {
        int dataLimit = 1000;
        if (!testCaseList.isEmpty()) {
            int size = testCaseList.size();
            if (dataLimit < size) {
                int part = size % dataLimit == 0 ? size / dataLimit : size / dataLimit + 1;
                int tmpData = 0;
                part=CheckUtil.checkLoop(part);
                for (int i = 0; i < part; i++) {
                    //最后一页可能不足1000条，特殊处理，防止超出边界
                    List<TestCase> list;
                    if (i == part - 1) {
                        list = testCaseList.subList(tmpData, testCaseList.size());
                    } else {
                        list = testCaseList.subList(tmpData, tmpData + 1000);
                        tmpData += 1000;
                    }
                    update(list, userNumber);
                }
            } else {
                if (!testCaseList.isEmpty()) {
                    update(testCaseList, userNumber);
                }
            }
        }
    }

    /**
     * @Title: findCaseIDByTradeResourceID
     * @description: 查询交易下已经存在的全部案例编号
     * <AUTHOR>
     * @date 2020/6/16
     */
    @Override
    public List<String> findCaseIDByTradeResourceID(Long tradeResourceID) {
        return testCaseDao.findCaseIDByTradeResourceID(tradeResourceID);
    }


    /**
     * @param testTaskResourceID
     * @param userNumber
     * @return
     * @Title: commitTestCase
     * @Description: 提交案例
     * <AUTHOR>
     * @date 2020年6月9日 下午6:02:21
     */
    @Override
    public Result<?> commitTestCase(String testTaskResourceID, String state, String userNumber) {

        //查询任务下的编写的所有未提交的案例
        List<TestCase> list = testCaseDao.findByTestTaskResourceIDAndNotCommitted(Long.valueOf(testTaskResourceID));
        if (!list.isEmpty()) {
            //将提交状态设置为：提交
            list.stream().forEach(item -> item.setCommitted(true));
            update(list, userNumber);
        }
        if (!org.springframework.util.StringUtils.isEmpty(state) && state.equals("2")) {
            //更新任务状态为已完成
            Result<?> result = manexecuteService.updateTestTaskStatus(testTaskResourceID, "3", userNumber);
            if (!result.isSuccess()) {
                return Result.renderError("更新任务状态失败！");
            }
        }
        return Result.renderSuccess();
    }

    /**
     * @Title findByTradeRidAndTaskRid
     * @description 查询交易下某一任务的案例
     * @date 20200628
     * <AUTHOR>
     */
    @Override
	public List<TestCase> findByTradeRidAndTaskRid(Long tradeResourceID, Long taskResourceID, String maintainer,
			String isNegative, String caseType, String caseId, String testMode, String testCaseResourceIDs, String leadsource) {
        return testCaseDao.findByTradeRidAndTaskRid(tradeResourceID, taskResourceID, maintainer, isNegative, caseType, caseId, testMode, testCaseResourceIDs, leadsource);
    }

    /**
     * @return
     * @Title: findProjectTestCase
     * @Description: 分页查询项目案例
     * <AUTHOR>
     * @date 2020年7月14日 下午5:21:51
     */
    @Override
    public PageImpl<TestCaseDTO> findProjectTestCase(PageRequest pageRequest, String testSystem, String trade, String caseId, String intent, String caseType, String casetLevel, String resourceID, String nodeType, String demandFlag, String tagResourceIDs, String caseFinalResult, String userNumber) {
        PageImpl<TestCaseDTO> temp = null;
        List<String> resourceIDlist = new ArrayList<>();
        resourceIDlist = testProjectService.getNodeList(nodeType, resourceID, "0", userNumber);
        if (!resourceIDlist.isEmpty()) {
            List<String> testCaseRid = null;
            if (StringUtils.isNotEmpty(tagResourceIDs)) {
                Result res = basicService.findTagRelationByTagResourceID(tagResourceIDs, "3");
                if (res.getCode() != StatusCode.OK) return temp;
                Map<String, Object> obj = (Map<String, Object>) res.getObj();
                List<Map<String, Object>> relationList = (List<Map<String, Object>>) obj.get("relation");
                List<Map<String, Object>> tagList = (List<Map<String, Object>>) obj.get("tag");
                testCaseRid = relationList.stream().map(s -> s.get("relationResourceID").toString()).distinct().collect(Collectors.toList());
                if (testCaseRid == null || testCaseRid.size() == 0) {
                    return temp;
                }
            }

            temp = testCaseDao.findProjectTestCase(pageRequest, testSystem, trade, caseId, intent, caseType, casetLevel,
                    resourceIDlist, demandFlag, testCaseRid, caseFinalResult);

        } else {
            return temp;
        }

        List<TestCaseDTO> models = temp.getContent();
        List<String> caseRids = new ArrayList<>();
        models.stream().forEach(e -> caseRids.add(e.getResourceID().toString()));
        //转换标签
        Result res = null;
        if (caseRids.size() > 0) {
            res = basicService.findTagRelationByRelationRid(caseRids, "3");
        }
        if (res == null) return temp;
        if (res.getCode() != StatusCode.OK) return temp;
        Map<String, Object> obj = (Map<String, Object>) res.getObj();
        List<Map<String, Object>> tagList = (List<Map<String, Object>>) obj.get("tag");
        List<Map<String, Object>> relationList = (List<Map<String, Object>>) obj.get("relation");
        Map<String, List<Map<String, Object>>> relationMap = relationList.stream().collect(Collectors.groupingBy(x -> x.get("relationResourceID").toString()));
        Map<String, Map<String, Object>> tagMap = tagList.stream().collect(Collectors.toMap(x -> x.get("resourceID").toString(), x -> x, (k1, k2) -> k2));
        Map<Object,Object> tmDics = redisUtils.getHashEntries("测试方式");
        Map<String, String> tmMap = new HashMap<>();
        if (tmDics != null){
            tmMap = tmDics.entrySet().stream() .collect(Collectors.toMap(x-> x.getKey().toString(), x->x.getValue().toString()));
        }

        List<TestCaseDTO> content = temp.getContent();
        for (TestCaseDTO testCaseDTO : content) {
            //处理测试步骤和预期结果
            /*testCaseDTO.setTestStep(getText(testCaseDTO.getTestStep()));
            testCaseDTO.setExpectedResult(getText(testCaseDTO.getExpectedResult()));*/
        	//测试方式转换
        	if(testCaseDTO.getTestMode()!=null) {
	            List<String> testModeList = BinaryDecimalUtil.TenToDicVal(Integer.valueOf(testCaseDTO.getTestMode()));
	            String testModeValue = "";
	            for (String s : testModeList) {
	                testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) + ",";
	            }
	            testCaseDTO.setTestModeKey(testModeList);
	            testCaseDTO.setTestModeName("".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
        	}
            //标签处理
            List<Map<String, Object>> list = relationMap.get(testCaseDTO.getResourceID().toString());
            if (list == null || list.isEmpty()) continue;
            ArrayList<Map<String, Object>> returnList = new ArrayList<>();
            for (Map<String, Object> map1 : list) {
                returnList.add(tagMap.get(map1.get("tagResourceID").toString()));
            }
            testCaseDTO.setTagList(returnList);
        }
        // 查询自定义字段为多选或者日期的字段
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result<?> resultDateType = basicService.findFieldTypeIsDateAndSelect(token);
        List<Map<String, Object>> resultDateTypeMap = (List<Map<String, Object>>) resultDateType.getObj();
        if (CollectionUtils.isEmpty(resultDateTypeMap)) {
            return temp;
        }
        Map<String, Map<String, Object>> resultMap = resultDateTypeMap.stream().collect(Collectors.toMap(x -> x.get("name").toString(), Function.identity()));

        // 查询下拉框字段的下拉数据
        Result<?> result = basicService.findFieldDataByFieldType(token);
        Map<String, Object> map = (Map<String, Object>) result.getObj();

        TestCaseDTO model = new TestCaseDTO();
        Field[] fields = model.getClass().getDeclaredFields();
        List<Field> fieldList = Arrays.asList(fields);
        List<Field> dateFieldList = fieldList.stream().filter(x -> resultMap.containsKey(x.getName())).collect(Collectors.toList());

        List<String> selectType = Arrays.asList("2", "3");
        for (Field field : dateFieldList) {
            if (resultMap.containsKey(field.getName())) {
                field.setAccessible(true);
                for (TestCaseDTO testCaseDTO : models) {
                    try {
                        String fieldName = field.getName();
                        if (field.get(testCaseDTO) != null && "5".equals(resultMap.get(fieldName).get("fieldType").toString())) {
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                            String format = df.format(new Date(Long.valueOf(field.get(testCaseDTO).toString())));
                            field.set(testCaseDTO, format);
                        }
                        if (field.get(testCaseDTO) != null && selectType.contains(resultMap.get(fieldName).get("fieldType").toString())) {
                            List<String> keyList = Arrays.asList(field.get(testCaseDTO).toString().split(","));
                            Map<String, String> dataMap = (Map<String, String>) map.get(fieldName);
                            String value = keyList.stream().filter(x -> StringUtils.isNotBlank(x)).map(x -> dataMap.get(x)).collect(Collectors.joining(","));
                            field.set(testCaseDTO, value);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        LOGGER.info("查询系统案例库案例转换时间失败！findProjectTestCase：{}", e);
                    }

                }

            }
        }
        return temp;
    }


    /***
     * 通过项目组的rid查询案例
     * @Method : findByProjectGroupResourceIDs
     * @Description : 通过项目组的rid查询案例
     * @param pgResourceIDList : 项目rids
     * @return : java.util.List<com.jettech.model.TestCase>
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 17:52:28
     *
     */
    @Override
    public List<TestCase> findByProjectGroupResourceIDs(List<String> pgResourceIDList, String testSystem, String trade, String caseId, String intent, String caseType, String casetLevel, String demandFlag) {
        return testCaseDao.findByProjectGroupResourceIDs(pgResourceIDList, testSystem, trade, caseId, intent, caseType, casetLevel, demandFlag);
    }

    /**
     * @Title: addProjectCaseLibTestCase
     * @Description: 新增项目案例库案例（用的表还是ds_testcase,通过leadsource字段区分，案例来源（默认0，项目案例为1））
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: cao_jinbao
     * @Date: 2020/7/15
     */
    @Override
    public Result addProjectCaseLibTestCase(TestCase testCase, String userNumber) {
        // 必输字段非空校验
        // *案例编号、*测试意图、*正反例、*案例级别、*测试步骤、*预期结果
//        if (testCase.getIntent() == null || testCase.getIsNegative() == null || testCase.getCasetLevel() == null
//                || testCase.getTestStep() == null || testCase.getExpectedResult() == null) {
//            return Result.renderError("必输项参数为空");
//        }

//        if (testCase.getTrade() == null || "".equals(testCase.getTrade()) || testCase.getTradeResourceID() == null || "".equals(String.valueOf(testCase.getTradeResourceID()))) {
//            return Result.renderError("交易参数为空");
//        }
//        if (testCase.getTestsystem() == null || "".equals(testCase.getTestsystem()) || testCase.getTestsystemResourceID() == null || "".equals(String.valueOf(testCase.getTestsystemResourceID()))) {
//            return Result.renderError("被测系统参数为空");
//        }
        if (org.springframework.util.StringUtils.isEmpty(testCase.getProjectgroupResourceID())) {
            return Result.renderError("项目组参数不能为空！");
        }
        ProjectGroup projectGroup = projectGroupService.findByResourceID(testCase.getProjectgroupResourceID());
        if (projectGroup == null) {
            return Result.renderError("当前项目组不存在或已被删除！");
        }
        if (!projectGroup.isSmallPoints()) {//不是末级节点
            return Result.renderError("只有末级节点项目组才可以新建案例！");
        }
        String token = HttpRequestUtils.getCurrentRequestToken();
        // 纯数字递增的id
        String testCaseCaseId = this.getNewTestCaseCaseId(testCase.getTradeResourceID());
        testCase.setCaseId(testCaseCaseId);
        testCase.setCaseEditId(testCaseCaseId);
        testCase.setLeadsource(1);
        //伪版本号，有修改做递增操作
        testCase.setIdentitynumber(0);
        JettechUserDTO byNumber = basicService.findByNumber(userNumber, token);
        testCase.setMaintainer(byNumber.getUserName());
        // 维护时间
        testCase.setMaintenanceTime(new Date());

        //api案例显示  需要设置tradeFlowCaseFolderId,testProjectId
        testCase.setTestProjectId(projectGroup.getTestProjectResourceID());
        List<Map<String, Object>> folderList = testProjectService.findTradeFlowCaseFolder(projectGroup.getTestProjectResourceID());
        if (folderList != null && folderList.size() > 0) {
            testCase.setTradeFlowCaseFolderId((long) folderList.get(0).get("id"));
        }

        //案例字段-评审状态未启用时新增案例的评审状态默认为"已评审",20211008dingwl
        this.reviewStatusUsingEnable(testCase);

        TestCase save = this.save(testCase, userNumber);
        return Result.renderSuccess(save);
    }

    /**
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @Title: updateProjectCaseLibTestCase
     * @Description: 修改项目案例库案例
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: cao_jinbao
     * @Date: 2020/7/15
     */
    @Override
    public Result updateProjectCaseLibTestCase(TestCase testCase, String userNumber) throws IllegalArgumentException, IllegalAccessException {
        // 必输字段非空校验
        // *案例编号、*测试意图、*正反例、*案例级别、*测试步骤、*预期结果
//        if (testCase.getCaseId() == null || testCase.getIntent() == null || testCase.getIsNegative() == null
//                || testCase.getCasetLevel() == null || testCase.getTestStep() == null
//                || testCase.getExpectedResult() == null) {
//            return Result.renderError("必输项参数为空");
//        }
        if (testCase.getTrade() == null || "".equals(testCase.getTrade()) || testCase.getTradeResourceID() == null || "".equals(String.valueOf(testCase.getTradeResourceID()))) {
            return Result.renderError("交易参数为空");
        }
        if (testCase.getTestsystem() == null || "".equals(testCase.getTestsystem()) || testCase.getTestsystemResourceID() == null || "".equals(String.valueOf(testCase.getTestsystemResourceID()))) {
            return Result.renderError("被测系统参数为空");
        }
        TestCase byResourceID = this.findByResourceID(testCase.getResourceID());

        //判断版本是否变更
        if ((testCase.getVersionResourceID() != null && !testCase.getVersionResourceID().equals(byResourceID.getVersionResourceID()))
                || (byResourceID.getVersionResourceID() != null && !byResourceID.getVersionResourceID().equals(testCase.getVersionResourceID()))) {

            TestCaseVersion testCaseVersion = new TestCaseVersion();
            BeanUtils.copyProperties(byResourceID, testCaseVersion);
            testCaseVersion.setId(null);
            testCaseVersion.setCreateUser("");
            testCaseVersion.setCreateTime(null);
            testCaseVersion.setEditUser("");
            testCaseVersion.setEditTime(null);
            testCaseVersion.setResourceID(null);
            testCaseVersion.setVersion(0);
            testCaseVersion.setTestCaseResourceID(testCase.getResourceID());
            this.testCaseVersionService.save(testCaseVersion, userNumber);
        }

        testCase.setId(byResourceID.getId());
        if (!CompareTestCase.compareTestCaseSame(testCase, byResourceID) && testCase.getIdentitynumber() != null) {
            testCase.setIdentitynumber(testCase.getIdentitynumber() + 1);
        } else {
            testCase.setIdentitynumber(0);
        }
        TestCase update = this.update(testCase, userNumber);
        return Result.renderSuccess(update);
    }

    /**
     * @Method: relationDemand
     * @Description: //案例关联需求
     * @Param: " [demandResourceID, testCaseResourceID, userNumber] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/7/15
     */
    @Override
    public Result relationDemand(String demandResourceID, String testCaseResourceIDs, String userNumber) {
        List<String> rids = Arrays.asList(testCaseResourceIDs.split(","));
        List<TestCase> list = this.findByResourceIDIn(rids);
        for (TestCase tc : list) {
            tc.setDemandResourceID(Long.valueOf(demandResourceID));
        }
        this.update(list, userNumber);
        return Result.renderSuccess();
    }

    /**
     * @Method: findAllTaskTestCase
     * @Description: 查询所有需求案例
     * @Param: " [] "
     * @return: com.jettech.dto.Result<java.util.List < com.jettech.DTO.TaskTestCaseDTO>>
     * @Author: wws
     * @Date: 2020/7/29
     */
    @Override
    public Result<List<TaskTestCaseDTO>> findAllTaskTestCase() {
        List<TaskTestCaseDTO> list = testCaseDao.findAllTaskTestCase();
        return Result.renderSuccess(list);
    }

    /**
     * //查询案例执行库是否有数据
     *
     * @param listS
     * @return caojinbao
     */
    @Override
    public int findPerformCaseByProjectGroupResourceIDs(List<String> listS) {

        return testCaseDao.findPerformCaseByProjectGroupResourceIDs(listS);
    }

    /**
     * @param @param  resourceIDs
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: findInfoByTestCaseResourceID
     * @Description: 根据当前案例的rid查找案例所属的系统模块交易和所属需求所属项目
     * <AUTHOR>
     */
    @Override
    public Result findInfoByTestCaseResourceID(String resourceID, String taskResourceID,String testProjectResourceID) {
        if (StringUtils.isBlank(resourceID)) {
            return Result.renderError("参数为空！");
        }
        TestCase testCase = this.findByResourceID(Long.valueOf(resourceID));
        if (testCase == null) {
            return Result.renderError("当前案例不存在！");
        }
        Long tradeResourceID = testCase.getTradeResourceID();
        Trade trade = tradeService.findByResourceID(tradeResourceID);
        if (trade == null) {
            return Result.renderError("当前案例所述交易不存在！");
        }
        Map<String, Object> mapOne = null;
        TestCaseInfoDTO model = new TestCaseInfoDTO();
        //交易直接挂在被测系统下，没有挂在模块下
        if (org.springframework.util.StringUtils.isEmpty(trade.getModuleResourceID())) {
            //根据当前案例的rid查找案例所属的系统模块交易和所属需求所属项目（交易直接挂在被测系统下）
            mapOne = testCaseDao.findInfoByTestCaseResourceIDNoModule(resourceID);
            model.setSysemModuleName(null);
            model.setSysemModuleResourceID(null);
        } else {
            //交易挂在模块下
            //根据当前案例的rid查找案例所属的系统模块交易和所属需求所属项目（交易直接挂在所属模块下）
            mapOne = testCaseDao.findInfoByTestCaseResourceIDHasModule(resourceID, taskResourceID);
            Long moduleResourceID = Long.valueOf(String.valueOf(mapOne.get("moduleResourceID")));
            String moduleNameStr = systemModuleService.findParentModuleNamesByModuleRid(moduleResourceID);
            model.setSysemModuleResourceID(moduleResourceID);
            model.setSysemModuleName(moduleNameStr);

        }
        if (mapOne == null) {
            return Result.renderError("数据有误！");
        }

        JSONObject testRoundMap = new JSONObject();
        JSONObject testStageMap = new JSONObject();
        //查询任务
        if (!StringUtils.isEmpty(taskResourceID)) {
            Map<String, String> testPlanMap = manexecuteService.findTestPlanByTestTaskResourceID(Long.valueOf(taskResourceID), null);
            if (testPlanMap != null) {
                String testRound = testPlanMap.get("testRound");
                String testRoundName = testPlanMap.get("testRoundName");
                String testStage = testPlanMap.get("testStage");
                String testStageName = testPlanMap.get("testStageName");

                testRoundMap.put("lable", testRound);
                testRoundMap.put("name", testRoundName);
                testStageMap.put("lable", testStage);
                testStageMap.put("name", testStageName);
            }
        }

        if (!"null".equals(String.valueOf(mapOne.get("testProjectName")))) {
            model.setTestProjectName(String.valueOf(mapOne.get("testProjectName")));
            model.setTestProjectResourceID(Long.valueOf(String.valueOf(mapOne.get("testProjectResourceID"))));
            model.setDemandName(String.valueOf(mapOne.get("demandName")));
            model.setDemandResourceID(Long.valueOf(String.valueOf(mapOne.get("demandResourceID"))));
        } else {
            if (!StringUtils.isEmpty(testProjectResourceID)) {
                TestProject testProject = testProjectService.findByResourceID(Long.parseLong(testProjectResourceID));
                model.setTestProjectName(testProject.getName());
                model.setTestProjectResourceID(testProject.getResourceID());
            }
        }

        model.setTestRound(testRoundMap.toString());
        model.setTestStage(testStageMap.toString());
        model.setTestSystemName(String.valueOf(mapOne.get("systemName")));
        model.setTestSystemResourceID(Long.valueOf(String.valueOf(mapOne.get("systemResourceID"))));
        model.setTradeName(String.valueOf(mapOne.get("tradeName")));
        model.setTradeResourceID(Long.valueOf(String.valueOf(mapOne.get("tradeResourceID"))));
        model.setCaseId(testCase.getCaseId());
        model.setCaseResourceID(Long.valueOf(resourceID));

        return Result.renderSuccess(model);
    }

    /**
     * @param @param  resourceIDs
     * @param @param  request
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: findAnotherInfoByTestCaseResourceID
     * @Description: 根据当前案例的rid查找案例所属的系统模块交易和所属项目(案例执行库页面用)--没有所属需求
     * <AUTHOR>
     */
    @Override
    public Result findAnotherInfoByTestCaseResourceID(String performCaseResourceID) {
        if (StringUtils.isBlank(performCaseResourceID)) {
            return Result.renderError("案例参数为空！");
        }
        Map<String, Object> performCaseInfo = testCaseDao.findPerformCaseInfo(performCaseResourceID);
        if (performCaseInfo == null) {
            return Result.renderError("当前案例不存在！");
        }

        TestCase testCase = new TestCase();
        if (!org.springframework.util.StringUtils.isEmpty(performCaseInfo.get("testsystemResourceID"))) {
            testCase.setTestsystemResourceID(Long.valueOf(String.valueOf(performCaseInfo.get("testsystemResourceID"))));
            testCase.setTestsystem(String.valueOf(performCaseInfo.get("testsystem")));
        }
        if (!org.springframework.util.StringUtils.isEmpty(performCaseInfo.get("systemmoduleResourceID"))) {
            Long moduleResourceID = Long.valueOf(String.valueOf(performCaseInfo.get("systemmoduleResourceID")));
            String moduleNameStr = systemModuleService.findParentModuleNamesByModuleRid(moduleResourceID);
            testCase.setSystemmoduleResourceID(moduleResourceID);
            testCase.setSystemmodule(moduleNameStr);
        }
        if (!org.springframework.util.StringUtils.isEmpty(performCaseInfo.get("tradeResourceID"))) {
            testCase.setTrade(String.valueOf(performCaseInfo.get("trade")));
            testCase.setTradeResourceID(Long.valueOf(String.valueOf(performCaseInfo.get("tradeResourceID"))));
        }
        if (!org.springframework.util.StringUtils.isEmpty(performCaseInfo.get("demandResourceID"))) {
            testCase.setDemandResourceID(Long.valueOf(String.valueOf(performCaseInfo.get("demandResourceID"))));
        }
        TestCaseInfoDTO model = new TestCaseInfoDTO();

        //案例的所属项目为当前项目案例所属的项目，不再是新建项目案例时候的所属项目
        ProjectGroup group = projectGroupService.findByResourceID(Long.valueOf(String.valueOf(performCaseInfo.get("projectgroupResourceID"))));
        TestProject testProject = testProjectService.findByResourceID(group.getTestProjectResourceID());
        model.setTestProjectName(testProject.getName());
        model.setTestProjectResourceID(testProject.getResourceID());
        //案例执行库没有需求（但是可以关联需求）
        model.setDemandName(null);
        model.setDemandResourceID(null);
        if (!org.springframework.util.StringUtils.isEmpty(testCase.getDemandResourceID())) {
            Demand demand = demandService.findByResourceID(testCase.getDemandResourceID());
            model.setDemandName(demand.getName());
            model.setDemandResourceID(demand.getResourceID());
        }
        model.setTestSystemName(testCase.getTestsystem());
        model.setTestSystemResourceID(testCase.getTestsystemResourceID());
        model.setSysemModuleResourceID(null);
        model.setSysemModuleName(null);
        if (!org.springframework.util.StringUtils.isEmpty(testCase.getSystemmoduleResourceID())) {
//			SystemModule systemModule = systemModuleService.findByResourceID(testCase.getSystemmoduleResourceID());
            model.setSysemModuleResourceID(testCase.getSystemmoduleResourceID());
            model.setSysemModuleName(testCase.getSystemmodule());
        }
        model.setTradeName(testCase.getTrade());
        model.setTradeResourceID(testCase.getTradeResourceID());

        model.setCaseId(String.valueOf(performCaseInfo.get("caseId")));
        model.setCaseResourceID(Long.valueOf(performCaseResourceID));

        return Result.renderSuccess(model);
    }

    /**
     * @param @param  demandResourceID
     * @param @return 参数
     * @return int    返回类型
     * @throws
     * @Title: countAllPerformCaseAddToExecute
     * @Description: 校验当前需求下的项目案例是否都都添加到项目案例执行表
     * <AUTHOR>
     */
    @Override
    public int countAllPerformCaseAddToExecute(Long demandResourceID) {

        return testCaseDao.countAllPerformCaseAddToExecute(demandResourceID);
    }

    /**
     * @param @param  demandResourceID
     * @param @return 参数
     * @return int    返回类型
     * @throws
     * @Title: countAllTestCaseAddToExecute
     * @Description:校验当前需求下的手工执行案例的数据是否都都添加到手工案例引用表
     * <AUTHOR>
     */
    @Override
    public int countAllTestCaseAddToExecute(Long demandResourceID) {

        return testCaseDao.countAllTestCaseAddToExecute(demandResourceID);
    }

    /**
     * @param @param  ceseRids
     * @param @return 参数
     * @return int    返回类型
     * @throws
     * @Title: findPerformcaseByCaseResourceID
     * @Description: 项目案例表里当前案例非成功取消状态的案例
     * <AUTHOR>
     */
    @Override
    public Integer findPerformcaseByCaseResourceID(List<Long> ceseRids, String flag) {

        return testCaseDao.findPerformcaseByCaseResourceID(ceseRids, flag);
    }

    /**
     * @param @param  ceseRids
     * @param @return 参数
     * @return int    返回类型
     * @throws
     * @Title: findTestCaseByCaseResourceID
     * @Description: 手工执行案例引用表里当前案例非成功取消状态的案例
     * <AUTHOR>
     */
    @Override
    public Integer findTestCaseByCaseResourceID(List<Long> ceseRids, String flag) {

        return testCaseDao.findTestCaseByCaseResourceID(ceseRids, flag);
    }

    /**
     * 根据需求的查询案例表信息
     *
     * @param demandResourceID
     * @return List<String, Object>
     * <AUTHOR>
     */
    @Override
    public List<Map<String, Object>> findCaseInfoByDemandResourceID(Long demandResourceID) {

        return testCaseDao.findCaseInfoByDemandResourceID(demandResourceID);
    }

    @Override
    public PageImpl<Map<String, Object>> initMyCaseToAssesWithBynumber(PageRequest pageRequest, Map<String, String> params) {
        PageHelper.startPage(pageRequest.getPageNumber() == 0 ? 1 : pageRequest.getPageNumber() + 1,
                pageRequest.getPageSize());
        String userNumber = params.get("userNumber");
        List<Map<String, Object>> caseList = testCaseDao.initMyCaseToAssesWithBynumber(userNumber, params);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>(caseList);
        return new PageImpl<Map<String, Object>>(caseList, pageRequest, pageInfo.getTotal());
    }

    @Override
    public Result updateCaseStatus(Map<String, String> params, String userNumber) {
        String resourceID = params.get("resourceID");
        TestCasequote testCasequote = testCasequoteService.findByTestcaseResourceID(resourceID);
        //TODO 新建类的案例阻塞取消确认应根据传入的resourceID搜索 CaseResultRecord 后根据performcaseResourceID搜索对应的记录去更新确认状态
//        PerFormCase perFormCase = perFormCaseService.findByTestcaseResourceID(resourceID);
        Optional<User> optionalUser = userinfoService.findByNumber(userNumber);
        User user = optionalUser.get();
        if (testCasequote != null) {
            TestCasequote testCasequote1 = new TestCasequote();
            testCasequote1.setId(testCasequote.getId());
            testCasequote1.setStatus(1);
            testCasequote1.setOpenUser(user.getUserName());
            testCasequoteService.update(testCasequote1, userNumber);
        }
//        if (perFormCase != null) {
//            PerFormCase perFormCase1 = new PerFormCase();
//            perFormCase1.setStatus(1);
//            perFormCase1.setOpenUser(userNumber);
//            perFormCaseService.update(perFormCase1, userNumber);
//        }
        return Result.renderSuccess();
    }

    /**
     * 根据tradeId更新tradeName
     *
     * @param tradeName
     * @param tradeResourceID
     * @return Result
     * by zhangsheng
     */
    @Override
    public Result updateTestCaseTradeNameByTradeId(String tradeName, Long tradeResourceID) {
        if (StringUtils.isBlank(tradeName)) {
            Result.renderError("交易名称为空");
        }
        if (null == tradeResourceID) {
            Result.renderError("tradeResourceID为null");
        }
        testCaseDao.updateTestCaseTradeNameByTradeId(tradeName, tradeResourceID);
        return Result.renderSuccess();
    }

    /**
     * 根据SystemId更新SystemName
     *
     * @param systemName
     * @param systemResourceId
     * @return Result
     * by zhangsheng
     */
    @Override
    public Result updateTestCaseSystemNameBySystemId(String systemName, Long systemResourceId) {
        if (StringUtils.isBlank(systemName)) {
            Result.renderError("系统名称为空");
        }
        if (null == systemResourceId) {
            Result.renderError("systemResourceId为null");
        }
        testCaseDao.updateTestCaseSystemNameBySystemId(systemName, systemResourceId);
        return Result.renderSuccess();
    }

    /**
     * 根据testSystemResourceID 将ds_testcase表中的模块根据name替换
     *
     * @param oldName
     * @param newName
     * @param testSystemResourceID
     * @return
     */
    @Override
    public Result updateTestCaseSystemModuleNameByNameAndSystemId(String oldName, String newName, Long testSystemResourceID) {
        if (StringUtils.isBlank(oldName) || StringUtils.isBlank(newName)) {
            Result.renderError("模块名称为空");
        }
        if (null == testSystemResourceID) {
            Result.renderError("testSystemResourceID为null");
        }
        testCaseDao.updateTestCaseSystemModuleNameByNameAndSystemId(oldName, newName, testSystemResourceID);
        return Result.renderSuccess();
    }

    /**
     * @param @param  params
     * @param @return 参数
     * @return Result    返回类型
     * @throws
     * @Title: findTestCaseDetailsForDefect
     * @Description:点击缺陷所属案例编号查询任务案例的信息
     * <AUTHOR>
     */
    @Override
    public Result<?> findTestCaseDetailsForDefect(String testCaseResourceID) {
        TestCase testCase = this.findByResourceID(Long.valueOf(testCaseResourceID));
        if (testCase == null) {
            return Result.renderError("当前案例不存在！");
        }
        return Result.renderSuccess(testCase);
    }


    /**
     * @param [entityList, batchCount]
     * @return void
     * <AUTHOR>
     * @description 根据caseID批量更新
     * @date 2020年11月09日 9:44
     **/
    @Override
    public void updateBatchByCaseId(Collection<TestCase> entityList, int batchCount) {
        testCaseDao.updateBatchByCaseId(entityList, batchCount);
    }

    /**
     * @param [caseId,               resourceID]
     * @return java.util.List<com.jettech.model.TestCase>
     * <AUTHOR>
     * @description 根据caseid和tradeResid查询案例
     * @date 2020年11月09日 10:04
     **/
    @Override
    public List<TestCase> findByCaseIdAndTradeResId(String caseId, Long tradeRresourceID) {
        return testCaseDao.findByCaseIdAndTradeResId(caseId, tradeRresourceID);
    }

    /**
     * @param [type]
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @description 通过infoname查询字典值
     * @date 2020年12月15日 14:22
     **/
    @Override
    public List<Map<String, Object>> findTestCaseDictionaryDataByInfoName(String infoName) {
        return testCaseDao.findTestCaseDictionaryDataByInfoName(infoName);
    }

    /**
     * @param [demandsResourceIDList]
     * @return java.util.List<com.jettech.model.TestCase>
     * <AUTHOR>
     * @description 根据需求resourceid查询需求下的案例
     * @date 2021年01月06日 10:04
     **/
    @Override
    public List<TestCase> findBydemandResourceIDs(List<Long> demandsResourceIDList) {
        return testCaseDao.findBydemandResourceIDs(demandsResourceIDList);
    }

    /**
     * 根据资源id查询标签关联表数据
     *
     * @param relationResourceIDs
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * 10:10
     * @update
     * @exception/throws [异常类型] [异常说明]
     * @see [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @Override
    public List<Map<String, Object>> findRelationResourceTags(List<String> relationResourceIDs,int relationType) {
        return testCaseDao.findRelationResourceTags(relationResourceIDs, relationType);
    }

    @Override
    public List<Map<String, Object>> findTaskTree() {
        List<Map<String, Object>> taskData = testCaseDao.findTaskTree();

        Map<String, String> projectMap = taskData.stream().collect(Collectors.toMap(x -> x.get("projectResourceID").toString(), y -> y.get("projectName").toString(), (x, y) -> x));

        Map<String, Map<String, String>> demandMap = taskData.stream().collect(Collectors.toMap(x -> x.get("projectResourceID").toString(), y -> {
            Map<String, String> map = taskData.stream().filter(x -> x.get("projectResourceID").equals(y.get("projectResourceID"))).collect(Collectors.toMap(a -> a.get("demandResourceID").toString(), b -> b.get("demandName").toString(), (a, b) -> a));
            return map;
        }, (x, y) -> x));

        Map<String, Map<String, String>> planMap = taskData.stream().collect(Collectors.toMap(x -> x.get("demandResourceID").toString(), y -> {
            Map<String, String> map = taskData.stream().filter(x -> x.get("demandResourceID").equals(y.get("demandResourceID"))).collect(Collectors.toMap(a -> a.get("planResourceID").toString(), b -> b.get("planName").toString(), (a, b) -> a));
            return map;
        }, (x, y) -> x));

        Map<String, Map<String, String>> taskMap = taskData.stream().collect(Collectors.toMap(x -> x.get("planResourceID").toString(), y -> {
            Map<String, String> map = taskData.stream().filter(x -> x.get("planResourceID").equals(y.get("planResourceID"))).collect(Collectors.toMap(a -> a.get("taskResourceID").toString(), b -> b.get("taskName").toString(), (a, b) -> a));
            return map;
        }, (x, y) -> x));


        List<Map<String,Object>> resultMapList = new ArrayList<>();
        projectMap.forEach((k1, v1) -> {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("key",k1 );
            map1.put("name",v1 );
            map1.put("type", "project");
            List<Map<String,Object>> map1ChildrenList = new ArrayList<>();
            demandMap.get(k1).forEach((k2, v2) -> {
                Map<String,Object> map2 = new HashMap<>();
                map2.put("key",k2 );
                map2.put("name",v2 );
                map2.put("type", "demand");
                List<Map<String,Object>> map2ChildrenList = new ArrayList<>();
                planMap.get(k2).forEach((k3,v3)->{
                    Map<String,Object> map3 = new HashMap<>();
                    map3.put("key",k3 );
                    map3.put("name",v3 );
                    map3.put("type", "plan");
                    List<Map<String,Object>> map3ChildrenList = new ArrayList<>();
                    taskMap.get(k3).forEach((k4, v4) -> {
                        Map<String, Object> map4 = new HashMap<>();
                        map4.put("key", k4);
                        map4.put("name", v4);
                        map4.put("type", "task");
                        map3ChildrenList.add(map4);
                    });
                    map3.put("children", map3ChildrenList);
                    map2ChildrenList.add(map3);
                });
                map2.put("children", map2ChildrenList);
                map1ChildrenList.add(map2);
            });
            map1.put("children", map1ChildrenList);
            resultMapList.add(map1);
        });

        return resultMapList;
    }

    @Override
    public List<Map<String, Object>> findTradeTree(String taskResourceIDs) {
        List<Map<String, Object>> tradeData = testCaseDao.findTradeTree(Arrays.asList(taskResourceIDs.split(",")));

        Map<String, String> systemMap = tradeData.stream().collect(Collectors.toMap(x -> x.get("systemResourceID").toString(), y -> y.get("systemName").toString(), (x, y) -> x));

        List<Map<String, Object>> resultList = new ArrayList<>();
        systemMap.forEach((k1,v1)->{
            Map<String, Object> map1 = new HashMap<>();
            map1.put("key", k1);
            map1.put("name", v1);
            map1.put("type", "system");
            List<Map<String, Object>> map1ChildrenList = new ArrayList<>();


            Map<String, Map<String, Object>> moduleMap = tradeData.stream().filter(item -> item.get("systemResourceID").toString().equals(k1)).collect(Collectors.toMap(x -> x.get("moduleResourceID").toString(), y -> y, (x, y) -> x));
            List<Map<String, Object>> systemModuleMapList = moduleMap.entrySet().stream().map(e -> e.getValue()).collect(Collectors.toList());


            Map<String, Map<String, Object>> tradeMap = tradeData.stream().filter(x -> x.get("tradeResourceID") != null && x.get("systemResourceID").toString().equals(k1)).collect(Collectors.toMap(x -> x.get("tradeResourceID").toString(), y -> y, (x, y) -> x));
            List<Map<String, Object>> tradeMapList = tradeMap.entrySet().stream().map(e -> e.getValue()).collect(Collectors.toList());

            systemModuleMapList.stream().forEach(item -> {
                if (item.get("parentResourceID").toString().equals(k1)) {
                    //一级模块
                    Map<String, Object> map2 = new HashMap<>();
                    map2.put("key", item.get("moduleResourceID"));
                    map2.put("name", item.get("moduleName"));
                    map2.put("type", "module");
                    List<Map<String, Object>> map2ChildrenList = new ArrayList<>();
                    map2.put("children", map2ChildrenList);
                    map1ChildrenList.add(map2);
                }
            });

            map1ChildrenList.stream().forEach(item -> {
                List<Map<String, Object>> map2ChildrenList = (List<Map<String, Object>>) item.get("children");
                moduleChildren(item.get("key").toString(), map2ChildrenList, systemModuleMapList, tradeMapList);
            });

            map1.put("children", map1ChildrenList);
            resultList.add(map1);
        });

        return resultList;
    }

    @Override
    public PageInfo<TaskTradeCaseView> findTaskTradeCasePage(TaskTradeCaseDto dto) {
        PageHelper.startPage(dto.getPageNumber() != null ? dto.getPageNumber() : 1, dto.getPageSize() != null ? dto.getPageSize() : 20);

        if (dto.getCreateTime() != null) {
            dto.setCreateTimeStrList(new ArrayList<>());
            dto.getCreateTime().stream().forEach(item -> {
                dto.getCreateTimeStrList().add(DateUtil.getDateStr(item, DateUtil.DATE_PATTERN));
            });
        }

        if (dto.getExecuteTime() != null ) {
            dto.setExecuteTimeStrList(new ArrayList<>());
            dto.getExecuteTime().stream().forEach(item->{
                dto.getExecuteTimeStrList().add(DateUtil.getDateStr(item, DateUtil.DATE_PATTERN));
            });
        }


        List<TaskTradeCaseView> testCaseList = testCaseDao.findTaskTradeCase(dto);
        PageInfo<TaskTradeCaseView> pageInfo = new PageInfo<>(testCaseList);
        pageInfo.getList().stream().forEach(item->{
            item.setCreateTimeStr(DateUtil.getDateStr(item.getCreateTime(), DateUtil.TIME_PATTREN));
        });
        return pageInfo;
    }

    @Override
    public List<String> findCaseQuote(TaskTradeCaseDto dto) {
        return testCaseDao.findCaseQuote(dto);
    }

    private void moduleChildren(String moduleResourceID, List<Map<String, Object>> childrenList, List<Map<String, Object>> systemModuleMapList,List<Map<String, Object>> tradeMapList) {
        List<Map<String, Object>> cmList = systemModuleMapList.stream().filter(a -> a.get("parentResourceID").toString().equals(moduleResourceID)).collect(Collectors.toList());
        if (cmList != null && cmList.size() > 0) {
            // 递归获取所有子模块
            cmList.stream().forEach(item -> {
                Map<String, Object> map2 = new HashMap<>();
                map2.put("key", item.get("moduleResourceID"));
                map2.put("name", item.get("moduleName"));
                map2.put("type", "module");
                List<Map<String, Object>> map2ChildrenList = new ArrayList<>();
                moduleChildren(item.get("moduleResourceID").toString(), map2ChildrenList, systemModuleMapList, tradeMapList);
                map2.put("children", map2ChildrenList);
                childrenList.add(map2);
            });
        }

        tradeMapList.stream().filter(trade -> trade.get("moduleResourceID").toString().equals(moduleResourceID)).forEach(trade -> {
            Map<String, Object> map3 = new HashMap<>();
            map3.put("key", trade.get("tradeResourceID"));
            map3.put("name", trade.get("tradeName"));
            map3.put("type", "trade");
            childrenList.add(map3);
        });
    }

    public Object findSceneCaseByTradeID(JSONObject params){

        Long tradeResourceID = params.getLong("tradeResourceID");
        Assert.notNull(tradeResourceID, "tradeResourceID不能为空");

        Page page = PageUtil.startPage(params);

        List<TestCase> result = testCaseDao.findSceneCaseByTradeID(tradeResourceID);

        boolean isTreeNode = params.getBooleanValue("treeNode");

        if(isTreeNode){
            return result.stream().map(t -> {
                TreeNode treeNode = new TreeNode();
                treeNode.setId(t.getId().toString());
                treeNode.setParentResourceID(tradeResourceID.toString());
                treeNode.setType("sceneCase");
                treeNode.setName(t.getCaseId());
                treeNode.setResourceID(t.getResourceID().toString());
                return treeNode;
            }).collect(Collectors.toList());
        }

        return page!=null?page.toPageSerializable():result;
    }

	/**
	 * 查询api脚本,通过脚本id
	 * 20211013dingwl
	 */
	@Override
	public Object findApiScriptById(String tradeFlowId) {
		return testCaseDao.findApiScriptById(Long.valueOf(tradeFlowId));
	}


    @Override
    public List<TestCase> findProjectCase(Map<String, Object> params) {
        return testCaseDao.findProjectCase(params);
    }

    @Override
    public Map<String, Object> findProjectCasePage(Map<String,Object> params) {
        int pageNumber = params.get("page") != null ? (int) params.get("page") : 1;
        int pageSize = params.get("pageSize") != null ? (int) params.get("pageSize") : 10;
        if (params.get("time") != null) {
            List<String> timeList = (List<String>) params.get("time");
            params.put("startTime", timeList.get(0));
            params.put("endTime", timeList.get(1));
        }
        Page page = PageHelper.startPage(pageNumber, pageSize);
        List<TestCase> list = this.findProjectCase(params);
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", page.getTotal());
        resultMap.put("pageSize", params.get("pageSize"));
        resultMap.put("totalPage", page.getPages());
        resultMap.put("page", params.get("page"));
        resultMap.put("pageData", list);
        return resultMap;
    }

	/**
	 * @Title: findAllUser
	 * @Description:  查询所有人员
	 * @Return: "com.jettech.dto.Result<?>"
	 * @Author: dwl
	 * @Date: 2021/11/1
	 */
	@Override
	public List<Map<String, String>> findAllUser() {
		List<Map<String, String>> resultList = new ArrayList<Map<String,String>>();
		String token = HttpRequestUtils.getCurrentRequestToken();
		List<JettechUserDTO> jettechUserDTOList = basicService.findAllPerson(token);
		if (!CollectionUtils.isEmpty(jettechUserDTOList)) {
			for (JettechUserDTO jettechUserDTO : jettechUserDTOList) {
				HashMap<String,String> hashMap = new HashMap<>();
				hashMap.put("userResourceID", jettechUserDTO.getResourceID().toString());
				hashMap.put("userName", jettechUserDTO.getUserName());
				hashMap.put("userNumber", jettechUserDTO.getNumber());
				resultList.add(hashMap);
			}
		}
		return resultList;
	}

    @Override
    public List<TestCase> findTaskCase(String taskResourceIDs) {
        return testCaseDao.findTaskCase(taskResourceIDs);
    }

    @Override
    public List<TestCase> findByProjectGroupResourceID(Long projectGroupResourceID) {
        return testCaseDao.findByProjectGroupResourceID(projectGroupResourceID);
    }

    private String getText(String richText) {
        String regx = "(<.+?>)|(</.+?>)";
        Matcher matcher = Pattern.compile(regx).matcher(richText);
        while (matcher.find()) {
            richText = matcher.replaceAll("").replace("&nbsp;", " ");
        }
        return richText;
    }

    //根据案例步骤生成相应的案例
    public Result generateCase(GenerageCaseDto generageCaseDto){
        List<LinkedList<TopicText>> processCaseList=generageCaseDto.getProcessCaseList();
        List<LinkedList<TopicText>> rulerCaseList=generageCaseDto.getRulerCaseList();
        List<LinkedList<TopicText>> pageCaseList=generageCaseDto.getPageCaseList();
        
        List<TestCase> allCaseList = new ArrayList<>();
        
        Long tradeResourceId=generageCaseDto.getTradeResourceId();
        String userNumber=generageCaseDto.getUserNumber();

        List<Long> tradeResourceIDStringList=new ArrayList<>();
        tradeResourceIDStringList.add(tradeResourceId);
        List<SystemModuleTradeDTO> systemModuleTradeDTOList = tradeService.getTradeSystemModule(tradeResourceIDStringList);
        if(!CollectionUtils.isEmpty(processCaseList)){
            for(int a=0;a<processCaseList.size();a++){
                TestCase testCase=new TestCase();
                fillupProcessCase(processCaseList.get(a),testCase,a, tradeResourceId,userNumber,systemModuleTradeDTOList,tradeResourceId);
//                this.save(testCase, userNumber);
                allCaseList.add(testCase);
            }
        }
        if(!CollectionUtils.isEmpty(rulerCaseList)){
            for(int a=0;a<rulerCaseList.size();a++){
                TestCase testCase=new TestCase();
                fillupRulerCase(rulerCaseList.get(a),testCase,a, tradeResourceId,userNumber,systemModuleTradeDTOList,tradeResourceId);
//                this.save(testCase, userNumber);
                allCaseList.add(testCase);
            }
        }
        if(!CollectionUtils.isEmpty(pageCaseList)){
            //页面控制的前提条件
            String title = "";
            if (!CollectionUtils.isEmpty(processCaseList)) {
                try {
                    String str = processCaseList.get(0).get(1).getTitle();
                    title = str.substring(5, str.length() - 1).toString();
                } catch (Exception e) {
                    LOGGER.error("页面控制前置条件解析失败,", e);
                }
            }
            for(int a=0;a<pageCaseList.size();a++){
                TestCase testCase=new TestCase();
                fillupPageControllerCase(pageCaseList.get(a),testCase,a, title,userNumber,systemModuleTradeDTOList,tradeResourceId);
//                this.save(testCase, userNumber);
                allCaseList.add(testCase);
            }
        }
        
        if (!allCaseList.isEmpty()) {
            this.save(allCaseList,userNumber);
        }
        
        return Result.renderSuccess(allCaseList.size());

        /*for(LinkedList<TopicText> topicTexts:processCaseList){
            TestCase testCase=new TestCase();

            //测试意图 没有
            //正反例 0反例1正例
            testCase.setIsNegative("1");
            //案例级别
            fillUp(topicTexts,testCase);

            //案例类型 0功能案例 1界面案例
            testCase.setCaseType(0);
            //测试步骤
            testCase.setTestStep("");
            //预期结果 交易成功或者交易拒绝 取叶子节点
            testCase.setExpectedResult();
            //前置条件 包含前提条件的步骤 通过map来进行获取
            testCase.setPreconditions();
            //所属系统名称
            testCase.setTestsystem();
            //所属系统resourceID
            testCase.setTestsystemResourceID();
            //所属模块名称
            testCase.setSystemmodule();
            //所属模块resourceid
            testCase.setSystemmoduleResourceID();
            //所属项目组
            testCase.setProjectgroupResourceID();
            //案例来源
            testCase.setLeadsource();
            //所属交易名称
            // 案例所属交易，指向at_Trade表

            //案例所属需求，指向ds_demand表  无
            //运行条件 没有
            //备注   没有
            //维护人  取模型创建人
            //维护时间  取当前时间
            //评审状态   默认已评审
            //案例所属任务外键 没有
            //是否提交  否
            //数据要求 无
            //检查点  无
            //备用字段1 无
            //备用字段2 无
            //备用字段3 无
            //备用字段4 无
            //备用字段5 无
            //备用字段6 无
            //备用字段7 无
            //备用字段8 无
            //备用字段9 无
            //备用字段10 无
            //备用字段1 无
            //版本
            //案例名称
            //测试方式*/
        }




    private void fillupProcessCase(List<TopicText> topicTexts,TestCase testCase,int a,Long tradeResourceId, String userNumber,List<SystemModuleTradeDTO> systemModuleTradeDTOList,Long tradeResourceI){
        //fillUp(topicTexts,testCase,false,a);
        fillUp1(systemModuleTradeDTOList,testCase,tradeResourceId);
        fillUp(topicTexts,testCase,false,a,userNumber);
        //0反例1正例
        testCase.setIsNegative("1");
        testCase.setName(topicTexts.get(0).getTitle()+a);
        //0功能案例1页面案例
        testCase.setCaseType("0");
        if(StringUtils.isBlank(testCase.getExpectedResult())){
            testCase.setExpectedResult("交易成功");
        }
    }






    private void fillupRulerCase(List<TopicText> topicTexts,TestCase testCase,int a,Long tradeResourceId, String userNumber,List<SystemModuleTradeDTO> systemModuleTradeDTOList,Long tradeResourceI){
        fillUp1(systemModuleTradeDTOList,testCase,tradeResourceId);
        fillUp(topicTexts,testCase,true,a,userNumber);
        //0反例1正例
        testCase.setIsNegative("0");
        //0功能案例1页面案例
        testCase.setCaseType("0");
        if(StringUtils.isBlank(testCase.getExpectedResult())){
            testCase.setExpectedResult("交易失败");
        }
    }

    private void fillupPageControllerCase(List<TopicText> topicTexts,TestCase testCase,int a,String title,String userNumber,List<SystemModuleTradeDTO> systemModuleTradeDTOList,Long tradeResourceId){
        fillUp1(systemModuleTradeDTOList,testCase,tradeResourceId);
        fillUp(topicTexts,testCase,false,a,userNumber);
        //0反例1正例
        testCase.setIsNegative("1");
        testCase.setName(topicTexts.get(0).getTitle()+a);
        //0功能案例1页面案例
        testCase.setCaseType("1");
        if(StringUtils.isBlank(testCase.getExpectedResult())){
            testCase.setExpectedResult("交易成功");
        }
        if(StringUtils.isBlank(testCase.getPreconditions())){
            testCase.setPreconditions(title);
        }

    }
    //公共填充案例数据 公共逻辑字段或者需要循环的字段在这里处理
    private void fillUp(List<TopicText> topicTexts,TestCase testCase,boolean isRuler,int a, String userNumber) {
        String testCaseCaseId = this.getNewTestCaseCaseId(testCase.getTradeResourceID());
        //案例编号
        testCase.setCaseId(testCaseCaseId);
        //案例编写专用id
        testCase.setCaseEditId(testCaseCaseId);
        StringBuilder sb = new StringBuilder();
        for (TopicText item : topicTexts) {
            sb.append(item.getTitle()).append(",");
            //高中低 012
            if (item.getMarkerRefs() != null) {
                if (item.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("priority-1")) {
                    testCase.setCasetLevel("0");
                }
                if (item.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("priority-2")) {
                    //businessProcessCase.add(topicText.getTitle());
                    // businessProcessCase.addLast("高");
                    testCase.setCasetLevel("1");
                }
                if (item.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("priority-3")) {
                    //businessProcessCase.add(topicText.getTitle());
                    // businessProcessCase.addLast("高");
                    testCase.setCasetLevel("2");
                }
            }
            if (item.getTitle().contains("前提条件")) {
                testCase.setPreconditions(item.getTitle().substring(5, item.getTitle().length() - 1));
            }
            //交易结果
            if(item.getMarkerRefs()!=null){
                if (item.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("symbol-question")) {
                    testCase.setExpectedResult(item.getTitle());
                }
            }

            //案例名称
            if (isRuler == true) {
                if (item.getNotes() != null && item.getNotes().getContent().equals("业务规则")) {
                    testCase.setName(item.getTitle() + a);
                }
            }

        }
        if(StringUtils.isBlank(testCase.getCasetLevel())){
            testCase.setCasetLevel("0");
        }
        String str = sb.deleteCharAt(sb.length() - 1).toString();
        testCase.setTestStep(str);//步骤
        //默认已评审
        testCase.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
        testCase.setCommitted(false);
        //2表示来源于模型
        testCase.setLeadsource(2);
        testCase.setTestMode(1);//测管案例

        testCase.setMaintainer(userNumber);
        testCase.setMaintenanceTime(new Date());
        testCase.setCreateTime(new Date());
        testCase.setEditUser(userNumber);
        testCase.setEditTime(new Date());
//        testCase.setResourceID(generateResourceID());
        testCase.setTestMode(1);
        testCase.setIntent("模型案例测试");
        //维护人维护时间
       // testCase.setTradeResourceID();
       /* TestSystem testSystem = testSystems.stream().filter(x -> x.getName().equals(testSystemStr)).findFirst().orElse(null);
        Trade trade = tradeList.stream().filter(x -> x.getTestSystemResourceID().equals(testSystem.getResourceID())
                && x.getName().equals(tradeName)).findFirst().orElse(null);
        SystemModule module = moduleList.stream().filter(x -> x.getResourceID().equals(trade.getModuleResourceID())).findFirst().orElse(null);
         Trade trade=tradeMapper.findByResourceId(TradeResourceID);
        List<SystemModuleTradeDTO> systemModuleTradeDTOList = tradeService.getTradeSystemModule(tradeResourceIDStringList);
        Optional<SystemModuleTradeDTO> optional = systemModuleTradeDTOList.stream().filter(y -> y.getTradeResourceID().equals(x.getTradeResourceID())).findFirst();
        if (optional.isPresent()) {
            x.setTrade(optional.get().getTradeName());
            x.setSystemmoduleResourceID(optional.get().getModuleResourceID());
            x.setSystemmodule(optional.get().getModuleNames());
            x.setTestsystemResourceID(optional.get().getSystemResourceID());
            x.setTestsystem(optional.get().getSystemName());*/
        }

    public void fillUp1(List<SystemModuleTradeDTO> systemModuleTradeDTOList,TestCase testcase,Long tradeResourceId){
	    if(!CollectionUtils.isEmpty(systemModuleTradeDTOList)){
	        for(SystemModuleTradeDTO item:systemModuleTradeDTOList){
	            if(item.getTradeResourceID().equals(tradeResourceId)){
	                testcase.setTestsystem(item.getSystemName());
                    testcase.setTestsystemResourceID(item.getSystemResourceID());
                    testcase.setSystemmodule(item.getModuleName());
                    testcase.setSystemmoduleResourceID(item.getModuleResourceID());
                    testcase.setTrade(item.getTradeName());
                    testcase.setTradeResourceID(tradeResourceId);
                }
            }
        }
    }

  //private void find
/* private static final long serialVersionUID = 8994893439478042859L;
 *//**
 * 案例编号   解决
 *//*
    @FieldNote(value = "案例编号",type = FieldType.String)
    @TableField(value="caseId")
    private String caseId;
    *//**
 * 案例编写专用ID  解决
 *//*
    @TableField(value="caseEditId")
    private String caseEditId;
    *//**
 * 测试意图  无
 *//*
    @FieldNote(value = "测试意图",type = FieldType.String)
    @TableField(value="intent")
    private String intent;
    *//**
 * 正反例（0反例1正例（默认正例1）） 解决
 *//*
    @FieldNote(value = "正反例",type = FieldType.String)
    @TableField(value="isNegative")
    private String isNegative;
    *//**
 * 案例级别(高、中、低)  解决
 *//*
    @FieldNote(value = "案例级别",type = FieldType.String)
    @TableField(value="casetLevel")
    private String casetLevel;
    *//**
 * 案例类型（0:功能案例，1:界面案例） 解决
 *//*
    @FieldNote(value = "案例类型",type = FieldType.String)
    @TableField(value="caseType")
    private String caseType;
    *//**
 * 测试步骤   解决
 *//*
    @FieldNote(value = "测试步骤",type = FieldType.String)
    @TableField(value="testStep")
    private String testStep;
    *//**
 * 预期结果   解决
 *//*
    @FieldNote(value = "预期结果",type = FieldType.String)
    @TableField(value="expectedResult")
    private String expectedResult;
    *//**
 * 前置条件   解决
 *//*
    @FieldNote(value = "前置条件",type = FieldType.String)
    @TableField(value="preconditions")
    private String preconditions;

    *//**
 * 所属系统名称
 *//*
    @FieldNote(value = "所属系统",type = FieldType.String)
    @TableField(value="testsystem")
    private String testsystem;
    *//**
 * 所属系统ResourceID
 *//*
    @TableField(value="testsystemResourceID")
    private Long testsystemResourceID;
    *//**
 * 所属模块名称（多级/分开）
 *//*
    @FieldNote(value = "所属模块",type = FieldType.String)
    @TableField(value="systemmodule")
    private String systemmodule;
    *//**
 * 所属模块的ResourceID
 *//*
    @TableField(value="systemmoduleResourceID")
    private Long systemmoduleResourceID;
    *//**
 * 所属项目组resourceID
 *//*
    @TableField(value="projectgroupResourceID")
    private Long projectgroupResourceID;
    *//**
 * 案例来源（默认0，项目案例为1）   解决
 *//*
    @TableField(value="leadsource")
    private Integer leadsource = 0;

    *//**
 * 所属交易名称
 *//*
    @FieldNote(value = "所属交易",type = FieldType.String)
    @TableField(value="trade")
    private String trade;
    *//**
 * 案例所属交易，指向at_Trade表
 *//*
    @TableField(value="tradeResourceID")
    private Long tradeResourceID;
    *//**
 案例所属需求，指向ds_demand表
 *//*
    @TableField(value = "demandResourceID")
    private Long demandResourceID;
    *//**
 * 运行条件名称
 *//*
    @FieldNote(value = "运行条件",type = FieldType.String)
    @TableField(value="timingName")
    private String timingName;
    *//**
 * 备注
 *//*
    @FieldNote(value = "备注",type = FieldType.String)
    @TableField(value="comment")
    private String comment;
    *//**
 * 维护人
 *//*
    @FieldNote(value = "维护人",type = FieldType.String)
    @TableField(value="maintainer")
    private String maintainer;
    *//**
 * 维护时间
 *//*
    @FieldNote(value = "维护时间",type = FieldType.Date)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 入参
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 出参
    @TableField(value="maintenanceTime")
    private Date maintenanceTime;

    *//**
 * 评审状态   解决
 *//*
    @FieldNote(value = "评审状态",type = FieldType.String)
    @TableField(value="reviewStatus")
    private String reviewStatus;

    *//*
        案例归属SIT/UAT
     *//*
    @TableField(value = "testEnviroment")
    private String testEnviroment;

    *//**
 * 案例所属任务外键
 *//*
    @TableField(value = "testtaskResourceID")
    private Long testtaskResourceID;

    *//**
 * 是否提交
 *//*
    @TableField(value = "`committed`")
    private Boolean committed;

    *//**
 * 数据需求
 *//*
    @FieldNote(value = "数据需求",type = FieldType.String)
    @TableField(value = "dataRequirements")
    private String dataRequirements;

    *//**
 * 检查点
 *//*
    @FieldNote(value = "检查点",type = FieldType.String)
    @TableField(value = "checkPoint")
    private String checkPoint;


    *//**
 * 备用字段1
 *//*
    @FieldNote(value = "案例备用字段1",type = FieldType.String)
    @TableField(value = "comments1")
    private String comments1;

    *//**
 * 备用字段2
 *//*
    @FieldNote(value = "案例备用字段2",type = FieldType.String)
    @TableField(value = "comments2")
    private String comments2;

    *//**
 * 备用字段3
 *//*
    @FieldNote(value = "案例备用字段3",type = FieldType.String)
    @TableField(value = "comments3")
    private String comments3;

    *//**
 * 备用字段4
 *//*
    @FieldNote(value = "案例备用字段4",type = FieldType.String)
    @TableField(value = "comments4")
    private String comments4;

    *//**
 * 备用字段5
 *//*
    @FieldNote(value = "案例备用字段5",type = FieldType.String)
    @TableField(value = "comments5")
    private String comments5;
    *//**
 * 备用字段6
 *//*
    @FieldNote(value = "案例备用字段6",type = FieldType.String)
    @TableField(value = "comments6")
    private String comments6;

    *//**
 * 备用字段7
 *//*
    @FieldNote(value = "案例备用字段7",type = FieldType.String)
    @TableField(value = "comments7")
    private String comments7;
    *//**
 * 备用字段8
 *//*
    @FieldNote(value = "案例备用字段8",type = FieldType.String)
    @TableField(value = "comments8")
    private String comments8;
    *//**
 * 备用字段9
 *//*
    @FieldNote(value = "案例备用字段9",type = FieldType.String)
    @TableField(value = "comments9")
    private String comments9;
    *//**
 * 备用字段10
 *//*
    @FieldNote(value = "案例备用字段10",type = FieldType.String)
    @TableField(value = "comments10")
    private String comments10;

    *//**
 * @Description: 伪版本
 *//*
    @TableField(value="identitynumber")
    private Integer identitynumber;

    *//**
 *  0:JettoManager,1:JettoAPI,2:JettoUI
 *//*
    @TableField(value = "testMode")
    private Integer testMode;

    @TableField(value = "versionResourceID")
    private Long versionResourceID;

    @FieldNote(value = "案例名称",type = FieldType.String)
    @TableField(value = "name")
    private String name;

    @TableField(exist = false)
    @FieldNote(value = "测试方式", type = FieldType.String)
    private String testModeName;

    *//**
 * 超时时间(分钟)
 *//*
    @TableField(value = "timeout")
    private Integer timeout;

    *//**
 * 是否场景案例
 *//*
    @TableField(value = "sceneCase")
    private Boolean sceneCase;

    *//**
 * UI脚本ID
 *//*
    @TableField(value = "scriptInfoID")
    private Integer scriptInfoID;
    *//**
 * API脚本ID
 *//*
    @TableField(value = "tradeFlowId")
    private Long tradeFlowId;

    @TableField(exist = false)
    @FieldNote(value = "所属项目", type = FieldType.String)
    private String testProjectName;

    @TableField(exist = false)
    private Long testProjectResourceID;
    *//**
 *  1:JettoManager,2:JettoAPI,4:JettoUI
 *  仅存储单平台
 *//*
    @TableField(value = "systemType")
    private Integer systemType;

    @TableField(value = "testProjectId")
    private Long testProjectId;

    @TableField(value = "tradeFlowCaseFolderId")
    private Long tradeFlowCaseFolderId;

*/


    @Override
    public  PageInfo<TestCase> InitModelTestCaseTables(Map<String, String> params) {
        int page = !StringUtils.isEmpty(params.get("page")) ? Integer.parseInt(params.get("page")) : 1;
        int pageSize = !StringUtils.isEmpty(params.get("pageSize")) ? Integer.parseInt(params.get("pageSize")) : 10;
        PageHelper.startPage(page, pageSize);
        List<TestCase> testCaseList = testCaseMapper.findByTradeResourceIdAndleadSource(params);
        PageInfo<TestCase> pageInfo = new PageInfo<TestCase>(testCaseList);
        return pageInfo;
            /*int pageNumber = params.get("page") != null ? (int) params.get("page") : 1;
            int pageSize = params.get("pageSize") != null ? (int) params.get("pageSize") : 10;
            if (params.get("time") != null) {
                List<String> timeList = (List<String>) params.get("time");
                params.put("startTime", timeList.get(0));
                params.put("endTime", timeList.get(1));
            }
            Page page = PageHelper.startPage(pageNumber, pageSize);
            List<TestCase> list = this.findProjectCase(params);
            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("total", page.getTotal());
            resultMap.put("pageSize", params.get("pageSize"));
            resultMap.put("totalPage", page.getPages());
            resultMap.put("page", params.get("page"));
            resultMap.put("pageData", list);*/

     }
}
