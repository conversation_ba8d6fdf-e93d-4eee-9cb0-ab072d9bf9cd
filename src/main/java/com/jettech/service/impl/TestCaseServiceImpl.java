package com.jettech.service.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import com.jettech.common.util.CheckUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.BinaryDecimalUtil;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.common.util.constants.testcase.TestCaseConstant;
import com.jettech.dao.idao.ITestCaseDao;
import com.jettech.feign.IFeignAssetsToBasic;
import com.jettech.feign.IFeignAssetsToDataDesign;
import com.jettech.model.AtTestCase;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.util.LongUtil;

/**
 * <AUTHOR>
 */
@Service
//@Transactional
public class TestCaseServiceImpl extends BaseHBServiceImpl<AtTestCase> implements ITestCaseService {

    private static final Logger logger = LoggerFactory.getLogger(TestCaseServiceImpl.class);
    /**
     * 案例
     */
    @Autowired
    private ITestCaseDao testCaseDao;
    @Autowired
    private IFeignAssetsToDataDesign feignAssetsToDataDesign;
    @Autowired
    private IFeignAssetsToBasic basicService;


    @PostConstruct
    public void postConstruct() {

        this.baseDao = testCaseDao;
    }

    /**
     * @Title: addTestCase
     * @Description: 新增案例
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @Override
    public Result addTestCase(AtTestCase testCase, String userNumber) {
    	String token = HttpRequestUtils.getCurrentRequestToken();
        synchronized (this) {
            // 必输字段非空校验
            //*案例编号、*测试意图、*正反例、*案例级别、*测试步骤、*预期结果
//            if (testCase.getIntent() == null
//                    || testCase.getIsNegative() == null || testCase.getCasetLevel() == null
//                    || testCase.getTestStep() == null || testCase.getExpectedResult() == null) {
//                return Result.renderError("必输项参数为空");
//            }
            AtTestCase save = null;
            try {
                // 根据案例编号递增规则返回案例编号，纯数字id,根据最大值递增的
                String caseId = this.getNewTestCaseCaseId(testCase.getTradeResourceID());
                testCase.setCaseId(caseId);

                HashMap<String, String> map = new HashMap<>();
                map.put("tradeResourceID", testCase.getTradeResourceID().toString());
                JettechUserDTO byNumber = basicService.findByNumber(userNumber, token);
                testCase.setMaintainer(byNumber.getUserName());
                // 维护时间
                testCase.setMaintenanceTime(new Date());

                testCase.setCaseEditId(caseId);
                //添加系统模块交易落库
                if(testCase.getTradeResourceID()!=null){
                    //根据交易id查询系统、交易及模块
                    Result tradeAndModuleResult =feignAssetsToDataDesign.getTradeSystemModule(testCase.getTradeResourceID(),token);
                    List<Map<String,Object>> tradeAndModule = (List<Map<String,Object>>)tradeAndModuleResult.getObj();
                    for (Map<String,Object> t : tradeAndModule) {
                        testCase.setTestsystem(t.get("systemName").toString());
                        testCase.setTestsystemResourceID(Long.valueOf(t.get("systemResourceID").toString()));
                        testCase.setSystemmoduleResourceID(t.get("moduleResourceID")==null?null:Long.valueOf(t.get("moduleResourceID").toString()));
                        testCase.setSystemmodule(t.get("moduleName")==null?"":t.get("moduleName").toString());
                        testCase.setTrade(t.get("tradeName").toString());
                    }
                }
                //案例字段-评审状态未启用时新增案例的评审状态默认为"已评审",20211008dingwl
                this.reviewStatusUsingEnable(testCase);

                save = this.save(testCase, userNumber);
            } catch (Exception e) {
                return Result.renderError("新增异常"+e.getMessage());
            }
        return Result.renderSuccess(save);
        }
    }
    /**
     * @Title: reviewStatusUsingEnable
     * @Description: 案例字段-评审状态未启用时新增案例的评审状态默认为"已评审"
     * @author: dingwenlong
     * @date: 2021年10月8日
     * @param testCase
     */
    @SuppressWarnings("unchecked")
	private void reviewStatusUsingEnable(AtTestCase testCase) {
    	logger.info("-- start reviewStatusUsingEnable -- ");
    	if (StringUtils.isBlank(testCase.getReviewStatus()) || !TestCaseConstant.REVIEW_STATUS_Y.equals(testCase.getReviewStatus())) {
        	//查询案例字段配置信息
    		logger.info("   1.查询案例字段配置信息 -- ");
        	Map<String, Object> fieldConfigMap;
        	Result<Map<String, Object>> fieldConfigResult = basicService.initTestCaseConfigData();
        	if (!fieldConfigResult.isSuccess()) {
        		logger.error("查询案例字段配置信息异常,异常信息: {}", fieldConfigResult.getMsg());
    			throw new RuntimeException(fieldConfigResult.getMsg());
    		} else {
    			List<Map<String, Object>> fieldConfigMapList = (List<Map<String, Object>>) fieldConfigResult.getObj();
    			if (fieldConfigMapList.isEmpty()) {
    				logger.error("案例字段配置为空！");
    				throw new RuntimeException("案例字段配置为空！");
    			}
    			fieldConfigMap = fieldConfigMapList.stream().collect(Collectors.toMap(x -> x.get("name").toString(), y -> y.get("usingenable").toString()));
    		}
        	//评审状态是否启用,未启用时设置为"已评审"
        	logger.info("   2.查询案评审状态是否启用 -- ");
        	if (!Boolean.parseBoolean(fieldConfigMap.get(TestCaseConstant.REVIEW_STATUS_FIELD).toString())) {
        		testCase.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
        	}
        }
    	logger.info("-- end reviewStatusUsingEnable -- ");
    }

    /**
     * @Title: getNewTestCaseCaseId
     * @Description: 根据最新要求返回案例编号
     * @Param: "[map]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/24
     */
    @Override
    public String getNewTestCaseCaseId(Long tradeResourceID) {
    	String token = HttpRequestUtils.getCurrentRequestToken();
        Result result = feignAssetsToDataDesign.getNewTestCaseCaseId(tradeResourceID,token);
        return String.valueOf(result.getObj());
    }

    /**
     * @Title: updateTestCase
     * @Description: 修改案例
     * @Param: "[testCase, userNumber]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @Override
    public Result updateTestCase(AtTestCase testCase, String userNumber) {
    	String token = HttpRequestUtils.getCurrentRequestToken();
        // 必输字段非空校验
        //*案例编号、*测试意图、*正反例、*案例级别、*测试步骤、*预期结果
        if (testCase.getCaseId() == null || testCase.getIntent() == null
                || testCase.getIsNegative() == null || testCase.getCasetLevel() == null
                || testCase.getTestStep() == null || testCase.getExpectedResult() == null) {
            return Result.renderError("必输项参数为空");
        }

//        Long tradeResourceID = testCase.getTradeResourceID();
//        Result userResult = feignAssetsToDataDesign.findTestSystemUserByUserNumber("trade",tradeResourceID.toString(),token);
//        String obj = (String) userResult.getObj();
//        if(!Boolean.valueOf(obj)) return Result.renderError("当前登录无权限操作该被测系统！");

        // 案例编号重复校验
        Result x = this.isNotRepeat(testCase);
        if (!x.isSuccess()) {
            return x;
        }
        AtTestCase update = null;
        try {
            AtTestCase byResourceID = this.findByResourceID(testCase.getResourceID());
            testCase.setId(byResourceID.getId());
            update = this.update(testCase, userNumber);
        } catch (Exception e) {
            return Result.renderError("修改异常");
        }
        return Result.renderSuccess(update);
    }

    /**
     * @Title: isNotRepeat
     * @Description: 判断当前交易下案例的案例编号是否重复
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/5
     */
    @Override
    public Result isNotRepeat(AtTestCase testCase) {
        boolean anyMatch;
        // 查询当前交易下的所有案例
        List<AtTestCase> testCaseList1 = this.findbyTradeResourceID(testCase.getTradeResourceID());
        // 判断是新建还是修改
        if (testCase.getResourceID() != null) {
            // 有业务主键存在是修改
            // 修改判断案例编号是否重复排除修改前的案例编号
            anyMatch = testCaseList1
                    .stream()
                    .filter(s -> !s.getResourceID().equals(testCase.getResourceID()))
                    .anyMatch(s -> s.getCaseId().equals(testCase.getCaseId()));
        } else {
            // 新增时还未生成业务主键
            anyMatch = testCaseList1
                    .stream()
                    .anyMatch(s -> s.getCaseId().equals(testCase.getCaseId()));
        }

        if (anyMatch) {
            return Result.renderError("案例编号重复");
        } else {
            return Result.renderSuccess();
        }
    }

    /**
     * @return java.lang.Integer
     */
    @Override
    public Integer findCountNumberByTradeResourceID(String t) {
        return testCaseDao.findCountNumberByTradeResourceID(t);
    }

    @Override
    /**
     * @Title findCaseTotalBySelectedCase
     * @Description 根据选中的案例查询所属交易下的案例总数
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/28
     */
    public Result findCaseTotalBySelectedCase(String caseResourceID) {
        AtTestCase byResourceID = this.findByResourceID(LongUtil.parseLong(caseResourceID));
        Integer total = testCaseDao.findCountNumberByTradeResourceID(String.valueOf(byResourceID.getTradeResourceID()));
        return Result.renderSuccess(total);
    }

    @Override
    /**
     * @Title findTestCaseMapByResourceID
     * @Description 查询单个案例
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @Date 2019/11/29
     */
    public Result<?> findTestCaseMapByResourceID(String resourceID) {
        if (null == resourceID || "".equals(resourceID)) {
            return Result.renderError("数据异常！");
        }
        AtTestCase byResourceID = this.findByResourceID(LongUtil.parseLong(resourceID));
        //JSONObject caseMap = JSONObject.parseObject(JSONObject.toJSON(byResourceID).toString());
        return Result.renderSuccess(byResourceID);
    }

    /**
     * @param
     * @return com.jettech.dto.Result<?>
     * @Description 查询当前项目下单点案例中被测系统、模块、交易树
     * <AUTHOR>
     * @date 2019-11-06 10:47
     */
    @Override
    public Result<?> findSinglePointLeftTreeByProjectResourceID() {
    	String token = HttpRequestUtils.getCurrentRequestToken();
        return feignAssetsToDataDesign.findSinglePointLeftTreeByProjectResourceID(String.valueOf(0),token);
    }

    /**
     * @Title: findbyTradeResourceID
     * @Description: 根据当前交易查询其下的所有案例
     * @Param: "[tradeResourceID]"
     * @Return: "java.util.List<com.jettech.model.TestCase>"
     * @Author: xpp
     * @Date: 2019/11/6
     */
    @Override
    public List<AtTestCase> findbyTradeResourceID(Long tradeResourceID) {
        return testCaseDao.findbyTradeResourceID(tradeResourceID);
    }

    /**
     * @Title findbyTradeResourceIDList
     * @Description 根据交易集合查询案例
     * @Params [collect]
     * @Return [List<TestCase>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    @Override
    public List<AtTestCase> findbyTradeResourceIDList(List<Long> collect) {
        return testCaseDao.findbyTradeResourceIDList(collect);
    }

    /**
     * @return com.jettech.dto.Result
     */
    @SuppressWarnings("unchecked")
	@Override
    public Result findByTradeAndOptions(HashMap paramMap) {
        String tradeResourceID = String.valueOf(paramMap.get("tradeResourceID")).trim();
        String nodeType = (String) paramMap.get("type");
        if (tradeResourceID == null || "".equals(tradeResourceID)) {
           return  Result.renderError("参数非法!");
        }
        if(StringUtils.isBlank(nodeType)) {
        	return Result.renderError("树节点类型不能为空!");
        }

        String maintainer = paramMap.get("maintainer") == null ? "" : String.valueOf(paramMap.get("maintainer")).trim();
        String isNegative = paramMap.get("isNegative") == null ? "" : String.valueOf(paramMap.get("isNegative")).trim();
        String caseType = paramMap.get("caseType") == null ? "" : String.valueOf(paramMap.get("caseType")).trim();
        int pageInt = paramMap.get("page") == null ? 1 : Integer.parseInt(paramMap.get("page").toString());
        int pageSizeInt = paramMap.get("pageSize") == null ? 10 : Integer.parseInt(paramMap.get("pageSize").toString());
        //添加环境筛选
//        if (paramMap.get("testEnviroment") == null || "".equals(String.valueOf(paramMap.get("testEnviroment")))) {
//            Result.renderError("参数testEnvironment有误!");
//        }

        List<String> tradeResIDs;
        if (nodeType.equals("trade")) {
            tradeResIDs = new ArrayList<>();
            tradeResIDs.add(tradeResourceID);
        } else {
        	 tradeResIDs=testCaseDao.findTradeBytypeResourceID(nodeType,tradeResourceID);
        }

//        String testEnvironment = paramMap.get("testEnviroment") == null ? "" : String.valueOf(paramMap.get("testEnviroment")).trim();
        String testEnvironment = "";
        //若系统下无交易，则不返回案例
        int total = tradeResIDs.size()==0 ? 0 : testCaseDao.findCountByTradeResourceID(tradeResIDs, maintainer, isNegative, caseType,testEnvironment);
        if (total == 0) {
            return Result.renderSuccess();
        }
        double totalPage = Math.ceil((double) total / pageSizeInt);
        int startIndex = (pageInt - 1) * pageSizeInt;
        List<Map<String,String>> list = testCaseDao.findByTradeAndOptions(tradeResIDs, startIndex, pageSizeInt, maintainer, isNegative, caseType,testEnvironment);
        List<Map<String, String>> resultList=new ArrayList<>();
		if(!list.isEmpty()) {
			String token = HttpRequestUtils.getCurrentRequestToken();
			Result<?> result=basicService.findFieldDataByFieldType(token);
			Map<String, Object> map=(Map<String, Object>)result.getObj();

			Result<?> resultDateType = basicService.findFieldTypeIsDate(token);
			List<String> resultDateTypeMap=(List<String>)resultDateType.getObj();
			//测试方式
            Result tmRes = basicService.findByName("TESTMODE", null);
            List<Map<String, Object>> tmList = (List<Map<String, Object>>) tmRes.getObj();
            Map<String, String> tmMap = tmList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));

            for (Map<String,String> tc : list) {
				for (Map.Entry<String, String> entry: tc.entrySet()) {
					if("maintenanceTime".equals(entry.getKey())) {
						if(!org.springframework.util.StringUtils.isEmpty(entry.getValue())) {
							String date = String.valueOf(entry.getValue());
							date = date.substring(0, 10);
							tc.put(entry.getKey(),date);
						}
					}
					//如果是日期字段，转换成字符串日期格式前端列表展示，不可用时间戳
					if(!resultDateTypeMap.isEmpty() && resultDateTypeMap.size()>0 && resultDateTypeMap.contains(entry.getKey())) {
						if(!org.springframework.util.StringUtils.isEmpty(entry.getValue())) {
							SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
				        	String format = df.format(new Date(Long.valueOf(entry.getValue().toString())));
							tc.put(entry.getKey(),format);
						}
					}

					if(!map.isEmpty() && map.containsKey(entry.getKey())) {
						if(entry.getKey().toString().equals("testMode")){//测试方式的存储是使用二进制，不适用以下方法拆分转换
                        	continue;
                        }
						Map<String, String> values=(Map<String, String>)map.get(entry.getKey());
						String names=entry.getValue();
						String resultValue="";
						if(names!=null && !"".equals(names)) {
							String[] arr=names.split(",");
							for (String str : arr) {
								resultValue="".equals(resultValue)?values.get(str):resultValue+","+values.get(str);
							}
						}
						tc.put(entry.getKey(),resultValue);
					}
				}
				 //测试方式
				try {
					if(tc.get("testMode")!=null) {
	                    List<String> testModeList = BinaryDecimalUtil.TenToDicVal(Integer.parseInt( String.valueOf(tc.get("testMode"))));
	                    String testModeValue = "";
	                    for (String s : testModeList) {
	                        testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) + ",";
	                    }
	                    tc.put("testModeKey", testModeList.toString());
	                    tc.put("testModeValue", "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
					}
                }catch (Exception ex){
                    ex.printStackTrace();
                }
				resultList.add(tc);
			}
		}


        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", total);
        resultMap.put("pageSize", pageSizeInt);
        resultMap.put("totalPage", totalPage);
        resultMap.put("page", pageInt);
        resultMap.put("pageData", list);
        return Result.renderSuccess(resultMap);
    }

    public List<Map<String, Object>> findAllTradeByParentResIDAndTaskResID( String type, String resourceID) {
        List<Map<String, Object>> res = new ArrayList<>();
        if(type.equals("system")) {
        	//通过系统查询 系统下的交易
        	//
        	List<String> tradelist=testCaseDao.findTradeBytypeResourceID(type,resourceID);
//        	 tradeResIDs = trades.stream().map(x -> x.get("resourceID").toString())
//                     .collect(Collectors.toList());
        }


//        List<Map<String, Object>> nodes = treeNodes.stream().
//                filter(x -> type.equals(x.get("type").toString()) && resId.equals(x.get("resourceID").toString())).collect(Collectors.toList());
//        Deque<Map<String, Object>> nodeQueue = new ArrayDeque<>(nodes);
//        while (!nodeQueue.isEmpty()) {
//            Map<String, Object> node = nodeQueue.poll();
//            if ("trade".equals(node.get("type"))) {
//                res.add(node);
//            } else {
//                nodes = (List<Map<String, Object>>) node.get("children");
//                if (!nodes.isEmpty()) {
//                    nodeQueue.addAll(nodes);
//                }
//            }
//        }
        return res;
    }

	/**
     * @Title: findByTestCase
     * @Description: 查询案例详情
     * @Param: "[currentTestCaseResourceID, userNumber]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @SuppressWarnings("unchecked")
	@Override
    public Result findByTestCase(String currentTestCaseResourceID) {

    	if(currentTestCaseResourceID==null || "".equals(currentTestCaseResourceID)) {
    		 return Result.renderError("参数为空！");
    	}

        Map<String, Object> map= testCaseDao.findByCaseResourceID(Long.valueOf(currentTestCaseResourceID));
        //查询多选下拉框的字段
        String token = HttpRequestUtils.getCurrentRequestToken();
        Result<?> result=basicService.findFieldsByFieldType(token);
        Map<String, String> fields=(Map<String, String>)result.getObj();
        if(!fields.isEmpty()) {
        	for (Map.Entry<String, Object> entry: map.entrySet()) {
        		if(fields.containsKey(entry.getKey())) {
        			List<String> valueList=new ArrayList<>();
        			String value=String.valueOf(entry.getValue());
        			if(value!=null && !"".equals(value) && !"null".equals(value)) {
        				String[] arr=value.split(",");
        				for (String str : arr) {
        					if(str!=null && !"".equals(str) && !"null".equals(str)) {
        						valueList.add(str);
        					}
						}
        			}
        			map.put(entry.getKey(), valueList);
        		}
        	}
        }
        //测试方式转换
        if (map.get("testMode")!=null && ((List) map.get("testMode")).size()>0 ) {
            //测试方式
            List<String> testModeList = BinaryDecimalUtil.TenToDicVal(Integer.valueOf(map.get("testMode").toString().replace("[","").replace("]","")));
            Result tmRes = basicService.findByName("TESTMODE", null);
            List<Map<String, Object>> tmList = (List<Map<String, Object>>) tmRes.getObj();
            Map<String, String> tmMap = tmList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));
            String testModeValue = "";
            for (String s : testModeList) {
                testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) + ",";
            }
            map.put("testModeKey", testModeList);
            map.put("testModeValue", "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
        }
        return Result.renderSuccess(map);
    }

    /**
     * @Title: deleteTestCase
     * @Description: 删除案例
     * @Param: "[iDList, userNumber]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @Override
    public Result deleteTestCase(List<String> iDList, String userNumber) {
        try {
        	String token = HttpRequestUtils.getCurrentRequestToken();
            List<AtTestCase> byTestCaseList = this.findByResourceIDIn(iDList);

//            Long tradeResourceID = byTestCaseList.get(0).getTradeResourceID();
//            Result userResult = feignAssetsToDataDesign.findTestSystemUserByUserNumber("trade",tradeResourceID.toString(),token);
//            String obj = (String) userResult.getObj();
//            if(!Boolean.valueOf(obj)) return Result.renderError("当前登录无权限操作该被测系统！");

            this.deleteInBatch(byTestCaseList, userNumber);
        } catch (Exception e) {
            return Result.renderError("删除异常");
        }
        return Result.renderSuccess();
    }

    /**
     * @return java.util.List<com.jettech.model.TestCase>
     */
    @Override
    public List<AtTestCase> findByCaseIdsAndTradeResourceID(List<String> caseIds, String tradeResourceID) {
        return testCaseDao.findByCaseIdsAndTradeResourceID(caseIds, tradeResourceID);
    }

    /**
     * @param
     * @return Result
     * @Title:copyTestCase
     * @Description:复制需求案例至案例库
     * @author: wu_yancheng
     * @date 2019年12月9日下午5:15:49
     */
    @Override
    public Result<?> copyTestCase(Map<String, Object> map) {
        String caseId = ((String) map.get("caseId")).trim();
        String intent = ((String) map.get("intent")).trim();
        String isNegative = ((String) map.get("isNegative")).trim();
        String casetLevel = ((String) map.get("casetLevel")).trim();
        String caseType = ((String) map.get("caseType")).trim();
        String testStep = ((String) map.get("testStep")).trim();
        String expectedResult = ((String) map.get("expectedResult")).trim();
        //String preconditions = ((String)map.get("preconditions")).trim();
        Long tradeResourceID = Long.valueOf((String) map.get("tradeResourceID"));//long
        Long demandResourceID = Long.valueOf((String) map.get("demandResourceID"));//long
        //String timingName = ((String)map.get("timingName")).trim();
        String maintainer = ((String) map.get("maintainer")).trim();
        String reviewStatus = ((String) map.get("reviewStatus")).trim();
        //String comment = ((String)map.get("comment")).trim();
        String userNumber = ((String) map.get("userNumber")).trim();
        String  caseEditId="";
        if(map.get("caseEditId")!=null)
        	caseEditId = ((String) map.get("caseEditId")).trim();
        AtTestCase testCase = new AtTestCase();
        DateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String maintenanceTime = (String) map.get("maintenanceTime");
            Date maintenanceTime1 = format.parse(maintenanceTime);
            long time = maintenanceTime1.getTime();
            Date date = new Date(time);
            testCase.setMaintenanceTime(date);
        } catch (ParseException e) {
            logger.debug(e.getMessage(), e);
        }
        testCase.setCaseId(caseId);
        testCase.setCaseEditId(caseId);
        testCase.setIntent(intent);
        testCase.setIsNegative(isNegative);
        testCase.setCasetLevel(casetLevel);
        testCase.setCaseType(caseType);
        testCase.setTestStep(testStep);
        testCase.setExpectedResult(expectedResult);
        //testCase.setPreconditions(preconditions);
        testCase.setTradeResourceID(tradeResourceID);
        testCase.setDemandResourceID(demandResourceID);
        //testCase.setTimingName(timingName);
        testCase.setMaintainer(maintainer);
        testCase.setReviewStatus(reviewStatus);
        testCase.setCaseEditId(caseEditId);
        //testCase.setComment(comment);
        if (map.get("preconditions") != null && (!((String) map.get("preconditions")).equals(""))) {
            String preconditions = ((String) map.get("preconditions")).trim();
            testCase.setPreconditions(preconditions);
        }
        if (map.get("timingName") != null && (!((String) map.get("timingName")).equals(""))) {
            String timingName = ((String) map.get("timingName")).trim();
            testCase.setTimingName(timingName);
        }
        if (map.get("comment") != null && (!((String) map.get("comment")).equals(""))) {
            String comment = ((String) map.get("comment")).trim();
            testCase.setComment(comment);
        }
        Integer count = testCaseDao.findCountByCaseID(caseId,String.valueOf(tradeResourceID));
        if (count == 0) {
            try {
                save(testCase, userNumber);
                return Result.renderSuccess();
            } catch (Exception e) {
                //System.out.println(e);
                return Result.renderError();
            }
        }
        return Result.renderSuccess();

    }

    @Override
    public Result updateTestCaseByTrade() throws Exception {
    	String token = HttpRequestUtils.getCurrentRequestToken();
        Result result = feignAssetsToDataDesign.findAllTrade(token);
        // 查询所有的交易
        List<Map<String,Object>> all = (List<Map<String, Object>>) result.getObj();
        // 根据每个交易查询其下的案例
        for (Map<String,Object> t : all) {
            List<AtTestCase> testCaseList = this.findbyTradeResourceID(Long.valueOf(t.get("resourceID").toString()));
            // 递增改变每个案例的编号
            if (testCaseList.size() > 0) {
                int i = 0000001;
                for (AtTestCase c : testCaseList) {
                    c.setCaseId(String.format("%07d", i++));
                }
                testCaseDao.update(testCaseList);
            }
        }
        return Result.renderSuccess();
    }

    @Override
    public void syncTestCase4Demand(String demandResourceID) {
        new Thread(() -> {
//        	List<AtTestCase> atTestCases = testCaseDao.findByDemandResourceIDAndTestEnviroment(demandResourceID, "SIT");
        	//需求变更，上线是上线当前需求下所有案例不再区分sit/uat/其他
            List<AtTestCase> atTestCases = testCaseDao.findByDemandResourceIDAndTestEnviroment(demandResourceID, null);
            testCaseDao.replace(atTestCases);
        }).start();
    }


    /**
     * @Title: getNewTestCaseCaseId
     * @Description: 案例资产库：根据最新要求返回最大的案例编号
     * @Param: "[tradeResourceID]"
     * @Return: "java.lang.String"
     * @Author: xpp
     * @Date: 2020/1/13
     */
    @Override
    public Result getMaxTestCaseCaseId(Long tradeResourceID) {
        String format;
        // 查询交易
        // 根据当前交易查询案例，当前交易下案例编号最大的案例
        List<AtTestCase> maxTestCase = testCaseDao.findMaxTestCasebyTradeResourceID(tradeResourceID);
        if (null != maxTestCase && maxTestCase.size()>0) {
            // 从案例编号的最大值
            format = maxTestCase.get(0).getCaseId();
        } else {
            // 当前交易下还没有案例
            format = "0000000";
        }
        return Result.renderSuccess(format);
    }

    /**
     * @Title: isOperationAuthority
     * @description: 登录用户是否有操作案例库的权限
     * @param "[userNumber]"
     * @return com.jettech.dto.Result
     * @throws
     * <AUTHOR>
     * @date 2020/1/14 11:08
     */
    @Override
    public Result isOperationAuthority() {
    	String token = HttpRequestUtils.getCurrentRequestToken();
        return feignAssetsToDataDesign.isOperationAuthority(token);
    }


    /**
     * @Title: saveImportList
     * @description: 批量导入
     * @param "[addCase, userNumber]"
     * @return java.util.List<junit.framework.TestCase>
     * @throws
     * <AUTHOR>
     * @date 2020/3/5 10:24
     */
    @Override
    public List<AtTestCase> saveImportList(ArrayList<AtTestCase> addCase, String userNumber) {
        for(AtTestCase model : addCase) {
            if(model.getCreateUser() == null || StringUtils.isBlank(model.getCreateUser())) {
                model.setCreateUser(userNumber);
            }
            if(model.getResourceID()==null){
                SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
                model.setResourceID(worker.genNextId());
            }
        }
        return testCaseDao.save(addCase);
    }
    /**
     * 根据交易和测试环境查询案例
    * @Title: findbyTradeResourceIDAndTestEnviroment
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param valueOf
    * @param @param testEnviroment
    * @param @return    参数
    * @return List<AtTestCase>    返回类型
    * @throws
    * <AUTHOR>
     */
	@Override
	public List<AtTestCase> findbyTradeResourceIDAndTestEnviroment(Long tradeRsourceID, String testEnviroment,String maintainer,
                                                                   String isNegative, String caseType) {

		return testCaseDao.findbyTradeResourceIDAndTestEnviroment(tradeRsourceID, testEnviroment, maintainer,
                 isNegative,  caseType);
	}
	/**
	 *
	 * @Title findTestCaseDictionaryData
	 * @Description 通过字典的name查询案例的字典数据
	 * @return List<Map<String,Object>> 返回类型
	 * <AUTHOR>
	 */
	@Override
	public List<Map<String, Object>> findTestCaseDictionaryData(String dicName) {
		return testCaseDao.findTestCaseDictionaryData(dicName);
	}
	/**
	 *
	 * 批量保存案例
	 *
	 */
	@Override
	public void batchesSaveTestCaseByLimit(List<AtTestCase> testCaseList, String userNumber) {
		int dataLimit=1000;
		if(!testCaseList.isEmpty()) {
			int size=testCaseList.size();
			if(dataLimit < size){
				int part = size%dataLimit==0?size/dataLimit:size/dataLimit+1;
				int tmpData = 0;
				part= CheckUtil.checkLoop(part);
				for(int i = 0; i < part; i++){
					//最后一页可能不足1000条，特殊处理，防止超出边界
					List<AtTestCase> list;
					if(i==part-1) {
						list =testCaseList.subList(tmpData, testCaseList.size());
					}else {
						list = testCaseList.subList(tmpData, tmpData+1000);
						tmpData += 1000;
					}
					save(testCaseList, userNumber);
				}
			}else{
				if (!testCaseList.isEmpty()) {
					save(testCaseList, userNumber);
				}
			}
		}
	}
	/**
	 *
	 * 批量更新案例
	 *
	 */
	@Override
	public void batchesUpdateTestCaseByLimit(List<AtTestCase> testCaseList, String userNumber) {
		int dataLimit=1000;
		if(!testCaseList.isEmpty()) {
			int size=testCaseList.size();
			if(dataLimit < size){
				int part = size%dataLimit==0?size/dataLimit:size/dataLimit+1;
				int tmpData = 0;
				part=CheckUtil.checkLoop(part);
				for(int i = 0; i < part; i++){
					//最后一页可能不足1000条，特殊处理，防止超出边界
					List<AtTestCase> list;
					if(i==part-1) {
						list =testCaseList.subList(tmpData, testCaseList.size());
					}else {
						list = testCaseList.subList(tmpData, tmpData+1000);
						tmpData += 1000;
					}
					update(testCaseList, userNumber);
				}
			}else{
				if (!testCaseList.isEmpty()) {
					update(testCaseList, userNumber);
				}
			}
		}
	}
	/**
	 *
	 * @Title: findAllUsersNameAndNumber
	 * @Description: 查询所有人员的name和number
	 * <AUTHOR>
	 */
	@Override
	public List<Map<String, String>> findAllUsersNameAndNumber() {
		return testCaseDao.findAllUsersNameAndNumber();
	}
	/**
	 *
	 * @Title findTestSystemByTradeResourceID
	 * @Description 查询交易对应的系统
	 * @return Map<String,Object> 返回类型
	 * <AUTHOR>
	 */
	@Override
	public Map<String, Object> findTestSystemByTradeResourceID(Long tradeResourceID) {
		return testCaseDao.findTestSystemByTradeResourceID(tradeResourceID);
	}
	/**
	 * @Title: findCaseIDByTradeResourceID
	 * @description: 查询交易下已经存在的全部案例编号
	 * <AUTHOR>
	 * @date 2020/6/16
	 */
	@Override
	public List<String> findCaseIDByTradeResourceID(Long tradeResourceID) {
		return testCaseDao.findCaseIDByTradeResourceID(tradeResourceID);
	}

    @Override
    public Result<?> findTestCaseMapByResourceIDFeginToDataDic(String caseResourceID) {
        if (null == caseResourceID || "".equals(caseResourceID)) {
            return Result.renderError("数据异常！");
        }
        String token = HttpRequestUtils.getCurrentRequestToken();
        AtTestCase byResourceID = this.findByResourceID(LongUtil.parseLong(caseResourceID));
        //JSONObject caseMap = JSONObject.parseObject(JSONObject.toJSON(byResourceID).toString());
        Result casetype = basicService.findByName("CASETYPE",token);
        Result casetlevel = basicService.findByName("CASETLEVEL",token);
        Result reviewstatus = basicService.findByName("REVIEWSTATUS",token);

        List<Map<String, String>> maps1 = (List<Map<String, String>>) casetype.getObj();
        List<Map<String, String>> maps2 = (List<Map<String, String>>) casetlevel.getObj();
        List<Map<String, String>> maps3 = (List<Map<String, String>>) reviewstatus.getObj();
        for (Map<String,String> map : maps1) {
            if(byResourceID.getCaseType().equals(map.get("value"))){
                byResourceID.setCaseType(map.get("textName"));
            }
        }
        for (Map<String,String> map : maps2) {
            if(byResourceID.getCasetLevel().equals(map.get("value"))){
                byResourceID.setCasetLevel(map.get("textName"));
            }
        }
        for (Map<String,String> map : maps3) {
            if(byResourceID.getReviewStatus().equals(map.get("value"))){
                byResourceID.setReviewStatus(map.get("textName"));
            }
        }
        return Result.renderSuccess(byResourceID);
    }

    @Override
    public Page<Map<String, Object>> initMyCaseToAssesWithBynumber(PageRequest pageRequest, Map<String, String> params) {
        PageHelper.startPage(pageRequest.getPageNumber() == 0 ? 1 : pageRequest.getPageNumber() + 1,
                pageRequest.getPageSize());
        String userNumber = params.get("userNumber");
        List<Map<String, Object>> caseList = testCaseDao.initMyCaseToAssesWithBynumber(userNumber, params);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>(caseList);
        return new PageImpl<Map<String, Object>>(caseList, pageRequest, pageInfo.getTotal());
	}

	/**
	 * <AUTHOR>
	 * @description 根据字典中infoName查询字典值
	 * @date 2021年01月21日 9:53
	 * @param
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 **/
    @Override
    public List<Map<String, Object>> findTestCaseDictionaryDataByInfoName(String nameDescription) {
        return testCaseDao.findTestCaseDictionaryDataByInfoName(nameDescription);
    }
}
