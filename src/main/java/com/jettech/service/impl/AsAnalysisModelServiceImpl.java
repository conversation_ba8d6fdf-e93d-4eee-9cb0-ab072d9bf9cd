package com.jettech.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jettech.common.Exception.JettoManagerException;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.GenerageCaseDto;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.constants.ComminConstants;
import com.jettech.common.vo.AttachmentAnalysisModelVO;
import com.jettech.common.vo.xmind.*;
import com.jettech.dto.*;
import com.jettech.enums.ReviewStatusEnums;
import com.jettech.feign.IFeignAssetsToAttachment;
import com.jettech.feign.IFeignAssetsToBasic;
import com.jettech.mapper.AsAnalysisModelMapper;
import com.jettech.model.AsAnalysisModel;
import com.jettech.model.AsAnalysisModelDetail;
import com.jettech.service.iservice.IAsAnalysisModelDetailService;
import com.jettech.service.iservice.IAsAnalysisModelService;
import com.jettech.util.xmind.JsonCompareUtils;
import com.jettech.util.xmind.XmindParseUtils;
import com.jettech.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 测试分析模型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Slf4j
@Service
public class AsAnalysisModelServiceImpl extends ServiceImpl<AsAnalysisModelMapper, AsAnalysisModel> implements IAsAnalysisModelService {

    @Autowired
    private LoginUserUtil loginUserUtil;

    @Autowired
    private IAsAnalysisModelDetailService iAsAnalysisModelDetailService;

    @Autowired
    private IFeignAssetsToBasic iFeignAssetsToBasic;

    private static final String currentPath = System.getProperty("user.dir");

    @Autowired
    private IFeignAssetsToAttachment feignAssetsToAttachment;

    @Autowired
    private com.jettech.feign.IFeignAssetsToDataDesign feignAssetsToDataDesign;

    /**
     * 支持分页的dto条件查询
     *
     * @param asAnalysisModelQueryDTO
     * @return IPage
     */
    @Override
    public Result<IPage<AsAnalysisModelVO>> selectPageByDto(AsAnalysisModelQueryDTO asAnalysisModelQueryDTO) {
        // 如果请求参数填写不正确 默认第1页 10条数据
        if (null == asAnalysisModelQueryDTO.getCurrent() || ComminConstants.INTEGER_0 == asAnalysisModelQueryDTO.getCurrent()) {
            asAnalysisModelQueryDTO.setCurrent(ComminConstants.INTEGER_1);
        }
        if (null == asAnalysisModelQueryDTO.getSize() || ComminConstants.INTEGER_0 == asAnalysisModelQueryDTO.getSize()) {
            asAnalysisModelQueryDTO.setSize(ComminConstants.INTEGER_10);
        }
        IPage<AsAnalysisModel> page = new Page<AsAnalysisModel>(asAnalysisModelQueryDTO.getCurrent(), asAnalysisModelQueryDTO.getSize());
        IPage<AsAnalysisModelVO> iPage = this.baseMapper.selectPageByDto(page, asAnalysisModelQueryDTO);
        iPage.getRecords().forEach(e -> {
            // 状态
            e.setStatusDesc(ReviewStatusEnums.getDesc(e.getStatus()));
        });
        return Result.renderSuccess(iPage);
    }

    /**
     * 保存或修改
     *
     * @param dto 参数
     * @return 保存结果
     */
    @Override
    public Result<Boolean> saveOne(AsAnalysisModelDTO dto, HttpServletRequest request) {
        AsAnalysisModel entity = new AsAnalysisModel();
        BeanUtils.copyProperties(dto, entity);
        entity.setTradeResourceId(Long.valueOf(dto.getTradeResourceId()));
        if (null == dto.getId() || ComminConstants.STRING_0.equals(dto.getId())) {
            // 测试模型名称去重校验
            QueryWrapper<AsAnalysisModel> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(AsAnalysisModel::getTradeResourceId, dto.getTradeResourceId()).eq(AsAnalysisModel::getName, dto.getName());
            AsAnalysisModel asAnalysisModelQuery = this.baseMapper.selectOne(queryWrapper);
            if (asAnalysisModelQuery != null) {
                return Result.renderError("该交易下已存在:" + asAnalysisModelQuery.getName());
            }
            // 新增
            //entity.setId((int)generateResourceID());
            //entity.onCreate();
            entity.setStatus(ComminConstants.INTEGER_0);
            entity.setCreateUser(loginUserUtil.getUserNumber(request));
            entity.setUpdateUser(loginUserUtil.getUserNumber(request));
            this.save(entity);
            // 保存修改的历史记录
            saveDetail(entity, dto.getRemarks(), ComminConstants.INTEGER_0, request);
            return Result.renderSuccess(entity.getId());
        }
        entity.setId(Integer.valueOf(dto.getId()));
        // 修改
        AsAnalysisModel asAnalysisModel = this.getById(entity.getId());
        if (null == asAnalysisModel) {
            return Result.renderError("数据不存在,不可做修改,请核对该数据:" + entity.getId());
        }
        if (asAnalysisModel.getStatus().equals(ComminConstants.INTEGER_2) || asAnalysisModel.getStatus().equals(ComminConstants.INTEGER_3)) {
            // TODO 修改内容如果是通过或拒绝的状态后 需要改为已变更 需要再次去提交审核
            entity.setStatus(ComminConstants.INTEGER_4);
        }
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(loginUserUtil.getUserNumber(request));
        this.updateById(entity);
        // 保存修改的历史记录
        saveDetail(entity, dto.getRemarks(), ComminConstants.INTEGER_5, request);
        return Result.renderSuccess(entity.getId());
    }


    /**
     * 修改(只做修改内容和模型名称)
     *
     * @param dto 修改参数
     * @return
     */
    @Override
    public Result<Boolean> updateOne(AsAnalysisModelUpdateDTO dto, HttpServletRequest request) {
        AsAnalysisModel entity = new AsAnalysisModel();
        BeanUtils.copyProperties(dto, entity);
        // 修改
        AsAnalysisModel asAnalysisModel = this.getById(dto.getId());
        if (null == asAnalysisModel) {
            return Result.renderError("数据不存在,不可做修改,请核对该数据:" + dto.getId());
        }
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(loginUserUtil.getUserNumber(request));
        this.updateById(entity);
        // 保存修改的历史记录
        saveDetail(entity, dto.getRemarks(), ComminConstants.INTEGER_5, request);
        return Result.renderSuccess(entity.getId());
    }

    /**
     * 根据主键查询VO
     *
     * @param id 主键
     * @return VO
     */
    @Override
    public Result<AsAnalysisModelVO> getById(String id) {
        AsAnalysisModel entity = this.getById(Long.valueOf(id));
        if (null == entity) {
            return Result.renderError("数据不存在");
        }
        AsAnalysisModelVO entityVO = new AsAnalysisModelVO();
        BeanUtils.copyProperties(entity, entityVO);
        return Result.renderSuccess(entityVO);
    }

    /**
     * 根据主键批量删除
     *
     * @param ids 主键
     * @return 删除结果
     */
    @Override
    public Result<Boolean> deleteById(List<String> ids) {
        List<AsAnalysisModel> asAnalysisModelList = this.baseMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(asAnalysisModelList) || asAnalysisModelList.size() != ids.size()) {
            return Result.renderError("数据不存在,不可做修改,请核对该数据:" + ids);
        }
        asAnalysisModelList.forEach(e -> {
            // 已经生成用例不支持删除
            if (e.getCaseNum() > 0) {
                throw new JettoManagerException("该模型已经生成用例不支持删除:" + e.getId());
            }
        });
        this.baseMapper.deleteBatchIds(ids);

        // 删除关联的模型审核
        QueryWrapper<AsAnalysisModelDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(AsAnalysisModelDetail::getModelId, ids);
        List<AsAnalysisModelDetail> asAnalysisModelDetailList = iAsAnalysisModelDetailService.list(queryWrapper);
        if (CollUtil.isNotEmpty(asAnalysisModelDetailList)) {
            iAsAnalysisModelDetailService.removeByIds(asAnalysisModelDetailList.stream().map(e -> e.getId()).collect(Collectors.toList()));
        }
        return Result.renderSuccess();
    }

    /**
     * 撤回操作
     *
     * @param id 主键
     * @return
     */
    @Override
    public Result<String> withdraw(String id, HttpServletRequest request) {
        AsAnalysisModel entity = this.getById(Long.valueOf(id));
        if (null == entity) {
            return Result.renderError("数据不存在");
        }
        // TODO 评审状态为已拒绝或已变更、且有评审通过版本的支持撤回 撤回成功后业务分析模型内容变更为最近一次的评审通过的内容
        if (!ComminConstants.INTEGER_3.equals(entity.getStatus()) && !ComminConstants.INTEGER_4.equals(entity.getStatus())) {
            // 状态0-新建,1-审核中,2-通过,3-已拒绝,4-已变更,5-修改
            return Result.renderError("该状态【" + ReviewStatusEnums.getDesc(entity.getStatus()) + "】不支持撤回");
        }
        // 有历史评审通过的版本
        QueryWrapper<AsAnalysisModelDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AsAnalysisModelDetail::getModelId, id).eq(AsAnalysisModelDetail::getStatus, ComminConstants.INTEGER_2)
                .eq(AsAnalysisModelDetail::getType, ComminConstants.INTEGER_0).orderByDesc(AsAnalysisModelDetail::getCreateDate);
        List<AsAnalysisModelDetail> asAnalysisModelDetailList = iAsAnalysisModelDetailService.list(queryWrapper);
        if (CollUtil.isEmpty(asAnalysisModelDetailList)) {
            return Result.renderError("没有审核通过的版本不可做撤回操作");
        }

        //TODO 1、先更新审核通过的内容到模型表中
        AsAnalysisModel asAnalysisModel = new AsAnalysisModel();
        asAnalysisModel.setId(entity.getId());
        asAnalysisModel.setStatus(ComminConstants.INTEGER_2);
        //asAnalysisModel.onUpdate();
        asAnalysisModel.setUpdateUser(loginUserUtil.getUserNumber(request));
        asAnalysisModel.setContent(asAnalysisModelDetailList.stream().findFirst().get().getContent());
        this.baseMapper.updateById(asAnalysisModel);

        // TODO 2、再去删除这个通过之后的审核操作记录
        QueryWrapper<AsAnalysisModelDetail> query = new QueryWrapper<>();
        query.lambda().eq(AsAnalysisModelDetail::getModelId, id).eq(AsAnalysisModelDetail::getType, ComminConstants.INTEGER_0)
                .gt(AsAnalysisModelDetail::getCreateDate, asAnalysisModelDetailList.stream().findFirst().get().getCreateDate());
        List<AsAnalysisModelDetail> deleteAsAnalysisModelDetailList = iAsAnalysisModelDetailService.list(query);
        if (CollUtil.isNotEmpty(deleteAsAnalysisModelDetailList)) {
            // 删除这个通过之后的审核操作记录
            iAsAnalysisModelDetailService.removeByIds(deleteAsAnalysisModelDetailList.stream().map(e -> e.getId()).collect(Collectors.toList()));
        }
        // TODO 增加撤回操作记录
        saveDetail(asAnalysisModel, loginUserUtil.getUserNumber(request) + "撤回", ComminConstants.INTEGER_6, request);
        return Result.renderSuccess();
    }

    /**
     * 模型内容对比
     *
     * @param id 主键id
     * @return
     */
    @Override
    public Result<AsAnalysisModelVersionVO> contrast(String id) {
        AsAnalysisModel entity = this.getById(Long.valueOf(id));
        if (null == entity) {
            return Result.renderError("数据不存在");
        }
        // 有历史评审通过的版本
        QueryWrapper<AsAnalysisModelDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AsAnalysisModelDetail::getModelId, id).eq(AsAnalysisModelDetail::getStatus, ComminConstants.INTEGER_2)
                .eq(AsAnalysisModelDetail::getType, ComminConstants.INTEGER_0).orderByDesc(AsAnalysisModelDetail::getCreateDate);
        List<AsAnalysisModelDetail> asAnalysisModelDetailList = iAsAnalysisModelDetailService.list(queryWrapper);
        if (CollUtil.isEmpty(asAnalysisModelDetailList) || asAnalysisModelDetailList.size() < ComminConstants.INTEGER_1) {
            // 评审通过的版本最少一个才能做对比 返回有多少审核通过的数据大小 给前端做判断
            return Result.renderError(String.valueOf(asAnalysisModelDetailList.size()));
        }
        AsAnalysisModelVersionVO asAnalysisModelVersionVO = new AsAnalysisModelVersionVO();
        JSONObject currentVerContentJson = JSON.parseObject(entity.getContent());
        JSONObject historyVerContentJson = JSON.parseObject(asAnalysisModelDetailList.stream().findFirst().get().getContent());
        JsonCompareUtils.jsonCompare(currentVerContentJson, historyVerContentJson);

        XmindRootVO currentVerContent = JSON.parseObject(currentVerContentJson.toJSONString(), XmindRootVO.class);
        XmindRootVO historyVerContent = JSON.parseObject(historyVerContentJson.toJSONString(), XmindRootVO.class);
        asAnalysisModelVersionVO.setCurrentVerContent(currentVerContent);
        // 对比的版本是上一个审核通过的内容
        asAnalysisModelVersionVO.setHistoryVerContent(historyVerContent);
        return Result.renderSuccess(asAnalysisModelVersionVO);
    }

    /**
     * 提交审核
     *
     * @param idList  提交的模型id
     * @param remarks 评审的内容
     * @return
     */
    @Override
    public Result<String> submitReview(List<String> idList, String remarks, HttpServletRequest request) {
        if (CollUtil.isEmpty(idList)) {
            return Result.renderError("模型最少传一条！");
        }
        List<AsAnalysisModel> asAnalysisModelList = this.baseMapper.selectBatchIds(idList);
        if (CollUtil.isEmpty(asAnalysisModelList) || asAnalysisModelList.size() != idList.size()) {
            return Result.renderError("数据不存在,请核查！" + idList);
        }
        // 状态0-新建,1-审核中,2-通过,3-已拒绝,4-已变更,5-修改
        asAnalysisModelList.stream().forEach(e -> {
            // 状态不是新建或已变更 不允许提交审核
            if (!ComminConstants.INTEGER_0.equals(e.getStatus()) && !ComminConstants.INTEGER_4.equals(e.getStatus())) {
                throw new JettoManagerException("此状态【" + ReviewStatusEnums.getDesc(e.getStatus()) + "】不允许提交审核");
            }
        });
        // 查询需要判断的角色列表
        Result<List<Map<String, String>>> result = iFeignAssetsToBasic.findByName("ROLE", "");
        log.info("result:{}", JSON.toJSONString(result));
        if (!result.isSuccess() || null == result.getObj() || result.getObj().size() <= ComminConstants.INTEGER_0) {
            return Result.renderError("数据字典配置有误,请联系管理员！");
        }
        // 模型审核角色id
        List<String> roleIdList = result.getObj().stream().filter(e -> e.get("textName").equals("模型审核角色")).map(e -> e.get("value")).collect(Collectors.toList());
        if (CollUtil.isEmpty(roleIdList)) {
            return Result.renderError("不存在模型审核角色,请确认！");
        }
        // TODO 通过角色查询该组下面有没有有这个角色的人
        JettechUserDTO jettechUserDTO = iFeignAssetsToBasic.findByNumber(loginUserUtil.getUserNumber(request), "");
        if (null == jettechUserDTO) {
            return Result.renderError("本人账号异常,请核查");
        }
      /*  Result<List<UserRoleVO>> resultUserRole = iFeignAssetsToBasic.findUserRole(roleIdList,null,Arrays.asList(String.valueOf(jettechUserDTO.getDeptResourceID())));
        if(!resultUserRole.isSuccess() ){
            return Result.renderError("此小组下没有模型审核角色,请先配置！" + resultUserRole.getMsg());
        }
        if(CollUtil.isEmpty(resultUserRole.getObj())){
            return Result.renderError("部门下模型审核角色人员为空,请核查！");
        }
        List<UserRoleVO> userRoleVOList = resultUserRole.getObj();
        String numberList = userRoleVOList.stream().map(e-> e.getNumber()).collect(Collectors.joining(","));*/

        String numberList = "zhangmengze";
        // TODO 1、批量更新审核状态
        List<AsAnalysisModel> asAnalysisModelListUpdate = new ArrayList<>();
        List<AsAnalysisModelDetail> asAnalysisModelDetailList = new ArrayList<>();
        asAnalysisModelList.stream().forEach(e -> {
            AsAnalysisModel asAnalysisModel = new AsAnalysisModel();
           /* asAnalysisModel.onUpdate();
            asAnalysisModel.setUpdateUser(loginUserUtil.getUser().getUserNumber());*/
            asAnalysisModel.setStatus(ComminConstants.INTEGER_1);
            asAnalysisModel.setId(e.getId());
            asAnalysisModel.setReviewUser(numberList);
            asAnalysisModelListUpdate.add(asAnalysisModel);

            AsAnalysisModelDetail asAnalysisModelDetail = new AsAnalysisModelDetail();
            asAnalysisModelDetail.setModelId((long) e.getId());
            asAnalysisModelDetail.setType(ComminConstants.INTEGER_0);
            //asAnalysisModelDetail.onCreate();
            asAnalysisModelDetail.setStatus(ComminConstants.INTEGER_1);
            asAnalysisModelDetail.setCreateUser(loginUserUtil.getUserNumber(request));
            asAnalysisModelDetail.setUpdateUser(loginUserUtil.getUserNumber(request));
            asAnalysisModelDetail.setRemarks(remarks);
            asAnalysisModelDetailList.add(asAnalysisModelDetail);
        });
        this.updateBatchById(asAnalysisModelListUpdate);
        // TODO 2、批量保存提交审核的
        iAsAnalysisModelDetailService.saveOrUpdateBatch(asAnalysisModelDetailList);
        return Result.renderSuccess();
    }

    /**
     * 待审核分页查询
     *
     * @param current    页数
     * @param size       条数
     * @param name       测试分析模型名称
     * @param updateUser 提交人
     * @return
     */
    @Override
    public Result<IPage<AsAnalysisModelVO>> reviewPage(Integer current, Integer size, String name, String updateUser, HttpServletRequest request) {
        IPage<AsAnalysisModel> page = new Page<AsAnalysisModel>(current, size);
        IPage<AsAnalysisModelVO> iPage = this.baseMapper.reviewPage(page, name, updateUser, loginUserUtil.getUserNumber(request), ComminConstants.STRING_1);
        iPage.getRecords().forEach(e -> {
            // 状态
            e.setStatusDesc(ReviewStatusEnums.getDesc(e.getStatus()));
        });
        return Result.renderSuccess(iPage);
    }

    /**
     * 批量审核
     *
     * @param idList  提交的模型id列表
     * @param remarks 评审的内容
     * @return
     */
    @Override
    public Result<String> reviewBatch(List<String> idList, String remarks, Integer status, HttpServletRequest request) {
        List<AsAnalysisModel> asAnalysisModelList = this.baseMapper.selectBatchIds(idList);
        if (CollUtil.isEmpty(asAnalysisModelList) || asAnalysisModelList.size() != idList.size()) {
            return Result.renderError("数据不存在,请核查！" + idList);
        }
        // 状态0-新建,1-审核中,2-通过,3-已拒绝,4-已变更,5-修改
        asAnalysisModelList.stream().forEach(e -> {
            // 不是审核中的模型不可审核
            if (!ComminConstants.INTEGER_1.equals(e.getStatus())) {
                throw new JettoManagerException("此状态【" + ReviewStatusEnums.getDesc(e.getStatus()) + "】不允许审核");
            }
        });
        // TODO 1、批量更新审核结果
        List<AsAnalysisModel> asAnalysisModelListUpdate = new ArrayList<>();
        List<AsAnalysisModelDetail> asAnalysisModelDetailList = new ArrayList<>();
        asAnalysisModelList.stream().forEach(e -> {
            AsAnalysisModel asAnalysisModel = new AsAnalysisModel();
            asAnalysisModel.setStatus(status);
            asAnalysisModel.setId(e.getId());
            asAnalysisModelListUpdate.add(asAnalysisModel);

            AsAnalysisModelDetail asAnalysisModelDetail = new AsAnalysisModelDetail();
            asAnalysisModelDetail.setModelId((long) e.getId());
            asAnalysisModelDetail.setType(ComminConstants.INTEGER_0);
            //asAnalysisModelDetail.onCreate();
            asAnalysisModelDetail.setStatus(status);
            asAnalysisModelDetail.setCreateUser(loginUserUtil.getUserNumber(request));
            asAnalysisModelDetail.setUpdateUser(loginUserUtil.getUserNumber(request));
            asAnalysisModelDetail.setRemarks(remarks);
            asAnalysisModelDetail.setContent(e.getContent());
            asAnalysisModelDetailList.add(asAnalysisModelDetail);
        });
        this.updateBatchById(asAnalysisModelListUpdate);
        // TODO 2、批量保存审核结果
        iAsAnalysisModelDetailService.saveOrUpdateBatch(asAnalysisModelDetailList);
        return Result.renderSuccess();
    }

    /**
     * 评审/修改记录
     *
     * @param id 主键id
     * @return
     */
    @Override
    public Result<List<AsAnalysisModelDetailVO>> reviewRecords(String id) {
        List<AsAnalysisModelDetailVO> asAnalysisModelDetailVOList = iAsAnalysisModelDetailService.reviewRecords(id);
        return Result.renderSuccess(asAnalysisModelDetailVOList);
    }

    /**
     * xmind解析成json
     *
     * @param multipartFile xmind文件流
     * @return
     */
    @Override
    public Result<AsAnalysisModelVersionVO> xmindParse(MultipartFile multipartFile, String tradeResourceId) {
        String fileName = multipartFile.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + ComminConstants.INTEGER_1);
        if (!fileType.toLowerCase().equals("xmind")) {
            // 文件格式校验
            return Result.renderError("导入的文件格式不正确，必须为xmind文件！");
        }
        AsAnalysisModelVersionVO asAnalysisModelVersionVO = new AsAnalysisModelVersionVO();
        QueryWrapper<AsAnalysisModel> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(AsAnalysisModel::getTradeResourceId, tradeResourceId)
                .eq(AsAnalysisModel::getName, fileName.substring(0, fileName.lastIndexOf(".")));
        AsAnalysisModel asAnalysisModel = this.baseMapper.selectOne(queryWrapper);
        // 临时存储文件
        File file = new File(currentPath + File.separator + fileName);
        try {
            multipartFile.transferTo(file);
            Result<XmindRootVO> result = XmindParseUtils.xmindToJson(file);
            if (!result.isSuccess()) {
                // 解析成功后 删除临时文件
                if (file.exists())
                    file.delete();
                return Result.renderError(result.getMsg());
            }
            if (asAnalysisModel != null) {
                // 有同名的测试分析模型
                asAnalysisModelVersionVO.setId(String.valueOf(asAnalysisModel.getId()));
                JSONObject currentVerContentJson = JSON.parseObject(JSON.toJSONString(result.getObj()));
                JSONObject historyVerContentJson = JSON.parseObject(asAnalysisModel.getContent());
                JsonCompareUtils.jsonCompare(currentVerContentJson, historyVerContentJson);

                XmindRootVO currentVerContent = JSON.parseObject(currentVerContentJson.toJSONString(), XmindRootVO.class);
                XmindRootVO historyVerContent = JSON.parseObject(historyVerContentJson.toJSONString(), XmindRootVO.class);
                asAnalysisModelVersionVO.setCurrentVerContent(currentVerContent);
                // 同名的测试分析模型
                asAnalysisModelVersionVO.setHistoryVerContent(historyVerContent);
                asAnalysisModelVersionVO.setName(asAnalysisModel.getName());
                return Result.renderSuccess(asAnalysisModelVersionVO);
            }
            asAnalysisModelVersionVO.setName(result.getObj().getRootTopic().getTitle());
            // 若不存在同名测试分析模型，则仅展示导入内容
            asAnalysisModelVersionVO.setCurrentVerContent(result.getObj());
            return Result.renderSuccess(asAnalysisModelVersionVO);
        } catch (Exception e) {
            log.error("解析xmind异常:{}", e);
            return Result.renderError("解析xmind异常:" + e);
        } finally {
            // 解析成功后 删除临时文件
            if (file.exists())
                file.delete();
        }
    }

    /**
     * xmind解析成json(只做解析)
     *
     * @param multipartFile xmind文件流
     * @return
     */
    @Override
    public Result<AsAnalysisModelToJsonVO> xmindToJson(MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf(".") + ComminConstants.INTEGER_1);
        if (!fileType.toLowerCase().equals("xmind")) {
            // 文件格式校验
            return Result.renderError("导入的文件格式不正确，必须为xmind文件！");
        }
        AsAnalysisModelToJsonVO asAnalysisModelVersionVO = new AsAnalysisModelToJsonVO();
        // 临时存储文件
        File file = new File(currentPath + File.separator + fileName);
        try {
            multipartFile.transferTo(file);
            Result<XmindRootVO> result = XmindParseUtils.xmindToJson(file);
            if (!result.isSuccess()) {
                // 解析成功后 删除临时文件
                if (file.exists())
                    file.delete();
                return Result.renderError(result.getMsg());
            }
            asAnalysisModelVersionVO.setFileName(result.getObj().getRootTopic().getTitle());
            // 若不存在同名测试分析模型，则仅展示导入内容
            asAnalysisModelVersionVO.setJsonContent(result.getObj());
            return Result.renderSuccess(asAnalysisModelVersionVO);
        } catch (Exception e) {
            log.error("解析xmind异常:{}", e);
            return Result.renderError("解析xmind异常:" + e);
        } finally {
            // 解析成功后 删除临时文件
            if (file.exists())
                file.delete();
        }
    }

    /**
     * xmind导出
     *
     * @param id       模型(主键)id
     * @param response
     */
    @Override
    public void xmindExport(String id, HttpServletResponse response) {
        AsAnalysisModel entity = this.getById(Long.valueOf(id));
        if (null == entity) {
            throw new JettoManagerException("数据不存在,请核对！");
        }
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();

            Result<String> result = XmindParseUtils.createXmind(entity.getContent(), outputStream);
            if (!result.isSuccess()) {
                throw new JettoManagerException("xmind导出异常信息:" + result.getMsg());
            }
            // 二进制流
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + java.net.URLEncoder.encode(entity.getName(), "UTF-8") + ".xmind");
            outputStream.flush();
        } catch (IOException e) {
            log.error("xmind导出异常信息:{}", e);
            throw new JettoManagerException("xmind导出异常信息:" + e);
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭流异常:{}", e);
                }
            }
        }
    }

    /**
     * 文档管理-引用模型评审通过的模型分页查询
     *
     * @param asAnalysisModelAttachmentQueryDTO 文档的测试模型查询
     * @return
     */
    @Override
    public Result<IPage<AsAnalysisModelVO>> attachmentPage(AsAnalysisModelAttachmentQueryDTO asAnalysisModelAttachmentQueryDTO) {
        // 如果请求参数填写不正确 默认第1页 10条数据
        if (null == asAnalysisModelAttachmentQueryDTO.getCurrent() || ComminConstants.INTEGER_0 == asAnalysisModelAttachmentQueryDTO.getCurrent()) {
            asAnalysisModelAttachmentQueryDTO.setCurrent(ComminConstants.INTEGER_1);
        }
        if (null == asAnalysisModelAttachmentQueryDTO.getSize() || ComminConstants.INTEGER_0 == asAnalysisModelAttachmentQueryDTO.getSize()) {
            asAnalysisModelAttachmentQueryDTO.setSize(ComminConstants.INTEGER_10);
        }
        IPage<AsAnalysisModelVO> page = new Page<AsAnalysisModelVO>(asAnalysisModelAttachmentQueryDTO.getCurrent(), asAnalysisModelAttachmentQueryDTO.getSize());
        // 评审通过的模型
        IPage<AsAnalysisModelVO> iPage = this.baseMapper.attachmentPage(page, asAnalysisModelAttachmentQueryDTO, ComminConstants.STRING_2);
        iPage.getRecords().forEach(e -> {
            // 状态
            e.setStatusDesc(ReviewStatusEnums.getDesc(e.getStatus()));
        });
        return Result.renderSuccess(iPage);
    }

    /**
     * 模型引用和案例数更新
     *
     * @param dto
     * @return
     */
    @Override
    public Result<Boolean> updateRepeatedOrCase(AsModelUpdateRepeatedCaseDTO dto) {
        if (StringUtils.isEmpty(dto.getCaseNum()) && StringUtils.isEmpty(dto.getRepeatedNum())) {
            return Result.renderError("产生用例数和模型引用必须传一个");
        }
        AsAnalysisModel asAnalysisModel = baseMapper.selectById(dto.getId());
        if (null == asAnalysisModel) {
            return Result.renderError("数据不存在,请核对该数据:" + dto.getId());
        }
        baseMapper.updateRepeatedOrCase(dto);
        return Result.renderSuccess(true);
    }

    @Override
    public void attachmentXmindExport(String id, HttpServletResponse response) {
        Result<AttachmentAnalysisModelVO> attachmentAnalysisModelVOResult = feignAssetsToAttachment.details(id);
        if (!attachmentAnalysisModelVOResult.isSuccess()) {
            throw new JettoManagerException(attachmentAnalysisModelVOResult.getMsg());
        }
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();

            Result<String> result = XmindParseUtils.createXmind(attachmentAnalysisModelVOResult.getObj().getContent(), outputStream);
            if (!result.isSuccess()) {
                throw new JettoManagerException("文档模型的xmind导出异常信息:" + result.getMsg());
            }
            // 二进制流
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + java.net.URLEncoder.encode(attachmentAnalysisModelVOResult.getObj().getName(), "UTF-8") + ".xmind");
            outputStream.flush();
        } catch (IOException e) {
            log.error("文档模型的xmind导出异常信息:{}", e);
            throw new JettoManagerException("文档模型的xmind导出异常信息:" + e);
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭流异常:{}", e);
                }
            }
        }
    }


    /**
     * 保存修改的操作记录
     *
     * @param entity
     * @param remarks
     */
    private void saveDetail(AsAnalysisModel entity, String remarks, Integer status, HttpServletRequest request) {
        AsAnalysisModelDetail asAnalysisModelDetail = new AsAnalysisModelDetail();
        asAnalysisModelDetail.setModelId((long) entity.getId());
        asAnalysisModelDetail.setType(ComminConstants.INTEGER_1);
        //asAnalysisModelDetail.onCreate();
        asAnalysisModelDetail.setCreateUser(loginUserUtil.getUserNumber(request));
        asAnalysisModelDetail.setUpdateUser(loginUserUtil.getUserNumber(request));
        asAnalysisModelDetail.setRemarks(remarks);
        asAnalysisModelDetail.setStatus(status);
        iAsAnalysisModelDetailService.save(asAnalysisModelDetail);
    }

    private long generateResourceID() {
        SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
        return worker.genNextId();
    }

    /* public void generateCase(AttachmentModelGenerateCaseDTO attachmentModelGenerateCaseDTO,HttpServletResponse response) {
         //获取trade 及相关模块和系统信息
         Result<List<SystemModuleTradeDTO>> tradeModuleResult = feignManexecuteToDatadesign.getTradeSystemModule(attachmentModelGenerateCaseDTO.getTradeResourceID());
         if (tradeModuleResult == null || !tradeModuleResult.isSuccess()) {
             throw new JettoManagerException(tradeModuleResult);
         }
         if(CollUtil.isEmpty(tradeModuleResult.getObj())){
             throw new JettoManagerException(Result.renderError("未查询到该资源[" + attachmentModelGenerateCaseDTO.getTradeResourceID()+ "]交易"));
         }
         // TODO 解析文档测试分析模型json数据 按照规则解析成用例数据
         TestCaseGenerateDTO testCaseGenerateDTO = new TestCaseGenerateDTO();
         testCaseGenerateDTO.setTradeResourceID(attachmentModelGenerateCaseDTO.getTradeResourceID());
         List<Map> caseData = new ArrayList<>();
         Map data = new HashMap();
         data.put("productAreas","企业网银/转账/行内同名");//交易路径
         data.put("name","测试引用新增用例0915-02");//用例名
         data.put("caseId","wss测试新增需求0914-01-行内同名转账-00012");//用例编号
         data.put("description","");//描述
         data.put("preconditions","");//前提条件
         data.put("isNegative","");//正反例
         data.put("tradeType","");//交易类型
         data.put("casetLevel","");//重要性
         data.put("type","");//类型
         data.put("stepType","");//步骤类型
         data.put("stepDescription","");//步骤描述
         data.put("designer","");//设计者
         data.put("owner","");//所有者
         data.put("reviewStatus","");//评审状态
         data.put("verifyIntegrationCase","");//是否验证集成用例
         data.put("whetherToPass","");//是否一次通过
         data.put("productRequirementNumber","");//需求条目编号
         data.put("reviewer","");//B岗复核人员
         data.put("auditor","");//C岗审核人员
         data.put("caseStatus","");//用例状态

         caseData.add(data);
         testCaseGenerateDTO.setCaseData(caseData);
         feignManexecuteToDatadesign.generateCaseExport(testCaseGenerateDTO,response);
     }*/
    public Result generateCase(String userNumber, String id) {
        //根据id获得模型
        AsAnalysisModel entity = this.getById(Integer.parseInt(id));
        if (null == entity) {
            return Result.renderError("数据不存在");
        }
        GenerageCaseDto generageCaseDto = new GenerageCaseDto();
        String str = entity.getContent();
        JSONObject jsonObject = JSONObject.parseObject(str);
        //json转javabean对象
        XmindRootVO xmindRootVO = JSONObject.parseObject(str, com.jettech.common.vo.xmind.XmindRootVO.class);
        TopicText topicText1 = xmindRootVO.getRootTopic();
        Children children = topicText1.getChildren();
        if (children == null) {
            return Result.renderError("请添加一级节点，至少包含一个(【业务流程】、【业务规则】、【页面控制】)");
        }
        List<TopicText> topicTexts = children.getAttached();
        //业务流程案例
        LinkedList<TopicText> businessProcessCase = new LinkedList<>();
        LinkedList<TopicText> businessRulerCase = new LinkedList<>();
        //页面控制案例
        LinkedList<TopicText> pageControllerCase = new LinkedList<>();
        List<LinkedList<TopicText>> businessProcessCaseStepList = new LinkedList<>();
        List<LinkedList<TopicText>> businessRulerCaseStepList = new LinkedList<>();
        List<LinkedList<TopicText>> pageControllerCaseStepList = new LinkedList<>();
        generageCaseDto.setProcessCaseList(businessProcessCaseStepList);
        generageCaseDto.setRulerCaseList(businessRulerCaseStepList);
        generageCaseDto.setPageCaseList(pageControllerCaseStepList);
        generageCaseDto.setTradeResourceId(entity.getTradeResourceId());
        generageCaseDto.setUserNumber(userNumber);

        List<String> topTextList = Arrays.asList(new String[]{"业务流程", "业务规则", "页面控制"});
        TopicText anyTopicText = topicTexts.stream().filter(item -> topTextList.contains(item.getTitle())).findFirst().orElse(null);
        if (anyTopicText == null) {
            return Result.renderError("请添加一级节点，至少包含一个(【业务流程】、【业务规则】、【页面控制】)");
        }
        
        for (TopicText topicText : topicTexts) {
            if (topicText.getTitle().equals("业务流程")) {
                //businessProcessCase.add(topicText.getTitle());
                getPositiveCaseStep(topicText, businessProcessCase, businessProcessCaseStepList);
            }
            if (topicText.getTitle().equals("业务规则")) {
                //businessRulerCase.add(topicText.getTitle());
                getNagativeCaseStep(topicText, businessRulerCase, businessRulerCaseStepList);
            }
            if (topicText.getTitle().equals("页面控制")) {
                // pageControllerCase.add(topicText.getTitle());
                getPositiveCaseStep(topicText, pageControllerCase, pageControllerCaseStepList);
            }

        }

        if (generageCaseDto.getProcessCaseList().size() == 0 
                && generageCaseDto.getRulerCaseList().size() == 0 
                && generageCaseDto.getPageCaseList().size() == 0) {
            return Result.renderError("模型不符合规范，二级及以上节点需要至少有一个包含2个子节点");
        }

        dealCaseStep(businessProcessCaseStepList, businessRulerCaseStepList, pageControllerCaseStepList);
        System.out.println(generageCaseDto.getTradeResourceId());
        Result r = feignAssetsToDataDesign.generateCase(generageCaseDto);
        if (r.isSuccess()) {
            entity.setCaseNum((int) r.getObj());
            entity.setReviewUser(userNumber);
            entity.setReviewDate(new Date());
            this.updateById(entity);
            return Result.renderSuccess();
        } else {
            return r;
        }
    }

    //得到正向案例
    public static void getPositiveCaseStep(TopicText topicText, LinkedList<TopicText> businessProcessCase, List<LinkedList<TopicText>> caseStepList) {
        //找到最后的节点
        if (topicText.getChildren() == null) {
            return;
        }
        List<TopicText> topicTexts = topicText.getChildren().getAttached();
        //找到有分支的节点
        if (topicTexts != null ) {
            if (topicTexts.size() > 1) {
                for (TopicText topicTextTemp : topicTexts) {
                    LinkedList<TopicText> businessProcessCase1 = new LinkedList<>();
                    businessProcessCase1.addAll(businessProcessCase);
                    businessProcessCase1.add(topicTextTemp);
                    caseStepList.add(businessProcessCase1);
                    //在这里进行递归调用
                    getPositiveCaseStep(topicTextTemp, businessProcessCase1, caseStepList);
                }
                caseStepList.remove(businessProcessCase);

            } else {
                businessProcessCase.add(topicTexts.get(0));
                getPositiveCaseStep(topicTexts.get(0), businessProcessCase, caseStepList);
            }
        }
    }

    //得到反向案例
    public static void getNagativeCaseStep(TopicText topicText, LinkedList<TopicText> businessProcessCase, List<LinkedList<TopicText>> caseStepList) {
        if (topicText.getChildren() == null) {
            return;
        }
        List<TopicText> topicTexts = topicText.getChildren().getAttached();

        if (topicTexts != null) {
            if (topicTexts.size() > 1) {

                for (TopicText topicTextTemp : topicTexts) {
                    LinkedList<TopicText> businessProcessCase1 = new LinkedList<>();
                    businessProcessCase1.addAll(businessProcessCase);
                    businessProcessCase1.add(topicTextTemp);
                    if (topicTextTemp.getMarkerRefs() != null) {
                        if (topicTextTemp.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("c_simbol-wrong") || topicTextTemp.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("symbol-wrong")) {
                            caseStepList.add(businessProcessCase1);
                        }
                    }
                    //在这里进行递归调用
                    getNagativeCaseStep(topicTextTemp, businessProcessCase1, caseStepList);
                }
                caseStepList.remove(businessProcessCase);

            } else {
                TopicText topicText1 = topicTexts.get(0);
                businessProcessCase.add(topicText1);
                if (topicText1.getMarkerRefs() != null) {

                    if (topicText1.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("c_simbol-wrong") || topicText1.getMarkerRefs().getMarkerRef().get(0).getMarkerId().equals("symbol-wrong")) {
                        caseStepList.add(businessProcessCase);
                    }


                }
                getNagativeCaseStep(topicTexts.get(0), businessProcessCase, caseStepList);
            }
        }

    }

    public static void dealCaseStep(List<LinkedList<TopicText>> businessProcessCaseStepList, List<LinkedList<TopicText>> businessRulerCaseStepList, List<LinkedList<TopicText>> pageControllerCaseStepList) {

        //判断哪一个业务流程案例包含此步骤
        for (int a = 0; a < businessRulerCaseStepList.size(); a++) {
            Notes notes = new Notes();
            notes.setContent("业务规则");
            businessRulerCaseStepList.get(a).get(0).setNotes(notes);
            for (LinkedList<TopicText> item1 : businessProcessCaseStepList) {
                ContainTitleDto data = containTitle(item1, businessRulerCaseStepList.get(a).get(0).getTitle());
                if (data.getFlag()) {
                    int count = data.getIndex();
                    for (int a1 = count - 1; a1 >= 0; a1--)
                        businessRulerCaseStepList.get(a).addFirst(item1.get(a1));

                }
                break;
            }

        }
    }


    private static ContainTitleDto containTitle(List<TopicText> topicTexts, String title) {
        int a = 0;
        ContainTitleDto containTitleDto = new ContainTitleDto();
        containTitleDto.setFlag(false);
        for (TopicText topicText : topicTexts) {

            if (topicText.getTitle().equals(title)) {
                containTitleDto.setFlag(true);
                containTitleDto.setIndex(a);
                return containTitleDto;
            }
            a++;
        }
        return containTitleDto;

    }


}

