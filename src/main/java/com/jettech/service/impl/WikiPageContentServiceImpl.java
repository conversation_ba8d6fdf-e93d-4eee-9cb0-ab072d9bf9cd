package com.jettech.service.impl;

import com.jettech.dao.idao.IWikiPageContentDao;
import com.jettech.model.WikiPageContent;
import com.jettech.service.iservice.IWikiPageContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

@Service
public class WikiPageContentServiceImpl extends BaseServiceImpl<WikiPageContent> implements IWikiPageContentService {

    @Autowired
    private IWikiPageContentDao wikiPageContentDao;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = wikiPageContentDao;
    }

    @Override
    public WikiPageContent findByPageResourceId(Long pageResourceId) {
        return wikiPageContentDao.findByPageResourceId(pageResourceId);
    }

    @Override
    public List<WikiPageContent> findByPageResourceIdIn(List<Long> pageResourceIdList) {
        return wikiPageContentDao.findByPageResourceIdIn(pageResourceIdList);
    }


}
