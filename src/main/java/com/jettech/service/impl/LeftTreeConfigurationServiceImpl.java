package com.jettech.service.impl;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jettech.dao.idao.ILeftTreeConfigurationDao;
import com.jettech.model.LeftTreeConfiguration;
import com.jettech.service.iservice.ILeftTreeConfigurationService;

/**
 * <AUTHOR>
 * @ClassName LeftTreeConfigurationServiceImpl
 * @Description 自定义左侧树实现类
 * @Date 2020-02-21 16:08
 * @Version V1.0
 */
@Service
@Transactional
public class LeftTreeConfigurationServiceImpl extends BaseServiceImpl<LeftTreeConfiguration> implements ILeftTreeConfigurationService {

    @Autowired
    private ILeftTreeConfigurationDao leftTreeConfigurationDao;

    @PostConstruct
    public void postConstruct() {
        this.baseDao = leftTreeConfigurationDao;
    }

    /**
     * @Title: findLeftTreeConfigurationByDemandResrouceIDAndType
     * @Description:  根据当前需求和类型查询自定义左侧树
     * @Param: "[demandResourceID, leftType]"
     * @Return: "java.lang.String"
     * @Author: xpp
     * @Date: 2020/2/21
     */
    @Override
    public LeftTreeConfiguration findLeftTreeConfigurationByDemandResrouceIDAndType(String demandResourceID, String leftType) {
        return leftTreeConfigurationDao.findLeftTreeConfigurationByDemandResrouceIDAndType(demandResourceID, leftType);
    }
}
