package com.jettech.service.impl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jettech.common.util.BinaryDecimalUtil;
import com.jettech.common.util.constants.testcase.TestCaseConstant;
import com.jettech.model.*;
import com.jettech.service.iservice.IProjectGroupService;
import com.jettech.service.iservice.ISystemModuleService;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.service.iservice.ITradeService;
import com.jettech.util.FieldNote;
import com.jettech.util.FieldType;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 解析案例库Excel每个行的监听器
 * @date 2020年11月05日 15:04
 **/
public class TestCaseExcelReadHandler extends AnalysisEventListener<Map<String, String>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestCaseExcelReadHandler.class);

    private static final Integer LEADSOURCE = 1; // 案例来源（默认0，项目案例为1）

    /**
     * 每隔500条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 500;

    //必须表头
    private final List<String> requiredHead = new ArrayList<>(Arrays.asList("所属系统", "所属模块", "所属交易"));

    // 数据库配置案例字段
    private List<String> testCaseFieldsAliasNameList = new ArrayList<>(Arrays.asList("* 所属模块交易层级","* 所属系统", "所属模块", "* 所属交易", "测试方式"));

    // 数据库配置案例字段
    private  List<String> requireAliasNameList = new ArrayList<>(Arrays.asList("* 所属系统", "* 所属交易"));

    //项目组
    private List<ProjectGroup> projectGroups;
    //字段
    private List<Map<String, Object>> fields;
    //被测系统
    private List<TestSystem> testSystems;
    //交易
    private List<Trade> tradeList;
    // 模块
    private List<SystemModule> moduleList;
    // 案例
    private List<TestCase> testCaseList;

    // 案例编号存在需要更新的案例caseID
    List<String> updateTestCaseCaseIdList = new ArrayList<>();

    // 案例server
    private ITestCaseService testCaseServiceImpl;
    private ITradeService tradeService;
    private ISystemModuleService systemModuleService;

    //项目组server
    private IProjectGroupService projectGroupService;
    private ProjectGroup projectGroup;
    //用户
    private String userNumber;
    private String userName;
    //字典
    private Map<String, Map<String, String>> fieldDic;
    //评审状态是否启用
    private boolean reviewStatusUsingEnable;

    //自动创建项目组是否启动
    private boolean isBuildGroup;
    // 所属项目组id
    private Long projectgroupResourceID;
    // 当前sheet也index
    private int currentSheetIndex;
    // 当前行号
    private int currentRowIndex;
    // sheet也的表头
    private Map<Integer, String> currentHead;

    private Long testProjectId;

    private Long tradeFlowCaseFolderId;

    // 存储sheet的map
    private static ConcurrentHashMap<Integer, String> sheetMap = new ConcurrentHashMap<Integer, String>();


    private List<TestCase> casesToAdd = new ArrayList<>();
    private List<TestCase> caseToUpdate = new ArrayList<>();
    public List<TestCase> getCasesToAdd() {
        return casesToAdd;
    }
    public List<TestCase> getCaseToUpdate() {
        return caseToUpdate;
    }


    //当前excel 交易与案例编号对应map
    private Map<String, List<String>> excelTradeCaseIdMap = new HashMap<>();

    private String errorMsg = "";
    public String getErrorMsg() {
        return errorMsg;
    }
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public TestCaseExcelReadHandler(Long projectResourceID,Long projectgroupResourceID, String isBuildGroup,List<Map<String, Object>> fields, Map<String, Map<String,
            String>> fieldDic,List<TestSystem> testSystems, List<Trade> tradeList, List<SystemModule> moduleList,
            ITestCaseService testCaseServiceImpl, IProjectGroupService projectGroupService,String userNumber, String userName, List<TestCase> testCaseList,boolean reviewStatusUsingEnable) {

        this.projectgroupResourceID = projectgroupResourceID;
        this.fields = fields;
        fields.forEach(x -> {
            if ((Boolean) x.get("writeRequired")) {
                requiredHead.add(x.get("aliasName").toString());
                testCaseFieldsAliasNameList.add("* " + x.get("aliasName").toString());
                requireAliasNameList.add("* " + x.get("aliasName").toString());
            } else {
                testCaseFieldsAliasNameList.add(x.get("aliasName").toString());
            }
        });

        this.testSystems = testSystems;
        this.tradeList = tradeList;
        this.moduleList = moduleList;
        this.testCaseList = testCaseList;
        this.testCaseServiceImpl = testCaseServiceImpl;
        this.tradeService = tradeService;
        this.systemModuleService = systemModuleService;

        this.userNumber = userNumber;
        this.userName = userName;
        this.fieldDic = fieldDic;
        this.reviewStatusUsingEnable = reviewStatusUsingEnable;
        this.testProjectId = projectResourceID;
        this.tradeFlowCaseFolderId = tradeFlowCaseFolderId;

        this.projectGroups = buildTree(projectGroupService.findByTestProjectResourceID(this.testProjectId,"0"));
        this.isBuildGroup = isBuildGroup.equals("1");
        this.projectGroupService = projectGroupService;
    }

    @Override
    public void invoke(Map<String, String> stringStringMap, AnalysisContext analysisContext) {
        currentRowIndex++;
        try {
            checkRowValue(stringStringMap);
        } catch (IllegalAccessException e) {
            throw new RuntimeException();
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        super.invokeHeadMap(headMap, context);
        String sheetName = context.readSheetHolder().getSheetName();
        currentSheetIndex++;
        currentRowIndex = 0;
        currentHead = headMap;
        sheetMap.put(currentSheetIndex,sheetName);
        checkSheetHead(headMap);
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        saveData();
        updateData();
        if (!CollectionUtils.isEmpty(casesToAdd)) {
            casesToAdd.clear();
        }
        if (!CollectionUtils.isEmpty(caseToUpdate)) {
            caseToUpdate.clear();
        }

        LOGGER.info("所有数据解析完成！");
    }

    private int getColIndexByName(String name) {
        for (Map.Entry<Integer, String> integerStringEntry : currentHead.entrySet()) {
            if (integerStringEntry.getValue().equals(name)) {
                return integerStringEntry.getKey();
            }
        }
        return -1;
    }

    //根据项目组树生成路径和末级节点rid的map
    public Map<String, Long> processProjectGroups(List<ProjectGroup> projectGroups) {
        Map<String, Long> resultMap = new HashMap<>();
        for (ProjectGroup group : projectGroups) {
            processGroup(group, "", resultMap);
        }
        return resultMap;
    }

    private void processGroup(ProjectGroup group, String path, Map<String, Long> resultMap) {
        String newPath = path.isEmpty() ? group.getName() : path + "/" + group.getName();

        if (group.getGroups() == null || group.getGroups().isEmpty()) {
            resultMap.put(newPath, group.getResourceID());
        } else {
            for (ProjectGroup subGroup : group.getGroups()) {
                processGroup(subGroup, newPath, resultMap);
            }
        }
    }
    //自动创建项目组
    private boolean buildGroup(String groupPath){
        String[] strings = groupPath.split("/");
        if (strings == null || projectGroups == null) {
            return false;
        }
        return processLevel(projectGroups, strings, 0);
    }
    private boolean processLevel(List<ProjectGroup> groups, String[] strings, int currentIndex) {
        if (currentIndex >= strings.length) {
            return false;
        }
        String currentName = strings[currentIndex];
        for (ProjectGroup group : groups) {
            if (group.getName().equals(currentName)) {
                if (currentIndex == strings.length - 1) {
                    return false;
                } else {
                    if(group.isSmallPoints()){
                        errorMsg += (group.getName()+"节点为末级节点，不可添加节点");
                        throw new RuntimeException(errorMsg);
                    }
                    this.projectGroup = group;
                    boolean result = processLevel(group.getGroups(), strings, currentIndex + 1);
                    if(currentIndex == strings.length-1 || result) {
                        return true;
                    }

                }
            }
        }
        this.projectGroup = addGroup(strings, currentIndex);
        List<ProjectGroup> projectGroupLists  = projectGroupService.findByParentResourceID(this.projectGroup.getParentResourceID(),"0");
        projectGroupService.save(projectGroup,userNumber);
        this.projectGroups = buildTree(projectGroupService.findByTestProjectResourceID(this.testProjectId,"0"));
        if(currentIndex<strings.length-1){
            return processLevel(projectGroupLists, strings, currentIndex + 1);
        }

        return this.projectGroup.isSmallPoints();
    }

    private ProjectGroup addGroup(String[] strings, int currentIndex) {
        ProjectGroup projectGroup = new ProjectGroup();
        projectGroup.setGroupType("0");
        projectGroup.setName(strings[currentIndex]);
        projectGroup.setTestProjectResourceID(this.testProjectId);
        projectGroup.setParentResourceID(currentIndex == 0?null:this.projectGroup.getResourceID());
        projectGroup.setSmallPoints(currentIndex==strings.length-1);
        SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
        projectGroup.setResourceID(worker.genNextId());
        return projectGroup;
    }

    //构建项目树
    public static List<ProjectGroup> buildTree(List<ProjectGroup> projectGroups) {
        Map<Long, ProjectGroup> groupMap = new HashMap<>();
        List<ProjectGroup> rootGroups = new ArrayList<>();

        for (ProjectGroup group : projectGroups) {
            groupMap.put(group.getResourceID(), group);
        }

        for (ProjectGroup group : projectGroups) {
            Long parentResourceID = group.getParentResourceID();
            ProjectGroup parentGroup = null;

            if (parentResourceID != null && groupMap.containsKey(parentResourceID)) {
                parentGroup = groupMap.get(parentResourceID);
                parentGroup.addGroup(group);
            } else {
                rootGroups.add(group);
            }
        }

        return rootGroups;
    }
    private void checkRowValue(Map<String, String> cellMap) throws IllegalAccessException {
        String msg = String.format("在名称为【%s】的sheet页内，存在以下错误：第【%s】行", sheetMap.get(currentSheetIndex), currentRowIndex + 1);

        int projectGroupKey = getColIndexByName("* 所属模块交易层级");
        String projectGroupStr = cellMap.get(projectGroupKey);
        if (StringUtils.isEmpty(projectGroupStr)) {
            errorMsg += (msg + "字段【所属模块交易层级】不能为空");
            throw new RuntimeException(errorMsg);
        } else{
            //处理项目组层级
            Map<String, Long> result =this.processProjectGroups(this.projectGroups);

            Long l = result.get(projectGroupStr);
            if (l == null) {
                if( this.isBuildGroup ){
                    if(!this.buildGroup(projectGroupStr)){
                        errorMsg += (msg + "【所属模块交易层级】创建失败");
                        throw new RuntimeException(errorMsg);
                    } else{
                        this.projectgroupResourceID = this.projectGroup.getResourceID();
                        testCaseList = new ArrayList<>();
                    }

                } if (!this.isBuildGroup){
                    errorMsg += (msg + "【所属模块交易层级】在该项目下不存在");
                    throw new RuntimeException(errorMsg);
                }
            } else{
                //判断该路径是否为末级节点
                ProjectGroup byResourceID = this.projectGroupService.findByResourceID(l);
                if (byResourceID.isSmallPoints()) {
                    this.projectgroupResourceID = l;
                    testCaseList = testCaseServiceImpl.findByProjectGroupResourceID(l);
                }else{
                    errorMsg += (msg + "【所属模块交易层级】路径为非末级节点，不可导入案例");
                    throw new RuntimeException(errorMsg);
                }
                
            }
        }

        int systemKey = getColIndexByName("* 所属系统");
        String testSystemStr = cellMap.get(systemKey);
        if (StringUtils.isEmpty(testSystemStr)) {
            errorMsg += (msg + "字段【所属系统】不能为空");
            throw new RuntimeException(errorMsg);
        }
        TestSystem testSystem = testSystems.stream().filter(x -> x.getName().equals(testSystemStr)).findFirst().orElse(null);
        if (testSystem == null) {
            errorMsg += String.format("%s，【%s】被测系统不存在。", msg, testSystemStr);
            throw new RuntimeException(errorMsg);
        }

        int tradeKey = getColIndexByName("* 所属交易");
        String tradeName = cellMap.get(tradeKey);
        if (StringUtils.isEmpty(tradeName)) {
            errorMsg += (msg + "字段【所属交易】不能为空");
            throw new RuntimeException(errorMsg);
        }
        /*Trade trade = tradeService.findByTestSystemResourceAndName(testSystem.getResourceID(), tradeName);*/
        Trade trade = tradeList.stream().filter(x -> x.getTestSystemResourceID().equals(testSystem.getResourceID())
                && x.getName().equals(tradeName)).findFirst().orElse(null);
        if (trade == null) {
            errorMsg += String.format("%s，【%s】被测系统下不存在【%s】交易。", msg, testSystemStr, tradeName);
            throw new RuntimeException(errorMsg);
        }


        // 用户在Excel文件模块列维护的数据和真实系统里面同一个被测系统下交易所属模块信息不一致，此时按照系统里面交易的所属模块存储数据
        /*SystemModule module = systemModuleService.findByResourceID(trade.getModuleResourceID());*/
        SystemModule module = moduleList.stream().filter(x -> x.getResourceID().equals(trade.getModuleResourceID())).findFirst().orElse(null);
        StringBuilder sb = new StringBuilder();
        Object[] cellKeys = cellMap.keySet().toArray();
        for (Object indexKey : cellKeys) {
            Integer index = (Integer) indexKey;
            String cellName = currentHead.get(index);
            if (StringUtils.isBlank(cellName)) {
                continue;
            }
            // 去除*号
            String cellFiledName = cellName.indexOf("*") < 0 ?  cellName : cellName.substring(cellName.indexOf("*") + 1).trim();
            if (requireAliasNameList.contains(cellName) && StringUtils.isEmpty(cellMap.get(index))) {
                sb.append(String.format("%s，字段【%s】不能为空。", msg, cellName));
                errorMsg += sb.toString();
                throw new RuntimeException(errorMsg);
            } else if (cellFiledName.equals("案例编号")) {
                String caseId = cellMap.get(index);
                if (!excelTradeCaseIdMap.containsKey(tradeName)) {
                    excelTradeCaseIdMap.put(tradeName, new ArrayList<>());
                }
                //根据交易查找交易下的案例
                List<TestCase> byCaseIdAndTradeResId = testCaseServiceImpl.findByCaseIdAndTradeResId(caseId, trade.getResourceID());
                if ((excelTradeCaseIdMap.containsKey(tradeName) && excelTradeCaseIdMap.get(tradeName).contains(caseId))) {
                    sb.append(String.format("%s，交易【%s】下存在相同的案例编号：【%s】", msg, tradeName, caseId));
                    errorMsg += sb.toString();
                    throw new RuntimeException(errorMsg);
                }
                excelTradeCaseIdMap.get(tradeName).add(caseId);

                // 如果导入文件编号唯一导入系统内发现已经存在当前案例编号则更新案例
                List<TestCase> testCases = testCaseList.stream().filter(x -> trade.getResourceID().equals(x.getTradeResourceID()) && caseId.equals(x.getCaseId())).collect(Collectors.toList());
                // 这里每一行的案例编号列都会去执行一次查询 会有问题
//                List<TestCase> testCases = testCaseServiceImpl.findByCaseIdAndTradeResId(caseId, trade.getResourceID(), projectgroupResourceID);
                if (!CollectionUtils.isEmpty(testCases)) {
                    updateTestCaseCaseIdList.add(caseId);
                }
                if(CollectionUtils.isEmpty(testCases) && !byCaseIdAndTradeResId.isEmpty()){
                    errorMsg += (msg + "字段【案例编号】在其他节点的相同交易下重复");
                    throw new RuntimeException(errorMsg);
                }
            } else if (fieldDic.containsKey(cellFiledName)) {
                String value = cellMap.get(index);
                if (StringUtils.isNotBlank(value)) {
                    List<String> valueList = Arrays.asList(value.split(","));
                    if (!fieldDic.get(cellFiledName).values().containsAll(valueList)) {
                        sb.append(String.format("%s，字段【%s】的取值不在范围【%s】内。", msg, cellName, StringUtils.joinWith(",", fieldDic.get(cellFiledName).values())));
                        errorMsg += sb.toString();
                        throw new RuntimeException(errorMsg);
                    }
                }
            }
        }

        // 封装数据
        TestCase testCase = fillTestCase(cellMap, module, null, testSystem, trade);

        //案例字段-评审状态未启用时导入案例的评审状态默认为"已评审",20211008dingwl
        if (!reviewStatusUsingEnable) {
        	testCase.setReviewStatus(TestCaseConstant.REVIEW_STATUS_Y);
        }

        if (!CollectionUtils.isEmpty(updateTestCaseCaseIdList) && updateTestCaseCaseIdList.contains(testCase.getCaseId())) {
            caseToUpdate.add(testCase);
        } else {
            casesToAdd.add(testCase);
        }

        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if ( casesToAdd.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            casesToAdd.clear();
        }

        // 达到BATCH_COUNT了，需要去更新一次数据库，防止数据几万条数据在内存，容易OOM
        if (caseToUpdate.size() >= BATCH_COUNT) {
            updateData();
            // 更新完成清理 list
            caseToUpdate.clear();
        }

    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        LOGGER.info("{}条数据，开始存储数据库！", casesToAdd.size());
        if (!CollectionUtils.isEmpty(casesToAdd)) {
            testCaseServiceImpl.save(casesToAdd, userNumber);
        }
        LOGGER.info("存储数据库成功！");
    }

    /**
     * 更新数据库
     */
    private void updateData() {
        LOGGER.info("{}条数据，开始更新数据库！", caseToUpdate.size());
        if (!CollectionUtils.isEmpty(caseToUpdate)) {
            testCaseServiceImpl.updateBatchByCaseId(caseToUpdate, BATCH_COUNT);
        }
        LOGGER.info("更新数据库成功！");
    }

    private void setFieldValue(TestCase testCase, String name, Object value) throws IllegalAccessException {

        Class clazz = TestCase.class;
        Field[] fields = clazz.getDeclaredFields();
        Field[] declaredFields = clazz.getSuperclass().getDeclaredFields();
        List<Field> fieldList = new ArrayList<>();
        fieldList.addAll(new ArrayList<>(Arrays.asList(fields)));
        fieldList.addAll(new ArrayList<>(Arrays.asList(declaredFields)));
        String[] pattern = new String[]{"yyyy-MM-dd", "yyyy/MM/dd"};
        String finalName = name;
        String dicDataName = "";
        Optional<Map<String, Object>> fieldDef = this.fields.stream().filter(x -> Objects.equals(x.get("aliasName"), finalName)).findFirst();
        if (fieldDef.isPresent()) {
            name = fieldDef.get().get("nameDescription") == null ? "" : fieldDef.get().get("nameDescription").toString();
            dicDataName = fieldDef.get().get("aliasName") == null ? "" : fieldDef.get().get("aliasName").toString();
        }
        if ("测试方式".equals(finalName)) {
            dicDataName = finalName;
        }
        Map<String,String> dateConfig = fieldDic.get("dateConfig");
        //get dic value
        if (fieldDic.containsKey(dicDataName)) {
            List<String> valueList = (value == null ? null : Arrays.asList(value.toString().split(",")));
            List<String> keyList = new ArrayList<>();
            for (Map.Entry<String, String> stringStringEntry : fieldDic.get(dicDataName).entrySet()) {
                if (!CollectionUtils.isEmpty(valueList) && valueList.contains(stringStringEntry.getValue())) {
                    keyList.add(stringStringEntry.getKey());
                }
            }
            value = keyList.stream().collect(Collectors.joining(","));
            //处理评审状态导入为空时，案例该字段设为空
            if ("评审状态".equals(dicDataName)&&value==null) {
                testCase.setReviewStatus("");
            }
            if ("测试方式".equals(dicDataName)) {
                String testMode = ((String) value).replaceAll(",", "");
                String x = BinaryDecimalUtil.DicValToBin(testMode);
                int y = BinaryDecimalUtil.BinToTen(x);
                testCase.setTestMode(y);
            }
        }
        if (fieldDef.isPresent()) {
            name = fieldDef.get().get("nameDescription") == null ? "" : fieldDef.get().get("nameDescription").toString();
        }
        for (Field f : fieldList) {
            f.setAccessible(true);
            FieldNote fieldNote = f.getAnnotation(FieldNote.class);
            if (fieldNote == null)
                continue;
            String fieldNoteValue = fieldNote.value();

            if (fieldNoteValue.equals(name)) {
                if (fieldNote.type() == FieldType.Date) {
                    Date date = null;
                    try {
                        date = DateUtils.parseDate(value.toString(), pattern);
                    } catch (Exception e) {
                        LOGGER.error("解析日期出错", e);
                    }
                    f.set(testCase, date);
                } else if (fieldNote.type() == FieldType.Long) {
                    f.set(testCase, (Long) value);
                } else {
                    if(dateConfig != null  && dateConfig.containsKey(name)){
                        Date date = new Date();
                        try {
                            date = DateUtils.parseDate(value.toString(), pattern);
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        f.set(testCase, String.valueOf(date.getTime()));
                    }else{
                        f.set(testCase, value.toString());
                    }
                }
                break;
            }

        }
    }

    private TestCase fillTestCase(Map<String, String> cellMap, SystemModule module, TestCaseRecycleBin recycleBinObj, TestSystem testSystem, Trade trade) throws IllegalAccessException {
        TestCase testCase = new TestCase();
        Object[] keys = cellMap.keySet().toArray();
        for (Object key : keys) {
            String name = currentHead.get((Integer) key);
            if (StringUtils.isBlank(name)) {
                continue;
            }
            // 去除*号
            String fildName = name.indexOf("*") < 0 ?  name : name.substring(name.indexOf("*") + 1).trim();
            //处理评审状态导入为空时，案例该字段设为空
            if (cellMap.get(key) == null && !name.equals("评审状态")) {
                continue;
            }
            setFieldValue(testCase, fildName, cellMap.get(key));
        }
        testCase.setLeadsource(LEADSOURCE);
        testCase.setProjectgroupResourceID(projectgroupResourceID);
        testCase.setTestsystemResourceID(testSystem.getResourceID());
        testCase.setTestsystem(testSystem.getName());
        testCase.setTradeResourceID(trade.getResourceID());
        testCase.setTrade(trade.getName());
        if (null != module) {
            // 拼装module层级目录
            String moduleNameTreeString = getModuleNameTreeString(module, module.getName());
            testCase.setSystemmodule(moduleNameTreeString);
            testCase.setSystemmoduleResourceID(module.getResourceID());
        }
        if (StringUtils.isBlank(testCase.getMaintainer())) {
            testCase.setMaintainer(userName);
        }
        if (null == testCase.getMaintenanceTime()) {
            testCase.setMaintenanceTime(new Date());
        }
        testCase.setTestProjectId(testProjectId);
        testCase.setTradeFlowCaseFolderId(tradeFlowCaseFolderId);

        testCase.setCaseEditId(testCase.getCaseId());
        if (testCase.getTestMode() == null){
            testCase.setTestMode(1);
        }
        //评审状态为空，设为空字符串
        if (null == testCase.getMaintenanceTime()) {
            testCase.setMaintenanceTime(new Date());
        }
        return testCase;
    }


    private String getModuleNameTreeString(SystemModule module, String name) {
        SystemModule parentModule = moduleList.stream().filter(x -> Objects.equals(x.getResourceID(), module.getParentResourceID())).findFirst().orElse(null);
        if (null != parentModule) {
            name = getModuleNameTreeString(parentModule, parentModule.getName() + "/" + name);
        }
        return name;
    }


    private void checkSheetHead(Map<Integer, String> headMap) {
        if (!errorMsg.isEmpty()) {
            errorMsg += "，";
        }
        boolean a = headMap.values().stream().allMatch( v -> testCaseFieldsAliasNameList.contains(v));
        if (!a ){
            errorMsg += String.format("在名称为【%s】的sheet页内某些列名与案例字段名称不匹配或必输项不存在或者缺少必输字段，请重新维护或下载模板！", sheetMap.get(currentSheetIndex));
            throw new RuntimeException(errorMsg);
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
        if (errorMsg.isEmpty()) {
            errorMsg = "导入时发生未知错误";
        }
        throw exception;
    }

}
