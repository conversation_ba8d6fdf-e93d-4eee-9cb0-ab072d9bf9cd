package com.jettech.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.DTO.VersionInfoDto;
import com.jettech.common.util.UserVo;
import com.jettech.dao.idao.IVersionInfoDao;
import com.jettech.model.VersionInfo;
import com.jettech.service.iservice.IVersionInfoService;
import com.jettech.view.VersionInfoView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/20
 **/
@Service
public class VersionInfoServiceImpl extends BaseHBServiceImpl<VersionInfo> implements IVersionInfoService {

    @Autowired
    private IVersionInfoDao versionInfoDao;

    @PostConstruct
    private void postConstruct() {
        this.baseDao = versionInfoDao;
    }

    public PageInfo<VersionInfoView> page(VersionInfoDto dto) {
        PageHelper.startPage(dto.getPageNumber(), dto.getPageSize());
        List<VersionInfoView> versionInfoList = versionInfoDao.findByCondition(dto);
        PageInfo<VersionInfoView> pageInfo = new PageInfo<>(versionInfoList);
        return pageInfo;
    }

    @Override
    public VersionInfo submit(VersionInfo versionInfo, UserVo user) {
        try {
            if (versionInfo.getId() != null && versionInfo.getId() > 0) {
                VersionInfo v = this.find(versionInfo.getId());
                if (v != null) {
                    v.setName(versionInfo.getName());
                    v.setRemarks(versionInfo.getRemarks());
                    versionInfo = this.update(v, user.getUserNumber());
                }
            } else {
                versionInfo = this.save(versionInfo, user.getUserNumber());
            }
            return versionInfo;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public int del(String ids, UserVo user) {
        String[] idsArr = ids.split(",");
        if (idsArr != null && idsArr.length > 0) {
            List<VersionInfo> list = new ArrayList<>();
            Arrays.stream(idsArr).forEach(x -> {
                VersionInfo info = new VersionInfo();
                info.setId(Long.parseLong(x));
                list.add(info);
            });
            this.deleteInBatch(list, user.getUserNumber());
            return idsArr.length;
        }
        return 0;
    }
}
