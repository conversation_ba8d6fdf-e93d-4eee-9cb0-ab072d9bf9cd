package com.jettech.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.DTO.VersionInfoDto;
import com.jettech.dao.idao.ITestCaseVersionDao;
import com.jettech.model.TestCaseVersion;
import com.jettech.service.iservice.ITestCaseVersionService;
import com.jettech.view.TestCaseVersionView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/25
 **/
@Service
public class TestCaseVersionServiceImpl extends BaseHBServiceImpl<TestCaseVersion> implements ITestCaseVersionService {

    @Autowired
    private ITestCaseVersionDao testCaseVersionDao;

    @PostConstruct
    private void postConstruct() {
        this.baseDao = testCaseVersionDao;
    }

    @Override
    public PageInfo<TestCaseVersionView> page(VersionInfoDto dto) {
        PageHelper.startPage(dto.getPageNumber(), dto.getPageSize());
        List<TestCaseVersionView> list = testCaseVersionDao.findByCondition(dto);
        PageInfo<TestCaseVersionView> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }
}
