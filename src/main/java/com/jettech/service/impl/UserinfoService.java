/**
 * 
 */
package com.jettech.service.impl;

import java.util.List;
import java.util.Optional;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jettech.dao.idao.IUserDao;
import com.jettech.model.User;
import com.jettech.service.iservice.IUserinfoService;

/**
 *  <AUTHOR>
 */

@Service
public class UserinfoService extends BaseServiceImpl<User> implements IUserinfoService {
	
	@Autowired
    IUserDao userDao;

	@PostConstruct
	public void postConstruct() {
		
		this.baseDao = userDao;
	}
	/**
	 * @desc 根据用户编码查询用户
	 * @param userName
	 * <AUTHOR>
     * @date: 2019年6月5日 上午10:41:37
	 */
	public Optional<User> findByNumber(String number) {
		
		return Optional.ofNullable(userDao.findByNumber(number));
	}
	/**
	 * @desc 根据用户名称查询用户
	 * @param userName
	 * <AUTHOR>
     * @date: 2019年6月5日 上午10:41:37
	 */
	@Override
	public Optional<User> findByUserName(String userName) {
		return Optional.ofNullable(userDao.findByUserName(userName));
	}

	@Override
	public List<String> findByUserResourceIDs(String valueOf) {

		return userDao.findByUserResourceIDs(valueOf);
	}

	@Override
	public List<String> findByUserNumberResourceIDs(String userResourceIDs) {
		return userDao.findByUserNumberResourceIDs(userResourceIDs);
	}

}
