package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.util.FtpUtil;
import com.jettech.common.util.ParamConfig;
import com.jettech.dao.idao.ITestCaseFileDao;
import com.jettech.model.TestCaseFile;
import com.jettech.service.iservice.ITestCaseFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TestCaseFileServiceImpl extends BaseServiceImpl<TestCaseFile> implements ITestCaseFileService {

    @Autowired
    ParamConfig paramConfig;

    @Autowired
    private FtpUtil ftpUtil;

    @Autowired
    private ITestCaseFileDao testCaseFileDao;

    @PostConstruct
    private void postConstruct() {
        baseDao = this.testCaseFileDao;
    }


    @Override
    public List<TestCaseFile> findTestCaseFileByResourceID(Long testCaseResourceID) {
        return testCaseFileDao.findTestCaseFileByResourceID(testCaseResourceID);
    }

    @Override
    public String downloadCaseFile(Map<String, Object> mapParam) throws IOException {
        HttpServletResponse response = (HttpServletResponse) mapParam.get("response");
        String resourceID = (String) mapParam.get("resourceID");

        //查询附件信息
        TestCaseFile testCaseFile = testCaseFileDao.findByResourceID(Long.valueOf(resourceID));
        //时间戳。。
        String extname = testCaseFile.getExtname();
        String path = testCaseFile.getPath();
        byte[] data = this.getFileData(resourceID, extname,path);
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
       // response.setContentType("application/octet-stream;charset=GBK");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("XXXX的附件下载.zip","utf-8"));
        response.addHeader("Content-lenth", "" + data.length);
        response.getOutputStream().write(data);
        return null;
    }

    @Override
    public Result<?> deleteCaseFile(String resourceIDs, String userNumber) {
        if(resourceIDs==null || "".equals(resourceIDs)) {
            return Result.renderError("附件的resourceIDs为空！");
        }
        String[] rids = resourceIDs.split(",");
        List<TestCaseFile> testCaseFileList = testCaseFileDao.findByResourceIDIn(Arrays.asList(resourceIDs.split(",")));
        Map<String,TestCaseFile> map = new HashMap<>();
        testCaseFileList.stream().forEach(x ->{
            map.put(String.valueOf(x.getResourceID()),x);
        });
        if(paramConfig.getIsFtpOn()) {//判断ftp是否打开
            for (String resourceID : rids) {
                Long testCaseresourceID=map.get(resourceID).gettestCaseresourceID();
                ftpUtil.removeFile(paramConfig.getFtpPath() +map.get(resourceID).getPath()+ File.separator , resourceID+map.get(resourceID).getExtname());
            }

        }else {
            for (String resourceID : rids) {
                File dir = new File(paramConfig.getAttachmentPath());
                if(dir.isDirectory()) {
                    File[] fiels=dir.listFiles();
                    for (File file : fiels) {
                        String name=file.getName();
                        if(name.equals(resourceID+map.get(resourceID).getExtname())) {
                            file.delete();
                        }
                    }
                }
            }
        }
        if(!testCaseFileList.isEmpty()) {
            testCaseFileDao.deleteInBatch(testCaseFileList);
        }
        return Result.renderSuccess("操作成功！");
    }

    /**
     *
     * @Title: getFileData
     * @Description: 获取附件
     * @param resourceID
     * @param exName
     * @return
     * <AUTHOR>
     * @param path
     */
    private byte[] getFileData(String resourceID,String exName, String path) {

        InputStream in = null;
        if(paramConfig.getIsFtpOn()) {//判断ftp是否打开
            in = ftpUtil.startDown(paramConfig.getFtpPath()+path+ File.separator+resourceID+exName);
            ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
            byte[] buff = new byte[1024]; //buff用于存放循环读取的临时数据
            int rc = 0;
            try {
                while ((rc = in.read(buff)) != -1) {
                    byteStram.write(buff, 0, rc);
                }
            } catch (IOException e) {
                e.printStackTrace();;
            }
            byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果

            return bytes;
        }else {
            File file = new File(paramConfig.getAttachmentPath() + File.separator+resourceID+exName);
            InputStream inputStream=null;
            if(file.exists()){
                try {
                     inputStream = new FileInputStream(file);
                    byte[] data=new byte[(int) file.length()];
                    inputStream.read(data);
                    inputStream.close();
                    return data;
                } catch (Exception e) {
                    e.printStackTrace();;
                }finally {
                    try {
                        if(inputStream!=null){
                            inputStream.close();
                        }
                    }catch (Exception e){

                    }
                }
            }

        }
        return null;
    }
}
