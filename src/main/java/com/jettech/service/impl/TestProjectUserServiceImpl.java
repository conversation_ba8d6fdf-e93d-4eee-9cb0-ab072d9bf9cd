package com.jettech.service.impl;

import com.jettech.DTO.ProjectUserDTO;
import com.jettech.common.dto.Result;
import com.jettech.dao.idao.ITestProjectUserDao;
import com.jettech.feign.IFeignDataDesignToBugService;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 项目和人员关联
* @ClassName: TestProjectUserServiceImpl
* @Description: TODO(这里用一句话描述这个类的作用)
* <AUTHOR>
* @date 2020年4月20日
*
 */
@Service
@Transactional
public class TestProjectUserServiceImpl extends BaseServiceImpl<TestProjectUser> implements ITestProjectUserService {
    @Autowired
    private ITestProjectUserDao testProjectUserDao;
    @Autowired 
    private ITestProjectService testProjectService;
    @Autowired
	private ITestCaseService testCaseService;
    @Autowired
	private ITestCasequoteService testCasequoteService;
    @Autowired
	private IPerFormCaseService perFormCaseService;
    @Autowired
	private IDemandService demandService;
    @Autowired
	private IFeignDataDesignToBugService feignDataDesignToBugService;
    @Autowired
	private IUserinfoService userinfoService;
    @Autowired
	private IProjectGroupService projectGroupService;

	@Autowired
	private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;

    @PostConstruct
    public void postConstruct(){
        baseDao = testProjectUserDao;
    }
    
    /**
	 * 
	 * @Title: findByTestProjectResourceID
	 * @Description: 查询项目关联的人员
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午3:01:07
	 */
    @Override
	public List<TestProjectUser>findByTestProjectResourceID(Long resourceID){
    	return testProjectUserDao.findByTestProjectResourceID(resourceID);
    }
    
    /**
	 * 
	 * @Title: findNotRelateUser 
	 * @Description: 查询未关联的人员
	 * @param name
	 * @param deptName
     * @param testProjectResourceID
     * @param userGroupReourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:05:58
	 */
	@Override
	public Result<?> findNotRelateUser(String name, String deptName, String testProjectResourceID, String userGroupReourceID, PageRequest pageRequest){
		if(testProjectResourceID==null || "".equals(testProjectResourceID)) {
			return Result.renderError("项目的resourceID为空！");
		}
		List<String> userGroupReourceIDs = null;
		if (StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		Page<Map<String, Object>>  list=testProjectUserDao.findNotRelateUser(name,deptName,Long.valueOf(testProjectResourceID),userGroupReourceIDs,pageRequest);
		return Result.renderSuccess(list);
	}
	
	/**
	 * 
	 * @Title: findRelatedUser 
	 * @Description: 查询已关联人员
	 * @param name
	 * @param deptName
	 * @param testProjectResourceID
	 * @param userGroupReourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:28:52
	 */
	@Override
	public Result<?> findRelatedUser(String name, String deptName, String testProjectResourceID, String userGroupReourceID, PageRequest pageRequest){
		if(testProjectResourceID==null || "".equals(testProjectResourceID)) {
			return Result.renderError("项目的resourceID为空！");
		}
		List<String> userGroupReourceIDs = null;
		if (StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		Page<Map<String, Object>> list=testProjectUserDao.findRelatedUser(name,deptName,Long.valueOf(testProjectResourceID),userGroupReourceIDs,pageRequest);
		return Result.renderSuccess(list);
	}
	
	/**
	 * 
	 * @Title: relateUser 
	 * @Description: 关联人员
	 * @param userResourceIDs
	 * @param testProjectResourceID
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:38:20
	 */
	@Override
	public Result<?> relateUser(String userResourceIDs,String testProjectResourceID,String userNumber){
		if(testProjectResourceID==null || "".equals(testProjectResourceID)) {
			return Result.renderError("项目的resourceID为空！");
		}
		if(userResourceIDs==null || "".equals(userResourceIDs)) {
			return Result.renderError("请选择人员！");
		}
		TestProject testProject=testProjectService.findByResourceID(Long.valueOf(testProjectResourceID));
		if(testProject==null) {
			return Result.renderError("项目不存在！");
		}
		String[] arr=userResourceIDs.split(",");
		List<TestProjectUser> list=new ArrayList<>();
		for (String str : arr) {
			if(str!=null && !"".equals(str)) {
				TestProjectUser testProjectUser=new TestProjectUser();
				testProjectUser.setTestProjectResourceID(testProject.getResourceID());
				testProjectUser.setUserResourceID(Long.valueOf(str));
				list.add(testProjectUser);
			}
		}
		if(!list.isEmpty()) {
			save(list, userNumber);
		}
		return Result.renderSuccess();
	}

	/**
	 *
	 * @Title: cancelRelatedUser
	 * @Description: 取消关联人员
	 * @param userResourceIDs
	 * @param testProjectResourceID
	 * @param userNumber
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:38:20
	 */
	@Override
	public Result<?> cancelRelatedUser(String userResourceIDs, String testProjectResourceID, String userNumber){
		List<String> user = userinfoService.findByUserResourceIDs(userResourceIDs);
		List<String> userNumbers = userinfoService.findByUserNumberResourceIDs(userResourceIDs);
		if(testProjectResourceID==null || "".equals(testProjectResourceID)) {
			return Result.renderError("项目的resourceID为空！");
		}
		if(userResourceIDs==null || "".equals(userResourceIDs)) {
			return Result.renderError("请选择人员！");
		}
		TestProject testProject=testProjectService.findByResourceID(Long.valueOf(testProjectResourceID));
		if(testProject==null) {
			return Result.renderError("项目不存在！");
		}
		String[] arr=userResourceIDs.split(",");

		List<ProjectGroup> groupList = projectGroupService.findByTestProjectResourceID(Long.valueOf(testProjectResourceID), null);
		List<String> projectGroups = new ArrayList<>();
		Map mapName = new HashMap();
		if(groupList!=null&&groupList.size()>0){
			for (ProjectGroup projectGroup : groupList) {
				projectGroups.add(projectGroup.getResourceID().toString());
			}
			List<TestCase> caseList = testCaseService.findByProjectGroupResourceIDs(projectGroups,"","","","","","","");
			if(caseList!=null&&caseList.size()>0){
				for (TestCase testCase : caseList) {
					String maintainer = testCase.getMaintainer();
					if(!StringUtils.isEmpty(maintainer)){
						for (String userName : user) {
							if(userName.equals(maintainer)){
								return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
							}
						}
					}

					List<TestCasequote> testCasequote = testCasequoteService.findByCaseResourceID(testCase.getResourceID().toString());
					for (TestCasequote casequote : testCasequote) {
						mapName.put(casequote.getExecutorNumber(),null);
						mapName.put(casequote.getCreateUser(),null);
					}
					if(testCasequote!=null){
						for (String userNumber1 : userNumbers) {
							if(mapName.get(userNumber1)!=null){
								return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
							}
						}
					}
					List<PerFormCase> perFormCase = perFormCaseService.findByTestcaseResourceID(testCase.getResourceID().toString());
					if(perFormCase!=null && perFormCase.size()>0){
						if (perFormCase.stream().anyMatch(x->  userNumbers.contains(x.getCreateUser())
								|| userNumbers.contains(x.getExecutornumber()))){
							return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
						}

					}
				}
			}
		}
		List<Map<String, String>> listOne = feignDataDesignToBugService.findbugsByTestProjectResourceID(testProjectResourceID);
		Map<String, String> mapUser = new HashMap<>();
		if(listOne!=null&&listOne.size()>0){
			for (Map<String, String> map : listOne) {
				mapUser.put(map.get("handleUser"),"");
				mapUser.put(map.get("createUserName"),"");
			}

			for (String name: user) {
				if(mapUser.get(name)!=null){
					return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
				}
			}
		}
		List<Demand> demandList = demandService.findByTestProjectResourceID(Long.valueOf(testProjectResourceID));
		for (Demand demand : demandList) {
			List<Map<String, Object>> mapBug = feignDataDesignToBugService.findBugsByDemandResourceID(demand.getResourceID(), null);
			for (Map<String, Object> map : mapBug) {
				for (String userNumber1 : userNumbers) {
					if(userNumber1.equals(map.get("handleUserNumber"))||userNumber1.equals(map.get("createUser"))){
						return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
					}
				}
			}
			List<TestCase> testCases = testCaseService.findBydemandResourceID(demand.getResourceID());
			for (TestCase testCase : testCases) {
				String maintainer = testCase.getMaintainer();
				if(!StringUtils.isEmpty(maintainer)){
					for (String userName : user) {
						if(userName.equals(maintainer)){
							return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
						}
					}
				}

				List<TestCasequote> testCasequote = testCasequoteService.findByCaseResourceID(testCase.getResourceID().toString());
				for (TestCasequote casequote : testCasequote) {
					mapName.put(casequote.getExecutorNumber(),null);
					mapName.put(casequote.getCreateUser(),null);
				}
				if(testCasequote!=null){
					for (String userNumber1 : userNumbers) {
						if(mapName.get(userNumber1)!=null){
							return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
						}
					}
				}
				List<PerFormCase> perFormCase = perFormCaseService.findByTestcaseResourceID(testCase.getResourceID().toString());
				if(perFormCase!=null && perFormCase.size()>0){
					if (perFormCase.stream().anyMatch(x->  userNumbers.contains(x.getCreateUser())
							|| userNumbers.contains(x.getExecutornumber()))){
						return Result.renderError("用户在当前项目已经维护资产（案例、缺陷），不允许移除");
					}

				}
			}
		}

		List<Long> list=new ArrayList<>();
		for (String str : arr) {
			if(str!=null && !"".equals(str)) {
				list.add(Long.valueOf(str));
			}
		}
		if(!list.isEmpty()) {
			List<TestProjectUser> users=findByTestProjectResourceIDAndUserResourceIDIn(testProject.getResourceID(),list);
			if(!users.isEmpty()) {
				deleteInBatch(users, userNumber);
			}
		}
		return Result.renderSuccess();
	}
	
	/**
	 * 
	 * @Title: findByTestProjectResourceIDAndUserResourceIDIn 
	 * @Description: 根据项目和人员查询关联关系
	 * @param testProjectResourceID
	 * @param userResourceIDs
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:52:21
	 */
	@Override
	public List<TestProjectUser> findByTestProjectResourceIDAndUserResourceIDIn(Long testProjectResourceID,List<Long> userResourceIDs){
		return testProjectUserDao.findByTestProjectResourceIDAndUserResourceIDIn(testProjectResourceID,userResourceIDs);
	}
	
	/**
	 * 
	 * @Title: findByTestProjectResourceIDIN 
	 * @Description: 查询项目与人员关联关系
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 上午9:53:14
	 */
	@Override
	public List<TestProjectUser> findByTestProjectResourceIDIN(List<Long> resourceIDList){
		return testProjectUserDao.findByTestProjectResourceIDIN(resourceIDList);
	}

	/**
	 * @Method: findALlProjectAndUser
	 * @Description: 项目和用户的关联关系（带用户名称和number）
	 * @Param: " [] "
	 * @return: java.util.List<com.jettech.model.TestProjectUser>
	 * @Author: wws
	 * @Date: 2020/9/23
	 */
	@Override
	public List<ProjectUserDTO> findALlProjectAndUser() {
		return testProjectUserDao.findALlProjectAndUser();
	}

}
