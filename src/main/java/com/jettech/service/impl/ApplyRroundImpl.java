package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.dao.idao.IApplyRroundDao;
import com.jettech.model.ApplyRround;
import com.jettech.service.iservice.IApplyRroundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
@Service
public class ApplyRroundImpl extends BaseHBServiceImpl<ApplyRround> implements IApplyRroundService {

    @Autowired
    private IApplyRroundDao applyRroundDao;


    @PostConstruct
    public void postConstruct() {

        this.baseDao = applyRroundDao;
    }

    /**
     * @Title: findApplyRround
     * @Description: 应用轮次(分页 + 条件查询)
     * @Param: params
     * @Author: dwl
     * @Date: 2024/7/15
     */
    @Override
    public Result findApplyRround(Map<String, String> params) {
        return applyRroundDao.findApplyRround(params);
    }

    /**
     * @Title: addApplyRround
     * @Description: 新增应用轮次
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @Override
    public Result<?> addApplyRround(Map<String, String> params, String userNumber) {
        String round = params.get("round");
        String ip = params.get("ip");
        String port = params.get("port");
        String remarks = params.get("remarks");
        String applyResourceID = params.get("applyResourceID");

        ApplyRround tr = new ApplyRround();
        tr.setRound(round);
        tr.setIp(ip);
        tr.setPort(port);
        tr.setRemarks(remarks);
        try {
            tr.setApplyResourceID(Long.valueOf(applyResourceID));
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return Result.renderError("应用ResourceID转换异常: " + applyResourceID + " , 异常信息: " +e.getMessage());
        }
        try {
            this.save(tr, userNumber);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("应用轮次创建异常, 异常信息: " +e.getMessage());
        }
        return Result.renderSuccess("关联轮次创建成功");
    }

    /**
     * @Title: updateApplyRround
     * @Description: 修改应用轮次
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @Override
    public Result<?> updateApplyRround(Map<String, String> params, String userNumber) {
        String resourceID = params.get("resourceID");
        String round = params.get("round");
        String ip = params.get("ip");
        String port = params.get("port");
        String remarks = params.get("remarks");
//        String applyResourceID = params.get("applyResourceID");

        try {
            ApplyRround tr = this.findByResourceID(Long.valueOf(resourceID));
            tr.setRound(round);
            tr.setIp(ip);
            tr.setPort(port);
            tr.setRemarks(remarks);
//            tr.setApplyResourceID(Long.valueOf(applyResourceID));
            tr.setEditUser(userNumber);
            tr.setEditTime(new Date());
            this.update(tr, userNumber);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("关联轮次修改异常, 异常信息: " +e.getMessage());
        }
        return Result.renderSuccess("关联轮次修改成功");
    }

    /**
     * @Title: deleteTestSystemApply
     * @Description: 删除应用轮次
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @Override
    public Result<?> deleteApplyRround(String resourceID, String userNumber) {
        try {
            Long applyRoundResourceID = Long.valueOf(resourceID);
            //删除轮次
            ApplyRround tsa = this.findByResourceID(applyRoundResourceID);
            if (null != tsa) {
                this.delete(tsa, userNumber);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("关联轮次删除异常, 异常信息: " +e.getMessage());
        }
        return Result.renderSuccess("关联轮次删除成功");
    }
}
