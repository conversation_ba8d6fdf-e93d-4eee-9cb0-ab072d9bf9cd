package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.dao.idao.IDemandTestSystemDao;
import com.jettech.dao.idao.ITestSystemDao;
import com.jettech.model.DemandTestSystem;
import com.jettech.model.TestSystem;
import com.jettech.service.iservice.IDemandTestSystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 *
 * @ClassName DemandTestSystemServiceImpl
 * @Description 需求与被测系统serviceimpl
 * <AUTHOR>
 * @date 2019年12月4日
 */
@Service
@Transactional
public class DemandTestSystemServiceImpl extends BaseServiceImpl<DemandTestSystem> implements IDemandTestSystemService {

    private static final Logger logger = LoggerFactory.getLogger(DemandTestSystemServiceImpl.class);
    @Autowired
    private IDemandTestSystemDao demandTestSystemDao;
    @Autowired
    private ITestSystemDao testSystemDao;
    @PostConstruct
    private void postConstruct() {
        baseDao = this.demandTestSystemDao;
    }

    /**
     *
     * @Title: findNotRelevanceSystem
     * @Description: 查询已关联系统
     * @param request
     * @return
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @Override
    public List<Map<String,Object>> findRelevanceSystem(String resourceID) {
        return demandTestSystemDao.findRelevanceSystem(resourceID);
    }
    /**
	 *
	 * @Title: findNotRelevanceSystem
	 * @Description: 查询未关联系统
	 * @param request
	 * @return
	 */
    @Override
	public List<Map<String, Object>> getNotRelevanceSystem(String name,String resourceID) {
    	  List<Map<String,Object>> list=demandTestSystemDao.getNotRelevanceSystem(name, resourceID);
          return list;
	}
    /**
     *
     * @Title: addDemandSystem
     * @Description: 给需求关联系统
     * @param request
     * @return
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @Override
    public DemandTestSystem findByTestSystemResourceIDAndDemandResourceID(String testSystemResourceID, String demandResourceID) {
        return demandTestSystemDao.findByTestSystemResourceIDAndDemandResourceID(testSystemResourceID,demandResourceID);
    }

    /**[
     *
     * @Title: findRelevanceSystem
     * @Description: 查询所有系统
     * @param request
     * @return
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @Override
    public List<Map<String,Object>> findAllSystem( String name,String resourceID) {
        List<Map<String,Object>> casePag=testSystemDao.findAllSystem(name);
        List<DemandTestSystem> list = findBydemandResourceID(Long.parseLong(resourceID));
        if (list != null && list.size() > 0) {
        for(Map<String, Object> map : casePag) {
            for (DemandTestSystem demandTestSystem : list) {
                    if ((demandTestSystem.getTestSystemResourceID()) != null && (String.valueOf(demandTestSystem.getTestSystemResourceID()).equals(String.valueOf(map.get("resourceID"))))){
                          map.put("checked",true);
                    }

                }
            }
        }
        return casePag;
    }

	@Override
	public List<DemandTestSystem> findBydemandResourceID(Long ResourceID) {
		List<DemandTestSystem>DemandTestSystems = demandTestSystemDao.findBydemandResourceID(ResourceID);
		return DemandTestSystems;
	}

	/**
	 * @Title: findBytestSystemResourceID
	 * @Description: 根据系统查询与需求的关联实体
	 * @Param: "[resourceID]"
	 * @Return: "java.util.List<DemandTestSystem>"
	 * @Author: xpp
	 * @Date: 2019/12/9
	 */
    @Override
    public List<DemandTestSystem> findBytestSystemResourceID(Long resourceID) {
        return demandTestSystemDao.findBytestSystemResourceID(resourceID);
    }
    /**
     * @Description 通过需求resourceid查询已关联的被测系统实体类
     * <AUTHOR>
     * @date 2019-12-12 20:31
     * @param demandResourceID
     * @return com.jettech.dto.Result<?>
     */
    @Override
    public Result<?> findTestSystemByDemandResourceID(Long demandResourceID) {
        List<TestSystem> testSystemList = testSystemDao.findTestSystemByDemandResourceID(demandResourceID);
        return Result.renderSuccess(testSystemList);
    }
    /**
     * 
     * @Title  findRelevanceSystemByDemandResourceID     
     * @Description  查询当前需求是否关联系统
     * @author: slq    
     * @date:   2020年8月17日 上午11:51:24
     */
	@Override
	public boolean findRelevanceSystemByDemandResourceID(String demandResourceID) {
		int num = testSystemDao.findRelevanceSystemByDemandResourceID(demandResourceID);
		boolean result = false;
		if(num>0) {
			result = true;
		}
		return result;
	}

    /**
     * 设置主系统
     * @param resouceID
     * @param isPrincipal 0 否  1 是
     * @return
     */
    @Override
    public int setPrincipalSystem(String number,String resouceID,String isPrincipal){
        DemandTestSystem dts= demandTestSystemDao.findByResourceID(Long.valueOf(resouceID));
        if(dts!=null){
            dts.setIsPrincipal(Integer.valueOf(isPrincipal));
            dts= this.update(dts,number);
            if(dts!=null){
                return 1;
            }
        }
        return 0;
    }

    /**
     * @Method: findByDemandResourceIDIn
     * @Description: 批量需求rid查询
     * @Param: " [demandRdis] "
     * @return: java.util.List<com.jettech.model.DemandTestSystem>
     * @Author: wws
     * @Date: 2020/11/10
     */
    @Override
    public List<DemandTestSystem> findByDemandResourceIDIn(List<String> demandRdis) {
        return demandTestSystemDao.findByDemandResourceIDIn(demandRdis);
    }

}