package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.dao.idao.IDemandDao;
import com.jettech.dao.idao.IDemandUserDao;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.model.Demand;
import com.jettech.model.DemandUser;
import com.jettech.service.iservice.IDemandUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * @ClassName DemandUserServiceImpl
 * @Description 需求与人员serviceimpl
 * <AUTHOR>
 * @date 2019年12月4日
 */
@Service
@Transactional
public class DemandUserServiceImpl extends BaseServiceImpl<DemandUser>  implements IDemandUserService {

    private static final Logger logger = LoggerFactory.getLogger(DemandUserServiceImpl.class);
    @Autowired
    private IDemandUserDao demandUserDao;
    @Autowired
	private IDemandDao demandDao;
    @Autowired
	private IFeignDataDesignToBasicService feignDataDesignToBasicService;


    @PostConstruct
    private void postConstruct() {
        baseDao = this.demandUserDao;
    }

    @Override
    public List<Map<String, Object>> findUserNotINDemandUser(String demandResourceID) {

        return demandUserDao.findUserNotINDemandUser(demandResourceID);
    }

    @Override
    public List<Map<String, Object>> findUserByDemandResourceID(String demandResourceID) {
    	
    	List<Map<String, Object>>  listUserAndRol = demandUserDao.findUserByDemandResourceID(demandResourceID);
    	List<Map<String, Object>> list = new ArrayList<>();
		for (Map<String, Object> map : listUserAndRol) {
//			if(!"需求项目经理".equals(String.valueOf(map.get("role")))) {
				list.add(map);
//			}
		}
    	
        return list;
    }
	/**
	 *
	 * @Title: addDemandUser
	 * @Description: 给需求关联人员
	 * @param "request"
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
    @Override
    public List<DemandUser> findByUserResourceIDAndDemandResourceID(String userResourceID, String demandResourceID) {
        return demandUserDao.findByUserResourceIDAndDemandResourceID(userResourceID,demandResourceID);
    }
	@Override
	public List<DemandUser> findBydemandResourceID(Long ResourceID) {
		return demandUserDao.findBydemandResourceID(ResourceID);

	}

	/**
	 * @Title getUserByDemandResourceID
	 * @Description 根据需求rid查询出用户
	 * @param demandResourceID
	 * @return   List<Map<Object,Object>>
	 * <AUTHOR>
	 * @data Dec 5, 20193:24:46 PM
	 */
	@Override
	public List<Map<String, String>> getUserByDemandResourceID(Long demandResourceID) {

		List<Map<String, String>> resultList = new ArrayList<Map<String, String>>();
		List<JettechUserDTO> jettechUserDTOList = new ArrayList<>();
		String token = HttpRequestUtils.getCurrentRequestToken();
		if (demandResourceID == 0L) {
			jettechUserDTOList = feignDataDesignToBasicService.findAllPerson(token);
		} else {
			List<DemandUser> demandUserList = this.findBydemandResourceID(demandResourceID);
			if (!demandUserList.isEmpty() && demandUserList.size() > 0) {
				List<String> ids = new ArrayList<String>();
				if (demandUserList != null && demandUserList.size() > 0) {
					for (DemandUser demandUser : demandUserList) {
						ids.add(demandUser.getUserResourceID().toString());
					}
				}
				String userRids = String.join(",", ids);
				jettechUserDTOList = feignDataDesignToBasicService.findUsersByResourceIDs(userRids, token);

			}
		}
		for (JettechUserDTO jettechUserDTO : jettechUserDTOList) {
			HashMap<String, String> hashMap = new HashMap();
			hashMap.put("userResourceID", jettechUserDTO.getResourceID().toString());
			hashMap.put("userName", jettechUserDTO.getUserName());
			hashMap.put("userNumber", jettechUserDTO.getNumber());
			resultList.add(hashMap);
		}
		return resultList;
	}

	/**
	 * 
	 * @Title:joinDemand
	 * @Description:用户加入需求
	 * @param 
	 * @return void
	 * @author: wu_yancheng
	 * @date 2019年12月9日上午10:55:57
	 */
	@Override
	public Result<?> joinDemand(List<String> demandRids, String userResourceID, String userNumber) {
		List<DemandUser> addDemandUsers = new ArrayList<DemandUser>();
		//key:demandRid;value:userresourceID需求的人员角色
		Map<Long,Long> existDemandAndRid = new HashMap<>();
		List<DemandUser> existDemandUsers = findUserResIDAnddemandResIDList(userResourceID,demandRids);
		List<Demand> demands=demandDao.findByResourceIDIn(demandRids);
		for (DemandUser demandUser : existDemandUsers) {
			existDemandAndRid.put(demandUser.getDemandResourceID(),demandUser.getUserResourceID());
		}
		for(Demand demand:demands){
			if(!demand.getType().equals("6") && !demand.getTypename().equals("已上线")) {
				if(!existDemandAndRid.containsKey(demand.getResourceID())){
					DemandUser demandUser = new DemandUser();
					demandUser.setDemandResourceID(demand.getResourceID());
					demandUser.setUserResourceID(Long.valueOf(userResourceID));
					addDemandUsers.add(demandUser);
				}
			}
		}
		if(!addDemandUsers.isEmpty()) {
			this.save(addDemandUsers, userNumber);
		}
		return Result.renderSuccess();
	}

	/**
	 * @Title findUserResIDAnddemandResIDList
	 * @Description 根据人员和需求查询
	 * @param userResID 
	 * @param demandResIDList
	 * <AUTHOR>
	 *
	 */
	@Override
	public List<DemandUser> findUserResIDAnddemandResIDList(String userResID, List<String> demandResIDList) {
		return demandUserDao.findUserResIDAnddemandResIDList(userResID,demandResIDList);
	}
	/**
	 * @Title findUserResIDAndDemandResIDListAndDemandType
	 * @Description 根据人员，需求ResourceID和需求类型查询
	 * @param userResID 
	 * @param demandResIDList
	 * <AUTHOR>
	 * @param typeName 
	 * @param type 
	 *
	 */
	@Override
	public List<DemandUser> findUserResIDAndDemandResIDListAndDemandType(String userResID, List<String> demandResIDList,
			String type, String typeName) {
		return demandUserDao.findUserResIDAndDemandResIDListAndDemandType(userResID,demandResIDList,type,typeName);
	}

	/**
	 * @Title: findByUserResourceID
	 * @description: 用户rid查询需求
	 * @param "[userResourceID]"
	 * @return java.util.List<Demand>
	 * @throws
	 * <AUTHOR>
	 * @date 2019/12/25 11:52
	 */
	@Override
	public List<DemandUser> findByUserResourceID(String userResourceID) {
		return demandUserDao.findByUserResourceID(userResourceID);
	}

	/**
     *
     * @Title:findUserByDemandRIDAndRole
     * @Description:根据需求id和角色查询人员
     * @param
     * @return List<Map<String,Object>>
     * @author: wu_yancheng
     * @date 2020年2月12日下午2:39:50
     */
	@Override
	public List<String> findUserByDemandRIDAndRole(String demandResourceID, String role) {
		return demandUserDao.findUserByDemandRIDAndRole(demandResourceID, role);
	}
	/**
	 * @Description 通过需求rid和人员rid查询人员与需求关联表DTO
	 * <AUTHOR>
	 * @date 2020-02-18 10:59
	  * @param demandResourceID
	 * @param userResourceID
	 * @return com.jettech.dto.Result
	 */
	@Override
	public Result findDemandUserMapByDemandResourceIDAndUserResourceID(String demandResourceID, String userResourceID) {
		List<DemandUser> demandUsers = demandUserDao.findDemandUserMapByDemandResourceIDAndUserResourceID(demandResourceID,userResourceID);
		return Result.renderSuccess(demandUsers);
	}

	/**
	 * @Title: getUserByTestTaskResourceID
	 * @Description:  根据任务查询需求关联人员,消除重复值
	 * @Param: "[demandResourceID]"
	 * @Return: "com.jettech.dto.Result<?>"
	 * @Author: xpp
	 * @Date: 2020/2/25
	 */
	@Override
	public List<Map<String, String>> getUserByTestTaskResourceID(String testTaskResourceID) {
		List<Map<String, String>> userList = this.getUserByTestTaskResourceID(Long.valueOf(testTaskResourceID));
		List<Map<String, String>> mapList = userList.stream().distinct().collect(Collectors.toList());
		return mapList;
	}
	
	private List<Map<String, String>> getUserByTestTaskResourceID(Long testTaskResourceID) {

		List<Map<String, String>> resultList = new ArrayList<Map<String,String>>();
		List<DemandUser> demandUserList = this.findByTestTaskResourceID(testTaskResourceID);
		if(!demandUserList.isEmpty() && demandUserList.size()>0) {
			List<String> ids= new ArrayList<String>();
			if(demandUserList != null && demandUserList.size()>0) {
				for (DemandUser demandUser : demandUserList) {
					ids.add(demandUser.getUserResourceID().toString());
				}
			}
			String userRids = String.join(",", ids);
			String token = HttpRequestUtils.getCurrentRequestToken();
			List<JettechUserDTO> jettechUserDTOList = feignDataDesignToBasicService.findUsersByResourceIDs(userRids,token);
			for (JettechUserDTO jettechUserDTO : jettechUserDTOList) {
				HashMap<String,String> hashMap = new HashMap();
				hashMap.put("userResourceID", jettechUserDTO.getResourceID().toString());
				hashMap.put("userName", jettechUserDTO.getUserName());
				hashMap.put("userNumber", jettechUserDTO.getNumber());
				resultList.add(hashMap);
			}
			return resultList;
		}
		return resultList;
	}
	
	
	/**
     * 
     * @Title: findByTestTaskResourceID 
     * @Description: 根据任务查询需求关联的人员
     * @param testTaskResourceID
     * @return
     * <AUTHOR>
     * @date 2020年6月24日 下午6:03:33
     */
	@Override
    public List<DemandUser> findByTestTaskResourceID(Long testTaskResourceID){
    	return demandUserDao.findByTestTaskResourceID(testTaskResourceID);
    }
	/***
	 *
	 * @Method : getUserByDemandResourceIDAndUserGroupResourceIDs
	 * @Description : 通过需求resourceID和角色resourceIDs查询人员
	 * @param demandResourceID : 需求resourceID
	 * @param userGroupResourceIDs : 角色resourceIDs
	 * @return : com.jettech.dto.Result<java.util.Map<java.lang.String,java.lang.String>>
	 * <AUTHOR> Hansiwei.
	 * @CreateDate : 2020-07-13 周一 17:53:25
	 *
	 */
	@Override
	public List<Map<String, String>> getUserByDemandResourceIDAndUserGroupResourceIDs(String demandResourceID, List<String> userGroupResourceIDs) {
		List<Map<String, String>> resultList = new ArrayList<>();
		if(userGroupResourceIDs ==null || userGroupResourceIDs.size() == 0){
			return resultList;
		}
		List<DemandUser> demandUserList = demandUserDao.findBydemandResourceIDAndUserGroupResourceIDs(demandResourceID,userGroupResourceIDs);
		if(!demandUserList.isEmpty() && demandUserList.size()>0) {
			List<String> ids= new ArrayList<String>();
			if(demandUserList != null && demandUserList.size()>0) {
				for (DemandUser demandUser : demandUserList) {
					ids.add(demandUser.getUserResourceID().toString());
				}
			}
			String userRids = String.join(",", ids);
			String token = HttpRequestUtils.getCurrentRequestToken();
			List<JettechUserDTO> jettechUserDTOList = feignDataDesignToBasicService.findUsersByResourceIDs(userRids,token);
			for (JettechUserDTO jettechUserDTO : jettechUserDTOList) {
				HashMap<String,String> hashMap = new HashMap();
				hashMap.put("userResourceID", jettechUserDTO.getResourceID().toString());
				hashMap.put("userName", jettechUserDTO.getUserName());
				hashMap.put("userNumber", jettechUserDTO.getNumber());
				resultList.add(hashMap);
			}
			return resultList;
		}
		return resultList;
	}

	/**
	 * 根据testManagerResourceID,demandResourceID,role查询关联表中的数据
	 * @param oldTestManagerResourceID
	 * @param demandResourceID
	 * @param roleName
	 * @return DemandUser
	 */
	@Override
	public DemandUser findByTestManagerResourceIDAndDemandResourceIDAndRole(Long oldTestManagerResourceID, Long demandResourceID, String roleName) {
		return demandUserDao.findByTestManagerResourceIDAndDemandResourceIDAndRole(oldTestManagerResourceID, demandResourceID, roleName);
	}
}
