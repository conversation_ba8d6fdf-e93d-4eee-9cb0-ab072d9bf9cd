package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.feign.IFeignAssetsToBasic;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
public class ImportExcelUtils {
	
	public static List<String> TRADE_IMPORT_TITLE = new ArrayList<String>();
	
	public static List<Map<String, String>> caseFieldConfiguration = new LinkedList<>();
	
	/**
	 * 校验单点案例导入的标题
	 * @return
	 */
	public static boolean valitaileExcelTitle(Row titleRow, IFeignAssetsToBasic iFeignAssetsToBasic) {
		String token = HttpRequestUtils.getCurrentRequestToken();
		//查询案例字段配置启用字段
		Result<?> data = iFeignAssetsToBasic.findAllTestCaseFields(token);
		List<Map<String, String>> list=(List<Map<String, String>>)data.getObj();
		int j = 0;
		for (Map<String, String> obj : list) {
			Map<String, String> map=new HashMap<>();
			map.put("name", obj.get("name"));
			map.put("writeRequired", String.valueOf(obj.get("writeRequired")));
			map.put("fieldType", String.valueOf(obj.get("fieldType")));
			map.put("nameDescription", obj.get("nameDescription"));
			caseFieldConfiguration.add(map);
			if(String.valueOf(obj.get("writeRequired")).equals("true")) {
				TRADE_IMPORT_TITLE.add("* " + obj.get("aliasName"));
				map.put("aliasName", "* " + obj.get("aliasName"));
			}else {
				TRADE_IMPORT_TITLE.add(obj.get("aliasName"));
				map.put("aliasName", obj.get("aliasName"));
			}
			j++;
		}
		int rowNumber = titleRow.getLastCellNum();
		for (int i = 0; i < rowNumber; i++) {
			Cell cell = titleRow.getCell(i);
			String cellValue = cell.getStringCellValue();
			if(!TRADE_IMPORT_TITLE.contains(cellValue)) {
				return false;
			}
		}
		return true;
	}
    /**
     * 读EXCEL文件，获取信息集合
     *
     * @param “filelName”
     * @return
     */
    public static Workbook getExcelInfo(MultipartFile mFile) {
        String fileName = mFile.getOriginalFilename();// 获取文件名
        InputStream is=null;
        try {
        	if (!"excel".equals(validateExcel(fileName))) {// 验证文件名是否合格
                return null;
            }
            boolean isExcel2003 = true;// 根据文件名判断文件是2003版本还是2007版本
            if (isExcel2007(fileName)) {
                isExcel2003 = false;
            }
            is=mFile.getInputStream();
            return createExcel(is, isExcel2003);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                if(is!=null){
                    is.close();
                }
            }catch (Exception e){

            }
        }
        return null;
    }

    /**
     * 根据excel里面的内容读取客户信息
     *
     * @param is      输入流
     * @param isExcel2003   excel是2003还是2007版本
     * @return
     * @throws IOException
     */
    private static Workbook createExcel(InputStream is, boolean isExcel2003) {
        try {
            Workbook wb = null;
            if (isExcel2003) {// 当excel是2003时,创建excel2003
                wb = new HSSFWorkbook(is);
            } else {// 当excel是2007时,创建excel2007
                wb = new XSSFWorkbook(is);
            }
            return wb;// 读取Excel里面客户的信息
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 验证EXCEL文件
     *
     * @param filePath
     * @return
     */
    private static String validateExcel(String fileName) {
        if (fileName == null || !(isExcel2003(fileName) || isExcel2007(fileName))) {
            return "文件名不是excel格式";
        }
        return "excel";
    }
    // @描述：是否是2003的excel，返回true是2003
    private static boolean isExcel2003(String fileName) {
        return fileName.matches("^.+\\.(?i)(xls)$");
    }

    // @描述：是否是2007的excel，返回true是2007
    private static boolean isExcel2007(String fileName) {
        return fileName.matches("^.+\\.(?i)(xlsx)$");
    }
    /**
     * 
     * 
     * @param cell
     * @return
     */
    public static String getValue(Cell cell) {
    	if(cell != null) {
            DataFormatter dataFormatter = new DataFormatter();
    		return dataFormatter.formatCellValue(cell).trim();
    	}
    	return "";
    }
  
}
