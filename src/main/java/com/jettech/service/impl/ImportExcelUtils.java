package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.util.CheckUtil;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.feign.IFeignDataDesignToBasicService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
public class ImportExcelUtils {
	
	public static List<String> TRADE_IMPORT_TITLE = new ArrayList<String>();
	
	public static List<String> TRADE_IMPORT_TITLE1 = new ArrayList<String>();
	
	public static List<Map<String, String>> caseFieldConfiguration = new LinkedList<>();
	
	public static List<Map<String, String>> caseFieldConfiguration1 = new LinkedList<>();
	
	/**
	 * 校验单点案例导入的标题
	 * @return
	 */
	public static boolean valitaileExcelTitle(Row titleRow, IFeignDataDesignToBasicService iFeignDataDesignToBasicService) {
		String token = HttpRequestUtils.getCurrentRequestToken();
		//查询案例字段配置启用字段
		Result<?> data = iFeignDataDesignToBasicService.findAllTestCaseFields(token);
		List<Map<String, String>> list=(List<Map<String, String>>)data.getObj();
		List<String> writeRequiredList = new ArrayList<>();
		int j = 0;
		for (Map<String, String> obj : list) {
			Map<String, String> map=new HashMap<>();
			map.put("name", obj.get("name"));
			map.put("writeRequired", String.valueOf(obj.get("writeRequired")));
			map.put("fieldType", String.valueOf(obj.get("fieldType")));
			map.put("nameDescription", obj.get("nameDescription"));
			if(String.valueOf(obj.get("writeRequired")).equals("true")) {
				TRADE_IMPORT_TITLE.add("* " + obj.get("aliasName"));
				map.put("aliasName", "* " + obj.get("aliasName"));
				writeRequiredList.add("* " + obj.get("aliasName"));
			}else {
				TRADE_IMPORT_TITLE.add(obj.get("aliasName"));
				map.put("aliasName", obj.get("aliasName"));
			}
			caseFieldConfiguration.add(map);
			j++;
		}
		List<String> rows = new ArrayList<>();
		int rowNumber = titleRow.getLastCellNum();
		rowNumber= CheckUtil.checkLoop(rowNumber);
		for (int i = 0; i < rowNumber; i++) {
			Cell cell = titleRow.getCell(i);
			String cellValue = getValue(cell);
			if(!TRADE_IMPORT_TITLE.contains(cellValue)) {
				return false;
			}
			rows.add(cellValue);
		}
		for(String s : writeRequiredList) {
			if(!rows.contains(s)) {
				return false;
			}
		}
		return true;
	}
	
	/**
	 * 校验单点案例导入的标题
	 * @return
	 */
	public static boolean valitaileProjectCaseExcelTitle(Row titleRow, IFeignDataDesignToBasicService iFeignDataDesignToBasicService) {
		String token = HttpRequestUtils.getCurrentRequestToken();
		//查询案例字段配置启用字段
		Result<?> data = iFeignDataDesignToBasicService.findAllTestCaseFields(token);
		List<Map<String, String>> list=(List<Map<String, String>>)data.getObj();
		List<String> writeRequiredList = new ArrayList<>();
		int j = 0;
		for (Map<String, String> obj : list) {
			Map<String, String> map=new HashMap<>();
			map.put("name", obj.get("name"));
			map.put("writeRequired", String.valueOf(obj.get("writeRequired")));
			map.put("fieldType", String.valueOf(obj.get("fieldType")));
			map.put("nameDescription", obj.get("nameDescription"));
			if(String.valueOf(obj.get("writeRequired")).equals("true")) {
				TRADE_IMPORT_TITLE1.add("* " + obj.get("aliasName"));
				map.put("aliasName", "* " + obj.get("aliasName"));
				writeRequiredList.add("* " + obj.get("aliasName"));
			}else {
				TRADE_IMPORT_TITLE1.add(obj.get("aliasName"));
				map.put("aliasName", obj.get("aliasName"));
			}
			caseFieldConfiguration1.add(map);
			j++;
		}
		Map<String, String> map=new HashMap<>();
		map.put("name", "testsystem");
		map.put("writeRequired", "true");
		map.put("fieldType", "1");
		map.put("nameDescription", "所属系统");
		map.put("aliasName", "* 所属系统");
		caseFieldConfiguration1.add(map);
		writeRequiredList.add("* 所属系统");
		Map<String, String> map1=new HashMap<>();
		map1.put("name", "systemmodule");
		map1.put("writeRequired", "false");
		map1.put("fieldType", "1");
		map1.put("nameDescription", "所属模块");
		map1.put("aliasName", "所属模块");
		caseFieldConfiguration1.add(map1);
		Map<String, String> map2=new HashMap<>();
		map2.put("name", "trade");
		map2.put("writeRequired", "true");
		map2.put("fieldType", "1");
		map2.put("nameDescription", "所属交易");
		map2.put("aliasName", "* 所属交易");
		caseFieldConfiguration1.add(map2);
		writeRequiredList.add("* 所属交易");
		
		TRADE_IMPORT_TITLE1.add("* 所属系统");
		TRADE_IMPORT_TITLE1.add("所属模块");
		TRADE_IMPORT_TITLE1.add("* 所属交易");
		int rowNumber = titleRow.getLastCellNum();
		List<String> rows = new ArrayList<>();
		for (int i = 0; i < rowNumber; i++) {
			Cell cell = titleRow.getCell(i);
			String cellValue = getValue(cell);
			if(!TRADE_IMPORT_TITLE1.contains(cellValue)) {
				return false;
			}
			rows.add(cellValue);
		}
		for(String s : writeRequiredList) {
			if(!rows.contains(s)) {
				return false;
			}
		}
		return true;
	}
    /**
     * 读EXCEL文件，获取信息集合
     *
     * @param “filelName”
     * @return
     */
    public static Workbook getExcelInfo(MultipartFile mFile) {
        String fileName = mFile.getOriginalFilename();// 获取文件名
        try {
        	if (!"excel".equals(validateExcel(fileName))) {// 验证文件名是否合格
                return null;
            }
            boolean isExcel2003 = true;// 根据文件名判断文件是2003版本还是2007版本
            if (isExcel2007(fileName)) {
                isExcel2003 = false;
            }
            return createExcel(mFile.getInputStream(), isExcel2003);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据excel里面的内容读取客户信息
     *
     * @param is      输入流
     * @param isExcel2003   excel是2003还是2007版本
     * @return
     * @throws IOException
     */
    private static Workbook createExcel(InputStream is, boolean isExcel2003) {
        try {
            Workbook wb = null;
            if (isExcel2003) {// 当excel是2003时,创建excel2003
                wb = new HSSFWorkbook(is);
            } else {// 当excel是2007时,创建excel2007
                wb = new XSSFWorkbook(is);
            }
            return wb;// 读取Excel里面客户的信息
        } catch (IOException e) {
            e.printStackTrace();
        }
        finally {
        	try {
        		if(is!=null){
        			is.close();
				}
			}catch (Exception e){

			}
		}
        return null;
    }
    /**
     * 验证EXCEL文件
     *
     * @param filePath
     * @return
     */
    private static String validateExcel(String fileName) {
        if (fileName == null || !(isExcel2003(fileName) || isExcel2007(fileName))) {
            return "文件名不是excel格式";
        }
        return "excel";
    }
    // @描述：是否是2003的excel，返回true是2003
    private static boolean isExcel2003(String fileName) {
        return fileName.matches("^.+\\.(?i)(xls)$");
    }

    // @描述：是否是2007的excel，返回true是2007
    private static boolean isExcel2007(String fileName) {
        return fileName.matches("^.+\\.(?i)(xlsx)$");
    }
    /**
     * 
     * 
     * @param cell
     * @return
     */
    public static String getValue(Cell cell) {
    	if(cell != null) {
    		return cell.getStringCellValue().trim();
    	}
    	return "";
    }
  
}
