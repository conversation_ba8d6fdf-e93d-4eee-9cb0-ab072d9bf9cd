package com.jettech.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jettech.dao.idao.ITestCasequoteDao;
import com.jettech.model.TestCasequote;
import com.jettech.service.iservice.ITestCasequoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-10-22 14:10
 */
@Service
@Transactional
public class TestCasequoteServiceImpl extends BaseServiceImpl<TestCasequote> implements ITestCasequoteService {

    @Autowired
    private ITestCasequoteDao testCasequoteDao;

    @PostConstruct
    public void postConstruct() {

        this.baseDao = testCasequoteDao;
    }


    @Override
    public TestCasequote findByTestcaseResourceID(String resourceID) {

        return testCasequoteDao.findByTestcaseResourceID(resourceID);
    }

    @Override
    public List<TestCasequote> findByCaseResourceID(String resourceID) {
        return testCasequoteDao.findByCaseResourceID(resourceID);
    }

    /**
     * <AUTHOR>
     * @description 根据案例resourceid查询案例的执行记录
     * @date 2021年01月06日 10:15
     * @param [testcaseResourceIDList]
     * @return java.util.List<com.jettech.model.TestCasequote>
     **/
    @Override
    public List<TestCasequote> findByCaseResourceIDs(List<Long> testcaseResourceIDList) {
        return testCasequoteDao.findByCaseResourceIDs(testcaseResourceIDList);
    }

    public List<TestCasequote> findByTaskAndCaseResourceIds(Long testTaskResourceID, List<Long> testCaseResourceIDList){
        return testCasequoteDao.findByTaskAndCaseResourceIds(testTaskResourceID, testCaseResourceIDList);
    }

    public List<TestCasequote> findByTaskAndTrade(Long testTaskResourceID, Long tradeResourceID){
        return testCasequoteDao.selectList(Wrappers.query()
                .eq("testTaskResourceID", testTaskResourceID)
                .eq("tradeResourceID", tradeResourceID)
        );
    }

    public boolean isQuote(String testCaseResourceID){
        return testCasequoteDao.isQuote(testCaseResourceID);
    }
}
