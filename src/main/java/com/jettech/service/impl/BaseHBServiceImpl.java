package com.jettech.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.dao.idao.IBaseDaoHB;
import com.jettech.model.BaseModelHB;
import com.jettech.service.iservice.BaseHBService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @title: BaseModelHBServiceImpl
 * @projectName jettomanager
 * @description: id和resourceID相同
 * @date 2021/3/2211:30
 */
public class BaseHBServiceImpl<T extends BaseModelHB> implements BaseHBService<T> {


    protected IBaseDaoHB<T> baseDao;

    /**
     * 根据业务主键查询
     * @param resourceID 业务主键
     * @return 返回当前数据
     */
    @Override
    public T findByResourceID(Long resourceID) {
        return baseDao.findByResourceID(resourceID);
    }
    /**
     * 根据业务主键批量查询
     * @param resourceIDs 业务主键
     * @return 当前一批数据
     */
    @Override
    public List<T> findByResourceIDIn(List<String> resourceIDs) {
        return baseDao.findByResourceIDIn(resourceIDs);
    }
    /**
     * 根据ID集合查询
     * @param idList 一批主键
     * @return
     */
    @Override
    public List<T> findByIdIn(List<Integer> idList) {
        return baseDao.findByIdIn(idList);
    }
    /**
     * 根据ID查询
     * @param id 一个主键
     * @return
     */
    @Override
    public T findById(Integer id) {
        return baseDao.findById(id);
    }
    /**
     * 单个保存数据
     * @param model 单个对象
     * @return
     */
    @Transactional(rollbackFor=Exception.class)
    @Override
    public T save(T model, String userNumber) {
        if(userNumber != null) {
            model.setCreateUser(userNumber);
            model.setEditUser(userNumber);
        }
        if(model.getResourceID()==null) {
            long l = generateResourceID(model);
            model.setResourceID(l);
            model.setId(l);
        }
        model.onCreate();
        return baseDao.save(model);
    }
    /**
     * 批量保存数据
     * @param modelList 批量对象
     * @return
     */
    @Transactional(rollbackFor=Exception.class)
    @Override
    public List<T> save(List<T> modelList, String userNumber) {
        for(T model : modelList) {
            if(userNumber != null) {
                model.setCreateUser(userNumber);
                model.setEditUser(userNumber);
            }
            if(model.getResourceID()==null){
                long l = generateResourceID(model);
                model.setResourceID(l);
                model.setId(l);
            }
            model.onCreate();
        }
        return baseDao.save(modelList);
    }
    /**
     * 更新一批数据
     * @param modelList 批量对象
     * @return
     */
    @Transactional(rollbackFor=Exception.class)
    @Override
    public List<T> update(List<T> modelList, String userNumber) {
        for(T model : modelList) {
            model.setEditUser(userNumber);
            model.onUpdate();
        }
        return baseDao.update(modelList);
    }
    /**
     * 更新单个对象
     * @param model 单个更新对象
     * @return
     */
    @Transactional(rollbackFor=Exception.class)
    @Override
    public T update(T model, String userNumber) {
        model.setEditUser(userNumber);
        model.onUpdate();
        return baseDao.update(model);
    }
    /**
     * @param id 根据ID查询
     * @return
     */
    @Override
    public T find(Integer id) {
        return baseDao.find(id);
    }
    /**
     * 查询所有数据
     * @return
     */
    @Override
    public List<T> findAll() {
        return baseDao.findAll();
    }
    /**
     * 查询总量
     * @return 当前总量
     */
    @Override
    public long count() {
        return baseDao.count();
    }
    /**
     * 删除对象
     * @param model 单个删除对象
     * @param userNumber 操作人
     */
    @Transactional(rollbackFor=Exception.class)
    @Override
    public void delete(T model, String userNumber) {
        preDelete(model, userNumber);
        baseDao.delete(model);
    }
    /**
     * 批量删除
     * @param modelList 批量删除数据
     */
    @Transactional(rollbackFor=Exception.class)
    @Override
    public void deleteInBatch(List<T> modelList, String userNumber) {
        for(T model : modelList) {
            preDelete(model, userNumber);
        }
        baseDao.deleteInBatch(modelList);
    }

    /**
     * 分页查询所有数据
     * @return
     */
    @Override
    public PageInfo<T> findAll(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<T> list = baseDao.findAll();
        //用PageInfo对结果进行包装
        PageInfo<T> page = new PageInfo<T>(list);
        return page;
    }

    protected void preDelete(T model, String userNumber){
        return;
    }

    private long generateResourceID(T model) {
        SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
        return worker.genNextId();
		/*StringBuffer result = new StringBuffer();

		result.append(platformID);
		result.append(".");
		result.append(model.getClass().getSimpleName());
		result.append(".");
		result.append(Calendar.getInstance().getTimeInMillis());
		result.append(UUID.randomUUID().toString());

		return result.toString();*/
    }

}
