package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.util.CheckUtil;
import com.jettech.dao.idao.IProjectGroupDao;
import com.jettech.feign.IFeignDataDesignToManexecuteService;
import com.jettech.model.ProjectGroup;
import com.jettech.model.TestCase;
import com.jettech.model.TestProject;
import com.jettech.service.iservice.IProjectGroupService;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.service.iservice.ITestProjectService;
import com.jettech.util.ReadExcel;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ProjectGroupServiceImpl
 * @projectName jettomanager
 * @description: 项目组service实现层
 * @date 2020/7/1319:26
 */
@Service
@Transactional
public class ProjectGroupServiceImpl extends BaseServiceImpl<ProjectGroup> implements IProjectGroupService {

    @Autowired
    private IProjectGroupDao projectGroupDao;
    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;
    @Autowired
    private ITestProjectService testProjectService;
    @PostConstruct
    public void postConstruct(){
        this.baseDao = projectGroupDao;
    }

    /**
     * @Title: saveProjectGroup
     * @description: 新增项目组
     * @param "[projectGroup, userNumber]"
     * @return com.jettech.dto.Result
     * @throws
     * <AUTHOR>
     * @date 2020/7/13 19:46
     */
    @Override
    public Result saveProjectGroup(ProjectGroup projectGroup, String userNumber) {
        if(StringUtils.isBlank(projectGroup.getName())) return Result.renderError("项目组名称不能为空!");
        Long parentResourceID = projectGroup.getParentResourceID();
        if(parentResourceID != null){
            ProjectGroup parent = this.findByResourceID(parentResourceID);
            if(parent.isSmallPoints()){
                parent.setSmallPoints(false);
                this.update(parent,userNumber);
            }
        }
        // 同一个节点下节点名称不能重复
        List<ProjectGroup> projectGroupList = projectGroupDao.findByTestProjectIdAndParentIdAndName(projectGroup);
        if (!CollectionUtils.isEmpty(projectGroupList)) {
            return Result.renderError("同一个节点下节点名称不能重复!");
        }
        this.save(projectGroup,userNumber);
        return Result.renderSuccess();
    }

    /**
     *
     * @Title: findByTestProjectResourceID
     * @Description:查询项目下的项目组
     * @param resourceID
     * @return
     * <AUTHOR>
     * @date 2020年7月14日 下午1:59:59
     */
    @Override
    public List<ProjectGroup> findByTestProjectResourceID(Long resourceID,String groupType){
    	return projectGroupDao.findByTestProjectResourceID(resourceID,groupType);
    }


    /***
     * 修改项目组
     * @Method : updateProjectGroup
     * @Description : 修改项目组
     * @param pg : 修改对象参数
     * @param userNumber : 当前登录用户number
     * @return : com.jettech.dto.Result
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 10:50:17
     *
     */
    @Override
    public Result updateProjectGroup(ProjectGroup pg, String userNumber,String updatePlace) {
        ProjectGroup old = this.findByResourceID(pg.getResourceID());
        if(old.isSmallPoints() != pg.isSmallPoints()){
            //true，末节根节点,false：不是末节根节点
            if(!old.isSmallPoints()){
                List<ProjectGroup> list = this.findByParentResourceID(old.getResourceID(),old.getGroupType());
                if(!list.isEmpty()) return Result.renderError("该节点下存在节点，不能修改为末节点！");
            }else{
                List<String> listS=new ArrayList<String>();
                listS.add(old.getResourceID().toString());
                if("design".equals(updatePlace)){//案例设计页面校验
                	List<TestCase> list = testCaseService.findByProjectGroupResourceIDs(listS,"","","","","","","");
                	if(!list.isEmpty()) return Result.renderError("该节点下存在案例，只能为末节点！");
                }else {
                	//查询案例执行库是否有数据
                	int count = testCaseService.findPerformCaseByProjectGroupResourceIDs(listS);
                	if(count>0) return Result.renderError("该节点下存在案例，只能为末节点！");
                }
            }

            old.setSmallPoints(pg.isSmallPoints());
        }
        old.setName(pg.getName());
        this.update(old,userNumber);
        return Result.renderSuccess();
    }


    /***
     * 父节点查询
     * @Method : findByParentResourceID
     * @Description : 父节点查询
     * @param resourceID : parentRid
     * @return : java.util.List<com.jettech.model.ProjectGroup>
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 10:58:03
     *
     */
    @Override
    public List<ProjectGroup> findByParentResourceID(Long resourceID,String groupType) {
        return projectGroupDao.findByParentResourceID(resourceID,groupType);
    }



    /***
     * 删除项目组
     * @Method : deleteProjectGroup
     * @Description : 删除项目组
     * @param resourceID : 被删除的rid
     * @param userNumber
     * @return : com.jettech.dto.Result
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 14:19:13
     *
     */
    @Override
    public Result deleteProjectGroup(String resourceID,String type, String userNumber) {
        List<ProjectGroup> deleteList = new ArrayList<>();
        deleteList = findNeedDeleteProjectGroup(this.findByParentResourceID(Long.valueOf(resourceID),""),deleteList);
        ProjectGroup pg = this.findByResourceID(Long.valueOf(resourceID));
        deleteList.add(pg);
        List<String> deletePGRids = deleteList.stream().map(s -> s.getResourceID().toString()).collect(Collectors.toList());
        //案例设计
        switch (type){
            case "datadesign":
                     List<TestCase> deleteCaseList = testCaseService.findByProjectGroupResourceIDs(deletePGRids,"","","","","","","");
                     if(deleteCaseList != null && deleteCaseList.size() > 0) return Result.renderError("节点下存在维护案例，不能删除!");
                break;
            case "perform" :
                Result result = feignDataDesignToManexecuteService.findPerFormCaseByProjectGroupRids(deletePGRids);
                if(result.getCode() != 20000) return Result.renderError("查询案例执行异常！");
                Object obj = result.getObj();
                if(obj != null){
                    List<Map<String,Object>> list = (List<Map<String, Object>>) obj;
                    if(list.size() > 0){
                        return Result.renderError("节点下存在维护案例，不能删除!");
                    }
                }
                break;
        }

        //删除节点
        this.deleteInBatch(deleteList,userNumber);

        //删除案例
//        List<String> pgResourceIDList = deleteList.stream().map(e -> e.getResourceID().toString()).collect(Collectors.toList());
//        if(!pgResourceIDList.isEmpty()){
//            List<TestCase> list = testCaseService.findByProjectGroupResourceIDs(pgResourceIDList);
//            if(!list.isEmpty()){
//                testCaseService.deleteInBatch(list,userNumber);
//
//            }
//            this.deleteInBatch(deleteList,userNumber);
//            //删除执行案例
//            Result res = feignDataDesignToManexecuteService.deletePerFormCase(pgResourceIDList,userNumber);
//            if(res.getCode() != 20000) return Result.renderError("删除执行案例异常！");
//        }

        return Result.renderSuccess();
    }

    /***
     * Excel导入项目组
     * @Method : importProjectGroup
     * @Description : Excel导入项目组
     * @param file : 要导入的Excel文件
     * @param testProjectResourceID : 项目的ResourceID
     * @param groupType
     * @return : com.jettech.dto.Result
     * <AUTHOR> zhangsheng
     * @CreateDate : 2020-09-17 下午 13:51:23
     *
     */
    @Override
    @Transactional
    public Result importProjectGroup(MultipartFile file, Long testProjectResourceID, String userNumber, String groupType) {
        String fileName = file.getOriginalFilename();
        ReadExcel readExcel = new ReadExcel();
        try {
            // 验证文件名是否合格
            if (!readExcel.validateExcel(fileName)) {
                return Result.renderError("文件名格式不正确");
            }
            InputStream is = file.getInputStream();
            Workbook workbook = null;
            // 判断Excel版本创建对应的读取对象
            if (readExcel.isExcel2007(fileName)) {
                workbook = new XSSFWorkbook(is);
            } else {
                workbook = new HSSFWorkbook(is);
            }
            // Excel导入逻辑
            Result result = this.exportListFromExcel(workbook, userNumber, testProjectResourceID, groupType);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getMsg());
            }
            return result;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Result.renderError(e.getMessage());
        }
    }

    /**
     * 递归查询被删除节点下的数据
     * @param pgList
     * @param deleteList
     * @return
     */
    private List<ProjectGroup> findNeedDeleteProjectGroup(List<ProjectGroup> pgList,List<ProjectGroup> deleteList) {
        for (ProjectGroup pg : pgList) {
            deleteList.add(pg);
            List<ProjectGroup> list = this.findByParentResourceID(pg.getResourceID(),"");
            if(list == null || list.isEmpty()){

            }else{
                findNeedDeleteProjectGroup(list,deleteList);
            }

        }
        return deleteList;
    }


    /**
     * @Method: findByProjectRidsAndIsSmallPoint
     * @Description: 项目rid查询末节点
     * @Param: " [projectResourceIDs] "
     * @return: java.util.List<com.jettech.model.ProjectGroup>
     * @Author: wws
     * @Date: 2020/9/17
     */
    @Override
    public List<String> findByProjectRidsAndIsSmallPoint(List<String> projectResourceIDs,String groupType) {
        return projectGroupDao.findByProjectRidsAndIsSmallPoint(projectResourceIDs,groupType);
    }

    /**
     * 生成resourceId方法
     * @return long resourceId
     * by zhangsheng
     */
    private long generateResourceID() {
        SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
        return worker.genNextId();
    }


    /**
     * 将 Excel中数据导入
     * @param workbook
     * @param userNumber
     * @param testProjectResourceID
     * @param groupType
     * @return Result
     * by zhangsheng
     */
    private Result exportListFromExcel(Workbook workbook, String userNumber, Long testProjectResourceID, String groupType) {
        // 检查模板正确性
        Result result = this.checkExcel(workbook);
        if (!result.isSuccess()) {
            return result;
        }
        Map<String, ProjectGroup> tempMap = new HashMap<>();
        DataFormatter dataFormatter = new DataFormatter();
        Sheet sheet = workbook.getSheetAt(1);
        // 跳过表头，获取Excel行数
        int m= CheckUtil.checkLoop(sheet.getPhysicalNumberOfRows());
        for (int i = 1;  i <m ; i++) {
            List<String> rowList = new ArrayList<>();
            Row row = sheet.getRow(i);
            if (null == row) {
                continue;
            }
            // 获取Excel列数
            for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
                Cell cell = row.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                String column = dataFormatter.formatCellValue(cell);
                if (StringUtils.isBlank(column)) {
                    continue;
                }
                ProjectGroup projectGroup = null;
                Boolean isSamallpoints = false;
                if (j == row.getLastCellNum()) {
                    // 最后一级为末级节点。末级节点下不允许再新增节点
                    isSamallpoints = true;
                }
                if (j < row.getLastCellNum() && (null == row.getCell(j + 1) || StringUtils.isBlank(dataFormatter.formatCellValue(row.getCell(j + 1))))) {
                    isSamallpoints = true;
                }

                Result<ProjectGroup> res = new Result<>();
                if (j == 0) {
                    // 第一列即为第一级节点，根据名称查询是否在数据库中存在这个节点，如果已存在不插入数据库，不存在就插入数据库
                    res = this.getProjectGroup(userNumber, column, null, testProjectResourceID, isSamallpoints, groupType);
                } else {
                    // 不是第一列则为子节点，父节点为前一列的节点，根据父节点id和名称查询数据库中是否存在这个子节点，不存在就插入数据库，存在就不插入数据库
                    ProjectGroup tempProjectGroup = tempMap.get(dataFormatter.formatCellValue(row.getCell(j - 1)) + "_" + i + "_" + (j - 1));
                    if (null == tempProjectGroup) {
                        return Result.renderError("第" + (i + 1) + "行，第" + j + "列为空，导入失败");
                    }
                    Long parentResourceID = tempProjectGroup.getResourceID();
                    res = this.getProjectGroup(userNumber, column, parentResourceID,testProjectResourceID, isSamallpoints, groupType);
                }
                if (!res.isSuccess()) {
                    return res;
                }
                projectGroup = res.getObj();

                if (!tempMap.containsKey(column + "_" + i + "_" + j)) {
                    // 将节点信息存到map中，之后父节点信息从这个map取
                    tempMap.put(column + "_" + i + "_" + j, projectGroup);
                }
            }
        }
        return Result.renderSuccess();
    }

    /**
     * 检查excel模板格式
     * @param workbook
     * by zhangsheng
     */
    private Result checkExcel(Workbook workbook) {
        // 得到第一个shell
        Sheet sheet0 = workbook.getSheetAt(0);
        if (!"填写说明".equals(sheet0.getSheetName())) {
            return Result.renderError("模板有误，请下载模板填写！");
        }
        Row row = sheet0.getRow(0);
        if (null != row.getCell(0) && !"".equals(row.getCell(0).toString())) {
            return Result.renderError("模板有误，请下载模板填写！");
        }
        if (null == row.getCell(1) || !"填写说明：".equals(row.getCell(1).toString())) {
            return Result.renderError("模板有误，请下载模板填写！");
        }
        Sheet sheet1 = workbook.getSheetAt(1);
        if (null == sheet1) {
            return Result.renderError("模板有误，请下载模板填写！");
        }
        if (sheet1.getPhysicalNumberOfRows() <= 1) {
            return Result.renderError("模板有误，请下载模板填写！");
        }
        if (!"左侧树结构".equals(sheet1.getSheetName())) {
            return Result.renderError("模板有误，请下载模板填写！");
        }
        Row row0 = sheet1.getRow(0);
        if (null == row0.getCell(0) || !"一级结构".equals(row0.getCell(0).toString())) {
            return Result.renderError("模板有误，请下载模板填写！");
        }
        return Result.renderSuccess();
    }


    /**
     * 封装projectGroup对象，并保存到数据库
     * @param userNumber
     * @param name
     * @param parentResourceID
     * @param testProjectResourceID
     * @param isSmallpoints
     * @param groupType
     * @return ProjectGroup
     * by zhangsheng
     */
    private Result<ProjectGroup> getProjectGroup(String userNumber, String name, Long parentResourceID, Long testProjectResourceID, Boolean isSmallpoints, String groupType){
        List<ProjectGroup> projectGroupList = projectGroupDao.findByParentIdAndName(name, parentResourceID, testProjectResourceID, groupType);
        if (CollectionUtils.isEmpty(projectGroupList)) {
            ProjectGroup projectGroup = new ProjectGroup();
            projectGroup.setName(name);
            projectGroup.setResourceID(this.generateResourceID());
            projectGroup.setParentResourceID(parentResourceID);
            projectGroup.setTestProjectResourceID(testProjectResourceID);
            projectGroup.setSmallPoints(isSmallpoints);
            projectGroup.setGroupType(groupType);
            projectGroup.setCreateUser(userNumber);
            projectGroup.setEditUser(userNumber);
            projectGroup.setCreateTime(new Date());
            projectGroup.setEditTime(new Date());
            // 保存到数据库
            this.save(projectGroup, userNumber);
            return Result.renderSuccess(projectGroup);
        }
        if (projectGroupList.size() > 1) {
            return Result.renderError("左侧树下存在同名节点" + name);
        }
        if (projectGroupList.get(0).isSmallPoints() && !isSmallpoints) {
            String projectName = projectGroupList.get(0).getName();
            ProjectGroup projectGroup = new ProjectGroup();
            return Result.renderError( "导入失败，" + projectName + "节点为末级节点不允许再维护子级！", 20001, projectGroup);
        }
        return Result.renderSuccess(projectGroupList.get(0));
    }
	/**
	 *
	* @Title: findPerformCaseLeadInfo
	* @Description: 获取项目案例的所属项目和项目组的层级结构
	* @param @param projectGroupResourceID
	* @param @return    参数
	* @return Result<?>    返回类型
	* @throws
	* <AUTHOR>
	 */
	@Override
	public String findPerformCaseLeadInfo(String projectGroupResourceID) {
		String strctureStr = "";
		ProjectGroup currentProjectGroup = this.findByResourceID(Long.valueOf(projectGroupResourceID));
		strctureStr = currentProjectGroup.getName();
		//拼接项目组名称
		Long parentGroupResourceID = currentProjectGroup.getParentResourceID();
		ProjectGroup finalGroup = currentProjectGroup;
		while(!org.springframework.util.StringUtils.isEmpty(parentGroupResourceID)) {

			ProjectGroup parentGroup = this.findByResourceID(parentGroupResourceID);
			strctureStr = parentGroup.getName()  +"/"+  strctureStr;
			finalGroup = parentGroup;
			parentGroupResourceID = parentGroup.getParentResourceID();

		}
		//拼接项目层级名称
		TestProject testProject = testProjectService.findByResourceID(finalGroup.getTestProjectResourceID());
		strctureStr = testProject.getName()  +"/"+  strctureStr;
		Long parentProjectResourceID = testProject.getParentResourceID();
		while(!org.springframework.util.StringUtils.isEmpty(parentProjectResourceID)) {

			TestProject parentTestProject = testProjectService.findByResourceID(parentProjectResourceID);
			strctureStr = parentTestProject.getName()  +"/"+  strctureStr;
			parentProjectResourceID = parentTestProject.getParentResourceID();
		}
		return strctureStr;
	}

    @Override
    public List<Map<String, String>> findDefectByGroupResourceIDs(List<String> projectGroups) {
        return  projectGroupDao.findDefectByGroupResourceIDs(projectGroups);
    }

    @Override
    public List<ProjectGroup> findByProjectRIDsAndGroupType(List<String> projectResourceIDs, String groupType) {
        return projectGroupDao.findByProjectRIDsAndGroupType(projectResourceIDs, groupType);
    }
}
