package com.jettech.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jettech.DTO.DemandItemDto;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.DataDictionaryDTO;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.enums.AttachmentStoreTypeEnums;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.util.*;
import com.jettech.common.util.DateUtil;
import com.jettech.dao.idao.IDemandAccessoryDao;
import com.jettech.dao.idao.IDemandDao;
import com.jettech.feign.*;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import com.jettech.util.DemandNotifyUtils;
import com.jettech.util.DemandType;
import com.jettech.util.ImportExcelUtils;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.io.*;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 *
 * @ClassName DemandService
 * @Description 需求serviceImpl
 * <AUTHOR>
 * @date 2019年12月3日下午8:21:51
 */
@Service
@Transactional
public class DemandServiceImpl extends BaseServiceImpl<Demand> implements IDemandService {

	private static final Logger logger = LoggerFactory.getLogger(DemandServiceImpl.class);
	@Autowired
	private IDemandDao demandDao;
	@Autowired
	private IDemandChangeNodesService iDemandChangeNodesService;
	@Autowired
	private IDemandUserService demandUserService;
	@Autowired
	private IDemandTestSystemService demandTestSystemService;
	@Autowired
	private IFeignDataDesignToBasicService feignTrp;
	@Autowired
	private IDemandUserService iDemandUserService;
	@Autowired
	private ITestCaseService iTestCaseService;
	@Autowired
	private IFeignDataDesignToBasicService feignDataDesignToBasicService;
	@Autowired
	private ITestSystemService testSystemService;
	@Autowired
	private IFeignDataDesignToAssets iFeignAssetsToAssets;
	@Autowired
	private ITestCaseService testCaseService;
	@Autowired
	private IFeignDataDesignToBugService feignDataDesignToBugService;
	@Autowired
	private IFeignDataDesignToManexecuteService feignDataDesignToManexecuteService;
	@Autowired
	private ITestSystemUserService testSystemUserService;
	@Autowired
	private DemandNotifyUtils demandNotifyUtils;
	@Autowired
	private ITestProjectService testProjectService;
	@Autowired
	private ISystemModuleService systemModuleService;
	@Autowired
	private IFeignDataDesignToReportForm feignDataDesignToReportForm;
	@Autowired
	private RedisUtils redisUtils;
	@Autowired
	private ParamConfig paramConfig;
	@Autowired
	private FtpUtil ftpUtil;
	@Autowired
	private IDemandAccessoryService demandAccessoryService;
	@Autowired
	private ITestProjectUserService testProjectUserService;

	@Value("${use_file_service}")
	private boolean useFileService;
	@Autowired
	private IFeignDataDesignToFileService feignDataDesignToFileService;
	private static final String ENABLE_AUTO_TESTCASE_ARCHIVE = "enableAutoTestcaseArchive";

	private static ScriptEngine SE = new ScriptEngineManager().getEngineByName("JavaScript");

	@Autowired
	private IDemandAccessoryDao iDemandAccessoryDao;


	@PostConstruct
	private void postConstruct() {
		baseDao = this.demandDao;
	}

	/**
	 * 查询需求
	 *
	 *
	 * 开发时间：2020年5月12日 下午3:09:14
	 *
	 * @author：moshco zhu
	 * @param pageRequest
	 * @param name
	 * @param number
	 * @param typeName
	 * @param testProjectResourceID
	 * @param testSystemResourceID
	 * @param projectManagerName
	 * @param testManagerName
	 * @return
	 */
	@Override
	@Transactional(readOnly = true)
	public Page<Map<String, Object>> findDemandPage(PageRequest pageRequest, String name, String number, String typeName,
													 String testProjectResourceID, String testSystemResourceID,
													 String projectManagerName, String testManagerName,String level) {

		// 分页查询需求
		Page<Map<String, Object>> page = demandDao.findDemandPage(pageRequest, name, number, typeName,
				testProjectResourceID, testSystemResourceID,projectManagerName,testManagerName,level);

		Map<String, String> bufferMap = new HashMap<String, String>();

		// 需求列表
		List<Map<String, Object>> demandList = page.getContent();
		if (demandList != null && demandList.size() > 0) {

			// 日期格式化的对象
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			for (Map<String, Object> demandRowMap : demandList) {

				// 项目经理
				Long projectManagerResourceID = (Long) demandRowMap.get("projectManagerResourceID");
				if (projectManagerResourceID != null) {
					String managerName = demandDao.getManagerName(projectManagerResourceID);
					demandRowMap.put("managerName", managerName);
				}
				// 测试经理
				Long testManagerResourceID = (Long) demandRowMap.get("testManagerResourceID");
				if (testManagerResourceID != null) {
					demandRowMap.put("testManagerName", demandDao.getManagerName(testManagerResourceID));
				}

				// 创建时间
				java.time.LocalDateTime time= (java.time.LocalDateTime)demandRowMap.get("demandCreateTime");
				if (time != null) {
					Instant instant = time.atZone(ZoneId.systemDefault()).toInstant();
					Date demandCreateTime = java.util.Date.from(instant);
					String dCreateTime = sdf.format(demandCreateTime);
					demandRowMap.put("dCreateTime", dCreateTime);
				}

				// 业务归口部门
				String centralizedDepartment = (String) demandRowMap.get("centralizedDepartment");
				if (centralizedDepartment != null) {
					String centName = bufferMap.get(centralizedDepartment);
					if (centName == null) {
						centName = demandDao.getCentName(centralizedDepartment);
						bufferMap.put(centralizedDepartment, centName);
					}
					demandRowMap.put("centName", centName);
				}

				// 查询系统名称
				Long resourceID = (Long) demandRowMap.get("resourceID");
				List<String> systemNameList = demandDao.getSystemNameList(resourceID);
				if (systemNameList != null && systemNameList.size() > 0) {
					StringBuilder builder = new StringBuilder();
					for (String systemName : systemNameList) {
						if (builder.length() > 0) {
							builder.append(",");
						}
						builder.append(systemName);
					}
					demandRowMap.put("SystemName", builder.toString());
				}
			}

		}

		return page;
	}

	/**
	 *
	 * @Title:addDemand
	 * @Description:新增需求
	 * @param
	 * @return
	 * @author: wu_yancheng
	 * @date 2019年12月4日下午2:23:04
	 */
	@Override
	public Result<?> addDemand(Map<String, Object> param,MultipartFile[] files)throws IOException{
		String name = ((String) param.get("name")).trim();
		if (StringUtils.isEmpty(name)) {
			return Result.renderError("请输入需求名称!");
		}
		String number = ((String) param.get("number")).trim();
		if (StringUtils.isEmpty(number)) {
			return Result.renderError("请输入需求编号!");
		}
		//校验需求编号长度
		if(!number.matches(".{0,50}")){
			return Result.renderError("需求编号超过50的长度，请重新输入!");
		}
		if (!checkIsNull(number)) {
			return Result.renderError("需求编号重复!");
		}
		String centralizedDepartment = ((String) param.get("centralizedDepartment")).trim();
		/*
		 * String hostTribe = ((String) param.get("hostTribe")).trim(); String hostTeam
		 * = ((String) param.get("hostTeam")).trim();
		 */
		String testProjectResourceID = ((String) param.get("testProjectResourceID")).trim();
		TestProject tp = testProjectService.findByResourceID(Long.valueOf(testProjectResourceID));
		String testProjectName = tp.getName();
		String type = ((String) param.get("type")).trim();
		String typename = ((String) param.get("typename")).trim();
		String userNumber = ((String) param.get("userNumber")).trim();
		String level = ((String) param.get("level")).trim();
		Demand demand = new Demand();
		demand.setName(name);
		demand.setNumber(number);
		demand.setCentralizedDepartment(centralizedDepartment);
		demand.setResourceID(this.generateResourceID());
		/*
		 * demand.setHostTribe(hostTribe); demand.setHostTeam(hostTeam);
		 */
		if (testProjectResourceID != null && !"".equals(testProjectResourceID)) {
			demand.setTestProjectResourceID(Long.valueOf(testProjectResourceID));
			demand.setTestProjectName(testProjectName);
		}
		demand.setType(type);
		demand.setTypename(typename);
		demand.setLevel(level);
		if (!StringUtils.isEmpty(param.get("dCreateTime"))) {
			String demandCreateTime = (String) param.get("dCreateTime");
			demand.setDemandCreateTime(DateUtil.getDate(demandCreateTime, "yyyy-MM-dd"));
		}
		if (param.get("projectManagerResourceID") != null
				&& (!((String) param.get("projectManagerResourceID")).equals(""))) {
			String projectManagerResourceID = ((String) param.get("projectManagerResourceID")).trim();
			demand.setProjectManagerResourceID(Long.valueOf(projectManagerResourceID));
		}
		// 测试经理
		if (param.get("testManagerResourceID") != null && (!((String) param.get("testManagerResourceID")).equals(""))) {
			String testManagerResourceID = ((String) param.get("testManagerResourceID")).trim();
			demand.setTestManagerResourceID(Long.valueOf(testManagerResourceID));
		}
		if (param.get("proposerResourceID") != null && (!((String) param.get("proposerResourceID")).equals(""))) {
			String proposerResourceID = ((String) param.get("proposerResourceID")).trim();
			demand.setProposerResourceID(Long.valueOf(proposerResourceID));
		} else {
			return Result.renderError("需求提出人不能为空！");
		}
		demand.setRemarks((String)param.get("remarks"));

		Demand save = save(demand, userNumber);
//		List<Long> demandRids = new ArrayList<>();
//		demandRids.add(save.getResourceID());
//		String token = HttpRequestUtils.getCurrentRequestToken();
//		Result result = feignDataDesignToManexecuteService.saveDefaultFourPlan(demandRids, token);
//		if (!result.isSuccess()) {
//			return Result.renderError("默认测试计划保存失败！");
//		}
		Set<DemandUser> dus = new HashSet<>();
		//待关联人员列表
		Set<Object> setIds = new HashSet<>();
		// 需求项目经理(一个需求目前只有一个需求项目经理)
		if (!StringUtils.isEmpty(save.getProjectManagerResourceID())) {
			DemandUser du = new DemandUser();
			du.setDemandResourceID(save.getResourceID());
			du.setUserResourceID(save.getProjectManagerResourceID());
			dus.add(du);
			//添加至待关联人员列表
			setIds.add(save.getProjectManagerResourceID());
		}
		// 需求提出人(关联关系需求提出人的角色为‘需求人员’)
		if (!StringUtils.isEmpty(save.getProposerResourceID())) {
			DemandUser du = new DemandUser();
			du.setDemandResourceID(save.getResourceID());
			du.setUserResourceID(save.getProposerResourceID());
			dus.add(du);
			//添加至待关联人员列表
			setIds.add(save.getProposerResourceID());
		} else {
			return Result.renderError("需求提出人不能为空，数据有误！");
		}
		// 测试经理
		if (!StringUtils.isEmpty(save.getTestManagerResourceID())) {
			DemandUser du = new DemandUser();
			du.setDemandResourceID(save.getResourceID());
			du.setUserResourceID(save.getTestManagerResourceID());
			dus.add(du);
			//添加至待关联人员列表
			setIds.add(save.getTestManagerResourceID());
		}

		demandUserService.save(new ArrayList<>(dus), userNumber);
		//拼接用户ID

		String ids = org.apache.commons.lang.StringUtils.join(setIds,',');
		//将关联需求的人员列表中没有关联项目的人员关联到项目
		relateProjectUser(ids,userNumber,save.getTestProjectResourceID());

		if(files!=null){
			if(useFileService){
                feignDataDesignToFileService.upload(files,demand.getResourceID(), ObjectTypeEnum.DEMAND.getValue(),true);
			}else{
				upload (files,demand.getResourceID(),userNumber);

			}
		}
		return Result.renderSuccess();

	}

	/**
	 *
	 * @Title: saveAndUpdateAttachment
	 * @Description: 上传附件信息方法
	 * @param @param userNumber
	 * @param @param files    参数
	 * @return void    返回类型
	 * @throws IOException
	 * @throws
	 * <AUTHOR>
	 */
	private void upload (MultipartFile[] files,Long defectResourceID,String userNumber) throws IOException {
		List<DemandAccessory> demandList = new ArrayList<>();
		Map<String,MultipartFile> fileMap = new HashMap<>();
		Map<String,DemandAccessory> demandAccessoryMap = new HashMap<>();
		String filepath= AttachmentPathGenerator.getDefectAttachmentPath(AttachmentStoreTypeEnums.FTP, Long.valueOf(defectResourceID));
		String  newPath=paramConfig.getFtpPath()+filepath;
		for (MultipartFile file : files) {
			DemandAccessory model = new DemandAccessory();
			model.setDefectResourceID(defectResourceID);
			model.setName(file.getOriginalFilename());
			model.setSize(String.valueOf(file.getSize()));
			model.setUploadUserName(userNumber);
			String fileName = file.getOriginalFilename().trim();
			String[] str = fileName.split("\\.");
			String fileType = str[str.length - 1];
			model.setExtname("."+fileType);
			//设置ResourceID
			model.setResourceID(generateResourceID());
			model.setPath(newPath);
			fileMap.put(String.valueOf(model.getResourceID()),file);
			demandAccessoryMap.put(String.valueOf(model.getResourceID()),model);
			demandList.add(model);
		}
		if(!demandList.isEmpty()){
			demandAccessoryService.save(demandList,userNumber);
		}
		if(!fileMap.isEmpty()){
			//保存修改附件
			this.saveAndUpdateAttachment(fileMap,demandAccessoryMap);
		}
	}
	/**
	 *
	 * @param defectFileMap
	 * @Title: saveAndUpdateAttachment
	 * @Description: 保存附件
	 * @param @param resourceID
	 * @param @param fileMap    参数
	 * @return void    返回类型
	 * @throws IOException
	 * @throws
	 * <AUTHOR>
	 */
	private void saveAndUpdateAttachment(Map<String, MultipartFile> fileMap, Map<String, DemandAccessory> defectFileMap) throws IOException {

		Set<String> resourceIDs = fileMap.keySet();
		for (String resourceID : resourceIDs) {

			InputStream inputStream = fileMap.get(resourceID).getInputStream();
			String folderPath = "";
			String fileName = "";
			ByteArrayOutputStream byteStram = new ByteArrayOutputStream();
			byte[] buff = new byte[100]; //buff用于存放循环读取的临时数据
			int rc = 0;
            FileOutputStream outPutStream = null;
			try {
				int m=CheckUtil.checkLoop(inputStream.available()/100);

				if(m==CheckUtil.MAX_LOOPS){
					return;
				}
				while ((rc = inputStream.read(buff, 0, 100)) > 0) {
					byteStram.write(buff, 0, rc);
				}
				byte[] bytes = byteStram.toByteArray(); //in_b为转换之后的结果
				fileName = resourceID + defectFileMap.get(resourceID).getExtname();
				if(paramConfig.getIsFtpOn()) {//判断ftp是否打开

					folderPath = paramConfig.getFtpPath()+defectFileMap.get(resourceID).getPath();

					//上传FTP
					ftpUtil.upload(folderPath, fileName, bytes);

				}else {
					folderPath = paramConfig.getAttachmentPath();

					if (!FileValidUtil.valid(folderPath + File.separator + fileName)) {
						throw new RuntimeException("文件路径不合法！");
					}

					File dir = new File(folderPath);
					if(!dir.exists()) {
						dir.mkdirs();
					}
					File file = new File(folderPath+File.separator+fileName);

					outPutStream = new FileOutputStream(file);

					outPutStream.write(bytes);

					outPutStream.flush();

					outPutStream.close();

				}
			} catch (Exception e1) {

			} finally {
				try {
					if (inputStream != null) {
						inputStream.close();
					}
					if (outPutStream != null) {
						outPutStream.close();
					}
				} catch (Exception e) {

				}
			}
		}
	}

	/**
	 *
	 * @Title: generateResourceID
	 * @Description: 生成ResourceID
	 * @param @return    参数
	 * @return long    返回类型
	 * @throws
	 * <AUTHOR>
	 */
	private long generateResourceID() {
		SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
		return worker.genNextId();
	}
	/**
	 *
	 * @Title:updateDemand
	 * @Description:修改需求
	 * @param
	 * @return
	 * @author: wu_yancheng
	 * @date 2019年12月4日下午2:23:04
	 */
	@Override
	public Result<?> updateDemand(Map<String, Object> param ,MultipartFile[] files) throws IOException {
		//待关联人员列表
		Set<Object> setIds = new HashSet<>();
		String resourceID = ((String) param.get("resourceID")).trim();
		Demand demand = findByResourceID(Long.valueOf(resourceID));
		Long oldManagerResourceId = demand.getProjectManagerResourceID();
		String oldtype = demand.getTypename();
		String name = ((String) param.get("name")).trim();
		if (StringUtils.isEmpty(name)) {
			return Result.renderError("请输入需求名称!");
		}
		String number = ((String) param.get("number")).trim();
		if (StringUtils.isEmpty(number)) {
			return Result.renderError("请输入需求编号!");
		}
		//校验需求编号长度
		if(!number.matches(".{0,50}")){
			return Result.renderError("需求编号超过50的长度，请重新输入!");
		}
		List<Demand> sameDemandlist = demandDao.findByNumber(number);
		if (sameDemandlist != null && !sameDemandlist.isEmpty()
				&& !demand.getResourceID().equals(sameDemandlist.get(0).getResourceID())) {
			return Result.renderError("需求编号重复!");
		}
		String type = ((String) param.get("type")).trim();
		String userNumber = ((String) param.get("userNumber")).trim();
		String typename = ((String) param.get("typename")).trim();
		String token = HttpRequestUtils.getCurrentRequestToken();
		Result<?> result1 = feignDataDesignToBugService.updateDefectSubordinateName("demand", demand.getResourceID(),
				name, null, token);
		if (!result1.isSuccess()) {
			return Result.renderError("缺陷需求名称更改失败!");
		}
		String centralizedDepartment = ((String) param.get("centralizedDepartment")).trim();
		String testProjectResourceID = null;
		String testProjectName = null;
		if (!StringUtils.isEmpty(param.get("testProjectResourceID"))) {
			testProjectResourceID = ((String) param.get("testProjectResourceID")).trim();
			TestProject tp = testProjectService.findByResourceID(Long.valueOf(testProjectResourceID));
			testProjectName = tp.getName();
		}
		String level = null;
		if (!StringUtils.isEmpty(param.get("level"))) {
			level = ((String) param.get("level")).trim();
		}
		DemandChangeNodes demandChangeNodes = new DemandChangeNodes();
		demandChangeNodes.setDemandResourceID(demand.getResourceID());
		demandChangeNodes.setBeforeChangeType(demand.getType());
		demandChangeNodes.setAfterChangeType(type);
		iDemandChangeNodesService.save(demandChangeNodes, userNumber);
		demand.setName(name);
		demand.setNumber(number);
		demand.setCentralizedDepartment(centralizedDepartment);
		demand.setTestProjectResourceID(testProjectResourceID == null ? null : Long.valueOf(testProjectResourceID));
		demand.setTestProjectName(testProjectName);
		demand.setType(type);
		demand.setTypename(typename);
		demand.setLevel(level);
		demand.setEditTime(new Date());
		demand.setEditUser(userNumber);
		demand.setRemarks((String) param.get("remarks"));
		if (!StringUtils.isEmpty(param.get("dCreateTime"))) {
			String demandCreateTime = (String) param.get("dCreateTime");
			demand.setDemandCreateTime(DateUtil.getDate(demandCreateTime, "yyyy-MM-dd"));
		} else {
			demand.setDemandCreateTime(null);
		}

		Set<DemandUser> demandUserSet = new HashSet<>();
		Set<Long> userResourceSet = new HashSet<>();
		Set<DemandUser> deleteDemandUserSet = new HashSet<>();
		// 需求项目经理可以为空
		if (!StringUtils.isEmpty(param.get("projectManagerResourceID"))) {// 新传入的需求项目经理不空
			Long projectManagerResourceID = Long.valueOf(((String) param.get("projectManagerResourceID")).trim());
			if (!StringUtils.isEmpty(oldManagerResourceId)) {// 修改之前是非空的
				if (!oldManagerResourceId.toString().equals(projectManagerResourceID.toString())) {// 判断是否为同一个,同一个的话就过，不是同一个，修改更新
					// 添加新的关联关系
					List<DemandUser> dus = demandUserService.findByUserResourceIDAndDemandResourceID(String.valueOf(projectManagerResourceID), String.valueOf(demand.getResourceID()));
					if (CollectionUtils.isEmpty(dus)) {
						DemandUser du = new DemandUser();
						du.setDemandResourceID(demand.getResourceID());
						du.setUserResourceID(projectManagerResourceID);
						demandUserSet.add(du);
					}
					// 删除旧的关联关系
					List<DemandUser> oldDus = demandUserService.findByUserResourceIDAndDemandResourceID(String.valueOf(oldManagerResourceId), String.valueOf(demand.getResourceID()));
					if (!CollectionUtils.isEmpty(oldDus)) {
						deleteDemandUserSet.addAll(oldDus);
					}
				}
			}
			userResourceSet.add(projectManagerResourceID);
			demand.setProjectManagerResourceID(projectManagerResourceID);
			//添加至待关联人员列表
			setIds.add(projectManagerResourceID);
		} else {// 新传入的项目经理为空
			if (!StringUtils.isEmpty(oldManagerResourceId)) {// 之前是空的，跳过，之前不空，删除关联关系
				List<DemandUser> dus = demandUserService.findByUserResourceIDAndDemandResourceID(String.valueOf(oldManagerResourceId), String.valueOf(demand.getResourceID()));
				if (!CollectionUtils.isEmpty(dus)) {
					deleteDemandUserSet.addAll(dus);
				}

			}
			demand.setProjectManagerResourceID(null);
		}
		// 需提出人不能为空,需求和人员的关联表角色为需求人员
		if (!StringUtils.isEmpty(param.get("proposerResourceID"))) {
			String proposerResourceID = ((String) param.get("proposerResourceID")).trim();
			Long oldProposerResourceID = demand.getProposerResourceID();
			demand.setProposerResourceID(Long.valueOf(proposerResourceID));

			if (!proposerResourceID.equals(oldProposerResourceID.toString())) {  // 需求提出人和之前保存的需求提出人不一样， 删除需求关联人员表中 老的维护关系，添加新的维护关系
				// 添加新的关联关系
				List<DemandUser> dus = demandUserService.findByUserResourceIDAndDemandResourceID(String.valueOf(proposerResourceID), String.valueOf(demand.getResourceID()));
				if (CollectionUtils.isEmpty(dus)) {
					DemandUser du = new DemandUser();
					du.setDemandResourceID(demand.getResourceID());
					du.setUserResourceID(Long.valueOf(proposerResourceID));
					demandUserSet.add(du);
				}
				// 删除旧的关联关系
				List<DemandUser> oldDus = demandUserService.findByUserResourceIDAndDemandResourceID(String.valueOf(oldProposerResourceID), String.valueOf(demand.getResourceID()));
				if (!CollectionUtils.isEmpty(oldDus)) {
					deleteDemandUserSet.addAll(oldDus);
				}
			}
			userResourceSet.add(oldProposerResourceID);
			//添加至待关联人员列表
			setIds.add(proposerResourceID);
		} else {
			Result.renderError("需求提出人不能为空！");
		}
		// 更新测试经理字段
		Long oldTestManagerResourceID = demand.getTestManagerResourceID();
		Long testManagerResourceID = param.get("testManagerResourceID") != null && !"".equals((String) param.get("testManagerResourceID")) ? Long.valueOf(param.get("testManagerResourceID").toString())  : null;

		//测试经理相同则不做操作
		if (!Objects.equals(oldTestManagerResourceID,testManagerResourceID)) {
			// 添加新的关联关系
			List<DemandUser> dus = demandUserService.findByUserResourceIDAndDemandResourceID(String.valueOf(testManagerResourceID), String.valueOf(demand.getResourceID()));
			if (CollectionUtils.isEmpty(dus)) {
				DemandUser du = new DemandUser();
				du.setDemandResourceID(demand.getResourceID());
				du.setUserResourceID(testManagerResourceID);
				demandUserSet.add(du);
			}
			if (oldTestManagerResourceID != null) {
				// 删除旧的关联关系
				List<DemandUser> oldDus = demandUserService.findByUserResourceIDAndDemandResourceID(String.valueOf(oldTestManagerResourceID), String.valueOf(demand.getResourceID()));
				if (!CollectionUtils.isEmpty(oldDus)) {
					deleteDemandUserSet.addAll(oldDus);
				}
			}
			//添加至待关联人员列表
			setIds.add(testManagerResourceID);
		}
		userResourceSet.add(testManagerResourceID);
		demand.setTestManagerResourceID(testManagerResourceID);

		Set<DemandUser> deleteSet = deleteDemandUserSet.stream().filter(x -> (!userResourceSet.contains(x.getUserResourceID()))).collect(Collectors.toSet());
		if (!CollectionUtils.isEmpty(deleteSet)) {
			demandUserService.deleteInBatch(new ArrayList<>(deleteSet), userNumber);
		}

		if (!CollectionUtils.isEmpty(demandUserSet)) {
			demandUserService.save(new ArrayList<>(demandUserSet), userNumber);
		}

//      检查需求变更成上线和已完成的状态时的条件
//		需求状态改为已完成时，需要判断需求下所有的案例是否都已至少执行一次，且直接结果均为成功或者取消；需求下所有的缺陷是否都已是闭环状态（缺陷流程配置中的结束状态），
//		若不满足以上两点中的任何一点，弹框提示，状态修改不成功。
		if(typename.equals("未开始")){
			// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
			List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demand.getResourceID());
			// 查看当前需求下是否还存在缺陷
			List<Map<String, Object>> bugList = this.findBugsByDemandResourceID(demand.getResourceID());
			if((testCaseList!=null && testCaseList.size()>0) || (bugList!=null && bugList.size()>0)){
				return Result.renderError("需求下已存在案例或缺陷，需求状态不能改为未开始");
			}else{
				this.updateDemand(demand);
			}
		}else if(typename.equals("准备中")){
			// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
			List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demand.getResourceID());
			if(testCaseList!=null && testCaseList.isEmpty()){
				return Result.renderError("需求下不存在案例，需求状态不能改为准备中");
			}else{
				this.updateDemand(demand);
			}
		}else if(typename.equals("测试中")){
			// 查看当前需求下是否还存在缺陷
			List<Map<String, Object>> bugList = this.findBugsByDemandResourceID(demand.getResourceID());
			// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
			List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demand.getResourceID());
			List<Long> ceseRids = new ArrayList<Long>();
			testCaseList.stream().forEach(x -> ceseRids.add(x.getResourceID()));
			Integer countOne=0;
			if(ceseRids!=null  && ceseRids.size()>0){
				//手工执行案例引用表里当前案例非成功取消状态的案例
				 countOne = iTestCaseService.findTestCaseByCaseResourceID(ceseRids, "");
			}
			if( (countOne!=null && countOne>0) || (bugList!=null && !bugList.isEmpty())){
				this.updateDemand(demand);
			}else{
				return Result.renderError("需求下不存在执行的案例或缺陷，需求状态不能改为测试中");
			}
		}
		else if(typename.equals("已完成")) {
			String checkResult = this.checkOnLineDemandType(demand.getResourceID());
			if (StringUtils.isEmpty(checkResult)) {
				this.updateDemand(demand);
				//生成质量报告数据
				Result result=feignDataDesignToReportForm.createQualityReports(demand.getResourceID(),userNumber);
				if(result==null) {
					return Result.renderError("创建质量报告失败！");
				}
				if(!result.isSuccess()) {
					return Result.renderError(result.getMsg());
				}
			}else {
				return Result.renderError(checkResult);
			}
		}
//		需求状态改为已上线时，判断逻辑同需求状态改为已完成，同时需求下所有的案例进行入库操作。
		else if (typename.equals("已上线") && oldtype.equals("已完成")) {
			//查询需求下的任务状态  pro3524
			Result<?> demend = feignDataDesignToManexecuteService.findByDemandResourceID(demand.getResourceID(),token);
			List<Map<String,Object>>  mapList = (List<Map<String,Object>>) demend.getObj();
			List<Map<String,Object>> collect1 = mapList.stream().filter(x->x.get("status").toString().equals("1")||x.get("status").toString().equals("2")).collect(Collectors.toList());
			if(collect1.size()>0&&!collect1.isEmpty()) {
				return Result.renderError("需求下有未完成或未开始的任务，暂时不能上线");
			}
			String checkResult = this.checkOnLineDemandType(demand.getResourceID());
			if (StringUtils.isEmpty(checkResult)) {
				String  isEnableAutoTestCaseArchive = this.feignDataDesignToBasicService.findValueByKey(ENABLE_AUTO_TESTCASE_ARCHIVE, token);
				Boolean isEnable = isEnableAutoTestCaseArchive == null || Boolean.parseBoolean(isEnableAutoTestCaseArchive);
				if(isEnable){
					// 同步案例
					logger.info("需求上线，案例自动入库");
					iFeignAssetsToAssets.syncTestCase4Demand(demand.getResourceID().toString(), token);
				}
				this.updateDemand(demand);
			} else {
				return Result.renderError(checkResult);
			}
		}
		else if (typename.equals("已上线") && !oldtype.equals("已完成")) {
			return Result.renderError("只有需求状态为【已完成】的需求才可以进行上线操作!");
		}
		else {
			this.updateDemand(demand);
		}
		String delResourceIds = (String) param.get("delResourceIds");
		if(!StringUtils.isEmpty(delResourceIds)){
			deleteDemandAccessory(delResourceIds,userNumber);
		}
		if(files!=null&&files.length>0){
           if(useFileService){
               feignDataDesignToFileService.upload(files,demand.getResourceID(),ObjectTypeEnum.DEMAND.getValue(),true);
           }else{
               upload(files,demand.getResourceID(),userNumber);
           }

		}

		//拼接用户ID
		String ids = org.apache.commons.lang.StringUtils.join(setIds,',');
		//将关联需求的人-员列表中没有关联项目的人员关联到项目
		relateProjectUser(ids,userNumber,demand.getTestProjectResourceID());

		return Result.renderSuccess();
	}

	/**
	 *
	 * @Title:checkIsNull
	 * @Description:需求编号唯一性
	 * @param
	 * @return boolean
	 * @author: wu_yancheng
	 * @date 2019年12月4日下午4:52:12
	 */
	public boolean checkIsNull(String number) {
		boolean flag = true;
		List<Demand> list = demandDao.findByNumber(number);
		if (list != null && list.size() > 0) {
			flag = false;
		}
		return flag;
	}

	/**
	 *
	 * @Title:delectDemand
	 * @Description:删除需求
	 * @param
	 * @return void
	 * @author:
	 * @date 2019年12月4日下午2:23:04
	 */
	@Override
	public Result delectDemand(String delectDemandResourceIDs, UserVo userVo) {
		List<Demand> deleteDemandList = new ArrayList<Demand>();
		String demandRidsStr = "";
		return this.checkDeleteDemand(delectDemandResourceIDs, deleteDemandList, demandRidsStr);
	}

	/**
	 * <AUTHOR>
	 * @description 确认删除需求
	 * @date 2020年11月25日 11:08
	 * @param [delectDemandResourceIDs, userVo]
	 * @return com.jettech.dto.Result<?>
	 **/
	@Override
	public Result<?> confirmDelectDemand(String delectDemandResourceIDs, UserVo userVo) {
		List<Demand> deleteDemandList = new ArrayList<Demand>();
		String demandRidsStr = "";

		Result checkResult = this.checkDeleteDemand(delectDemandResourceIDs, deleteDemandList, demandRidsStr);
		if (!checkResult.isSuccess()) {
			return checkResult;
		}

		Map<String, String> params = new HashMap<String, String>();
		if (!CollectionUtils.isEmpty(deleteDemandList)) {
			params.put("demandResourceIDs", deleteDemandList.stream().map(x -> x.getResourceID().toString()).collect(Collectors.joining(",")));
			params.put("userNumber", userVo.getUserNumber());
		}
		if (!deleteDemandList.isEmpty()) {
			// 删除需求底下的测试计划
			/*Result result = feignDataDesignToManexecuteService.deleteAllPlanByDemandList(params);
			if (!result.isSuccess()) {
				return Result.renderError("测试计划删除失败！");
			}*/
			String token = HttpRequestUtils.getCurrentRequestToken();
			Result result1 = feignDataDesignToBasicService.deleteByUserNumberAndTargetResourceID(delectDemandResourceIDs,
					userVo.getUserNumber(), token);
			if (!result1.isSuccess()) {
				logger.error("删除消息通知失败!");
			}
			this.deleteInBatch(deleteDemandList, userVo.getUserNumber());
			//在删除需求的时候，将需求附属的文件也进行删除
			if(useFileService){
				feignDataDesignToFileService.deleteFileByResourceIds(delectDemandResourceIDs,ObjectTypeEnum.DEMAND.getValue());

			}
		}
		return Result.renderSuccess("成功");
	}

	/**
	 * <AUTHOR>
	 * @description 校验是否能删除
	 * @date 2020年11月25日 11:08
	 * @param
	 * @return
	 **/
	private Result checkDeleteDemand(String delectDemandResourceIDs, List<Demand> deleteDemandList, String demandRidsStr) {
		String[] split = delectDemandResourceIDs.split(",");
		if (delectDemandResourceIDs == null || delectDemandResourceIDs.isEmpty()) {
			return Result.renderError("参数有误");
		}
		List<String> delectDemandList = new ArrayList<>();
		for (String string : split) {
			delectDemandList.add(string);
		}
		List<Demand> DemandList = demandDao.findByResourceIDIn(delectDemandList);

		if (DemandList == null || DemandList.isEmpty()) {
			return Result.renderError("库里数据不存在");
		}

		for (Demand demand : DemandList) {
			Result planByDemandResourceID = feignDataDesignToManexecuteService.findPlanByDemandResourceID(demand.getResourceID().toString());
			List<Map<String, String>> obj = (List<Map<String, String>>) planByDemandResourceID.getObj();
			if(obj!=null&&obj.size()>0){
				return Result.renderError("当前需求已有测试计划，不允许删除");
			}
			if (!"已上线".equals(demand.getTypename())) {
				deleteDemandList.add(demand);
				demandRidsStr += demand.getResourceID() + ",";
//				params.put("demandResourceIDs", String.valueOf(demand.getResourceID()));
			} else {
				return Result.renderError("存在已上线数据，无法删除！");
			}
		}
		return Result.renderSuccess("成功");
	}

	/**
	 *
	 * @Title: findNotRelevanceUser
	 * @Description: 查询未关联的用户
	 * @param id
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	@Override
	public List<Map<String, Object>> findNotRelevanceUser(String id) {

		List<Map<String, Object>> mapList = new ArrayList<>();

		if (id != null && !"".equals(id)) {

			Demand demand = find(Integer.parseInt(id));
			if (demand != null) {
				mapList = demandUserService.findUserNotINDemandUser(String.valueOf(demand.getResourceID()));
			}
		}
		return mapList;
	}

	/**
	 *
	 * @Title: findRelevanceSystem
	 * @Description: 查询已关联的系统
	 * @param resourceID
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	@Override
	public List<Map<String, Object>> findRelevanceSystem(String resourceID) {
		return demandTestSystemService.findRelevanceSystem(resourceID);
	}

	/**
	 *
	 * @Title: findRelevanceSystem
	 * @Description: 查询所有系统
	 * @param "request"
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	@Override
	public List<Map<String, Object>> findAllSystem(String name, String resourceID) {
		return demandTestSystemService.findAllSystem(name, resourceID);
	}

	/**
	 *
	 * @Title: findRelevanceUser
	 * @Description: 查询已关联的用户
	 * @param id
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	@Override
	public List<Map<String, Object>> findRelevanceUser(String id) {

		List<Map<String, Object>> mapList = new ArrayList<>();

		if (id != null && !"".equals(id)) {

			Demand demand = find(Integer.parseInt(id));
			if (demand != null) {
				mapList = demandUserService.findUserByDemandResourceID(String.valueOf(demand.getResourceID()));
			}
		}
		return mapList;
	}
	/**
	 *
	 * @Description   多个需求同时关联一个人
	 * @param demandId
	 * @param ids
	 * @param role
	 * @param userNumber
	 * @return
	 * @throws Exception
	 * tianliping
	 * 2023年1月18日
	 */
	@Override
	public String moreDemandAddDemandUser(String demandId, String ids, String role, String userNumber) throws Exception {
		if (demandId == null || "".equals(demandId)) {
			return "需求异常，不能添加人员！";
		}
		if (ids == null || "".equals(ids)) {
			return "请选择要添加的人员!";
		}
		String token = HttpRequestUtils.getCurrentRequestToken();
		List<Demand> demandList=new ArrayList<>();
		//
		List<JettechUserDTO> userDTOs = feignTrp.findByIds(ids, token);
		JettechUserDTO userDTO = userDTOs.stream().findFirst().get();

		String userResourceIDsForNotify = "";
		List<DemandUser> demandUsers = new ArrayList<DemandUser>();
		String[] split = demandId.split(",");
		for(String drid :split) {
			Demand demand = find(Integer.parseInt(drid));
			if (demand == null) {
				return "需求异常，不能添加人员！";
			}
			demandList.add(demand);
			List<Map<String, Object>> listUserAndRole = demandUserService
					.findUserByDemandResourceID(demand.getResourceID().toString());
			List<Long> hasUserRids = new ArrayList();
			List<Long> noUserRids = new ArrayList();
			for (Map<String, Object> map : listUserAndRole) {
				hasUserRids.add(Long.valueOf(String.valueOf(map.get("resourceID"))));
			}
			String relateUserIds = "";

		if (!hasUserRids.contains(userDTO.getResourceID())) {
				noUserRids.add(userDTO.getResourceID());
				DemandUser demandUser = new DemandUser();
				demandUser.setUserResourceID(userDTO.getResourceID());
				demandUser.setDemandResourceID(demand.getResourceID());
				demandUser.setRole(role);
				demandUsers.add(demandUser);
				userResourceIDsForNotify=userDTO.getResourceID().toString();
				//将关联需求的人员列表中没有关联项目的人员关联到项目
				relateProjectUser(userDTO.getResourceID().toString(), userNumber, demand.getTestProjectResourceID());
		    }
		}

		    demandUserService.save(demandUsers, userNumber);
			if (!StringUtils.isEmpty(userResourceIDsForNotify)) {
				//一个或者 多个需求
				List<Map<String, Object>> notifyResult = demandNotifyUtils
						.DemandAddUserNotifyOnePersonMoreDemand(userResourceIDsForNotify, demandList, userNumber);
				Result result = feignDataDesignToBasicService.sendAddDemandUserNotify(notifyResult,token);
				if (!result.isSuccess()) {
					throw new Exception();
				}
			}
			return "操作成功";


	}


	/**
	 *
	 * @Title: addDemandUser
	 * @Description: 给需求关联人员
	 * @param demandId
	 * @param ids
	 * @param userNumber
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	@Override
	public String addDemandUser(String demandId, String ids, String role, String userNumber) throws Exception {
		if (demandId == null || "".equals(demandId)) {
			return "需求异常，不能添加人员！";
		}
		if (ids == null || "".equals(ids)) {
			return "请选择要添加的人员!";
		}
		String[] split = demandId.split(",");

	//	Demand demand = find(Integer.parseInt(demandId));
		for(String drid :split) {
			Demand demand = find(Integer.parseInt(drid));

		if (demand == null) {
			return "需求异常，不能添加人员！";
		}

		List<DemandUser> demandUsers = new ArrayList<DemandUser>();
		String token = HttpRequestUtils.getCurrentRequestToken();
		List<JettechUserDTO> userDTOs = feignTrp.findByIds(ids, token);
		List<Map<String, Object>> listUserAndRole = demandUserService
				.findUserByDemandResourceID(demand.getResourceID().toString());
		List<Long> hasUserRids = new ArrayList();
		List<Long> noUserRids = new ArrayList();
		for (Map<String, Object> map : listUserAndRole) {
			hasUserRids.add(Long.valueOf(String.valueOf(map.get("resourceID"))));
		}
		for (JettechUserDTO userDTO : userDTOs) {
			if (!hasUserRids.contains(userDTO.getResourceID())) {
				noUserRids.add(userDTO.getResourceID());
			}
		}

		String relateUserIds = "";
		if (!noUserRids.isEmpty() && noUserRids.size() > 0) {
			for (Long rid : noUserRids) {
				DemandUser demandUser = new DemandUser();
				demandUser.setUserResourceID(rid);
				demandUser.setDemandResourceID(demand.getResourceID());
				demandUser.setRole(role);
				demandUsers.add(demandUser);
			}
			relateUserIds = org.apache.commons.lang.StringUtils.join(noUserRids,',');
		}
		if (!demandUsers.isEmpty() && demandUsers.size() > 0) {
			// 如果是这三种角色，需要消息通知
			String userResourceIDsForNotify = "";
//			List<String> roles = new ArrayList<>();
//			roles.add("开发人员");
//			roles.add("SIT测试");
//			roles.add("UAT测试");
			// 当某个人第一次关联开发人员，sit人员,uat人员时要给消息通知（之前关联过这三种角色的人员有过通知就不再给新的通知）
//			if (roles.contains(role)) {
				Set<String> roleUserSet = new HashSet<>();// 存放相关角色的用户的rid
//				List<DemandUser> demandHasRalationAllUsers = demandUserService
//						.findBydemandResourceID(demand.getResourceID());
//				demandHasRalationAllUsers.stream().forEach(du -> {
//					if (roles.contains(du.getRole())) {
//						roleUserSet.add(String.valueOf(du.getUserResourceID()));
//					}
//				});
				for (Long rid : noUserRids) {
					if (!roleUserSet.contains(String.valueOf(rid))) {
						userResourceIDsForNotify += rid + ",";
					}
				}

//			}
			demandUserService.save(demandUsers, userNumber);

			//将关联需求的人员列表中没有关联项目的人员关联到项目
			relateProjectUser(relateUserIds, userNumber, demand.getTestProjectResourceID());


			if (!StringUtils.isEmpty(userResourceIDsForNotify)) {
				List<Map<String, Object>> notifyResult = demandNotifyUtils
						.DemandAddUserNotifyMorePersonOneDemand(userResourceIDsForNotify, demand, userNumber);
				Result result = feignDataDesignToBasicService.sendAddDemandUserNotify(notifyResult,token);
				if (!result.isSuccess()) {
					throw new Exception();
				}
			}
		  }
		}

		return "操作成功";
	}

	/**
	  * 将关联需求的人员列表中没有关联项目的人员关联到项目
	  *
	  * <AUTHOR>
	  *    6:11 下午
	  * @update
	  * @param ids, userNumber, testProjectResourceID     用户ID列表 ','分隔, 操作用户, 项目ID
	  * @return  void
	  * @since [1.0]
	  */
	private void relateProjectUser(String ids, String userNumber, Long testProjectResourceID) {
		//查询所有已关联项目的人员
		List<TestProjectUser> list = testProjectUserService.findByTestProjectResourceID(testProjectResourceID);
		Set<String> set = Sets.newHashSet();
		for (TestProjectUser testProjectUser:list) {
			set.add(testProjectUser.getUserResourceID().toString());
		}
		Set<String> noSet = Sets.newHashSet();
		//筛选未关联的人员
		for (String uid:ids.split(",")) {
			if(!set.contains(uid)){
				noSet.add(uid);
			}
		}
		//拼接集合为字符串
		String noIds = org.apache.commons.lang.StringUtils.join(noSet,",");
		//将没有关联项目的人员关联到项目
		testProjectUserService.relateUser(noIds,testProjectResourceID.toString(),userNumber);
	}

	/**
	 *
	 * @Title: addDemandSystem
	 * @Description: 给需求关联系统
	 * @param "request"
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */

	@Override
	public String addDemandSystem(List<String> testSystemResourceIDs, String demandId, String ids, String userNumber) {
		if (demandId == null || "".equals(demandId)) {
			return "需求异常，不能关联系统！";
		}
		Demand demand = find(Integer.parseInt(demandId));
		if (demand == null) {
			return "需求异常，不能关联系统！";
		}
		List<String> addSysResIDs = new ArrayList<String>();
		List<String> deleteSysResIDs = new ArrayList<String>();
		addSysResIDs.addAll(testSystemResourceIDs);

		List<DemandTestSystem> demandTestSystem = demandTestSystemService
				.findBydemandResourceID(Long.valueOf(demand.getResourceID()));
		for (DemandTestSystem set : demandTestSystem) {
			if (!addSysResIDs.isEmpty() && !addSysResIDs.contains(String.valueOf(set.getTestSystemResourceID()))) {
				deleteSysResIDs.add(String.valueOf(set.getResourceID()));
			} else if (addSysResIDs.isEmpty()) {
				deleteSysResIDs.add(String.valueOf(set.getResourceID()));
			}
		}

		if (ids != null && !"".equals(ids)) {
			String[] idArr = ids.split(",");
			List<Long> idList = new ArrayList<Long>();
			for (String id : idArr) {
				idList.add(Long.parseLong(id));
			}
			List<DemandTestSystem> demandTestSystems = new ArrayList<DemandTestSystem>();
			List<TestSystem> testSystems = testSystemService.findByIdIn(idList);
			for (TestSystem testSystem : testSystems) {
				DemandTestSystem testSystem1 = demandTestSystemService.findByTestSystemResourceIDAndDemandResourceID(
						String.valueOf(testSystem.getResourceID()), String.valueOf(demand.getResourceID()));
				if (testSystem1 == null) {
					testSystem1 = new DemandTestSystem();
					testSystem1.setTestSystemResourceID(testSystem.getResourceID());
					testSystem1.setDemandResourceID(demand.getResourceID());
					demandTestSystems.add(testSystem1);
				}
			}
			if (!demandTestSystems.isEmpty()) {
				demandTestSystemService.save(demandTestSystems, userNumber);
				HashMap<String, Object> param = new HashMap<>();
				param.put("demandId", demandTestSystems.get(0).getDemandResourceID());
				param.put("testSystem", demandTestSystems.stream().map(DemandTestSystem::getTestSystemResourceID)
						.collect(Collectors.toList()));
				param.put("type", "add");
				Result result = feignDataDesignToManexecuteService.synchronousModificationTestPlanRelationSystem(param);
				if (!result.isSuccess()) {
					throw new RuntimeException("调用testPlan同步方法失败!");
				}
			}
		}
		// 需求变更，关联系统时把系统的行方开发经理添加到当前需求的行方开发经理下
//		Set<Long> userSet = new HashSet<>();
//		List<TestSystem> testsystems = testSystemService.findByResourceIDIn(testSystemResourceIDs);
//		List<String> listAll = new ArrayList<String>();
//		for (TestSystem t : testsystems) {
//			if (null != t.getProjectManager()) {
//				listAll.addAll(Arrays.asList(t.getProjectManager().split(",")));
//			}
//		}
//		Set<String> userList = new LinkedHashSet<>(listAll);
		// 通过UserNumber获取人员resourceID
//		String token = HttpRequestUtils.getCurrentRequestToken();
//		List<JettechUserDTO> jettechUserDTOS = feignDataDesignToBasicService.findByNumberIn(new ArrayList<>(userList),
//				token);
//		for (JettechUserDTO j : jettechUserDTOS) {
//			userSet.add(j.getResourceID());
//		}
//		List<Map<String, Object>> duMap = iDemandUserService
//				.findUserByDemandResourceID(demand.getResourceID().toString());
//		List<Long> hasUserRelation = new ArrayList<>();
//		for (Map<String, Object> map : duMap) {
//				hasUserRelation.add(Long.valueOf(String.valueOf(map.get("resourceID"))));
//		}
//		List<Long> addRelation = new ArrayList<>();
//		for (Long perhapsUser : userSet) {
//			if (!hasUserRelation.contains(perhapsUser)) {
//				addRelation.add(perhapsUser);
//			}
//		}
		// 补充添加行方开发经理关系
//		if (!addRelation.isEmpty() && addRelation.size() > 0) {
//			List<DemandUser> addDus = new ArrayList<>();
//			for (Long urid : addRelation) {
//				DemandUser du = new DemandUser();
//				du.setDemandResourceID(demand.getResourceID());
//				du.setRole("行方开发经理");
//				du.setUserResourceID(urid);
//				addDus.add(du);
//			}
//			iDemandUserService.save(addDus, userNumber);
//		}
		return "操作成功";
	}

	/**
	 *
	 * @Title: removeDemandUser
	 * @Description: 删除关联人员
	 * @param demandId
	 * @param rIds
	 * @param userNumber
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
	@Override
	public Result removeDemandUser(String demandId, String rIds, String userNumber, String duResourceIDs) {
		// 根据id查询出需求的详细信息
//		Demand demand = find(Integer.valueOf(demandId));
//		// 根据需求的resourceID查询出需求下的所有人员
//		List<Map<String, Object>> relevanceUser = demandUserService
//				.findUserByDemandResourceID(String.valueOf(demand.getResourceID()));
//		ArrayList<String> userNumberList = new ArrayList<>();
//		ArrayList<String> userResourceIDList = new ArrayList<>();
//		ArrayList<Object> duResourceIDList = new ArrayList<>();
//		for (Map<String, Object> map : relevanceUser) {
//			String role = (String) map.get("role");
//			if (role.equals("UAT测试") || role.equals("开发人员") || role.equals("SIT测试")) {
//				userResourceIDList.add(String.valueOf(map.get("resourceID")));
//				duResourceIDList.add(String.valueOf(map.get("duResourceID")));
//			}
//		}
		//

		if (StringUtils.isEmpty(duResourceIDs)) {
			return  Result.renderError("请选择人员!");
		}
		String[] duRids = duResourceIDs.split(",");
		List<String> del = new ArrayList<>();
		for (String durid : duRids) {
			del.add(durid);
		}
		if (!del.isEmpty() && del.size() > 0) {
			List<DemandUser> findDel = demandUserService.findByResourceIDIn(del);
			for (DemandUser demandUser : findDel) {
				Map<String, String> params = new HashMap<>();
				params.put("managerResourceID",demandUser.getUserResourceID().toString());
				params.put("demandResourceID",demandUser.getDemandResourceID().toString());
				Result result = feignDataDesignToManexecuteService.findTestTaskByDemandIdAndManagerResourceID(params);
				if(result.getCode()==20000){
					return Result.renderError("当前用户已分配任务不允许移除");
				}
			}
			if (!findDel.isEmpty() && findDel.size() > 0) {
				demandUserService.deleteInBatch(findDel, userNumber);
			}
		}

		// 删除消息通知操作
//		HashMap<String, Object> userResourceIDHashMap = new HashMap<>();
//		for (String s : userResourceIDList) {
//			if (userResourceIDHashMap.get(s) == null) {
//				userResourceIDHashMap.put(s, 1);
//			} else {
//				userResourceIDHashMap.put(s, (int) userResourceIDHashMap.get(s) + 1);
//			}
//		}
//		HashMap<String, Object> rIDHashMap = new HashMap<>();
//		List<String> list = Arrays.asList(rIds.split(","));
//		for (int i = 0; i < del.size(); i++) {
//			if (duResourceIDList.contains(del.get(i))) {
//				if (rIDHashMap.get(list.get(i)) == null) {
//					rIDHashMap.put(list.get(i), 1);
//				} else {
//					rIDHashMap.put(list.get(i), (int) rIDHashMap.get(list.get(i)) + 1);
//				}
//			}
//
//		}
//		// 判断如果传递过来的人员id的个数与查询出来三种角色出现的次数一致,添加到集合
//		ArrayList<String> objects = new ArrayList<>();
//		for (String s : userResourceIDList.stream().distinct().collect(Collectors.toList())) {
//			if (userResourceIDHashMap.get(s).equals(rIDHashMap.get(s))) {
//				objects.add(s);
//			}
//		}
//		if (!objects.isEmpty() && objects.size() > 0) {
//			String token = HttpRequestUtils.getCurrentRequestToken();
//			List<JettechUserDTO> byResIDs = feignDataDesignToBasicService
//					.findByResIDs(org.apache.commons.lang3.StringUtils.join(objects, ","), token);
//			if (!byResIDs.isEmpty()) {
//				for (JettechUserDTO usersByResourceID : byResIDs) {
//					userNumberList.add(usersByResourceID.getNumber());
//				}
//				Result result = feignDataDesignToBasicService.deleteByTargetResourceIDAndUserNumber(
//						String.valueOf(demand.getResourceID()),
//						org.apache.commons.lang3.StringUtils.join(userNumberList, ","), token,userNumber);
//				if (result.getCode() != 20000) {
//					return "操作成功!但消息通知开小差了!请联系管理员!";
//				}
//			}
//
//		}

		return Result.renderSuccess("操作成功");
	}

	@Override
	protected void preDelete(Demand demand, String userNumber) {
		if (!"已上线".equals(demand.getTypename())) {
			List<DemandTestSystem> demandTestSystems = demandTestSystemService
					.findBydemandResourceID(demand.getResourceID());
			if (demandTestSystems != null && !demandTestSystems.isEmpty()) {
				demandTestSystemService.deleteInBatch(demandTestSystems, userNumber);
			}
			List<DemandUser> DemandUsers = iDemandUserService.findBydemandResourceID(demand.getResourceID());
			if (DemandUsers != null && !DemandUsers.isEmpty()) {
				iDemandUserService.deleteInBatch(DemandUsers, userNumber);
			}

			List<TestCase> TestCases = iTestCaseService.findBydemandResourceID(demand.getResourceID());
			if (TestCases != null && !TestCases.isEmpty()) {
				iTestCaseService.deleteInBatch(TestCases, userNumber);
			}
			// 删除和该需求有关系的任务（在我的需求下，测试计划下，被测系统的我的任务）保留
//			List<Map<String,Object>> findTestTask = demandDao.findTestTaskByDemandResourceID(demand.getResourceID());
//			if(!findTestTask.isEmpty() && findTestTask.size()>0) {
//				demandDao.deleteTestTaskByDemandResourceID(demand.getResourceID());
//			}
		}
	}

	/**
	 *
	 * @Title: initWorkBenchMyDemand
	 * @Description: 初始化工作台我的需求
	 * @param pageRequest
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月5日下午4:12:09
	 */
	/*
	 * @Override public Page<Map<String, Object>> initWorkBenchMyDemand(PageRequest
	 * pageRequest, Map<String, String> params) { String userNumber =
	 * params.get("userNumber"); JettechUserDTO user=
	 * feignDataDesignToBasicService.findByNumber(userNumber); String
	 * userRid=String.valueOf(user.getResourceID()); params.put("userResourceID",
	 * userRid); Page<Map<String, Object>>
	 * page=demandDao.initWorkBenchMyDemandByPage(pageRequest,params); return page;
	 * }
	 */
	@Override
	public Page<Map<String, Object>> initWorkBenchMyDemand(PageRequest pageRequest, Map<String, String> params) {
		// 查询当前用户下的需求
		String userNumber = params.get("userNumber");
		logger.info("datadesign查询需求开始" + params.toString());
		Page<Map<String, Object>> pageDemand = demandDao.findDemandPageByNumber(pageRequest, userNumber, params);
		logger.info("datadesign查询需求结束" + pageDemand.getContent().toString());
		List<String> demandResourceIDs = new ArrayList<>();
		Map<Long, String> dsTestCaseCountMap = new HashMap<>();
		Map<Long, String> totalCountMap = new HashMap<>();
		Map<Long, String> dsTestCaseCountUnExecuteMap = new HashMap<>();
		if (pageDemand.getNumberOfElements() != 0) {
			pageDemand.getContent().stream().forEach(x -> {
				demandResourceIDs.add(x.get("resourceID").toString());
			});
			List<Map<String, Object>> listDemand = pageDemand.getContent();
			// 根据需求查询需求下手工编写案例数
			List<Map<String, Object>> dsTestCaseCount = testCaseService.findCountNumberByDemandRids(demandResourceIDs,
					userNumber);
			// 查询需求下的案例总数
			List<Map<String, Object>> totalCount = testCaseService.findTotalNumberByDemandRids(demandResourceIDs);
			// 根据需求查询需求下手工编写案例未执行案例
			List<Map<String, Object>> dsTestCaseCountUnExecute = demandDao.findUnExecuteUnderDemand(demandResourceIDs,
					userNumber);
			// 案例不空
			if (!dsTestCaseCount.isEmpty() && dsTestCaseCount.size() > 0) {
				dsTestCaseCount.stream().forEach(x -> {
					dsTestCaseCountMap.put(Long.valueOf(x.get("demandResourceID").toString()),
							String.valueOf(x.get("dstescasecount")));
				});
				// 安利不空，才有待执行的不空
				if (!dsTestCaseCountUnExecute.isEmpty() && dsTestCaseCountUnExecute.size() > 0) {
					dsTestCaseCountUnExecute.stream().forEach(x -> {
						dsTestCaseCountUnExecuteMap.put(Long.valueOf(x.get("demandResourceID").toString()),
								String.valueOf(x.get("unexecute")));
					});
					listDemand.stream().forEach(x -> {
						x.put("dstestcaseNumber", dsTestCaseCountMap.get(x.get("resourceID")) == null ? "0"
								: dsTestCaseCountMap.get(x.get("resourceID")));
						x.put("dstestcaseNumberNot", dsTestCaseCountUnExecuteMap.get(x.get("resourceID")) == null ? "0"
								: dsTestCaseCountUnExecuteMap.get(x.get("resourceID")));
					});

				} else {// 案例不空，待执行的空
					listDemand.stream().forEach(x -> {
						x.put("dstestcaseNumber", dsTestCaseCountMap.get(x.get("resourceID")) == null ? "0"
								: dsTestCaseCountMap.get(x.get("resourceID")));
						x.put("dstestcaseNumberNot", "0");
					});
				}
			} else {// 案例空
				listDemand.stream().forEach(x -> {
					x.put("dstestcaseNumber", "0");
					x.put("dstestcaseNumberNot", "0");
				});
			}

			if (!totalCount.isEmpty() && totalCount.size() > 0) {
				totalCount.stream().forEach(x -> {
					totalCountMap.put(Long.valueOf(x.get("demandResourceID").toString()),
							String.valueOf(x.get("totalCount")));
				});
				listDemand.stream().forEach(x -> {
					x.put("totalCount", totalCountMap.get(x.get("resourceID")) == null ? "0"
							: totalCountMap.get(x.get("resourceID")));
				});

			} else {
				listDemand.stream().forEach(x -> {
					x.put("totalCount", "0");
				});
			}

		}
		return pageDemand;
	}

	/**
	 *
	 * @Title: initWorkbenchAlreadyMyDemand
	 * @Description: 查询我的已办需求列表
	 * @param pageRequest
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月23日 下午3:41:26
	 */
	@Override
	public Page<Map<String, Object>> initWorkbenchAlreadyMyDemand(PageRequest pageRequest, Map<String, String> params) {
		// 查询当前用户下的需求
		String userNumber = params.get("userNumber");
		logger.info("datadesign查询需求开始" + params.toString());
		Page<Map<String, Object>> pageDemand = demandDao.findWorkbenchAlreadyDemandPageByNumber(pageRequest, userNumber,
				params);
		logger.info("datadesign查询需求结束" + pageDemand.getContent().toString());
		List<String> demandResourceIDs = new ArrayList<>();
		Map<Long, String> dsTestCaseCountMap = new HashMap<>();
		Map<Long, String> totalCountMap = new HashMap<>();
		Map<Long, String> dsTestCaseCountUnExecuteMap = new HashMap<>();
		if (pageDemand.getNumberOfElements() != 0) {
			pageDemand.getContent().stream().forEach(x -> {
				demandResourceIDs.add(x.get("resourceID").toString());
			});
			List<Map<String, Object>> listDemand = pageDemand.getContent();
			// 根据需求查询需求下手工编写案例数
			List<Map<String, Object>> dsTestCaseCount = testCaseService.findCountNumberByDemandRids(demandResourceIDs,
					userNumber);
			// 查询需求下的案例总数
			List<Map<String, Object>> totalCount = testCaseService.findTotalNumberByDemandRids(demandResourceIDs);
			// 根据需求查询需求下手工编写案例未执行案例
			List<Map<String, Object>> dsTestCaseCountUnExecute = demandDao.findUnExecuteUnderDemand(demandResourceIDs,
					userNumber);
			// 案例不空
			if (!dsTestCaseCount.isEmpty() && dsTestCaseCount.size() > 0) {
				dsTestCaseCount.stream().forEach(x -> {
					dsTestCaseCountMap.put(Long.valueOf(x.get("demandResourceID").toString()),
							String.valueOf(x.get("dstescasecount")));
				});
				// 安利不空，才有待执行的不空
				if (!dsTestCaseCountUnExecute.isEmpty() && dsTestCaseCountUnExecute.size() > 0) {
					dsTestCaseCountUnExecute.stream().forEach(x -> {
						dsTestCaseCountUnExecuteMap.put(Long.valueOf(x.get("demandResourceID").toString()),
								String.valueOf(x.get("unexecute")));
					});
					listDemand.stream().forEach(x -> {
						x.put("dstestcaseNumber", dsTestCaseCountMap.get(x.get("resourceID")) == null ? "0"
								: dsTestCaseCountMap.get(x.get("resourceID")));
						x.put("dstestcaseNumberNot", dsTestCaseCountUnExecuteMap.get(x.get("resourceID")) == null ? "0"
								: dsTestCaseCountUnExecuteMap.get(x.get("resourceID")));
					});

				} else {// 案例不空，待执行的空
					listDemand.stream().forEach(x -> {
						x.put("dstestcaseNumber", dsTestCaseCountMap.get(x.get("resourceID")) == null ? "0"
								: dsTestCaseCountMap.get(x.get("resourceID")));
						x.put("dstestcaseNumberNot", "0");
					});
				}
			} else {// 案例空
				listDemand.stream().forEach(x -> {
					x.put("dstestcaseNumber", "0");
					x.put("dstestcaseNumberNot", "0");
				});
			}

			if (!totalCount.isEmpty() && totalCount.size() > 0) {
				totalCount.stream().forEach(x -> {
					totalCountMap.put(Long.valueOf(x.get("demandResourceID").toString()),
							String.valueOf(x.get("totalCount")));
				});
				listDemand.stream().forEach(x -> {
					x.put("totalCount", totalCountMap.get(x.get("resourceID")) == null ? "0"
							: totalCountMap.get(x.get("resourceID")));
				});

			} else {
				listDemand.stream().forEach(x -> {
					x.put("totalCount", "0");
				});
			}

		}
		return pageDemand;
	}

	/**
	 *
	 * @Title: initWorkbenchCreateMyDemand
	 * @Description: 查询我创建的需求列表
	 * @param pageRequest
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月23日 下午4:12:13
	 */
	@Override
	public Page<Map<String, Object>> initWorkbenchCreateMyDemand(PageRequest pageRequest, Map<String, String> params) {
		// 查询当前用户下的需求
		String userNumber = params.get("userNumber");
		logger.info("datadesign查询需求开始" + params.toString());
		Page<Map<String, Object>> pageDemand = demandDao.findWorkbenchCreateDemandPageByNumber(pageRequest, userNumber,
				params);
		logger.info("datadesign查询需求结束count:" + pageDemand.getNumberOfElements()  + ",content:" + pageDemand.getContent().toString());
		if (pageDemand.getNumberOfElements() != 0) {
			List<String> closedKeys = testProjectService.findEndStateBytestProjectResourceID(params.get("testProjectResourceID"));
			for (Map<String, Object> x : pageDemand.getContent()) {
				if(!StringUtils.isEmpty(x.get("finishTime"))&&!StringUtils.isEmpty(x.get("startTime"))){
					Object finishTime = x.get("finishTime");
					Long day = null;
					try {
						day = this.compareTimeTwo(finishTime);
					} catch (ParseException e) {
						e.printStackTrace();
					}
					if(day>0){
						x.put("overStatus",true);
						x.put("overday",day);
					}else{
						x.put("overStatus",false);
						x.put("overday",0);
					}
					x.put("starToend",x.get("startTime")+"/"+x.get("finishTime"));
				}else{
					x.put("starToend",null);
					x.put("overStatus",false);
					x.put("overday",0);
				}
				if(!StringUtils.isEmpty(x.get("guidelineinfo"))) {
					@SuppressWarnings("unchecked")
					HashMap<String, JSONObject> map = JSONObject.parseObject(x.get("guidelineinfo").toString(), HashMap.class);
					JSONObject s = map.get("缺陷命中率");
					x.put("effectiveDefectRateLab", s.get("lab"));
					x.put("effectiveDefectRateValue", s.get("value"));
					JSONObject s1 = map.get("案例执行进度偏差");
					x.put("caseExecutionDeviationLab", s1.get("lab"));
					x.put("caseExecutionDeviationValue", s1.get("value"));
					JSONObject s2 = map.get("缺陷修复率");
					x.put("defectRepairRateLab", s2.get("lab"));
					x.put("defectRepairRateValue", s2.get("value"));
					JSONObject s3 = map.get("案例执行通过率");
					x.put("casePassRateLab", s3.get("lab"));
					x.put("casePassRateValue", s3.get("value"));
				}

				int countDect = 0;
				if(!StringUtils.isEmpty(x.get("resourceID"))){
					Long resourceID=(Long)x.get("resourceID");
					String planName=(String)x.get("planName");
					Result bugsFeignBytestPlanName = feignDataDesignToBugService.findBugsFeignBytestPlanName(planName, resourceID.toString(),closedKeys);

					if(bugsFeignBytestPlanName!=null&&bugsFeignBytestPlanName.getObj()!=null){
						List<Map<String, Object>> defectList =(List<Map<String, Object>>)bugsFeignBytestPlanName.getObj();
						for (Map<String, Object> defct : defectList) {
							Object editTime = defct.get("editTime");
							Object createTime = defct.get("createTime");
							try {
								Long day = null;
								if(editTime==null){
									day = compareTime(createTime);
								}else{
									day = compareTime(editTime);
								}
								Map<String, Object> resultMap = feignDataDesignToReportForm.findGuidelineByName("缺陷超期天数");
								if (resultMap != null) {
									String targetValue = (String)resultMap.get("targetValue");
									String targetsymbol = (String)resultMap.get("targetsymbol");
//									String str = day + targetsymbol + targetValue;
//									Boolean result = Boolean.valueOf(SE.eval(str).toString());
									if (GuidelineCalcUtil.compare(day.toString(), targetValue, targetsymbol)) {
										countDect++;
									}
								}
//                            logger.info("超期缺陷比较结果》》》》》》》》》》》》》》{}",result);

							} catch (ParseException e) {
								e.printStackTrace();
							}
						}
					}
					x.put("countDect",countDect);
				}
			}
		}
		return pageDemand;
	}
	/**
	 *
	 * @Title: initMyDemandToDealWith
	 * @Description: 工作台待我处理需求列表（当前用户加入当前需求就显示）
	 * @param pageRequest  params
	 * @return
	 * <AUTHOR>
	 * @date 2020年6月15日
	 */
	@Override
	public Page<Map<String, Object>> initMyDemandToDealWith(PageRequest pageRequest, Map<String, String> params) {
		// 查询当前用户下的需求
				String userNumber = params.get("userNumber");
				logger.info("datadesign查询需求开始" + params.toString());
				Page<Map<String, Object>> pageDemand = demandDao.initMyDemandToDealWithByNumber(pageRequest, userNumber,params);
				logger.info("datadesign查询需求结束" + pageDemand.getContent().toString());

				if (pageDemand.getNumberOfElements() != 0) {
					List<String> closekeys = testProjectService.findEndStateBytestProjectResourceID(params.get("testProjectResourceID"));

					pageDemand.getContent().stream().forEach(x -> {
						if(!StringUtils.isEmpty(x.get("finishTime"))&&!StringUtils.isEmpty(x.get("startTime"))){
							Object finishTime = x.get("finishTime");
							Long day = null;
							try {
								day = this.compareTimeTwo(finishTime);
							} catch (ParseException e) {
								e.printStackTrace();
							}
							if(day>0){
								x.put("overStatus",true);
								x.put("overday",day);
							}else{
								x.put("overStatus",false);
								x.put("overday",0);
							}
							x.put("starToend",x.get("startTime")+"/"+x.get("finishTime"));
						}else{
							x.put("starToend",null);
							x.put("overStatus",false);
							x.put("overday",0);
						}
						if(!StringUtils.isEmpty(x.get("guidelineinfo"))) {
							@SuppressWarnings("unchecked")
							HashMap<String, JSONObject> map = JSONObject.parseObject(x.get("guidelineinfo").toString(), HashMap.class);
							JSONObject s = map.get("缺陷命中率");
							x.put("effectiveDefectRateLab", s.get("lab"));
							x.put("effectiveDefectRateValue", s.get("value"));
							JSONObject s1 = map.get("案例执行进度偏差");
							x.put("caseExecutionDeviationLab", s1.get("lab"));
							x.put("caseExecutionDeviationValue", s1.get("value"));
							JSONObject s2 = map.get("缺陷修复率");
							x.put("defectRepairRateLab", s2.get("lab"));
							x.put("defectRepairRateValue", s2.get("value"));
							JSONObject s3 = map.get("案例执行通过率");
							x.put("casePassRateLab", s3.get("lab"));
							x.put("casePassRateValue", s3.get("value"));
						}
						int countDect = 0;
						if(!StringUtils.isEmpty(x.get("resourceID"))){
							Long resourceID=(Long)x.get("resourceID");
							String planName=(String)x.get("planName");
							Result bugsFeignBytestPlanName = feignDataDesignToBugService.findBugsFeignBytestPlanName(planName, resourceID.toString(),closekeys);
//							logger.info("缺陷结果返回"+bugsFeignBytestPlanName.getObj());
							if(bugsFeignBytestPlanName!=null&&bugsFeignBytestPlanName.getObj()!=null){
//								logger.info("进入统计超期缺陷"+bugsFeignBytestPlanName.getObj());
								List<Map<String, Object>> defectList =(List<Map<String, Object>>)bugsFeignBytestPlanName.getObj();
								for (Map<String, Object> defct : defectList) {
									Object editTime = defct.get("editTime");
									Object createTime = defct.get("createTime");
									try {
										Long day = null;
										if(editTime==null){
											day = compareTime(createTime);
										}else{
											day = compareTime(editTime);
										}
//										logger.info("超期缺陷数量"+day+"天");
										Map<String, Object> resultMap = feignDataDesignToReportForm.findGuidelineByName("缺陷超期天数");
										String targetValue = (String)resultMap.get("targetValue");
										String targetsymbol = (String)resultMap.get("targetsymbol");
										String str = day + targetsymbol + targetValue;
//										Boolean result = Boolean.valueOf(SE.eval(str).toString());
//										logger.info("超期缺陷比较结果》》》》》》》》》》》》》》{}",result);
										if (GuidelineCalcUtil.compare(day.toString(), targetValue, targetsymbol)) {
											countDect++;
										}
									} catch (ParseException e) {
										e.printStackTrace();
									}
								}
							}
							x.put("countDect",countDect);
						}
					});
				}
				return pageDemand;
	}

	/**
	 * 求时间差
	 * @param editTime
	 */
	private Long compareTime(Object editTime) throws ParseException {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Long edit = (Long)editTime;
		Date date = new Date(edit);
		String time = df.format(date);
		Date d1 = df.parse(time);
		Date date2 = new Date(System.currentTimeMillis());
		String Datetime = df.format(date2);
		Date  d2= df.parse(Datetime);
		long diff = d2.getTime() - d1.getTime();
		long days = diff / (1000 * 60 * 60 * 24);
		return days;

	}
	/**
	 * 求时间差
	 * @param editTime
	 */
	private Long compareTimeTwo(Object editTime) throws ParseException {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = (Date)editTime;
		String time = df.format(date);
		Date d1 = df.parse(time);
		Date date2 = new Date(System.currentTimeMillis());
		String Datetime = df.format(date2);
		Date  d2= df.parse(Datetime);
		long diff = d2.getTime() - d1.getTime();
		long days = diff / (1000 * 60 * 60 * 24);
		return days;

	}

	/**
	 * @Title removeUserFromDemand
	 * @Description 从需求中批量移除人员
	 * @param demandResIDs
	 * @param user
	 * <AUTHOR>
	 *
	 */
	@Override
	public String removeUserFromDemand(String demandResIDs, UserVo user) {
		String[] demandResIDStr = demandResIDs.split(",");
		List<String> demandResIDList = new ArrayList<String>();
		for (String resID : demandResIDStr) {
			demandResIDList.add(resID);
		}
		String token = HttpRequestUtils.getCurrentRequestToken();
		JettechUserDTO userDTO = feignDataDesignToBasicService.findByNumber(user.getUserNumber(), token);
		if (demandResIDList.isEmpty() || userDTO == null || userDTO.getResourceID() == null) {
			return "操作失败";
		}

		List<DemandUser> findDel = iDemandUserService.findUserResIDAnddemandResIDList(userDTO.getResourceID().toString(),demandResIDList);
		for (DemandUser demandUser : findDel) {
			Map<String, String> params = new HashMap<>();
			params.put("managerResourceID",demandUser.getUserResourceID().toString());
			params.put("demandResourceID",demandUser.getDemandResourceID().toString());
			Result result = feignDataDesignToManexecuteService.findTestTaskByDemandIdAndManagerResourceID(params);
			if(result.getCode()==20000){
				return "当前用户已分配任务不允许移除";
			}
		}

		List<DemandUser> demandUsers = iDemandUserService.findUserResIDAndDemandResIDListAndDemandType(
				String.valueOf(userDTO.getResourceID()), demandResIDList, "6", "已上线");
		List<DemandUser> listForDel = new ArrayList<>();
		if (demandUsers != null && !demandUsers.isEmpty()) {
			for (DemandUser demandUser : demandUsers) {
				if (!"需求项目经理".equals(demandUser.getRole())) {
					listForDel.add(demandUser);
				}
			}
			iDemandUserService.deleteInBatch(listForDel, user.getUserNumber());
		}

		Result result = feignDataDesignToBasicService.deleteByUserNumberAndTargetResourceID(demandResIDs,
				user.getUserNumber(), token);
		if (result.getCode() != 2000) {
			logger.error("删除消息通知失败!");
		}
		return "SUCCESS";
	}

	/**
	 * @Description 查询未上线的需求,缺陷管理新增缺陷使用
	 * <AUTHOR>
	 * @date 2019-12-12 20:07
	 * @param
	 * @return com.jettech.dto.Result<?>
	 */
	@Override
	public Result<?> queryNotOnLineDemand() {
		List<Demand> demandList = demandDao.queryNotOnLineDemand();
		return Result.renderSuccess(demandList);
	}

	@Override
	public List<String> importExcel(MultipartFile file, UserVo userVo) {
		String fileType = "";
		List<String> Strings = new ArrayList<>();
		String string = "";
		String token = HttpRequestUtils.getCurrentRequestToken();
		Map<String, String> Type = feignDataDesignToBasicService.findByTextNameAndName("DemandState", token);
		Map<String, String> demandLevel = feignDataDesignToBasicService.findByTextNameAndName("DemandLevel", token);
		/*
		 * Map<String, String> findAllPerson = iFeignDataDictionary.findAllPersons();
		 * Map<String, String> findTypeAllPerson =
		 * iFeignDataDictionary.findTypeAllPersons();
		 */
		Map<String, String> CentralizedDepartment = feignDataDesignToBasicService
				.findByTextNameAndName("CentralizedBusinessDepartment", token);
		Map<String, String> HostTribe = feignDataDesignToBasicService.findByTextNameAndName("所属部落", token);
		Map<String, String> HostTeam = feignDataDesignToBasicService.findByTextNameAndName("HostTeam", token);
		InputStream is=null;
		try {
			String fileName = file.getOriginalFilename();
			fileType = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.lastIndexOf(".") + 4);
			if (!fileType.toLowerCase().equals("xls") && !fileType.toLowerCase().equals("xlsx")) {
				Strings.add("导入的文件格式不正确，应该不是excel文件");
				return Strings;
			}
			is=file.getInputStream();
			List<Map<String, String>> readexcel = readexcel(is);
				if (readexcel == null) {
				Strings.add("模板标题不允许修改,请重新下载模板");
				return Strings;
			}
			if (readexcel.size() == 1) {
				for (Map<String, String> map : readexcel) {
					if (!map.containsKey("需求编号重复")) {
						break;
					}
					String s = String.valueOf(map.get("需求编号重复") == null ? "" : String.valueOf(map.get("需求编号重复")));
					s = s.substring(1, s.length() - 1);
					if (s.length() > 0) {
						String pin = "";
						String[] split = s.split(",");
						for (String zi : split) {

							String[] split1 = zi.split("&");
							if (split1.length == 1) {
								pin += ("第" + split1[0] + "行需求编号不能为空,");
								continue;
							}
							pin += ("第" + split1[0] + "行需求编号重复:" + split1[1] + ",");
						}
						if (pin != "")
							Strings.add(pin);
						return Strings;
					}

				}

			}
			// 非空校验移到外面
			int count = 1;
			String pin = "";
			for (Map<String, String> map : readexcel) {

				if (map.isEmpty()) {
					count++;
					continue;
				}
				if (map.get("0") == null || map.get("0") == "") {
					pin += ("第:" + count + "行,需求编号不能为空,");
				}
				count++;
			}
			if (pin != "") {
				Strings.add(pin);
				return Strings;
			}
			List<Demand> demandList = new ArrayList<>();
			List<DemandUser> demandUserList = new ArrayList<>();

			int sum = 2;
			for (Map<String, String> map : readexcel) {
				if (map.isEmpty()) {
					continue;
				}
//					2019-12-18 11:11:53
				DataDictionaryDTO dataDictionary = null;
				Date date = null;
				Demand demand = null;
				JettechUserDTO jettechUserDTO = null;
				DemandUser demandUser = null;
				DemandUser du = null;
				try {
					dataDictionary = new DataDictionaryDTO();
					jettechUserDTO = new JettechUserDTO();
					demandUser = new DemandUser();
					demand = new Demand();
					demand.setNumber(String.valueOf(map.get("0")) == null ? "" : String.valueOf(map.get("0")));
					List<Demand> findByNumber = demandDao.findByNumber(String.valueOf(map.get("0")));
					demand.setName(String.valueOf(map.get("1")) == null ? "" : String.valueOf(map.get("1")));
					demand.setType(
							Type.get(String.valueOf(map.get("2")) == null ? "" : String.valueOf(map.get("2"))) == null
									? ""
									: Type.get(
									String.valueOf(map.get("2")) == null ? "" : String.valueOf(map.get("2"))));
					demand.setTypename(String.valueOf(map.get("2")) == null ? "" : String.valueOf(map.get("2")));
					demand.setLevel(demandLevel
							.get(String.valueOf(map.get("3")) == null ? "" : String.valueOf(map.get("3"))) == null ? ""
							: demandLevel.get(
							String.valueOf(map.get("3")) == null ? "" : String.valueOf(map.get("3"))));
					demand.setResourceID(generateResourceID(demand));
					// 需求提出人user
					JettechUserDTO user = feignDataDesignToBasicService.findUserByUserName(String.valueOf(map.get("4")),
							token);
					// 需求项目经理user1
					JettechUserDTO user1 = feignDataDesignToBasicService
							.findUserByUserName(String.valueOf(map.get("5")), token);
					if (user != null) {
						demand.setProposerResourceID(user.getResourceID());
						demandUser.setUserResourceID(user.getResourceID());
						demandUser.setDemandResourceID(demand.getResourceID());
						demandUser.setRole("需求人员");
						if (user1 != null) {
							du = new DemandUser();
							demand.setProjectManagerResourceID(user1.getResourceID());
							du.setUserResourceID(user.getResourceID());
							du.setDemandResourceID(demand.getResourceID());
							du.setRole("需求项目经理");
						}
					}
//						if(user!=null){
//						demandUser.setUserResourceID(user.getResourceID());
//						demandUser.setDemandResourceID(generateResourceID(demand));
//						demand.setProjectManagerResourceID(user1.getResourceID());}
//						if(user1!=null){
//						demand.setProposerResourceID(user.getResourceID());}
					demand.setCentralizedDepartment(CentralizedDepartment
							.get(String.valueOf(map.get("6")) == null ? "" : String.valueOf(map.get("6"))) == null ? ""
							: CentralizedDepartment.get(
							String.valueOf(map.get("6")) == null ? "" : String.valueOf(map.get("6"))));
					//if (String.valueOf(map.get("8")) != "") {
						//DataDictionaryDTO dictionary = feignDataDesignToBasicService
							//	.findByNameAndTextNameFromDataDesign("DemandState", String.valueOf(map.get("8")),
								//		token);
						//demand.setHostTeam(dictionary.getValue());
					//}
					//if (String.valueOf(map.get("7")) != "") {
					//	DataDictionaryDTO dictionary1 = feignDataDesignToBasicService
					//			.findByInfoNamesFromDataDesign(String.valueOf(map.get("7")), token);
						//demand.setHostTribe(dictionary1.getName());
					//}

					String valueOf = String.valueOf(map.get("9"));
					if (valueOf != "") {
						Date time = new Date(valueOf);
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						String timeFormat = sdf.format(time);
						Date parse = sdf.parse(timeFormat);
						long time1 = parse.getTime();
						Date date1 = new Date(time1);
						/*
						 * SimpleDateFormat simpleDateFormat = new
						 * SimpleDateFormat("EEE MMM dd HH:mm:ss Z yyyy", Locale.UK);
						 * date=simpleDateFormat.parse(valueOf); DateFormat formatTo = new
						 * SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); String format =
						 * formatTo.format(date); Date parse1 = formatTo.parse(format);
						 */
						demand.setDemandCreateTime(date1);
					}
					if (demand.getNumber() == "" || demand.getNumber() == null)
						string += "第" + sum + "行的需求编号为必输项,";
					if (demand.getName() == "" || demand.getName() == null)
						string += "第" + sum + "行的需求名称为必输项,";
					if (demand.getTypename() == "" || demand.getTypename() == null)
						string += "第" + sum + "行的需求状态为必输项,";
					if (user == null || "".equals(user))
						string += "第" + sum + "行需求提出人为必输项,";
					if (findByNumber.size() != 0)
						string += "第" + sum + "行的需求编号重复,";
					if (demand.getCentralizedDepartment() == "" || demand.getCentralizedDepartment() == null)
						string += "第" + sum + "行的业务归口部门为必输项,";
					if (demand.getDemandCreateTime() == null || "".equals(demand.getDemandCreateTime())
							|| valueOf.length() > 28)
						string += "第" + sum + "行的创建日期为必输项或者日期格式错误,";
					demandList.add(demand);
					demandUserList.add(demandUser);
					if (du != null) {
						demandUserList.add(du);
					}
				} catch (Exception e) {
					string += "模板格式有误请重新下载模板!";
				}
				sum++;
			}
			if (string != "") {
				Strings.add(string);
			}
//		        判断是否有错误信息
			if (Strings.size() != 0) {
				return Strings;
			}
//				没有错误信息就进行保存方法
			if (Strings.size() == 0) {
				batchesSaveDemandByLimit(demandList, userVo.getUserNumber());
				batchesSaveDemandUserByLimit(demandUserList, userVo.getUserNumber());
			}
//		        this.save(demand, userVo.getUserNumber());
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			try {
				if(is!=null){
					is.close();
				}
			}catch (Exception e){

			}
		}
		return Strings;
	}

	/**
	 * 需求导入批量保存需求
	 *
	 * @Title: batchesSaveDemandByLimit
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param demandList
	 * @param @param userNumber 参数
	 * @return void 返回类型
	 * @throws <AUTHOR>
	 */
	private void batchesSaveDemandByLimit(List<Demand> demandList, String userNumber) {
		int dataLimit = 1000;
		String token = HttpRequestUtils.getCurrentRequestToken();
		if (!demandList.isEmpty()) {
			int size = demandList.size();
			if (dataLimit < size) {
				int part = size % dataLimit == 0 ? size / dataLimit : size / dataLimit + 1;
				int tmpData = 0;
				for (int i = 0; i < part; i++) {
					// 最后一页可能不足1000条，特殊处理，防止超出边界
					List<Demand> list;
					if (i == part - 1) {
						list = demandList.subList(tmpData, demandList.size());
					} else {
						list = demandList.subList(tmpData, tmpData + 1000);
						tmpData += 1000;
					}
					List<Demand> demands = this.save(list, userNumber);
//					List<Long> demandRids = new ArrayList<>();
//					for (Demand de : demands) {
//						demandRids.add(de.getResourceID());
//					}
//					feignDataDesignToManexecuteService.saveDefaultFourPlan(demandRids, token);
				}
			} else {
				if (!demandList.isEmpty()) {
					List<Demand> demands = this.save(demandList, userNumber);
//					List<Long> demandRids = new ArrayList<>();
//					for (Demand de : demands) {
//						demandRids.add(de.getResourceID());
//					}
//					feignDataDesignToManexecuteService.saveDefaultFourPlan(demandRids, token);
				}
			}
		}

	}

	/**
	 * 需求导入批量保存需求人员关系
	 *
	 * @Title: batchesSaveDemandUserByLimit
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param demandUserList
	 * @param @param userNumber 参数
	 * @return void 返回类型
	 * @throws <AUTHOR>
	 */
	private void batchesSaveDemandUserByLimit(List<DemandUser> demandUserList, String userNumber) {
		int dataLimit = 1000;
		if (!demandUserList.isEmpty()) {
			int size = demandUserList.size();
			if (dataLimit < size) {
				int part = size % dataLimit == 0 ? size / dataLimit : size / dataLimit + 1;
				int tmpData = 0;
				for (int i = 0; i < part; i++) {
					// 最后一页可能不足1000条，特殊处理，防止超出边界
					List<DemandUser> list;
					if (i == part - 1) {
						list = demandUserList.subList(tmpData, demandUserList.size());
					} else {
						list = demandUserList.subList(tmpData, tmpData + 1000);
						tmpData += 1000;
					}
					iDemandUserService.save(list, userNumber);
				}

			} else {
				if (!demandUserList.isEmpty()) {
					iDemandUserService.save(demandUserList, userNumber);
				}
			}
		}

	}

	public List<Map<String, String>> readexcel(InputStream iStream) throws Exception {
		// .xlsx
		HSSFWorkbook workbook = new HSSFWorkbook(iStream);
		// Cycle each page and process the current page
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		List<String> aList = new ArrayList<>();
		List<String> oldtitle = new ArrayList<>();
		List<String> anaa = new ArrayList<>();
		oldtitle.add("*需求编号");
		oldtitle.add("*需求名称");
		oldtitle.add("*需求状态");
		oldtitle.add("需求级别");
		oldtitle.add("*需求提出人");
		oldtitle.add("需求项目经理");
		oldtitle.add("*业务归口部门");
		oldtitle.add("主办部落");
		oldtitle.add("主办小队");
		oldtitle.add("*创建日期（年/月/日 或 年-月-日）");
		List<String> title = new ArrayList<>();
		int count = 0;
		HSSFSheet sheet = null;
		/* for (int i = 1; i < workbook.getNumberOfSheets(); i++) { */
		sheet = workbook.getSheetAt(1);
		for (int l = 0; l <= sheet.getLastRowNum(); l++) {
			Map<String, String> map = new HashMap<String, String>();
			HSSFRow row = sheet.getRow(l);
			if (row != null) {
				for (int m = 0; m < 10; m++) {
					HSSFCell cell = row.getCell(m);

					if (l == 0) {
						row.getCell(m).setCellType(CellType.STRING);
						aList.add(
								row.getCell(m).getStringCellValue() == null ? "" : row.getCell(m).getStringCellValue());
						continue;
					}

					if (m == 0) {
						if (row.getCell(m) != null) {
							row.getCell(m).setCellType(CellType.STRING);
							anaa.add(row.getCell(m).getStringCellValue() == null ? ""
									: row.getCell(m).getStringCellValue());
						} else {
							anaa.add("");
						}
					}

					if (cell == null) {
						map.put(String.valueOf(m), "");

					} else {
						if (m == 9) {
							if (row.getCell(m).getCellType() == CellType.NUMERIC) {
								row.getCell(m).setCellType(CellType.NUMERIC);
								if (cell.getDateCellValue() != null) {
									if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
										Date dateCellValue = cell.getDateCellValue();
										if (!"Sun Dec 31 00:00:00 CST 1899".equals(dateCellValue.toString())) {
											map.put(String.valueOf(9), dateCellValue.toString());
											continue;
										} else {
											map.put(String.valueOf(m), "");
											continue;
										}
									}
								}
							} else {
								map.put(String.valueOf(m), "");
								continue;
							}
						}
						row.getCell(m).setCellType(CellType.STRING);
						map.put(String.valueOf(m),
								row.getCell(m).getStringCellValue() == null ? "" : row.getCell(m).getStringCellValue());
					}

				}
			} else {
				for (int m = 0; m < 10; m++) {
					map.put(String.valueOf(m), "");
				}
			}

			list.add(map);
		}
		// 过滤尾部空行
		for (int i = list.size() - 1; i > 0; i--) {
			Map<String, String> map = list.get(i);
			if (!isNull(map)) {
				list.remove(i);
			} else {
				break;
			}
		}
//				jdk1.8新特性判断excel是否重复

		List<Map<String, String>> repetitions = repetitions(anaa);
		for (Map<String, String> map : repetitions) {
			String s = String.valueOf(map.get("需求编号重复") == null ? "" : String.valueOf(map.get("需求编号重复")));
			if (s.length() > 0) {
				try {
					return repetitions;
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
//				更改头部信息导致失败
		if (!getDiffrent(aList, oldtitle)) {
			return null;
		}
		return list;

	}

	/**
	 * md5加密法使用 方法5 md5 能判断内容，能判断顺序
	 */
	private static boolean getDiffrent(List<String> list, List<String> list1) {
		/** 使用Security的md5方法进行加密 */
		String changeStringToMD5 = MD5Utils.changeStringToMD5(list.toString());
		String changeStringToMD52 = MD5Utils.changeStringToMD5(list1.toString());
		return changeStringToMD5.equals(changeStringToMD52);
	}

	/**
	 * 判断案例编号是否重复
	 */
	private static List<Map<String, String>> repetitions(List<String> aList) {
		List<Map<String, String>> repetitionList = new ArrayList<Map<String, String>>();
		List<String> arrayList = new ArrayList<>();
		arrayList.add("");
		List<String> arrayList1 = new ArrayList<>();
		String repetition = "";
		int count = 2;
		for (String string : aList) {
			if (!string.equals("")) {
				if (!arrayList.contains(string)) {
					arrayList.add(string);
				} else {
					arrayList1.add(count + "&" + string);
				}
			}

			count++;
		}
		Map<String, String> hashMap = new HashMap<>();
		if (arrayList1.size() == 0) {
			hashMap.put("需求编号重复", "");
		} else {
			hashMap.put("需求编号重复", arrayList1.toString());
		}
		repetitionList.add(hashMap);
		return repetitionList;
	}

	/**
	 * @Title: createFives
	 * @description: 5000条缺陷
	 * @param "[userResourceID]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2019/12/25 11:51
	 */
	@Override
	public Result createFives(String userResourceID) {
		List<DemandUser> demandUserList = demandUserService.findByUserResourceID(userResourceID);
		if (demandUserList == null || demandUserList.isEmpty())
			return Result.renderError("用户未关联需求!");

		Demand demand = this.findByResourceID(demandUserList.get(0).getDemandResourceID());

		List<DemandTestSystem> tsList = demandTestSystemService
				.findBydemandResourceID(demandUserList.get(0).getDemandResourceID());
		if (tsList == null || tsList.isEmpty())
			return Result.renderError("需求为关联被测系统!");

		Result<?> result = testSystemService.queryNextLowerLevelMap(tsList.get(0).getTestSystemResourceID());
		if (result.getCode() != 20000)
			return Result.renderError("没有所属模块!");

		TestSystem ts = testSystemService.findByResourceID(tsList.get(0).getTestSystemResourceID());

		List<HashMap<String, Object>> listMap = (List<HashMap<String, Object>>) result.getObj();
		String name = listMap.get(0).get("name").toString();
		String resourceID = listMap.get(0).get("resourceID").toString();

		HashMap<String, Object> map = new HashMap<>();
		map.put("demandResourceID", demandUserList.get(0).getDemandResourceID());
		map.put("testSystemResourceID", ts.getResourceID());
		map.put("moduleResourceID", resourceID);
		map.put("moduleName", name);
		map.put("systemName", ts.getName());
		map.put("demandName", demand.getName());

		return Result.renderSuccess(map);
	}

	private long generateResourceID(Demand demand) {
		SnowflakeIdWorker worker = SnowflakeIdWorker.getInstance();
		return worker.genNextId();
	}

	/**
	 *
	 * @Title:isNull
	 * @Description:判断map是否有数据
	 * @param
	 * @return boolean
	 * @author: wu_yancheng
	 * @date 2020年1月16日上午11:32:26
	 */
	private boolean isNull(Map<String, String> map) {
		boolean flag = false;
		for (String value : map.values()) {
			if (value != null && (!value.equals(""))) {
				flag = true;
			}
		}
		return flag;
	}

	/**
	 * @Title findDemandRelevanceUser
	 * @Description 根据需求主键查询已关联的人员
	 * @param demandResourceID
	 * @return List<Map<String,Object>>
	 * <AUTHOR>
	 * @data Jan 8, 20203:50:33 PM
	 */
	@Override
	public List<Map<String, Object>> findDemandRelevanceUser(String demandResourceID) {
		return demandUserService.findUserByDemandResourceID(String.valueOf(demandResourceID));
	}

	/**
	 *
	 * @Title: findNotRelevanceSystem
	 * @Description: 查询未关联系统
	 * @param name  \resourceID
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getNotRelevanceSystem(String name,String resourceID) {
		return demandTestSystemService.getNotRelevanceSystem(name, resourceID);
	}

	/**
	 *
	 * @Title: removeDemandSystem
	 * @Description: 移除需求关联系统
	 * @param demandResourceID  ids userNumber
	 * @return
	 */
	@Override
	public String removeDemandSystem(String demandResourceID, String ids, String userNumber) {
		if (demandResourceID == null || "".equals(demandResourceID)) {
			return "需求异常，不能移除系统！";
		}
		if (ids == null || "".equals(ids)) {
			return "请选择系统!";
		}

		Demand demand = find(Integer.parseInt(demandResourceID));
		if (demand == null) {
			return "需求异常，不能移除系统！";
		}

		String[] resourceIDArr = ids.split(",");
		List<String> delSysTemRids = new ArrayList<>();
		List<DemandTestSystem> deleteList = new ArrayList<>();
		if (resourceIDArr != null) {
			for (String resourceID : resourceIDArr) {
				delSysTemRids.add(resourceID);
				DemandTestSystem demandTestSystem = demandTestSystemService
						.findByTestSystemResourceIDAndDemandResourceID(resourceID,
								String.valueOf(demand.getResourceID()));
				if (demandTestSystem != null) {
					deleteList.add(demandTestSystem);
				}
			}
		}
		// 需求变更，当移除需求下的系统时，将改系统下的行方开发经理从当前需求下移除（如果当前需求下的其他系统有该人员，则不移除该行方开发经理）
		Set<Long> delUserSet = new HashSet<>();
		Set<String> numberSet = new HashSet<>();
		List<TestSystem> systems = testSystemService.findByResourceIDIn(delSysTemRids);
		for (TestSystem testSystem : systems) {
			if (!StringUtils.isEmpty(testSystem.getProjectManager())) {
				String[] Unmber = testSystem.getProjectManager().split(",");
				for (String number : Unmber) {
					numberSet.add(number);
				}
			}
		}
		String token = HttpRequestUtils.getCurrentRequestToken();
		List<JettechUserDTO> userDtos = feignDataDesignToBasicService.findByNumberIn(new ArrayList<>(numberSet), token);
		for (JettechUserDTO jettechUserDTO : userDtos) {
			delUserSet.add(jettechUserDTO.getResourceID());
		}
		// 查找需求下面的所有关联的系统
		List<DemandTestSystem> demanfSystems = demandTestSystemService.findBydemandResourceID(demand.getResourceID());
		// 没有被选中删除的系统
		List<String> otherSysTemRids = new ArrayList<>();
		demanfSystems.stream().forEach(x -> {
			if (!delSysTemRids.contains(x.getTestSystemResourceID().toString())) {
				otherSysTemRids.add(x.getTestSystemResourceID().toString());
			}
		});
		// 这个系统下行方开发经理和需求下行方开发经理相同的人员会被从需求的行方开发经理下移除
		// 需求人员表查询跟这个需求有关联的所有人员
		List<Map<String, Object>> dus = demandUserService.findUserByDemandResourceID(demand.getResourceID().toString());
		// key:人员rid；value：关联关系rid---当前需求下所有行方开发经理map
		Map<Long, Long> hasUserRelation = new HashMap();
		for (Map<String, Object> map : dus) {
			if ("行方开发经理".equals(map.get("role"))) {
				hasUserRelation.put(Long.valueOf(String.valueOf(map.get("resourceID"))),
						Long.valueOf(String.valueOf(map.get("duResourceID"))));
			}
		}
		if (otherSysTemRids.isEmpty() && otherSysTemRids.size() == 0) {// 所有的系统都已被选中移除
			if (!hasUserRelation.isEmpty() && hasUserRelation.size() > 0) {// 有一样的就移除，没有不处理
				List<String> delRea = new ArrayList<>();
				for (Map.Entry<Long, Long> entry : hasUserRelation.entrySet()) {
					if (delUserSet.contains(entry.getKey())) {
						delRea.add(entry.getValue().toString());
					}
					;
				}
				if (!delRea.isEmpty() && delRea.size() > 0) {
					demandUserService.deleteInBatch(demandUserService.findByResourceIDIn(delRea), userNumber);
				}

			}

		} else {// 剩余一部分系统没有被选中移除
			Set<Long> otherUserSet = new HashSet<>();
			Set<String> otherNumberSet = new HashSet<>();
			List<TestSystem> otherSys = testSystemService.findByResourceIDIn(otherSysTemRids);
			for (TestSystem testSystem : otherSys) {
				if (!StringUtils.isEmpty(testSystem.getProjectManager())) {
					String[] Unmber = testSystem.getProjectManager().split(",");
					for (String number : Unmber) {
						otherNumberSet.add(number);
					}
				}
			}
			List<JettechUserDTO> otherUserDtos = feignDataDesignToBasicService
					.findByNumberIn(new ArrayList<>(otherNumberSet), token);
			for (JettechUserDTO jettechUserDTO : otherUserDtos) {
				otherUserSet.add(jettechUserDTO.getResourceID());
			}
			// 找出删除系统中人员和剩余关联系统中人员非交集部分
			List<Long> lastUser = new ArrayList<>();
			for (Long delUser : delUserSet) {
				if (!otherUserSet.contains(delUser)) {
					lastUser.add(delUser);
				}
			}
			// 非交集部分和需求下的行方开发经理对比
			if (!hasUserRelation.isEmpty() && !lastUser.isEmpty() && lastUser.size() > 0) {// 有一样的就移除，没有不处理
				List<String> delRea = new ArrayList<>();
				for (Map.Entry<Long, Long> entry : hasUserRelation.entrySet()) {
					if (lastUser.contains(entry.getKey())) {
						delRea.add(entry.getValue().toString());
					}
					;
				}
				if (!delRea.isEmpty() && delRea.size() > 0) {
					demandUserService.deleteInBatch(demandUserService.findByResourceIDIn(delRea), userNumber);
				}

			}

		}
		if (!deleteList.isEmpty()) {
			demandTestSystemService.deleteInBatch(deleteList, userNumber);
			HashMap<String, Object> param = new HashMap<>();
			param.put("demandId", demand.getResourceID());
			param.put("testSystem",
					deleteList.stream().map(DemandTestSystem::getTestSystemResourceID).collect(Collectors.toList()));
			param.put("type", "delete");
			Result result = feignDataDesignToManexecuteService.synchronousModificationTestPlanRelationSystem(param);
			if (!result.isSuccess()) {
				throw new RuntimeException("调用testPlan同步方法失败!");
			}
		}

		return "操作成功";
	}

	@Override
	public void updateByResourceID(Demand demand, String userNumber) {
		this.update(demand, userNumber);
		//demandDao.updateByResourceID(demand, userNumber);
	}

	/**
	 * 更新带空值
	 */
	@Override
	public void updateDemand(Demand demand) {

		demandDao.updateDemand(demand);
	}

	/**
	 * 查找所有用户
	 *
	 * @return cao_jinbao
	 */
	@Override
	public Result<?> findAllUsersForDemand(String demandResourceID) {

		if (demandResourceID == null || "".equals(demandResourceID)) {
			return Result.renderError("需求resourceID为空！");
		}
		Demand demand = findByResourceID(Long.valueOf(demandResourceID));
		if (demand == null) {
			return Result.renderError("需求异常！");
		}
		List<Map<String, Object>> list = new ArrayList<>();
		if (demand.getTestProjectResourceID() != null) {
			list = demandDao.findAllUsersForDemand(demand.getTestProjectResourceID());
		}
		return Result.renderSuccess(list);
	}

	/**
	 * @Title: testPlanRotationPie
	 * @description: 报表需求中测试计划轮次占比图(sit,uat)
	 * @param "[demandResourceID, testEnviroment]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2020/2/10 17:28
	 */
	@Override
	public Result testPlanRotationPie(String demandResourceID, String testEnviroment) {
		return null;
	}

	@Override
	public Result testVenture(String demandResourceID) {
		List<Map<String, Object>> maps = demandDao.testVenture(demandResourceID);
		if (maps == null && maps.size() == 0) {
			return null;
		}
		List<Map> sitList = new ArrayList<>();
		List<Map> uatList = new ArrayList<>();
		for (Map<String, Object> map : maps) {
			String planName = (String) map.get("planName");
			String testStage = (String) map.get("testStage");
			String testRound = (String) map.get("testRound");
			String venture = (String) map.get("venture");
			if (venture != null && venture.trim().length() > 0) {
				if (testStage.equals("SIT")) {
					sitList.add(map);
				}
				if (testStage.equals("UAT")) {
					uatList.add(map);
				}
			}
		}
		Map map = new HashMap();
		map.put("sitList", sitList);
		map.put("uatList", uatList);
		return Result.renderSuccess(map);
	}

	@Override
	public List<Map<String, String>> findDemandIDs(String ids) {
		List<String> list = null;
		if (!StringUtils.isEmpty(ids)) {
			String[] split = ids.split(",");
			list = Arrays.asList(split);
		}
		return demandDao.findDemandIDs(list);
	}

	/**
	 *
	 * @Method importTrade
	 * @Descripation 导入交易Excel
	 * @param file
	 * <AUTHOR>
	 * @return
	 */
	@Override
	public Result importDemandExcel(MultipartFile file, UserVo userVo) {
		Result result = new Result<>();
		String errorMsg = "";
		if (file == null) {
			return Result.renderError("请上传需求excel！");
		}
		Workbook wb = ImportExcelUtils.getExcelInfo(file);
		if (wb == null) {
			return Result.renderError("该文件不是excel格式！");
		}
		// 开始读取文件内容
		// 获取sheet页数量
		int numberOfSheets = wb.getNumberOfSheets();
		// 需求
		List<Demand> demandForSave = new ArrayList<>();
		// 需求人员
		List<DemandUser> demandUserForSave = new ArrayList<>();
		Map<String, String> demandNumberInDB = new HashMap<>();// 用于存数据库里面已经存在的的需求编号
		Map<String, String> mapSheetRepeatDemandNumber = new HashMap<>();// 用于存表格里面已经存在的的需求编号
		Map<String, String> mapError = this.createErrorList();// 用于存储各种错误信息
		// 目前仅支持单sheet页导入
		int count = 0;
		numberOfSheets=CheckUtil.checkLoop(numberOfSheets);
		for (int i = 0; i < numberOfSheets; i++) {
			// 第i页
			Sheet sheeti = wb.getSheetAt(i);
			String sheetName = sheeti.getSheetName();
			if (sheetName.indexOf("hidden")==0) {
				continue;
			}
			count++;
			if (count > 1) {
				continue;
//				errorMsg = errorMsg + "当前只支持单sheet页需求导入，请删除多余sheet页；";
//				return Result.renderError(errorMsg);
			}
			// 校验标题
			Row rowTitle = sheeti.getRow(0);
			result = ImportExcelUtils.valitaileExcelTitle(rowTitle);
			if (!result.isSuccess()) {
				errorMsg = errorMsg + "模板标题有误，请使用正确的模板！；";
				return Result.renderError(errorMsg);
			}
			String token = HttpRequestUtils.getCurrentRequestToken();
			// 需求状态
			Map<String, String> Type = feignDataDesignToBasicService.findByTextNameAndName("DemandState", token);
			// 需求级别
			Map<String, String> demandLevel = feignDataDesignToBasicService.findByTextNameAndName("DemandLevel", token);
			// 业务归口部门
			Map<String, String> CentralizedDepartment = feignDataDesignToBasicService
					.findByTextNameAndName("CentralizedBusinessDepartment", token);
			/*
			 * //主办部落name Map<String, String> HostTribe = new HashMap<String,String>();
			 * //主办部落和下面的主办小队 Map<String, Map<String,String>> HostTribeAndTeam = new
			 * HashMap<String, Map<String,String>>(); List<DataDictionaryDTO> dataDtos =
			 * feignDataDesignToBasicService.findTribeAndTeamByName("所属部落"); for
			 * (DataDictionaryDTO data : dataDtos) {
			 * if(!HostTribe.containsKey(data.getInfoName())) {
			 * HostTribe.put(data.getInfoName(), data.getName()); Map<String,String> mapTeam
			 * = new HashMap<String, String>(); mapTeam.put(data.getTextName(),
			 * data.getValue()); HostTribeAndTeam.put(data.getInfoName(), mapTeam); }else {
			 * HostTribeAndTeam.get(data.getInfoName()).put(data.getTextName(),
			 * data.getValue()); } }
			 */

			// 所有人员key:name,value :resourceID
			Map<String, Long> userMap = new HashMap<String, Long>();
			List<JettechUserDTO> users = feignDataDesignToBasicService.findAllPerson(token);
			users.stream().forEach(u -> {
				userMap.put(u.getUserName(), u.getResourceID());
			});
			// 查询所有需求
			List<Demand> allDemand = this.findAll();
			if (!allDemand.isEmpty() && allDemand.size() > 0) {
				allDemand.stream().forEach(x -> {
					demandNumberInDB.put(x.getNumber(), x.getNumber());
				});
			}

			// 有 数据时,获取所有行数
			int totalNumbers = sheeti.getPhysicalNumberOfRows();
			totalNumbers=CheckUtil.checkLoop(totalNumbers);
			for (int j = 1; j < totalNumbers; j++) {// 剔除标题行
				// 遍历每一行
				Row rowj = sheeti.getRow(j);
				if (rowj == null) {
					// 跳过空行
					totalNumbers++;
					continue;
				}
				Demand demand = new Demand();
				long demangTrsourceID = this.generateResourceID(demand);
				demand.setResourceID(demangTrsourceID);
				DemandUser demandUserProposer = new DemandUser();
				DemandUser demandUserManager = null;
				// 需求编号
				if (StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(0)))) {// *需求编号是否为空
					mapError.put("numberBlank", mapError.get("numberBlank") + (j + 1) + ",");
				} else {// 不空
					if (mapSheetRepeatDemandNumber.containsKey(ImportExcelUtils.getValue(rowj.getCell(0)))) {// 判断当前sheet表格是否存在demandNumber重复
						mapError.put("numberRepeatInSheet", mapError.get("numberRepeatInSheet") + (j + 1) + ",");
					} else {
						if (demandNumberInDB.get(String.valueOf(rowj.getCell(0))) != null) {// 当前数据库里相同的交易编码
							mapError.put("numberRepeatInDB", mapError.get("numberRepeatInDB") + (j + 1) + ",");
						} else {
							String number = ImportExcelUtils.getValue(rowj.getCell(0));
							// 校验？
							demand.setNumber(number);
						}
					}
					mapSheetRepeatDemandNumber.put(demand.getNumber(), demand.getNumber());
				}
				// 需求名称
				if (StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(1)))) {// 需求名称是否为空
					mapError.put("nameBlank", mapError.get("nameBlank") + (j + 1) + ",");
				} else {
					String name = ImportExcelUtils.getValue(rowj.getCell(1));
					demand.setName(name);
				}
				// 需求状态
				if (StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(2)))) {// 需求状态是否为空
					mapError.put("demandStatusBlank", mapError.get("demandStatusBlank") + (j + 1) + ",");
				} else {
					String demandStatus = ImportExcelUtils.getValue(rowj.getCell(2));
					if (Type.containsKey(demandStatus)) {
						demand.setTypename(demandStatus);
						demand.setType(Type.get(demandStatus));
					} else {
						// 不存在这个状态
						mapError.put("demandStatusError", mapError.get("demandStatusError") + (j + 1) + ",");
					}
				}
				// 需求级别
				if (!StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(3)))) {// 需求级别
					String dLevel = ImportExcelUtils.getValue(rowj.getCell(3));
					if (demandLevel.containsKey(dLevel)) {
						demand.setLevel(demandLevel.get(dLevel));
					} else {
						mapError.put("demandLevelError", mapError.get("demandLevelError") + (j + 1) + ",");
					}
				}else {
					mapError.put("demandLevelError", mapError.get("demandLevelError") + (j + 1) + ",");
				}
				// 需求提出人
				if (StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(4)))) {// 需求提出人是否为空
					mapError.put("demandProposerBlank", mapError.get("demandProposerBlank") + (j + 1) + ",");
				} else {
					String demandProposer = ImportExcelUtils.getValue(rowj.getCell(4));
					if (userMap.containsKey(demandProposer)) {
						demand.setProposerResourceID(userMap.get(demandProposer));
						demandUserProposer.setUserResourceID(userMap.get(demandProposer));
						demandUserProposer.setDemandResourceID(demand.getResourceID());
						demandUserProposer.setRole("需求人员");
					} else {
						// 不存在这个提出人
						mapError.put("noDemandProposer", mapError.get("noDemandProposer") + (j + 1) + ",");
					}
				}
				// 需求项目经理
				if (!StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(5)))) {// 需求项目经理是否为空
					String demandManager = ImportExcelUtils.getValue(rowj.getCell(5));
					if (userMap.containsKey(demandManager)) {
						demand.setProjectManagerResourceID(userMap.get(demandManager));
						demandUserManager = new DemandUser();
						demandUserManager.setUserResourceID(userMap.get(demandManager));
						demandUserManager.setDemandResourceID(demand.getResourceID());
						demandUserManager.setRole("需求项目经理");
					} else {
						// 不存在这个提出人
						mapError.put("noDemandManager", mapError.get("noDemandManager") + (j + 1) + ",");
					}
				}
				// *业务归口部门
				if (StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(6)))) {// 业务归口部门是否为空
					mapError.put("businessDepartmentBlank", mapError.get("businessDepartmentBlank") + (j + 1) + ",");
				} else {
					String businessDepartment = ImportExcelUtils.getValue(rowj.getCell(6));
					if (CentralizedDepartment.containsKey(businessDepartment)) {
						demand.setCentralizedDepartment(CentralizedDepartment.get(businessDepartment));
					} else {
						// 不存在这个业务归口部门
						mapError.put("noBusinessDepartment", mapError.get("noBusinessDepartment") + (j + 1) + ",");
					}
				}
				// 所属项目
				if (!StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(7)))) {
					String tpName = ImportExcelUtils.getValue(rowj.getCell(7));
					List<TestProject> testProjects = testProjectService.findByName(tpName);
					if (testProjects.isEmpty()) {
						//判断不存在
						mapError.put("noTestProject", (j + 1) + ",");
					} else {
						demand.setTestProjectResourceID(testProjects.get(0).getResourceID());
						demand.setTestProjectName(testProjects.get(0).getName());
					}
				} else {
					//判断为空
					mapError.put("testProjectBlank", (j + 1) + ",");
				}
				/*
				 * //主办部落HostTribe
				 * if(!StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(7))))
				 * {//主办部落是否为空 String demandHostTribe =
				 * ImportExcelUtils.getValue(rowj.getCell(7));
				 * if(HostTribe.containsKey(demandHostTribe)) {
				 * demand.setHostTribe(HostTribe.get(demandHostTribe));
				 * if(!StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(8))))
				 * {//主办小队是否为空 String demandHostTream =
				 * ImportExcelUtils.getValue(rowj.getCell(8));
				 * if(HostTribeAndTeam.get(demandHostTribe).containsKey(demandHostTream)) {
				 * demand.setHostTeam(HostTribeAndTeam.get(demandHostTribe).get(demandHostTream)
				 * ); }else { mapError.put("noDemandHostTeam", mapError.get("noDemandHostTeam")
				 * +(j+1)+","); } } }else { //不存在这个主办部落 mapError.put("noDemandHostTribe",
				 * mapError.get("noDemandHostTribe") +(j+1)+","); } }else {//主办部落为空
				 * if(!StringUtils.isEmpty(ImportExcelUtils.getValue(rowj.getCell(8))))
				 * {//主办小队是否为空 //先维护主办部落 mapError.put("defineHostTribeFirst",
				 * mapError.get("defineHostTribeFirst") +(j+1)+","); }
				 *
				 * }
				 */
				// 需求创建日期
				/*if (rowj.getCell(8) != null) {

					try {
						String dateStr = String.valueOf(rowj.getCell(8));
						if (this.isValidDate(dateStr)) {
							Date date = new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
							demand.setDemandCreateTime(date);
						} else if (ReadExcel.checkDateFormat(rowj.getCell(8))) {
							Date dateCellValue = rowj.getCell(8).getDateCellValue();
							demand.setDemandCreateTime(dateCellValue);
						} else {
							mapError.put("errorDateFormat", mapError.get("errorDateFormat") + (j + 1) + ",");
						}

					} catch (Exception e) {
						e.printStackTrace();
						mapError.put("errorDateFormat", mapError.get("errorDateFormat") + (j + 1) + ",");
					}
				}*/
				// 需求
				demandForSave.add(demand);
				// 需求人员
				demandUserForSave.add(demandUserProposer);
				if (demandUserManager != null) {
					demandUserForSave.add(demandUserManager);
				}
			}

		}
		// 处理错误信息
		errorMsg = this.dealErrorLog(mapError);
		// 校验是否通过
		if (!StringUtils.isEmpty(errorMsg)) {
			return Result.renderError(errorMsg);
		}
		// 批量保存需求
		if (!demandForSave.isEmpty() && demandForSave.size() > 0) {
			this.batchesSaveDemandByLimit(demandForSave, userVo.getUserNumber());
		}
		// 批量保存需求人员
		if (!demandForSave.isEmpty() && demandForSave.size() > 0) {
			List<DemandUser> demandUserList = new ArrayList<>();
			 Map<Long, List<DemandUser>>  demandUserMap= demandUserForSave.stream()
             .collect(Collectors.groupingBy(s -> s.getDemandResourceID()));
			 for(Long uu :demandUserMap.keySet()) {
				 List<DemandUser> list = demandUserMap.get(uu);
				 Set<String> demandUserSet = new HashSet<>();
				 for(DemandUser du : list) {
					 String userResourceIdStr = String.valueOf(du.getUserResourceID());
						if(!demandUserSet.contains(userResourceIdStr))
						{
							demandUserSet.add(userResourceIdStr);
							demandUserList.add(du);
						}
				 }
			 }
			demandUserForSave = demandUserList;
			this.batchesSaveDemandUserByLimit(demandUserForSave, userVo.getUserNumber());
		}
		return result.renderSuccess("导入成功！");
	}

	/**
	 * 判断时间格式 格式必须为“YYYY-MM-dd” 2004-2-30 是无效的 2003-2-29 是无效的
	 *
	 * @param str
	 * @return
	 */
	private boolean isValidDate(String str) {
		// String str = "2007-01-02";
		DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		try {
			Date date = (Date) formatter.parse(str);
			return str.equals(formatter.format(date));
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 处理错误信息
	 *
	 * @param mapError
	 * @return
	 */
	private String dealErrorLog(Map<String, String> mapError) {
		String ErrorLog = "";
		if (!StringUtils.isEmpty(mapError.get("numberBlank"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("numberBlank").substring(0, mapError.get("numberBlank").length() - 1) + "】行需求编号为空；";
			;
		}
		if (!StringUtils.isEmpty(mapError.get("numberRepeatInSheet"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("numberRepeatInSheet").substring(0, mapError.get("numberRepeatInSheet").length() - 1)
					+ "】行需求编号在当前sheet页重复；";
		}
		if (!StringUtils.isEmpty(mapError.get("numberRepeatInDB"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("numberRepeatInDB").substring(0, mapError.get("numberRepeatInDB").length() - 1)
					+ "】行需求编号在当前数据库重复；";
		}
		if (!StringUtils.isEmpty(mapError.get("nameBlank"))) {
			ErrorLog = ErrorLog + "第【" + mapError.get("nameBlank").substring(0, mapError.get("nameBlank").length() - 1)
					+ "】行需求名称为空；";
		}
		if (!StringUtils.isEmpty(mapError.get("demandStatusBlank"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("demandStatusBlank").substring(0, mapError.get("demandStatusBlank").length() - 1)
					+ "】行需求状态为空；";
		}
		if (!StringUtils.isEmpty(mapError.get("demandStatusError"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("demandStatusError").substring(0, mapError.get("demandStatusError").length() - 1)
					+ "】行需求状态有误；";
		}
		if (!StringUtils.isEmpty(mapError.get("demandLevelError"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("demandLevelError").substring(0, mapError.get("demandLevelError").length() - 1)
					+ "】行需求级别有误；";
		}
		if (!StringUtils.isEmpty(mapError.get("demandProposerBlank"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("demandProposerBlank").substring(0, mapError.get("demandProposerBlank").length() - 1)
					+ "】行需求提出人为空；";
		}
		if (!StringUtils.isEmpty(mapError.get("noDemandProposer"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("noDemandProposer").substring(0, mapError.get("noDemandProposer").length() - 1)
					+ "】行需求提出人有误或不存在；";
		}
		if (!StringUtils.isEmpty(mapError.get("noDemandManager"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("noDemandManager").substring(0, mapError.get("noDemandManager").length() - 1)
					+ "】行需求项目经理有误或不存在；";
		}
		if (!StringUtils.isEmpty(mapError.get("businessDepartmentBlank"))) {
			ErrorLog = ErrorLog + "第【" + mapError.get("businessDepartmentBlank").substring(0,
					mapError.get("businessDepartmentBlank").length() - 1) + "】行业务归口部门为空；";
		}
		if (!StringUtils.isEmpty(mapError.get("noBusinessDepartment"))) {
			ErrorLog = ErrorLog + "第【" + mapError.get("noBusinessDepartment").substring(0,
					mapError.get("noBusinessDepartment").length() - 1) + "】行业务归口部门有误或不存在；";
		}
		if (!StringUtils.isEmpty(mapError.get("noDemandHostTeam"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("noDemandHostTeam").substring(0, mapError.get("noDemandHostTeam").length() - 1)
					+ "】行主办小队不存在或当前小队不属于当前主办部落；";
		}
		if (!StringUtils.isEmpty(mapError.get("noDemandHostTribe"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("noDemandHostTribe").substring(0, mapError.get("noDemandHostTribe").length() - 1)
					+ "】行主办部落不存在；";
		}
		if (!StringUtils.isEmpty(mapError.get("defineHostTribeFirst"))) {
			ErrorLog = ErrorLog + "第【" + mapError.get("defineHostTribeFirst").substring(0,
					mapError.get("defineHostTribeFirst").length() - 1) + "】行请先维护该小队主办部落或删除该小队；";
		}
		if (!StringUtils.isEmpty(mapError.get("errorDateFormat"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("errorDateFormat").substring(0, mapError.get("errorDateFormat").length() - 1)
					+ "】行日期有误，请填写正确日期；";
		}
		if (!StringUtils.isEmpty(mapError.get("noTestProject"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("noTestProject").substring(0, mapError.get("noTestProject").length() - 1)
					+ "】行所属项目不存在；";
		}
		if (!StringUtils.isEmpty(mapError.get("testProjectBlank"))) {
			ErrorLog = ErrorLog + "第【"
					+ mapError.get("testProjectBlank").substring(0, mapError.get("testProjectBlank").length() - 1)
					+ "】行所属项目为空，请填写正确所属项目；";
		}
		return ErrorLog;
	}

	/**
	 * 创建导入时存储各种错误信息的list
	 *
	 * @return
	 */
	private Map<String, String> createErrorList() {
		Map<String, String> map = new HashMap<>();
		// 需求编号为控
		map.put("numberBlank", "");
		// 需求编号在当前sheet页重复
		map.put("numberRepeatInSheet", "");
		// 需求编号在数据库里重复
		map.put("numberRepeatInDB", "");
		// 需求名称为空
		map.put("nameBlank", "");
		// 需求状态为空
		map.put("demandStatusBlank", "");
		// 需求状态有误
		map.put("demandStatusError", "");
		// 需求级别有误
		map.put("demandLevelError", "");
		// 需求提出人为空
		map.put("demandProposerBlank", "");
		// 需求提出人有误
		map.put("noDemandProposer", "");
		// 需求项目经理有误
		map.put("noDemandManager", "");
		// 业务归口部门为空
		map.put("businessDepartmentBlank", "");
		// 业务归口部门有误
		map.put("noBusinessDepartment", "");
		// 主办小队有误
		map.put("noDemandHostTeam", "");
		// 主办部落有误
		map.put("noDemandHostTribe", "");
		// 请先定义主办部落或删除当前小队
		map.put("defineHostTribeFirst", "");
		// 日期格式有误
		map.put("errorDateFormat", "");
		return map;
	}

	/**
	 * 更新需求的状态（需求状态：未开始、准备中、测试中、已完成、特殊状态（部分上线、已上线、已暂停、已取消））
	 *
	 * @Title: changeDemandType
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param demandResourceID 参数
	 * @return void 返回类型
	 * @throws <AUTHOR>
	 */
	@Override
	@GlobalTransactional(name = "changeDemandType",rollbackFor = Exception.class)
	public String changeDemandType(Long demandResourceID, String userNumber) {

		if (StringUtils.isEmpty(demandResourceID)) {
			return "需求的ResourceID为空！";
		}
		// 获取当前需求
		Demand demandNow = this.findByResourceID(demandResourceID);
		if (StringUtils.isEmpty(demandNow)) {
			return "当前需求不存在或已被删除！！";
		}
		// 部分上线、已上线、已暂停和已取消。变为这些状态后，需求的状态将不再自动变更
		if (!"已上线".equals(demandNow.getTypename()) && !"部分上线".equals(demandNow.getTypename())
				&& !"已暂停".equals(demandNow.getTypename()) && !"已取消".equals(demandNow.getTypename())) {
			String token = HttpRequestUtils.getCurrentRequestToken();
			// 查看当前需求下是否还存在案例
			List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demandResourceID);
			// 查看当前需求下是否还存在缺陷
			List<Map<String, Object>> bugList = feignDataDesignToBugService.findBugsByDemandResourceID(demandResourceID,
					token);
			if ((!testCaseList.isEmpty() && testCaseList.size() > 0) || (!bugList.isEmpty() && bugList.size() > 0)) {
				// 需求下案例不空，缺陷是空
				if ((!testCaseList.isEmpty() && testCaseList.size() > 0)
						&& (bugList.isEmpty() || bugList.size() == 0)) {
					List<Long> ceseRids = new ArrayList<Long>();
					testCaseList.stream().forEach(x -> ceseRids.add(x.getResourceID()));
					// 继续看引用表
					List<Map<String, Object>> caeQuoteList = feignDataDesignToManexecuteService
							.findCaseQuoteByTestCaseResourceIDs(ceseRids, token);
					// 案例引用表有数据
					if (!caeQuoteList.isEmpty() && caeQuoteList.size() > 0) {
						// 非成功取消状态的案例引用
						long countQuote = caeQuoteList.stream()
								.filter(x -> !"成功".equals(String.valueOf(x.get("caseResult")))
										&& !"取消".equals(String.valueOf(x.get("caseResult"))))
								.count();
						if (countQuote > 0) {// 有其他状态
							if (!"测试中".equals(demandNow.getTypename())) {
								demandNow.setTypename("测试中");
								demandNow.setType("3");
								this.update(demandNow, userNumber);
							}
						} else {// 只有成功、取消状态
							// 继续校验需求下的所有测试计划
							boolean isPass = this.checkTestPlan(demandResourceID);
							if (isPass) {
								if (!"已完成".equals(demandNow.getTypename())) {
									demandNow.setTypename("已完成");
									demandNow.setType("4");
									this.update(demandNow, userNumber);
									//生成质量报告数据
									Result result=feignDataDesignToReportForm.createQualityReports(demandResourceID,userNumber);
									if(result==null) {
										return "创建质量报告失败！";
									}
									if(!result.isSuccess()) {
										return result.getMsg();
									}
								}
							} else {
								if (!"测试中".equals(demandNow.getTypename())) {
									demandNow.setTypename("测试中");
									demandNow.setType("3");
									this.update(demandNow, userNumber);
								}
							}
						}

					} else {// 引用表没数据，说明只有案例
						if (!"准备中".equals(demandNow.getTypename())) {
							demandNow.setTypename("准备中");
							demandNow.setType("2");
							this.update(demandNow, userNumber);
						}
					}
				} else if ((testCaseList.isEmpty() && testCaseList.size() == 0)
						&& (!bugList.isEmpty() || bugList.size() > 0)) {// 需求下案例空，缺陷不空;
					this.validateBugAndTestPlan(bugList, demandNow, userNumber);
				} else {// 案例和缺陷都不空
					List<Long> ceseRids = new ArrayList<Long>();
					testCaseList.stream().forEach(x -> ceseRids.add(x.getResourceID()));
					// 继续看是否有引用
					List<Map<String, Object>> caeQuoteList = feignDataDesignToManexecuteService
							.findCaseQuoteByTestCaseResourceIDs(ceseRids, token);
					// 案例引用表有数据
					if (!caeQuoteList.isEmpty() && caeQuoteList.size() > 0) {
						// 非成功取消状态的案例引用
						long countQuote = caeQuoteList.stream()
								.filter(x -> !"成功".equals(String.valueOf(x.get("caseResult")))
										&& !"取消".equals(String.valueOf(x.get("caseResult"))))
								.count();
						if (countQuote > 0) {// 有其他状态
							if (!"测试中".equals(demandNow.getTypename())) {
								demandNow.setTypename("测试中");
								demandNow.setType("3");
								this.update(demandNow, userNumber);
							}
						} else {// 只有成功、取消状态
							this.validateBugAndTestPlan(bugList, demandNow, userNumber);

						}
					} else {// 引用表没数据，说明只有缺陷
						this.validateBugAndTestPlan(bugList, demandNow, userNumber);
					}

				}

			} else {// 需求下不存在案例和缺陷，将需求的状态置为未开始A、未开始：未添加任何案例和缺陷时;
				if (!"未开始".equals(demandNow.getTypename())) {
					demandNow.setTypename("未开始");
					demandNow.setType("1");
					this.update(demandNow, userNumber);
				}

			}

		}
		return "";
	}

	/**
	 * @Title findDemandsNotOnLine
	 * @Description 查询非已上线的需求
	 * @Params []
	 * @Return java.util.List<Demand>
	 * <AUTHOR>
	 * @Date 2020/3/6
	 */
	@Override
	public List<Demand> findDemandsNotOnLine() {
		return demandDao.findDemandsNotOnLine();
	}

	/**
	 * 校验需求下的测试计划结束时间是否在现在之前
	 *
	 * @Title: checkTestPlan
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  demandResourceID
	 * @param @return 参数
	 * @return boolean 返回类型
	 * @throws <AUTHOR>
	 */
	private boolean checkTestPlan(Long demandResourceID) {
		String token = HttpRequestUtils.getCurrentRequestToken();
		List<Map<String, Object>> testPlansList = feignDataDesignToManexecuteService
				.findTestPlanByResourceID(demandResourceID, token);
		boolean isPass = true;
		for (Map<String, Object> map : testPlansList) {
			if (!StringUtils.isEmpty(map.get("finishTime"))) {
				Long time = Long.valueOf(String.valueOf(map.get("finishTime")));
				if (time > new Date().getTime()) {
					isPass = false;
					break;
				}
			}
		}
		return isPass;
	}

	/**
	 * 改变需求状态时校验方法(私有)
	 *
	 * @Title: validateBugAndTestPlan
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param bugList
	 * @param @param demandNow
	 * @param @param userNumber 参数
	 * @return void 返回类型
	 * @throws <AUTHOR>
	 */
	private void validateBugAndTestPlan(List<Map<String, Object>> bugList, Demand demandNow, String userNumber) {

		//查找多个流程配置闭环状态（目前取多个配置的所有闭环状态，暂时不区分哪个流程）
		List<String> closeStates = demandDao.findBugsClosedStsate(String.valueOf(demandNow.getTestProjectResourceID()));

		//缺陷状态不是闭环状态的个数
		long counBug = bugList.stream().filter(x ->!closeStates.contains(String.valueOf(x.get("defectState")))).count();
//		long counBug = bugList.stream()
//				.filter(x -> !"仲裁不改".equals(String.valueOf(x.get("defectState")))
//						&& !"已通过".equals(String.valueOf(x.get("defectState")))
//						&& !"已关闭".equals(String.valueOf(x.get("defectState"))))
//				.count();
		if (counBug > 0) {// 非闭合状态
			if (!"测试中".equals(demandNow.getTypename())) {
				demandNow.setTypename("测试中");
				demandNow.setType("3");
				this.update(demandNow, userNumber);
			}
		} else {// 全部是闭环状态
			boolean isPass = this.checkTestPlan(demandNow.getResourceID());
			if (isPass) {
				if (!"已完成".equals(demandNow.getTypename())) {
					demandNow.setTypename("已完成");
					demandNow.setType("4");
					this.update(demandNow, userNumber);
				}
			} else {// 测试计划截止日期大于现在
				if (!"测试中".equals(demandNow.getTypename())) {
					demandNow.setTypename("测试中");
					demandNow.setType("3");
					this.update(demandNow, userNumber);
				}
			}
		}
	}

	/**
	 * 需求已上线时，校验案例，缺陷，测试计划的完成情况（需求变更，暂不考虑测试计划）
	 *
	 * @Title: checkOnLineDemandType
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  demandResourceID
	 * @param @return 参数
	 * @return String 返回类型
	 * @throws <AUTHOR>
	 */
	private String checkOnLineDemandType(Long demandResourceID) {
		// 获取当前需求
		Demand demandNow = this.findByResourceID(demandResourceID);
		if (StringUtils.isEmpty(demandNow)) {
			return "当前需求不存在或已被删除！！";
		}
		// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
		List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demandResourceID);
		if(!testCaseList.isEmpty() && testCaseList.size()>0) {
			//校验当前需求下的项目案例是否都都添加到项目案例执行表，
			int count  = iTestCaseService.countAllPerformCaseAddToExecute(demandResourceID);
			if(count > 0) {//有部分项目案例没有添加到项目案例表
				return "当前需求下所编写的项目案例没有全部添加到执行！";
			}
			//校验当前需求下的手工执行案例的数据是否都都添加到手工案例引用表
			int count2  = iTestCaseService.countAllTestCaseAddToExecute(demandResourceID);
			if(count2 > 0) {//有部分项目案例没有添加到项目案例表
				return "当前需求下所编写的手工执行案例没有全部添加到执行！";
			}
			//全部添加到项目案例执行表之后，校验所有案例执行成功或者取消
			//leadsource == 1为项目案例
			List<TestCase> performCases = testCaseList.stream().filter(e ->e.getLeadsource() == 1).collect(Collectors.toList());
			if(!performCases.isEmpty()) {
				List<Long> ceseRids = new ArrayList<Long>();
				performCases.stream().forEach(x -> ceseRids.add(x.getResourceID()));
				//项目案例表里当前案例非成功取消状态的案例
				Integer  countOne =  iTestCaseService.findPerformcaseByCaseResourceID(ceseRids,"flag");
				if(countOne != null) {
					return  "请确认全部项目案例执行【成功】或【取消】状态！";
				}
			}
			List<TestCase> manTestCases = testCaseList.stream().filter(e ->e.getLeadsource() != 1).collect(Collectors.toList());
			//手工执行案例
			if(!manTestCases.isEmpty()) {
				List<Long> ceseRids = new ArrayList<Long>();
				manTestCases.stream().forEach(x -> ceseRids.add(x.getResourceID()));
				//手工执行案例引用表里当前案例非成功取消状态的案例
				Integer  countOne =  iTestCaseService.findTestCaseByCaseResourceID(ceseRids,"flag");
				if(countOne != null) {
					return "请确认全部手工执行案例执行【成功】或【取消】状态！";
				}
			}
		}
		// 查看当前需求下是否还存在缺陷
		List<Map<String, Object>> bugList = this.findBugsByDemandResourceID(demandResourceID);
		if (!bugList.isEmpty() && bugList.size() > 0) {
			//查找当前需求所关联的项目配置流程的闭环状态
			if(StringUtils.isEmpty(demandNow.getTestProjectResourceID())) {
				return "当前所属需求的项目流程有误，不能获取当前流程的闭环状态！";
			}
			String testProjectResourceID = String.valueOf(demandNow.getTestProjectResourceID());
			List<String> closeStates = demandDao.findBugsClosedStsate(testProjectResourceID);

			//缺陷状态不是闭环状态的个数
			long counBug = bugList.stream().filter(x ->!closeStates.contains(String.valueOf(x.get("defectState")))).count();
			if (counBug > 0) {
				return "请确认当前需求下的缺陷全部处于闭环状态！";
			}
		}
		 return "";
	}
	/**
	 *
	* @Title: findBugsByDemandResourceID
	* @Description: 查看当前需求下是否还存在缺陷
	* @param @param demandResourceID
	* @param @return    参数
	* @return List<Map<String,Object>>    返回类型
	* @throws
	* <AUTHOR>
	 */
	private List<Map<String, Object>> findBugsByDemandResourceID(Long demandResourceID) {

		return demandDao.findBugsByDemandResourceID(demandResourceID);
	}

	/**
	 *
	 * @Title: findByTestProjectResourceID
	 * @Description: 根据所属项目查询需求
	 * @param testProjectResourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午10:03:32
	 */
	@Override
	public List<Demand> findByTestProjectResourceID(Long testProjectResourceID) {
		return demandDao.findByTestProjectResourceID(testProjectResourceID);
	}

	/**
	 * 根据项目ResourceIDs查询符合需求
	 * bao_qiuxu
	 *
	 * @param ids
	 */
	@Override
	public List<Demand> findByTestProjectResourceIDs(List<String> ids) {
		return demandDao.findByTestProjectResourceIDs(ids);
	}

	/**
	 * @Title: initTaskScopeLeftTree
	 * @Description: //根据需求，初始化添加任务范围弹框左侧树
	 * @Param: " [demandResourceID] "
	 * @return: " com.jettech.dto.Result "
	 * @throws:
	 * @Author: xpp
	 * @Date: 14:24 2020/6/10
	 */
	@Override
	public Result initTaskScopeLeftTree(String demandResourceID) {
		List<String> resourceIDs = new ArrayList<>();

		if ("0".equals(demandResourceID)) {
			// 查询系统数据权限
			List<TestSystem> testSystemList = testSystemService.findAllByDataAuth();
			resourceIDs = testSystemList.stream().map(s -> s.getResourceID().toString()).collect(Collectors.toList());
		} else {
			// 查询需求下所有的被测系统
			Result r = testSystemService.findTestSystemByDemandResourceID(demandResourceID);
			if (r.isSuccess()) {
				List<DemandTestSystem> demandTestSystems = (List<DemandTestSystem>) r.getObj();
				resourceIDs = demandTestSystems.stream().map(s -> s.getTestSystemResourceID().toString()).collect(Collectors.toList());
			}
		}

		if (resourceIDs.size() == 0) {
			return Result.renderSuccess(null);
		} else {
			List<TestSystem> testSystemList = testSystemService.findByResourceIDIn(resourceIDs);
			// 查询被测系统下所有的模块
			List<Map<String, Object>> moduleMaps = systemModuleService.findByTestSystemResourceIDs(resourceIDs);
			// 直接挂在被测系统下
			if (moduleMaps.size() > 0) {
				// 直接在被测系统下的模块，并且根据系统id分组
				Map<String, List<Map<String, Object>>> collect = moduleMaps.stream()
						.filter(s -> s.get("testSystemResourceID").equals(s.get("parentResourceID")))
						.collect(Collectors.groupingBy(s -> s.get("testSystemResourceID").toString()));
				Map<String, List<Map<String, Object>>> maps1 = moduleMaps.stream()
						.filter(s -> !s.get("testSystemResourceID").equals(s.get("parentResourceID")))
						.collect(Collectors.groupingBy(s -> s.get("parentResourceID").toString()));
				List<Map<String, Object>> leftTree = testSystemList.stream()
						.map(s -> {
							Map<String, Object> map = new HashMap<>();
							map.put("nodeName", s.getName());
							map.put("nodeType", "system");
							map.put("nodeResourceID", s.getResourceID());

							List<Map<String, Object>> maps = collect.get(s.getResourceID().toString());
							if (null != maps && maps.size() > 0) {
								List<Object> children = maps.stream().filter(t -> t.get("testSystemResourceID").equals(s.getResourceID()))
										.map(t -> {
											Map<String, Object> m2 = new HashMap<>();
											m2.put("nodeName", t.get("name"));
											m2.put("nodeType", "module");
											m2.put("nodeResourceID", t.get("resourceID"));
											m2.put("parentResourceID", t.get("parentResourceID"));
											m2.put("testSystemResourceID", t.get("testSystemResourceID"));
											if (null != maps1 && maps1.size() > 0) {
												m2.put("children", getChildren(maps1, t));
											}
											return m2;
										}).collect(Collectors.toList());
								map.put("children", children);
							}
							return map;
						}).collect(Collectors.toList());
				return Result.renderSuccess(leftTree);
			} else {
				List<Map<String, Object>> leftTree = testSystemList.stream()
						.map(s -> {
							Map<String, Object> map = new HashMap<>();
							map.put("nodeName", s.getName());
							map.put("nodeType", "system");
							map.put("nodeResourceID", s.getResourceID());
							return map;
						}).collect(Collectors.toList());
				return Result.renderSuccess(leftTree);
			}
		}
	}

	public List<Map<String,Object>> getChildren(Map<String, List<Map<String, Object>>> modules, Map<String,Object> module){
		List<Map<String, Object>> listMap = new ArrayList<>();
		// 根据父级节点查询子级节点
		// 父级节点下的子节点
		List<Map<String, Object>> children = modules.get(module.get("resourceID").toString());
		if (null != children && children.size() > 0 ) {
			children.forEach(e -> {
				HashMap<String, Object> map = new HashMap<>();
				map.put("nodeName", e.get("name"));
				map.put("nodeType", "module");
				map.put("nodeResourceID", e.get("resourceID"));
				map.put("testSystemResourceID", e.get("testSystemResourceID"));
				map.put("children", getChildren(modules,e));
				listMap.add(map);
			});
		}
		return listMap;
	}

	/**
	  * @Title: findAllDemandToTestTask
	  * @description: 查询所有需求
	  * @param "[name]"
	  * @return com.jettech.dto.Result
	  * @throws
	  * <AUTHOR>
	  * @date 2020/6/23 15:10
	  */
	@Override
	public Result findAllDemandToTestTask(String name) {
		if("null".equals(name) || org.apache.commons.lang3.StringUtils.isBlank(name)){
			name = null;
		}
		 List<Demand> list = demandDao.findAllByName(name);
		return Result.renderSuccess(list);
	}
	/**
	 *
	 * @Title: initMyDemandToDealWith
	 * @Description: 工作台待我处理需求列表（当前用户加入当前需求就显示）的需求个数
	 * <AUTHOR>
	 * @date 2020年6月15日
	 */
	@Override
	public int initMyDemandNumberToDealWith(Map<String, String> params) {
		return demandDao.initMyDemandNumberToDealWith(params);
	}
	/**
	 * @Title: initWorkbenchCreateMyDemandNumber
	 * @Description: 查询我创建的需求数
	 * @date 2020年4月23日 下午4:11:37
	 */
	@Override
	public int initWorkbenchCreateMyDemandNumber(Map<String, String> params) {
		return demandDao.initWorkbenchCreateMyDemandNumber(params);
	}

	/**
	 *
	 * @Title: findByTestProjectResourceIDIn
	 * @Description: 根据所属项目查询需求
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 上午9:58:54
	 */
	@Override
	public List<Demand> findByTestProjectResourceIDIn(List<Long> resourceIDList){
		return demandDao.findByTestProjectResourceIDIn(resourceIDList);
	}

	/***
	 * 弹窗中需求列表
	 * @Method : initDemandTable
	 * @Description : 弹窗中需求列表
	 * @param map : 项目rid
	 * @return : com.jettech.dto.Result
	 * <AUTHOR> Wws.
	 * @CreateDate : 2020-07-15 星期三 10:40:05
	 *
	 */
	@Override
	public Result initDemandTable(HashMap<String, Object> map) {
		Integer rows = map.get("rows") == null || org.apache.commons.lang3.StringUtils.isBlank(map.get("rows").toString()) ? 10 : Integer.valueOf(map.get("rows").toString());
		Integer page = map.get("page") == null || org.apache.commons.lang3.StringUtils.isBlank(map.get("page").toString()) ? 1 : Integer.valueOf(map.get("page").toString());
		String resourceID = map.get("testProjectResourceID").toString();
		PageRequest pageRequest = PageRequest.of(page - 1, rows);
		Page<Demand> list = demandDao.initDemandTable(pageRequest,resourceID);
		return Result.renderSuccess(list);
	}

	/**
	 * 需求状态刷新（只更新---未开始、准备中、测试中、已完成---四种状态的需求）
	 * @param
	 * @return
	 * <AUTHOR> && bao_qiuxu
	 */
	@Override
	public String refreshDemandType(String userNumber,String resourceID) {
		//查询所有需求状态符合的需求
		List<String> typeNames = new ArrayList<>();
		typeNames.add(DemandType.NOTSTART);typeNames.add(DemandType.PREPARATION);typeNames.add(DemandType.TESTING);typeNames.add(DemandType.FINISHED);
		//临时测试一个用,实际使用时，前端不传参数，把下面按类型查询的放开,start-end部分注释
		 //---------------- start
//		Demand demandOne = this.findByResourceID(Long.valueOf(resourceID));
//		List<Demand> demandFind = new ArrayList<>();
//		demandFind.add(demandOne);
		//--------------------end
		List<Demand>  demandFind = this.findDemandByTypeNames(typeNames);
		if(demandFind == null || demandFind.isEmpty()){
			return "刷新成功！";
		}
		//缺陷闭环状态的map，key当前项目，value:闭环状态
		Map<String,List<String>> closeStateMap = new HashMap<>();

		//记录需要更新状态的需求key:状态；value：状态对应的list
		Map<String,List<Demand>> demandForUpdate = new HashMap<>();
		//循环处理可以处理的需求
		for (Demand demand:demandFind) {
			// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
			List<Map<String, Object>> caseList = iTestCaseService.findCaseInfoByDemandResourceID(demand.getResourceID());
			// 查看当前需求下是否还存在缺陷
			List<Map<String, Object>> bugList = this.findBugsByDemandResourceID(demand.getResourceID());
			//没有案例，没有缺陷-未开始状态
			if (caseList.isEmpty() && bugList.isEmpty()) {
				List<Demand> notStart = demandForUpdate.get(DemandType.NOTSTART);
				if (notStart == null) {
					notStart = new ArrayList<>();
					demandForUpdate.put(DemandType.NOTSTART,notStart);
				}
				notStart.add(demand);
				continue;
			}

			boolean hasManExecuteDate = false;
			boolean hasPerformCaseExecuteDate = false;
			if (!caseList.isEmpty()) {
				//案例中的手工执行案例
				List<Map<String, Object>> manTestCases = caseList.stream().filter(e -> !"1".equals(String.valueOf(e.get("leadsource")))).collect(Collectors.toList());
				//手工执行案例
				if (!manTestCases.isEmpty()) {
					List<Long> caseRids = new ArrayList<Long>();
					manTestCases.stream().forEach(x -> caseRids.add(Long.valueOf(x.get("resourceID").toString())));
					//手工执行案例引用表里当前案例非成功取消状态的案例
					Integer countOne = iTestCaseService.findTestCaseByCaseResourceID(caseRids,"flag");
					if (countOne != null) {
						List<Demand> testing = demandForUpdate.get(DemandType.TESTING);
						if (testing == null) {
							testing = new ArrayList<>();
							demandForUpdate.put(DemandType.TESTING,testing);
						}
						testing.add(demand);
						continue;
					}
					//案例执行表是否有数据
					Integer countAnother = iTestCaseService.findTestCaseByCaseResourceID(caseRids,"");
					if(countAnother != null){
						hasManExecuteDate = true;
					}
				}
				//项目案例库中案例
				List<Map<String, Object>> performCases = caseList.stream().filter(e -> "1".equals(String.valueOf(e.get("leadsource")))).collect(Collectors.toList());
				if (!performCases.isEmpty()) {
					List<Long> caseRids = new ArrayList<Long>();
					performCases.stream().forEach(x -> caseRids.add(Long.valueOf(x.get("resourceID").toString())));
					//手工执行案例引用表里当前案例非成功取消状态的案例
					Integer countOne = iTestCaseService.findPerformcaseByCaseResourceID(caseRids,"flag");
					if (countOne != null) {
						List<Demand> testing = demandForUpdate.get(DemandType.TESTING);
						if (testing == null) {
							testing = new ArrayList<>();
							demandForUpdate.put(DemandType.TESTING,testing);
						}
						testing.add(demand);
						continue;
					}
					//案例执行表是否有数据
					Integer countAnother = iTestCaseService.findPerformcaseByCaseResourceID(caseRids,"");
					if(countAnother != null){
						hasPerformCaseExecuteDate = true;
					}
				}
			}
			//测试中校验
			if (!bugList.isEmpty()) {
				String testProjectResourceID = String.valueOf(demand.getTestProjectResourceID());
				List<String> closeStates = null;
				if (closeStateMap.get(testProjectResourceID) == null) {
					//当前缺陷流程下闭环状态
					closeStates = demandDao.findBugsClosedStsate(testProjectResourceID);
					if (closeStates.isEmpty()) {
						return "需求" + demand.getName() + "所属缺陷流程有误！";
					}
					closeStateMap.put(testProjectResourceID, closeStates);
				}
				//缺陷状态不是闭环状态的个数
				long countBug = bugList.stream().filter(x -> !closeStateMap.get(testProjectResourceID).contains(String.valueOf(x.get("defectState")))).count();
				if (countBug > 0) {//有非闭环的缺陷,就是测试中
					List<Demand> testing = demandForUpdate.get(DemandType.TESTING);
					if (testing == null) {
						testing = new ArrayList<>();
						demandForUpdate.put(DemandType.TESTING,testing);
					}
					testing.add(demand);
					continue;
				}
			}
			//已完成(此时，缺陷都是闭环状态，并且执行的案例都是成功或者取消状态),再次校验所有的案例是否都添加到执行库
			//校验当前需求下的项目案例是否都都添加到项目案例执行表，
			int count = iTestCaseService.countAllPerformCaseAddToExecute(demand.getResourceID());
			//校验当前需求下的手工执行案例的数据是否都都添加到手工案例引用表
			int count2 = iTestCaseService.countAllTestCaseAddToExecute(demand.getResourceID());
			//都添加到了执行库
			if (count == 0 && count2 == 0) {
				List<Demand> finished = demandForUpdate.get(DemandType.FINISHED);
				if (finished == null) {
					finished = new ArrayList<>();
					demandForUpdate.put(DemandType.FINISHED,finished);
				}
				finished.add(demand);
				continue;
			}

			//有些没有添加到执行，并且有执行记录的，为测试中
			if(hasPerformCaseExecuteDate || hasManExecuteDate){
				List<Demand> testing = demandForUpdate.get(DemandType.TESTING);
				if (testing == null) {
					testing = new ArrayList<>();
					demandForUpdate.put(DemandType.TESTING,testing);
				}
				testing.add(demand);
				continue;
			}
			//测试中校验
			if (!bugList.isEmpty()) {
				// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
				List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demand.getResourceID());
				List<Long> ceseRids = new ArrayList<Long>();
				testCaseList.stream().forEach(x -> ceseRids.add(x.getResourceID()));
				Integer countOne=0;
				Integer countOne2 = 0;
				if(ceseRids!=null  && ceseRids.size()>0){
					//手工执行案例引用表引用案例个数
					countOne = iTestCaseService.findTestCaseByCaseResourceID(ceseRids, "");
					//项目案例库案例引用案例个数
					countOne2 = iTestCaseService.findPerformcaseByCaseResourceID(ceseRids,"");
				}
				if((countOne!=null && countOne>0) || (countOne2!=null && countOne2>0) || (bugList!=null && !bugList.isEmpty())){
					List<Demand> testing = demandForUpdate.get(DemandType.TESTING);
					if (testing == null) {
						testing = new ArrayList<>();
						demandForUpdate.put(DemandType.TESTING,testing);
					}
					testing.add(demand);
					continue;
				}
			}

			//以上都是不是的话，就是准备中
			List<Demand> preparation = demandForUpdate.get(DemandType.PREPARATION);
			if (preparation == null) {
				preparation = new ArrayList<>();
				demandForUpdate.put(DemandType.PREPARATION,preparation);
			}
			preparation.add(demand);
		}
		if(!demandForUpdate.isEmpty()){
			String result = this.updateDemandForRefresh(demandForUpdate,userNumber);
			if(!StringUtils.isEmpty(result)){
				return result;
			}
		}
		return "刷新成功！";
	}

	/**
	 * 根据需求的状态名称查询需求
	 * @return List<Demand>
	 * <AUTHOR>
	 */
	private List<Demand> findDemandByTypeNames(List<String> typeNames){

		return demandDao.findDemandByTypeNames(typeNames);
	}

	/**
	 * 刷新需求状态方法更新需求操作
	 */
	private String updateDemandForRefresh(Map<String,List<Demand>> demandForUpdate,String userNumber){

		Map<String,String> demandTypeDic = new HashMap<>();
		Map<Object, Object> demandTypeDicMap = redisUtils.getHashEntries("需求状态");
		Set<Object> keys = demandTypeDicMap.keySet();
		List<Long> finishedDemand = new ArrayList<>();

		keys.stream().forEach(e ->{
			demandTypeDic.put(String.valueOf(demandTypeDicMap.get(e)),String.valueOf(e));
		});
		if(!demandTypeDic.containsKey(DemandType.NOTSTART) || !demandTypeDic.containsKey(DemandType.PREPARATION)
				|| !demandTypeDic.containsKey(DemandType.TESTING) ||!demandTypeDic.containsKey(DemandType.FINISHED)  ){
			return "数据字典需求状态有误，找不到要相应的字典值！";
		}

		List<Demand> demandUpdate  = new ArrayList<>();
		for (Map.Entry<String, List<Demand>> entry : demandForUpdate.entrySet()) {
			switch (entry.getKey()){
				//未开始
				case DemandType.NOTSTART:
					List<Demand> values0 = entry.getValue();
					if(!values0.isEmpty()){
						for (Demand demand : values0){
							if(!DemandType.NOTSTART.equals(demand.getTypename())){
								demand.setTypename(DemandType.NOTSTART);
								demand.setType(demandTypeDic.get(DemandType.NOTSTART));
								demandUpdate.add(demand);
							}
						}
					}
					break;
				//准备中
				case DemandType.PREPARATION:
					List<Demand> values1 = entry.getValue();
					if(!values1.isEmpty()){
						for (Demand demand : values1){
							if(!DemandType.PREPARATION.equals(demand.getTypename())){
								demand.setTypename(DemandType.PREPARATION);
								demand.setType(demandTypeDic.get(DemandType.PREPARATION));
								demandUpdate.add(demand);
							}
						}
					}
					break;
				//测试中
				case DemandType.TESTING:
					List<Demand> values2 = entry.getValue();
					if(!values2.isEmpty()){
						for (Demand demand : values2){
							if(!DemandType.TESTING.equals(demand.getTypename())){
								demand.setTypename(DemandType.TESTING);
								demand.setType(demandTypeDic.get(DemandType.TESTING));
								demandUpdate.add(demand);
							}
						}
					}
					break;
				//已完成
				case DemandType.FINISHED:
					List<Demand> values3 = entry.getValue();
					if(!values3.isEmpty()){
						for (Demand demand : values3){
							if(!DemandType.FINISHED.equals(demand.getTypename())){
								demand.setTypename(DemandType.FINISHED);
								demand.setType(demandTypeDic.get(DemandType.FINISHED));
								demandUpdate.add(demand);
								finishedDemand.add(demand.getResourceID());
							}
						}
					}
					break;
				default: break;
			}

		}
		if(!demandUpdate.isEmpty()){
			this.update(demandUpdate,userNumber);
		}
        //标记完成的需求需创建质量报告
        for (Long aLong : finishedDemand) {
            feignDataDesignToReportForm.createQualityReports(aLong, userNumber);
        }
		return "";
	}

	/**
	 *
	 * @Title: uploadDemandAccessory
	 * @Description: 上传附件
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public Result<?> uploadDemandAccessory(Map<String, Object> param, MultipartFile[] files) throws IOException {
		String defectResourceID=(String)param.get("defectResourceID");
		String userNumber=(String)param.get("userNumber");
		upload(files,Long.valueOf(defectResourceID),userNumber);
		return Result.renderSuccess();
	}

	/**
	 *
	 * @Title: deleteDemandAccessory
	 * @Description: 删除附件
	 * @param resourceIDs
	 * @return
	 * <AUTHOR>
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Result<?> deleteDemandAccessory(String resourceIDs, String userNumber) {

		if(resourceIDs==null || "".equals(resourceIDs)) {
			return Result.renderError("附件的resourceIDs为空！");
		}
		if(useFileService){
		    feignDataDesignToFileService.deleteFile(resourceIDs);
        }else{
            String[] rids = resourceIDs.split(",");
            List<DemandAccessory> defectFiles = demandAccessoryService.findByResourceIDIn(Arrays.asList(resourceIDs.split(",")));
            Map<String,DemandAccessory> map = new HashMap<>();
            defectFiles.stream().forEach(x ->{
                map.put(String.valueOf(x.getResourceID()),x);
            });
            if(paramConfig.getIsFtpOn()) {//判断ftp是否打开
                for (String resourceID : rids) {
                    Long defectResourceID=map.get(resourceID).getDefectResourceID();
                    ftpUtil.removeFile(paramConfig.getFtpPath() +map.get(resourceID).getPath()+ File.separator , resourceID+map.get(resourceID).getExtname());
                }

            }else {
                for (String resourceID : rids) {
                    File dir = new File(paramConfig.getAttachmentPath());
                    if(dir.isDirectory()) {
                        File[] fiels=dir.listFiles();
                        for (File file : fiels) {
                            String name=file.getName();
                            if(name.equals(resourceID+map.get(resourceID).getExtname())) {
                                file.delete();
                            }
                        }
                    }
                }
            }
            if(!defectFiles.isEmpty()) {
                demandAccessoryService.deleteInBatch(defectFiles, userNumber);
            }
        }

		return Result.renderSuccess("操作成功！");
	}

    /**
     * 根据需求的编号、名称和项目id查询需求
     *
     * @param condition      编号或名称模糊查询
     * @param projectIDs     项目ID
     * @param projectTypeStr
     * @param demandTypes
     * @return 需求列表
     */
    @Override
    public List<DemandItemDto> findDemandItemByCondition(String condition, List<Long> projectIDs, String projectTypeStr, String demandTypes) {
        if (projectIDs == null || projectIDs.size() == 0 && !StringUtils.isEmpty(projectTypeStr)) {
            List<TestProject> projects = testProjectService.findTestProjectByCondition(null, null, null, projectTypeStr,null);
            if (projects == null || projects.size() == 0) {
                return new ArrayList<>();
            } else {
                projectIDs = projects.stream().map(BaseModelHB::getResourceID).collect(Collectors.toList());
            }
        }
        List<String> demandTypeList = null;
        if (!StringUtils.isEmpty(demandTypes)) {
            demandTypeList = Arrays.asList(demandTypes.split(","));

        }
        return demandDao.findDemandItemByCondition(condition, projectIDs, demandTypeList);
    }

	/**
	 * 查找需求管理下未关联的所有用户
	 * @param name
	 * @param testSystemResourceID
	 * @param demandResourceID
	 * @param userGroupReourceID
	 * @param pageRequest
	 * @return
	 * by zhangsheng
	 */
	@Override
	public Result<?> findDemandNotRelevanceUser(String name, String testSystemResourceID, String demandResourceID, String userGroupReourceID,String deptResourceID, PageRequest pageRequest) {
		if (demandResourceID == null || "".equals(demandResourceID)) {
			return Result.renderError("需求resourceID为空！");
		}
		Demand demand = findByResourceID(Long.valueOf(demandResourceID));
		if (demand == null) {
			return Result.renderError("需求异常！");
		}
		List<String> userGroupReourceIDs = null;
		if (org.apache.commons.lang3.StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		List<String> deptResourceIDList = null;
		if (!org.apache.commons.lang3.StringUtils.isEmpty(deptResourceID)) {
			deptResourceIDList = Arrays.asList(deptResourceID.split(","));
		}
		Page<Map<String, Object>>  list = demandDao.findDemandNotRelevanceUser(name,testSystemResourceID,demand,userGroupReourceIDs,deptResourceIDList,pageRequest);
		return Result.renderSuccess(list);
	}

	/**
	 * 查找需求管理下已关联的所有用户
	 * @param name
	 * @param testSystemResourceID
	 * @param demandResourceID
	 * @param userGroupReourceID
	 * @param pageRequest
	 * @return
	 * by zhangsheng
	 */
	@Override
	public Result<?> findDemandRelevanceUser(String name, String testSystemResourceID, String demandResourceID, String userGroupReourceID,String deptResourceID, PageRequest pageRequest) {
		if (demandResourceID == null || "".equals(demandResourceID)) {
			return Result.renderError("需求resourceID为空！");
		}
		Demand demand = findByResourceID(Long.valueOf(demandResourceID));
		if (demand == null) {
			return Result.renderError("需求异常！");
		}
		List<String> userGroupReourceIDs = null;
		if (org.apache.commons.lang3.StringUtils.isNotBlank(userGroupReourceID)) {
			userGroupReourceIDs = Arrays.asList(userGroupReourceID .split(",")).stream().map(s -> (s.trim())).collect(Collectors.toList());
		}
		List<String> deptResourceIDList = null;
		if (!org.apache.commons.lang3.StringUtils.isEmpty(deptResourceID)) {
			deptResourceIDList = Arrays.asList(deptResourceID.split(","));
		}
		Page<Map<String, Object>>  list = demandDao.findDemandRelevanceUser(name,testSystemResourceID,demand,userGroupReourceIDs,deptResourceIDList,pageRequest);
		return Result.renderSuccess(list);
	}

	/**
	 * @Method: findByDemandNames
	 * @Description: 通过需求名称查询
	 * @Param: " [demandNames] "
	 * @return: java.util.List<com.jettech.model.Demand>
	 * @Author: wws
	 * @Date: 2020/11/9
	 */
	@Override
	public List<Demand> findByDemandNames(ArrayList<String> demandNames) {
		return demandDao.findByDemandNames(demandNames);
	}

	/**
	 * @Method: demandProjectDataDeal
	 * @Description:  通过excel中的数据更新需求和项目的关联关系，更新缺陷数据
	 * @Param: " [] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/11/11
	 */
	@Override
	public Result demandProjectDataDeal() throws IOException {
		//查询所有用户
		List<JettechUserDTO> personList = feignDataDesignToBasicService.findAllPerson(null);
		Map<String, String> personMap = personList.stream()
				.collect(Collectors.toMap(s -> s.getUserName(), s -> s.getResourceID().toString(), (k1, k2) -> k2));
		//查询所有项目
		List<TestProject> projectList = testProjectService.findAll();
		Map<String, TestProject> projectMap = projectList.stream()
				.collect(Collectors.toMap(s -> s.getName(), s -> s, (k1, k2) -> k2));
		//查询所有被测系统
		List<TestSystem> tsList = testSystemService.findAll();
		Map<String, String> tsMap = tsList.stream().collect(Collectors.toMap(s -> s.getName(), s -> s.getResourceID().toString(), (k1, k2) -> k2));
		//查询项目类型
		Result projectTypeRes = feignDataDesignToBasicService.findByName("TESTPROJECTTYPE", null);
		List<Map<String,Object>> projectTypeList = (List<Map<String, Object>>) projectTypeRes.getObj();
		Map<String, String> projectTypeMap = projectTypeList.stream()
				.collect(Collectors.toMap(s -> s.get("textName").toString(), s -> s.get("value").toString(), (k1, k2) -> k2));

		//key:需求名称：value：需求对应的值
		Map<String, Map<String, Object>> dataMap = getExcelData();
		Set<String> keySet = dataMap.keySet();
		ArrayList<String> demandNames = new ArrayList<>(keySet);
		List<Demand> demandList = this.findByDemandNames(demandNames);
		//查询需求和被测系统的关联
		List<String> demandRdis = demandList.stream().map(s -> s.getResourceID().toString()).collect(Collectors.toList());
		List<DemandTestSystem> dtsList = demandTestSystemService.findByDemandResourceIDIn(demandRdis);
		Map<String, DemandTestSystem> dtsMap = dtsList.stream()
				.collect(Collectors.toMap(s -> s.getDemandResourceID().toString() + s.getTestSystemResourceID().toString(), s -> s, (k1, k2) -> k2));

		ArrayList<DemandTestSystem> saveDemandSystemList = new ArrayList<>();
		ArrayList<DemandTestSystem> updateDemandSystemList = new ArrayList<>();
		ArrayList<TestProject> updateProjectList = new ArrayList<>();
		List<String> projectNames = new ArrayList<>();

		for (Demand d : demandList) {
			Map<String, Object> data = dataMap.get(d.getName());
			if(data == null) continue;


			//测试经理rid
			String testManagerResourceID = personMap.get(data.get("testManager") == null ? null : data.get("testManager").toString());
			if(org.apache.commons.lang3.StringUtils.isNotBlank(testManagerResourceID))
				d.setTestManagerResourceID(Long.valueOf(testManagerResourceID));
			//项目经理rid
			String projectManagerResourceID = personMap.get(data.get("projectManager") == null ? null : data.get("projectManager").toString());
			if(org.apache.commons.lang3.StringUtils.isNotBlank(projectManagerResourceID))
				d.setProjectManagerResourceID(Long.valueOf(projectManagerResourceID));

			//所属项目名称
			TestProject project = projectMap.get(data.get("projectName"));
			if(project != null){
				projectNames.add(project.getName());
				d.setTestProjectName(project.getName());
				d.setTestProjectResourceID(project.getResourceID());
				//所属项目类型名称
				String projectTypeName = data.get("projectTypeName") == null ? null : data.get("projectTypeName").toString();
				String value = projectTypeMap.get(projectTypeName);
				project.setProjectType(value == null ? null : Integer.valueOf(value));
				updateProjectList.add(project);
			}

			//主要被测系统
			String isPrincipalCellName = data.get("isPrincipalCellName") == null ? null : data.get("isPrincipalCellName").toString();
			String systemResourceID = tsMap.get(isPrincipalCellName);
			if(org.apache.commons.lang3.StringUtils.isNotBlank(systemResourceID)){
				DemandTestSystem demandTestSystem = dtsMap.get(d.getResourceID().toString() + systemResourceID);
				if(demandTestSystem == null ){
					DemandTestSystem dts = new DemandTestSystem();
					dts.setDemandResourceID(d.getResourceID());
					dts.setTestSystemResourceID(Long.valueOf(systemResourceID));
					dts.setIsPrincipal(1);
					saveDemandSystemList.add(dts);
				}else{
					demandTestSystem.setIsPrincipal(1);
					updateDemandSystemList.add(demandTestSystem);
				}
			}
		}
			//更新需求
		if(demandList.size() > 0) this.update(demandList,"Admin");
		//更新项目
		if(updateProjectList.size() > 0) testProjectService.update(updateProjectList,"Admin");
		//更新被测系统和需求的关联
		if(saveDemandSystemList.size() > 0) demandTestSystemService.save(saveDemandSystemList,"Admin");
		if(updateDemandSystemList.size() > 0) demandTestSystemService.update(updateDemandSystemList,"Admin");

		Map<String, Object> returnMap = new HashMap<>();
		returnMap.put("demandList",demandList);
		return Result.renderSuccess(returnMap);
	}

	public Map<String, Map<String, Object>> getExcelData() throws IOException {
		InputStream is1=null;
		try {
			InputStream is = DemandServiceImpl.class.getResourceAsStream("/GLDemandProject.xls");
			HSSFWorkbook wb = new HSSFWorkbook(is);
			HSSFSheet sheet = wb.getSheetAt(0);
			//key:需求名称：value：需求对应的值
			Map<String, Map<String, Object>> dataMap = new HashMap<String, Map<String, Object>>();
			int rows = sheet.getPhysicalNumberOfRows();
			for(int i = 3 ; i < rows; i++){
				HSSFRow row = sheet.getRow(i);
				//需求名称
				HSSFCell demandNameCell = row.getCell(1);
				String demandName = demandNameCell.getStringCellValue();
				//所属条线类型名称
				HSSFCell projectTypeCell = row.getCell(2);
				String projectTypeName = projectTypeCell.getStringCellValue();
				//条线名称
				HSSFCell projectNameCell = row.getCell(3);
				String projectName = projectNameCell.getStringCellValue();
				//主系统名称
				HSSFCell isPrincipalCell = row.getCell(4);
				String isPrincipalCellName = isPrincipalCell.getStringCellValue();
				//项目经理
				HSSFCell projectManagerCell = row.getCell(5);
				String projectManager = projectManagerCell.getStringCellValue();
				//测试经理
				HSSFCell testManagerCell = row.getCell(6);
				String testManager = testManagerCell.getStringCellValue();

				HashMap<String, Object> data = new HashMap<>();
				data.put("projectTypeName",projectTypeName);
				data.put("projectName",projectName);
				data.put("isPrincipalCellName",isPrincipalCellName);
				data.put("projectManager",projectManager);
				data.put("testManager",testManager);
				data.put("demandName",demandName);
				dataMap.put(demandName,data);
			}
			return dataMap;
		}catch (Exception e){
			return null;
		}finally {
			try {
				if(is1!=null){
					is1.close();
				}
			}catch (Exception e){

			}
		}

	}

	/**
	 * 根据项目的业务主键查询项目关联的所有需求
	 *
	 * @param projectResourceID
	 * @return com.jettech.dto.Result
	 * <AUTHOR>
	 * 10:18
	 * @update
	 * @exception/throws [异常类型] [异常说明]
	 * @see [类、类#方法、类#成员]
	 * @since [起始版本]
	 */
	@Override
	public List<Map<String, Object>> findDemandsByTestProjectResourceID(String projectResourceID) {

		return demandDao.findDemandsByTestProjectResourceID(projectResourceID);
	}

	/**
	 * 根据需求的业务主键查询需求的所属项目
	 *
	 * @param demandResourceID
	 * @return com.jettech.dto.Result
	 * <AUTHOR>
	 * 10:18
	 * @update
	 * @exception/throws [异常类型] [异常说明]
	 * @see [类、类#方法、类#成员]
	 * @since [起始版本]
	 */
	@Override
	public List<Map<String, Object>> findProjectBydemandResourceID(String demandResourceID,String userNumber) {
		return demandDao.findProjectBydemandResourceID(demandResourceID,userNumber);
	}

	/**
	 * 富滇新增需求
	 *
	 * @param paramList
	 * @param userVo
	 * @return com.jettech.dto.Result<?>
	 * <AUTHOR>
	 * 上午11:56
	 * @update
	 * @exception/throws [异常类型] [异常说明]
	 * @see [类、类#方法、类#成员]
	 * @since [起始版本]
	 */
	@Override
	public Result<?> createOrUpdateDemand(List<Map<String,String>> paramList, UserVo userVo) {
		for(Map<String,String> map : paramList){
			if (checkIsNull(map.get("number"))) {
				Result<Demand> res = crateDemand(map,userVo.getUserNumber());
				if(!res.isSuccess()){
					return res;
				}
			} else {
				Result<Demand> res = updateDemand(map,userVo.getUserNumber());
				if(!res.isSuccess()){
					return res;
				}
			}
		}
		return Result.renderSuccess();
	}

	/**
	  * 判断null和空字符串
	  *
	  * <AUTHOR>  
	  *   2021/1/18
	  * @param str 要判断的字符
	  *
	  */
	public boolean isBlank(String str){
		return org.apache.commons.lang.StringUtils.isBlank(str);
	}

	/**
	 * 创建需求 - 富滇专用
	 *
	 * @param param
	 * @param userNumber
	 * @return Demand
	 * <AUTHOR>
	 * 上午11:56
	 */
	public Result<Demand> crateDemand(Map<String,String> param, String userNumber) {
		String name = param.get("name");
		if (isBlank(name)) {
			return Result.renderError("需求名称为空!");
		}
		String number = param.get("number");
		if (isBlank(number)) {
			return Result.renderError("需求编号为空!");
		}
		String type = param.get("type");
		if (isBlank(type)) {
			return Result.renderError("需求状态为空!");
		}
		String level = param.get("level");
		if (isBlank(level)) {
			return Result.renderError("需求级别为空!");
		}

		//需求状态名称
		String typeName = ((Map)redisUtils.get("需求状态")).get(type).toString();

		Demand demand = new Demand();
		demand.setName(name);
		demand.setNumber(number);
		demand.setResourceID(this.generateResourceID());
		demand.setType(type);
		demand.setTypename(typeName);
		demand.setLevel(level);
		//项目ID
		if (!isBlank(param.get("testProjectResourceID"))) {
			String testProjectResourceID = param.get("testProjectResourceID");
			TestProject tp = testProjectService.findByResourceID(Long.valueOf(testProjectResourceID));
			demand.setTestProjectResourceID(Long.valueOf(testProjectResourceID));
			demand.setTestProjectName(tp.getName());
		}else{
			return Result.renderError("需求所属项目不能为空！");
		}
		//项目经理
		if (!isBlank(param.get("projectManagerResourceID"))) {
			String projectManagerResourceID = param.get("projectManagerResourceID");
			demand.setProjectManagerResourceID(Long.valueOf(projectManagerResourceID));
		}
		// 测试经理
		if (!isBlank(param.get("testManagerResourceID"))) {
			String testManagerResourceID = param.get("testManagerResourceID");
			demand.setTestManagerResourceID(Long.valueOf(testManagerResourceID));
		}
		//需求提出人
		if (!isBlank(param.get("proposerResourceID"))) {
			String proposerResourceID = param.get("proposerResourceID");
			demand.setProposerResourceID(Long.valueOf(proposerResourceID));
		} else {
			return Result.renderError("需求提出人不能为空！");
		}
		//描述
		demand.setRemarks(param.get("describeInfo"));

		Demand save = save(demand, userNumber);

		List<DemandUser> dus = new ArrayList<>();
		//待关联人员列表
		Set<Object> setIds = new HashSet<>();
		// 需求项目经理(一个需求目前只有一个需求项目经理)
		if (!StringUtils.isEmpty(save.getProjectManagerResourceID())) {
			DemandUser du = new DemandUser();
			du.setDemandResourceID(save.getResourceID());
			du.setUserResourceID(save.getProjectManagerResourceID());
			du.setRole("需求项目经理");
			dus.add(du);
			//添加至待关联人员列表
			setIds.add(save.getProjectManagerResourceID());
		}
		// 需求提出人(关联关系需求提出人的角色为‘需求人员’)
		if (!StringUtils.isEmpty(save.getProposerResourceID())) {
			DemandUser du = new DemandUser();
			du.setDemandResourceID(save.getResourceID());
			du.setUserResourceID(save.getProposerResourceID());
			du.setRole("需求人员");
			dus.add(du);
			//添加至待关联人员列表
			setIds.add(save.getProposerResourceID());
		} else {
			return Result.renderError("需求提出人不能为空，数据有误！");
		}
		// 测试经理
		if (!StringUtils.isEmpty(save.getTestManagerResourceID())) {
			DemandUser du = new DemandUser();
			du.setDemandResourceID(save.getResourceID());
			du.setUserResourceID(save.getTestManagerResourceID());
			du.setRole("测试经理");
			dus.add(du);
			//添加至待关联人员列表
			setIds.add(save.getTestManagerResourceID());
		}
		demandUserService.save(dus, userNumber);
		//拼接用户ID
		String ids = org.apache.commons.lang.StringUtils.join(setIds,',');
		//将关联需求的人员列表中没有关联项目的人员关联到项目
		relateProjectUser(ids,userNumber,save.getTestProjectResourceID());
		return Result.renderSuccess();
	}

	/**
	 * 富滇修改需求
	 *
	 * @param param
	 * @param userNumber
	 * @return Demand
	 * <AUTHOR>
	 * 上午11:56
	 */
	public Result<Demand> updateDemand(Map<String,String> param, String userNumber) {
		String number = param.get("number");
		if (isBlank(number)) {
			return Result.renderError("需求编号为空!");
		}
		List<Demand> demandList = demandDao.findByNumber(number);
		if(null == demandList || demandList.isEmpty()){
			return Result.renderError("需求编号不存在！");
		}
		Demand demand = demandList.get(0);
		Long oldManagerResourceId = demand.getProjectManagerResourceID();

		String name = param.get("name");
		if (isBlank(name)) {
			return Result.renderError("需求名称为空!");
		}

		String type = param.get("type");
		if (isBlank(type)) {
			return Result.renderError("需求状态为空!");
		}
		String level = param.get("level");
		if (isBlank(level)) {
			return Result.renderError("需求级别为空!");
		}

		//需求状态名称
		String typeName = ((Map)redisUtils.get("需求状态")).get(type).toString();

		if (!demand.getName().equals(name)) {
			return Result.renderError("需求名称请在测管系统中修改!");
		}

		//项目ID
		if (!isBlank(param.get("testProjectResourceID"))) {
			String testProjectResourceID = param.get("testProjectResourceID");
			TestProject tp = testProjectService.findByResourceID(Long.valueOf(testProjectResourceID));
			demand.setTestProjectResourceID(Long.valueOf(testProjectResourceID));
			demand.setTestProjectName(tp.getName());
		}else{
			return Result.renderError("需求所属项目不能为空！");
		}

		DemandChangeNodes demandChangeNodes = new DemandChangeNodes();
		demandChangeNodes.setDemandResourceID(demand.getResourceID());
		demandChangeNodes.setBeforeChangeType(demand.getType());
		demandChangeNodes.setAfterChangeType(type);
		iDemandChangeNodesService.save(demandChangeNodes, userNumber);

		demand.setType(type);
		demand.setTypename(typeName);
		demand.setLevel(level);
		demand.setEditUser(userNumber);
		demand.setRemarks(param.get("describeInfo"));

		//待关联人员列表
		Set<Object> setIds = new HashSet<>();

		// 需求项目经理可以为空
		if (!isBlank(param.get("projectManagerResourceID"))) {// 新传入的需求项目经理不空
			Long projectManagerResourceID = Long.valueOf(param.get("projectManagerResourceID"));
			// 修改之前是非空的并且和修改的不是同一个，修改更新
			if (!StringUtils.isEmpty(oldManagerResourceId) && !oldManagerResourceId.equals(projectManagerResourceID)) {
				List<DemandUser> dus = demandUserService.findByUserResourceIDAndDemandResourceID(
						String.valueOf(oldManagerResourceId), String.valueOf(demand.getResourceID()));
				for (DemandUser fdu : dus) {
					if ("需求项目经理".equals(fdu.getRole())) {
						fdu.setRole(null);
						demandUserService.update(fdu, userNumber);// 需求项目经理只有一个 ,修改之前关联的需求项目经理为普通角色即 NULL
						break;
					}
				}
			}

			// 关联表新增需求项目经理角色
			DemandUser du = new DemandUser();
			du.setDemandResourceID(demand.getResourceID());
			du.setRole("需求项目经理");
			du.setUserResourceID(projectManagerResourceID);
			demandUserService.save(du, userNumber);

			demand.setProjectManagerResourceID(projectManagerResourceID);
			//添加至待关联人员列表
			setIds.add(projectManagerResourceID);
		} else {// 新传入的项目经理为空
			if (!StringUtils.isEmpty(oldManagerResourceId)) {// 之前是空的，跳过，之前不空，删除关联关系
				List<DemandUser> dus = demandUserService.findByUserResourceIDAndDemandResourceID(
						String.valueOf(oldManagerResourceId), String.valueOf(demand.getResourceID()));
				for (DemandUser fdu : dus) {
					if ("需求项目经理".equals(fdu.getRole())) {
						fdu.setRole(null);
						demandUserService.update(fdu, userNumber);// 找到之前的需求项目经理，变为普通角色即 NULL
						break;
					}
				}
			}
			demand.setProjectManagerResourceID(null);
		}
		// 需提出人不能为空,需求和人员的关联表角色为需求人员
		if (!isBlank(param.get("proposerResourceID"))) {
			String proposerResourceID = param.get("proposerResourceID");
			demand.setProposerResourceID(Long.valueOf(proposerResourceID));
			List<DemandUser> dus = demandUserService.findByUserResourceIDAndDemandResourceID(proposerResourceID,
					String.valueOf(demand.getResourceID()));
			DemandUser findDu = null;
			for (DemandUser fdu : dus) {
				if ("需求人员".equals(fdu.getRole())) {
					findDu = fdu;
				}
			}
			if (StringUtils.isEmpty(findDu)) {// 如果合格需求下没有加过这个人员，角色为需求人员，那么新建，如果有，过
				DemandUser du = new DemandUser();
				du.setDemandResourceID(demand.getResourceID());
				du.setRole("需求人员");
				du.setUserResourceID(Long.valueOf(proposerResourceID));
				demandUserService.save(du, userNumber);
			}
			//添加至待关联人员列表
			setIds.add(proposerResourceID);
		} else {
			Result.renderError("需求提出人不能为空！");
		}

		// 更新测试经理字段
		Long oldTestManagerResourceID = demand.getTestManagerResourceID();
		Long testManagerResourceID = !isBlank(param.get("testManagerResourceID")) ? Long.valueOf(param.get("testManagerResourceID"))  : null;

		//测试经理相同则不做操作
		if (oldTestManagerResourceID != null && oldTestManagerResourceID.equals(testManagerResourceID)) {
			if (null != oldTestManagerResourceID) {
				// 根据testManagerResourceID,demandResourceID,role查询关联表中的数据
				DemandUser dus = demandUserService.findByTestManagerResourceIDAndDemandResourceIDAndRole(oldTestManagerResourceID, demand.getResourceID(), "测试经理");
				// 本次需求更新没有测试经理，设置为普通角色
				dus.setRole(null);
				demandUserService.update(dus, userNumber);
			}
			if (null != testManagerResourceID) {
				DemandUser du = new DemandUser();
				du.setDemandResourceID(demand.getResourceID());
				du.setRole("测试经理");
				du.setUserResourceID(testManagerResourceID);
				demandUserService.save(du, userNumber);
				//添加至待关联人员列表
				setIds.add(testManagerResourceID);
			}
		}
		demand.setTestManagerResourceID(testManagerResourceID);

//      检查需求变更成上线和已完成的状态时的条件
//		需求状态改为已完成时，需要判断需求下所有的案例是否都已至少执行一次，且直接结果均为成功或者取消；需求下所有的缺陷是否都已是闭环状态（缺陷流程配置中的结束状态），
//		若不满足以上两点中的任何一点，弹框提示，状态修改不成功。
		if(typeName.equals("未开始")){
			// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
			List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demand.getResourceID());
			// 查看当前需求下是否还存在缺陷
			List<Map<String, Object>> bugList = this.findBugsByDemandResourceID(demand.getResourceID());
			if((testCaseList!=null && testCaseList.isEmpty()) || (bugList!=null && bugList.isEmpty())){
				return Result.renderError("需求下已存在案例或缺陷，需求状态不能改为未开始");
			}else{
				this.updateDemand(demand);
			}
		}else if(typeName.equals("准备中")){
			// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
			List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demand.getResourceID());
			if(testCaseList!=null && testCaseList.isEmpty()){
				return Result.renderError("需求下不存在案例，需求状态不能改为准备中");
			}else{
				this.updateDemand(demand);
			}
		}else if(typeName.equals("测试中")){
			// 查看当前需求下是否还存在缺陷
			List<Map<String, Object>> bugList = this.findBugsByDemandResourceID(demand.getResourceID());
			// 查看当前需求下是否还存在案例(包括需求案例和项目案例两种类型)
			List<TestCase> testCaseList = iTestCaseService.findBydemandResourceID(demand.getResourceID());
			List<Long> ceseRids = new ArrayList<>();
			testCaseList.stream().forEach(x -> ceseRids.add(x.getResourceID()));
			Integer countOne=0;
			if(ceseRids!=null  && ceseRids.isEmpty()){
				//手工执行案例引用表里当前案例非成功取消状态的案例
				countOne = iTestCaseService.findTestCaseByCaseResourceID(ceseRids, "");
			}
			if( (countOne!=null && countOne>0) || (bugList!=null && !bugList.isEmpty())){
				this.updateDemand(demand);
			}else{
				return Result.renderError("需求下不存在执行的案例或缺陷，需求状态不能改为测试中");
			}
		}
		else if(typeName.equals("已完成")) {
			String checkResult = this.checkOnLineDemandType(demand.getResourceID());
			if (StringUtils.isEmpty(checkResult)) {
				this.updateDemand(demand);
				//生成质量报告数据
				Result result=feignDataDesignToReportForm.createQualityReports(demand.getResourceID(),userNumber);
				if(result==null) {
					return Result.renderError("创建质量报告失败！");
				}
				if(!result.isSuccess()) {
					return Result.renderError(result.getMsg());
				}
			}else {
				return Result.renderError(checkResult);
			}
		} else if (typeName.equals("已上线")) {
			return Result.renderError("请在测管系统中更新上线状态");
		} else {
			this.updateDemand(demand);
		}

		//拼接用户ID
		String ids = org.apache.commons.lang.StringUtils.join(setIds,',');
		//将关联需求的人-员列表中没有关联项目的人员关联到项目
		relateProjectUser(ids,userNumber,demand.getTestProjectResourceID());

		return Result.renderSuccess();
	}
}

