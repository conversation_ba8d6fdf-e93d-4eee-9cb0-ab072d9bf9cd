package com.jettech.service.impl;

import com.jettech.common.dto.Result;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.dao.idao.ITestCaseRecycleBinDao;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.model.*;
import com.jettech.service.iservice.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * @ClassName TestCaseRecycleBinServiceImpl
 * @description 案例编写（回收站表）ServiceImpl层
 * <AUTHOR>
 * @create 2020-05-19
 */
@Service
@Transactional
public class TestCaseRecycleBinServiceImpl extends BaseHBServiceImpl<TestCaseRecycleBin>
		implements ITestCaseRecycleBinService {
	@Autowired
	private ITestCaseRecycleBinDao iTestCaseRecycleBinDao;
	@Autowired
	private ITestSystemService iTestSystemService;
	@Autowired
	private ISystemModuleService iSystemModuleService;
	@Autowired
	private ITradeService iTradeService;
	@Autowired
	private ITestCaseService iTestCaseService;
	
	@Autowired
	private IFeignDataDesignToBasicService iFeignDataDesignToBasicService;
	@Value("${use_file_service}")
	private boolean useFileService;
	@Autowired
	private IFeignDataDesignToFileService feignDataDesignToFileService;
	@PostConstruct
	public void postConstruct() {
		this.baseDao = iTestCaseRecycleBinDao;
	}

	/**
	 * 
	 * @Title: findLeftTreeByTestTaskResourceID
	 * @Description: 根据任务的rid查询该任务维护的系统模块交易范围，展示为左侧树
	 * @param params
	 * @param 参数
	 * @return Result<?> 返回类型
	 * @throws <AUTHOR>
	 * 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public Result<?> findLeftTreeByTestTaskResourceID(Map<String, String> params) {
		String taskResourceID = params.get("taskResourceID");
		if (StringUtils.isEmpty(taskResourceID)) {
			return Result.renderError("taskResourceID参数为空！");
		}
		Map<String, String> taskMap = iTestCaseRecycleBinDao.findByTestTaskResourceID(taskResourceID);
		if (taskMap.isEmpty() || taskMap == null) {
			return Result.renderError("当前任务不存在或已被删除！");
		}
		List<Trade> tradesFind = new ArrayList<Trade>();
		List<Trade> tradesFindList = iTradeService.findSelectedTradeByTaskResourceID(taskResourceID);
		List<Long> tradeResourceIDs = new ArrayList<Long>();
		for (Trade trade : tradesFindList) {
			tradeResourceIDs.add(trade.getResourceID());
		}
		if (!tradeResourceIDs.isEmpty()) {
			List<String> tradeResIDList = iTestCaseRecycleBinDao
					.findTradeResIDsByTestTaskResourceIDAndTradeResID(taskResourceID, tradeResourceIDs);
			for (String resID : tradeResIDList) {
				for (Trade trade : tradesFindList) {
					if (String.valueOf(trade.getResourceID()).equals(resID)) {
						tradesFind.add(trade);
					}
				}
			}
		}
		HashMap<String, Object> map = new HashMap<>();
		map.put("id", "0");
		map.put("name", "系统交易");
		map.put("type", "root");
		map.put("environment", taskMap.get("testStage"));
		map.put("testStageResourceID", String.valueOf(taskMap.get("testStageResourceID")));
		// 如果有保存的交易数据
		if (!tradesFind.isEmpty() && tradesFind.size() > 0) {
			Map<String, Object> systemMap = (Map<String, Object>) this.buildTreeOne(tradesFind, "").getObj();
			List<Map<String, Object>> maps = new ArrayList<>();
			List<Map<String, Object>> listSys = new ArrayList<Map<String, Object>>();
			listSys.add(systemMap);
			map.put("children", listSys);
			maps.add(map);
			return Result.renderSuccess(maps);
		} else {// 没有保存数据
			map.put("children", new ArrayList<List<Map<String, Object>>>());
		}
		List<Map<String, Object>> maps = new ArrayList<>();
		maps.add(map);
		return Result.renderSuccess(maps);
	}

	private Result<?> buildTreeOne(List<Trade> tradesFind, String taskResourceID) {
		// 被测系统和交易
		Map<String, List<Trade>> mapTestSystemTrade = new HashMap<>();
		// 模块和交易
		Map<String, List<Trade>> mapModuleTrade = new HashMap<>();
		String testSystemResourceID = "";
		for (Trade trade : tradesFind) {
			if (org.springframework.util.StringUtils.isEmpty(trade.getTestSystemResourceID())) {// UAT流程用例
				continue;
			}
			if (!org.springframework.util.StringUtils.isEmpty(trade.getModuleResourceID())) {// 模块不空，交易挂载模块下
				if (!mapModuleTrade.isEmpty() && mapModuleTrade.containsKey(trade.getModuleResourceID().toString())) {
					mapModuleTrade.get(trade.getModuleResourceID().toString()).add(trade);
				} else {
					List<Trade> listTrade = new ArrayList<Trade>();
					listTrade.add(trade);
					mapModuleTrade.put(trade.getModuleResourceID().toString(), listTrade);
				}
			} else {// 否则直接挂在被测系统下
				if (!mapTestSystemTrade.isEmpty()
						&& mapTestSystemTrade.containsKey(trade.getTestSystemResourceID().toString())) {
					mapTestSystemTrade.get(trade.getTestSystemResourceID().toString()).add(trade);
				} else {
					List<Trade> listTrade = new ArrayList<Trade>();
					listTrade.add(trade);
					mapTestSystemTrade.put(trade.getTestSystemResourceID().toString(), listTrade);
				}
			}
			testSystemResourceID = trade.getTestSystemResourceID().toString();
		}
		Map<String, Object> systemMap = new HashMap<>();
		if (org.springframework.util.StringUtils.isEmpty(testSystemResourceID)) {
			return Result.renderError("当前交易数据信息有误！");
		} else {
			TestSystem testSystem = iTestSystemService.findByResourceID(Long.valueOf(testSystemResourceID));
			if (testSystem == null) {
				return Result.renderError("当前树结构被测系统不存在或已被删除！");
			}
			systemMap.put("id", testSystem.getId());
			systemMap.put("name", testSystem.getName());
			systemMap.put("resourceID", testSystem.getResourceID());
			systemMap.put("type", "system");
			systemMap.put("children", new ArrayList<>());
			systemMap.put("parentID", "0");
			for (Map.Entry<String, List<Trade>> entry : mapTestSystemTrade.entrySet()) {
				List<Trade> trades = entry.getValue();
				List<Map<String, Object>> childrenTradeList = new ArrayList<>();
				for (Trade trade : trades) {
					Map<String, Object> tradeMap = new HashMap<>();
					tradeMap.put("id", trade.getId());
					tradeMap.put("name", trade.getName());
					tradeMap.put("resourceID", trade.getResourceID());
					tradeMap.put("type", "trade");
					tradeMap.put("parentID", entry.getKey());
//	    		        if(!org.springframework.util.StringUtils.isEmpty(taskResourceID)) {
//	    		        	tradeMap.put("children", this.buildTestCaseTreeList(trade.getResourceID().toString(), taskResourceID));
//	    		        }else {
					tradeMap.put("children", new ArrayList<>());
//	    		        }
					childrenTradeList.add(tradeMap);
				}
				if (!org.springframework.util.StringUtils.isEmpty(childrenTradeList) && childrenTradeList.size() > 0) {
					systemMap.put("children", childrenTradeList);
				}
			}
		}

		// 查询当前被测系统下所有的模块（包含子模块）
		List<SystemModule> allModules = iSystemModuleService.findbyTestSystemResourceID(testSystemResourceID);
		Map<String, SystemModule> mapAllModule = new HashMap<>();
		allModules.stream().forEach(x -> {
			mapAllModule.put(x.getResourceID().toString(), x);
		});
		// 用来存储所有已存在树模块节点
		Set<String> moduleSet = new HashSet<>();
		Map<String, Map<String, Object>> moduleMapMap = new HashMap<>();
		moduleMapMap.put(testSystemResourceID, systemMap);
		// 建立交易和直属模块的关系
		for (Map.Entry<String, List<Trade>> entry : mapModuleTrade.entrySet()) {
			String moduleRid = entry.getKey();
			SystemModule systemModule = mapAllModule.get(moduleRid);
			Map<String, Object> moduleMap = new HashMap<>();
			moduleMap.put("id", systemModule.getId());
			moduleMap.put("name", systemModule.getName());
			moduleMap.put("resourceID", systemModule.getResourceID());
			moduleMap.put("type", "module");
			moduleMap.put("parentID", systemModule.getParentResourceID().toString());
			moduleMap.put("children", new ArrayList<>());
			List<Trade> trades = entry.getValue();
			List<Map<String, Object>> childrenTradeList = new ArrayList<>();
			for (Trade trade : trades) {
				Map<String, Object> tradeMap = new HashMap<>();
				tradeMap.put("id", trade.getId());
				tradeMap.put("name", trade.getName());
				tradeMap.put("resourceID", trade.getResourceID());
				tradeMap.put("type", "trade");
				tradeMap.put("parentID", moduleRid);
//		        if(!org.springframework.util.StringUtils.isEmpty(taskResourceID)) {
//		        	tradeMap.put("children", this.buildTestCaseTreeList(trade.getResourceID().toString(), taskResourceID));
//		        }else {
				tradeMap.put("children", new ArrayList<>());
//		        }
//		        tradeMap.put("children", new ArrayList<>());
				childrenTradeList.add(tradeMap);
			}
			if (!org.springframework.util.StringUtils.isEmpty(childrenTradeList) && childrenTradeList.size() > 0) {
				moduleMap.put("children", childrenTradeList);
			}
			// 如果这个模块的父级为被测系统
			if (systemModule.getParentResourceID().equals(systemModule.getTestSystemResourceID())) {
				List<Map<String, Object>> systemChilds = (List<Map<String, Object>>) systemMap.get("children");
				if (!systemChilds.contains(moduleMap)) {
					systemChilds.add(moduleMap);
					moduleSet.add(moduleRid);
					moduleMapMap.put(moduleRid, moduleMap);
				}
				continue;
			}
			moduleSet.add(moduleRid);
			moduleMapMap.put(moduleRid, moduleMap);
			systemMap = this.buildTree(moduleSet, mapAllModule, systemModule, systemMap, moduleMapMap);

		}
		return Result.renderSuccess(systemMap);
	}

	/**
	 * 
	 * @Title: buildTree
	 * @Description: 构造树结构
	 * @param allModules       系统下所有模块
	 * @param module           当前模块
	 * @param systemResourceID 参数 被测系统rid
	 * @return void 返回类型
	 * @throws <AUTHOR>
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> buildTree(Set<String> moduleSet, Map<String, SystemModule> mapAllModule,
			SystemModule module, Map<String, Object> systemMap, Map<String, Map<String, Object>> moduleMapMap) {
		// 父节点和被测系统节点一样的话，该模块直接挂在被测系统下
		if (module.getParentResourceID().equals(module.getTestSystemResourceID())) {
			List<Map<String, Object>> systemChilds = (List<Map<String, Object>>) systemMap.get("children");
			if (!systemChilds.contains(moduleMapMap.get(module.getResourceID().toString()))) {
				systemChilds.add(moduleMapMap.get(module.getResourceID().toString()));
			}
			return systemMap;
		}
		// 模块父模块
		SystemModule systemModuleParent = mapAllModule.get(module.getParentResourceID().toString());
		if (moduleMapMap.get(systemModuleParent.getResourceID().toString()) != null) {
			Map<String, Object> parentMap = moduleMapMap.get(systemModuleParent.getResourceID().toString());
			List<Map<String, Object>> parentChilds = (List<Map<String, Object>>) parentMap.get("children");
			if (!parentChilds.contains(moduleMapMap.get(module.getResourceID().toString()))) {
				parentChilds.add(moduleMapMap.get(module.getResourceID().toString()));
			}
		} else {
			Map<String, Object> moduleMap = new HashMap<>();
			moduleMap.put("id", systemModuleParent.getId());
			moduleMap.put("name", systemModuleParent.getName());
			moduleMap.put("resourceID", systemModuleParent.getResourceID());
			moduleMap.put("type", "module");
			moduleMap.put("parentID", systemModuleParent.getParentResourceID().toString());
			List<Map<String, Object>> chlds = new ArrayList<>();
			chlds.add(moduleMapMap.get(module.getResourceID().toString()));
			moduleMap.put("children", chlds);
			if (!moduleSet.contains(systemModuleParent.getResourceID().toString())) {// 如果这个模块没有被处理过
				moduleSet.add(systemModuleParent.getResourceID().toString());
			}
			moduleMapMap.put(systemModuleParent.getResourceID().toString(), moduleMap);

		}
		return this.buildTree(moduleSet, mapAllModule, systemModuleParent, systemMap, moduleMapMap);

	}

	/**
	 * @Title: findTestCaseRecycleBinPage
	 * @Description: 根据左侧树展示右侧回收站案例列表
	 * @Return: Page<TestCaseRecycleBin>
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 *  (东莞银行)增加展示要求
	 */
	@Override
	public Page<TestCaseRecycleBin> findTestCaseRecycleBinPage(PageRequest pageRequest, String taskResourceID,String testsystemResourceID,String systemmoduleResourceID,
			String tradeResourceID, String userNumber) {
		
		List<Long> tradeResIds= new ArrayList<Long>();

		//如果交易ResourceID不为空，添加到集合里
		if(!StringUtils.isEmpty(tradeResourceID)) {
			tradeResIds.add(Long.valueOf(tradeResourceID));
		}else {
			tradeResIds =iTestCaseRecycleBinDao.findBytestsystemResourceidAndtaskResourceID(testsystemResourceID,taskResourceID,systemmoduleResourceID);
		}
		if (tradeResIds.isEmpty()){
			return new PageImpl<>(new ArrayList<>());
		}

//		//系统，模块，交易必须传一个参数而且任务esourceID必传，这块前端已经修改
//		//如果系统ResourceID不为空
//		//根据系统ResourceID+任务ResourceID查询所对应交易组
//		if(!StringUtils.isEmpty(testsystemResourceID)) {
//			 tradeResIds =iTestCaseRecycleBinDao.findBytestsystemResourceidAndtaskResourceID(testsystemResourceID,taskResourceID,systemmoduleResourceID);
//		}
//		//如果模块ResourceID不为空
//		//根据模块ResourceID+任务ResourceID查询所对应交易组
//		if(!StringUtils.isEmpty(systemmoduleResourceID)) {
//			 tradeResIds =iTestCaseRecycleBinDao.findBytestsystemResourceidAndtaskResourceID(testsystemResourceID,taskResourceID,systemmoduleResourceID);
//		}
//		//如果交易ResourceID不为空，添加到集合里
//		if(!StringUtils.isEmpty(tradeResourceID)) {
//			tradeResIds.add(Long.valueOf(tradeResourceID));
//		}
		//调用原来查询方法，根据交易esourceID查询
		return iTestCaseRecycleBinDao.findTestCaseRecycleBinPage(pageRequest, taskResourceID, tradeResIds,
				userNumber);
	}

	/**
	 * 
	 * @Title: restoreTestCaseData
	 * @Description: 还原回收站案例数据
	 * @param restoreTestCaseResIDs
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @Date: 20200520
	 */
	@Override
	public Result<?> restoreTestCaseData(List<String> restoreTestCaseResIDs, String userNumber) {
		List<TestCaseRecycleBin> caseRecycleBins = this.findByResourceIDIn(restoreTestCaseResIDs);
		List<TestCase> addCase = new ArrayList<TestCase>();
		for (TestCaseRecycleBin caseRecycleBin : caseRecycleBins) {
			TestCase testCase = new TestCase();
			BeanUtils.copyProperties(caseRecycleBin, testCase);
			addCase.add(testCase);
		}
		if (!addCase.isEmpty()) {
			iTestCaseService.save(addCase, userNumber);
		}
		if (!caseRecycleBins.isEmpty()) {
			this.deleteInBatch(caseRecycleBins, userNumber);
		}
		return Result.renderSuccess();
	}

	/**
	 * 
	 * @Title: deleteTestCaseRecycleBins
	 * @Description: 删除回收站案例数据
	 * @param restoreTestCaseResIDs
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @Date: 20200520
	 */
	@Override
	public Result<?> deleteTestCaseRecycleBins(List<String> restoreTestCaseResIDs, String userNumber) {
		List<TestCaseRecycleBin> caseRecycleBins = this.findByResourceIDIn(restoreTestCaseResIDs);
		//删除案例的相关文件


		if (!caseRecycleBins.isEmpty()) {
			if(useFileService){
				StringBuilder sb=new StringBuilder();
				for(TestCaseRecycleBin item:caseRecycleBins){

					sb.append(item.getResourceID()).append(",");
				}
				String resourceIds=sb.deleteCharAt(sb.length()-1).toString();
				feignDataDesignToFileService.deleteFileByResourceIds(resourceIds, ObjectTypeEnum.SCRIPT.getValue());
			}

			this.deleteInBatch(caseRecycleBins, userNumber);
		}
		return Result.renderSuccess();
	}

	/**
	 * 获取早于某创建时间的数据
	 * 
	 * @param createTime
	 */
	public List<TestCaseRecycleBin> findByLECreateTime(Date createTime) {
		return iTestCaseRecycleBinDao.findByLECreateTime(createTime);
	}

	@Override
	public TestCaseRecycleBin findMaxTestCasebyTradeResourceID(Long resourceID) {
		return iTestCaseRecycleBinDao.findMaxTestCasebyTradeResourceID(resourceID);
	}
	/**
	 * @Title: findByTradeResourceID
	 * @Description: 根据TradeResourceID查询回收站案例
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	@Override
	public List<TestCaseRecycleBin> findByTradeResourceID(Long tradeResourceID) {
		return iTestCaseRecycleBinDao.findByTradeResourceID(tradeResourceID);
	}
	/**
	 * @Title: findByTradeResourceID
	 * @Description: 根据TradeResourceID查询回收站案例CaseID
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	@Override
	public List<String> findCaseIDByTradeResourceID(Long tradeResourceID) {
		return iTestCaseRecycleBinDao.findCaseIDByTradeResourceID(tradeResourceID);
	}

	@Override
	public List<TestCaseRecycleBin> findCaseByTradeResourceIDList(List<Long> mlist) {
		return iTestCaseRecycleBinDao.findCaseByTradeResourceIDList(mlist);
	}
	
}
