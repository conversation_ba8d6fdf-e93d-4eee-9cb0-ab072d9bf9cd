package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.model.SystemModule;
import com.jettech.service.iservice.ISystemModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @Title SystemModuleController
 * @Description 系统模块控制层
 * <AUTHOR>
 * @Date 2019/11/5
 */
@RestController
@RequestMapping("/systemModule")
public class SystemModuleController {

    @Autowired
    private ISystemModuleService systemModuleService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    @PostMapping()
    /**
     * @Title saveorUpdateSystemModule
     * @Description 新增或修改系统模块
     * @Params [request, params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    public Result saveorUpdateSystemModule(HttpServletRequest request, @RequestBody Map<String,String> params){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return systemModuleService.saveorUpdateSystemModule(params,userNumber);
    }

    @DeleteMapping(value = "/{resourceID}")
    /**
     * @Title deleteSystemModule
     * @Description 删除系统模块
     * @Params [request, resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result deleteSystemModule(HttpServletRequest request,@PathVariable String resourceID){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return systemModuleService.deleteSystemModuleByResourceID(resourceID,userNumber);

    }

    /**
     * <AUTHOR>
     * @description 确认删除系统模块
     * @date 2020年11月25日 15:02
     * @param [request, resourceID]
     * @return com.jettech.dto.Result
     **/
    @DeleteMapping(value = "/confirmDeleteSystemModule/{resourceID}")
    public Result confirmDeleteSystemModule(HttpServletRequest request,@PathVariable String resourceID){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return systemModuleService.confirmDeleteSystemModule(resourceID,userNumber);

    }

    @PostMapping(value = "/checkSonModuleORTradeForAdd")
    /**
     * @Title checkSonModuleORTradeForAdd
     * @Description 校验 模块下新增模块或者交易时校验子节点
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/7
     */
    public Result checkSonModuleORTradeForAdd(@RequestBody Map<String,String> params){
        String type = params.get("type");
        String resourceID =params.get("resourceID");
        return systemModuleService.checkSonModuleORTradeForAdd(type,resourceID);

    }

    @PostMapping(value="/checkResourceByModuleForDelete/{resourceID}")
    /**
     * @Title checkResourceByModuleForDelete
     * @Description 校验删除时模块下是否维护数据
     * @Params [resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result checkResourceByModuleForDelete(@PathVariable String resourceID){
        return systemModuleService.findResourceBySystemModule(resourceID);
    }
    
    /**
     * @Title: whetherToRepeat
     * @Description: 判断模块名称是否重复
     * @Param: "[request, testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/21
     */
    @PostMapping("/whetherToRepeat")
    public Result whetherToRepeat(@RequestBody Map<String, String> paramsMap) {
        return systemModuleService.whetherToRepeat(paramsMap);
    }
    
    /**
     * @Title: whetherToRepeat
     * @Description: 查找所有系统
     * @Param: 
     * @Return: Result
     * @Author: ZhangBo
     * @Date: 2019/11/21
     */
    @PostMapping("/findAll")
    public Result findAll(HttpServletRequest request) {
         List<SystemModule> findAll = systemModuleService.findAll();
         return Result.renderSuccess(findAll);
    }
    /**
     * 根据被测系统查询个各层级模块，平拼写成List返回(如：模块1/模块11/模块111)
     * @param request
     * <AUTHOR>
     * @Date 2020/7/14
     * @return
     */
    @PostMapping("/findModuleLevelBySystemRid")
    public Result findModuleLevelBySystemRid(HttpServletRequest request) {
    	 String systemResourceID =request.getParameter("systemResourceID");
         return systemModuleService.findModuleLevelBySystemRid(systemResourceID);
    }


    @PostMapping("/verifyModuleSimpleNameNotRepeated")
    public Result verifyModuleSimpleNameNotRepeated(@RequestBody Map<String, String> paramsMap) {
        return systemModuleService.verifyModuleSimpleNameNotRepeated(paramsMap);
    }
    
    /**
     * 根据所属系统查询交易
     * @param request
     * <AUTHOR> @Date 2022/5/13
     * @return
     */
    @PostMapping("/findTradeBySystemRid")
    public Result findTradeBySystemRid(HttpServletRequest request) {
    	 String systemResourceID = request.getParameter("systemResourceID");
    	 return systemModuleService.findTradeBysystemResourceID(systemResourceID);
    }
    
    @PostMapping("/moreModuleDelete")
    @ResponseBody
    public Result moreModuleDelete(HttpServletRequest request) {
    	String userNumber = LoginUserUtil.getUserNumber(request);
        String moduleResourceID = request.getParameter("moduleResourceID");
        String testSytemResourceID = request.getParameter("testSytemResourceID");
        return systemModuleService.moreModuleDelete(moduleResourceID,testSytemResourceID,userNumber);
    }
}
