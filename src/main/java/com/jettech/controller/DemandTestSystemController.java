package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.service.iservice.IDemandTestSystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * @ClassName DemandTestSystemController
 * @Description 需求与被测系统controller
 * <AUTHOR>
 * @date 2019年12月4日
 */
@Controller
@RequestMapping("/demandtestsystem")
public class DemandTestSystemController {

    private static final Logger logger = LoggerFactory.getLogger(DemandTestSystemController.class);
    @Autowired
    private LoginUserUtil loginUserUtil;
    @Autowired
    private IDemandTestSystemService demandTestSystemService;
    /**
     * @Description 通过需求resourceid查询已关联的被测系统实体类
     * <AUTHOR>
     * @date 2019-12-12 20:31
      * @param demandResourceID
     * @return com.jettech.dto.Result<?>
     */
    @GetMapping("/findTestSystemByDemandResourceID/{demandResourceID}")
    @ResponseBody
    public Result<?> findTestSystemByDemandResourceID(@PathVariable Long demandResourceID){
        return demandTestSystemService.findTestSystemByDemandResourceID(demandResourceID);
    }
    
    /**
     * 
     * @Title  findRelevanceSystemByDemandResourceID     
     * @Description  查询当前需求是否关联系统
     * @author: slq    
     * @date:   2020年8月17日 上午11:51:24
     */
    @PostMapping("/findRelevanceSystemByDemandResourceID")
    @ResponseBody
    public Result<?> findRelevanceSystemByDemandResourceID(HttpServletRequest request){
    	String demandResourceID = request.getParameter("demandResourceID");
    	if(StringUtils.isEmpty(demandResourceID)) {
			return Result.renderError("入参demandResourceID不能为空！");
		}
    	boolean result = demandTestSystemService.findRelevanceSystemByDemandResourceID(demandResourceID);
    	if(result) {
    		return Result.renderSuccess();
    	}else{
    		return Result.renderError("当前需求未关联系统，请先维护关联系统信息！");
    	}
        
    }

    /**
     * 设置主系统
     * @param request
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/setPrincipalSystem", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> setPrincipalSystem(HttpServletRequest request) {
        UserVo loginUser = loginUserUtil.getLoginUser(request);
        String resourceID = request.getParameter("resourceID");
        String isPrincipal = request.getParameter("isPrincipal");
        int result=demandTestSystemService.setPrincipalSystem(loginUser.getUserNumber(),resourceID,isPrincipal);
        if(result>0){
           return  Result.renderSuccess();
        }
       return  Result.renderError("主系统设置失败！");
    }
}
