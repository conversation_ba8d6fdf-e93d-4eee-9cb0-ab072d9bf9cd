package com.jettech.controller;

import com.jettech.DTO.VersionInfoDto;
import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.model.VersionInfo;
import com.jettech.service.iservice.IVersionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Date 2021/5/20
 **/

@RestController
@RequestMapping("/version")
public class VersionInfoController {

    @Autowired
    private IVersionInfoService versionInfoService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    @PostMapping("/page")
    public Result page(@RequestBody VersionInfoDto versionInfoDto) {
        return Result.renderSuccess(versionInfoService.page(versionInfoDto));
    }

    @GetMapping("/list")
    public Result list() {
        return Result.renderSuccess(versionInfoService.findAll());
    }

    @PostMapping("/submit")
    public Result submit(HttpServletRequest request, @RequestBody VersionInfo versionInfo) {
        UserVo user = loginUserUtil.getLoginUser(request);
        VersionInfo v = versionInfoService.submit(versionInfo, user);
        if (v != null) {
            return Result.renderSuccess(v);
        } else {
            return Result.renderError();
        }
    }

    @DeleteMapping("/del/{ids}")
    public Result del(HttpServletRequest request, @PathVariable(value = "ids") String ids) {
        UserVo user = loginUserUtil.getLoginUser(request);
        int num = versionInfoService.del(ids, user);
        if (num > 0) {
            return Result.renderSuccess();
        } else {
            return Result.renderError();
        }
    }
}
