package com.jettech.controller;

import com.jettech.DTO.TaskTradeCaseDto;
import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.service.iservice.IExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ImportExcelController
 * @projectName jettopro
 * @description: excel导入导出
 * @date 2019/11/411:43
 */
@Api(value="excel导入导出controller",tags={"excel导入导出控制层接口"})
@RestController
@RequestMapping("/excel")
public class ExcelController {

    private static final Logger logger = LoggerFactory.getLogger(ExcelController.class);
    @Autowired
    private IExcelService excelService;

    /**
      * @Title: importExcel
      * @description: excel导入
      * @param  "[file, request]"
      * @return com.jettech.dto.Result<?>
      * @throws
      * <AUTHOR>
      * @date 2019/11/4 11:55
      */
    @RequestMapping(value = "/import",method = RequestMethod.POST)
    public @ResponseBody Result<?> importExcel(@RequestParam(value="file",required = false) MultipartFile file,
                                               @RequestParam("nodeResourceID") String nodeResourceID,
                                               @RequestParam("nodeType") String nodeType,
                                               @RequestParam("taskResourceID") String taskResourceID,
                                               HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        try {
            return excelService.importExcel(file,nodeType,nodeResourceID,userNumber,taskResourceID);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("导入失败！");
        }
    }
    @RequestMapping(value = "/importShouzi",method = RequestMethod.POST)
    public @ResponseBody Result<?> importShouzi(@RequestParam(value="file",required = false) MultipartFile file,
                                               @RequestParam("nodeResourceID") String nodeResourceID,
                                               @RequestParam("nodeType") String nodeType,
                                               @RequestParam("taskResourceID") String taskResourceID,
                                               HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        try {
            return excelService.importShouzi(file,nodeType,nodeResourceID,userNumber,taskResourceID);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("导入失败！");
        }
    }

    /**
      * @Title: exportExcel
      * @description: 导出excel(支持批量)
      * @param  "[map]"
      * @return com.jettech.dto.Result<?>
      * @throws
      * <AUTHOR>
      * @date 2019/11/4 15:26
      */
    @ApiOperation(value = "导出excel(支持批量)")
    @RequestMapping(value = "/export",method = RequestMethod.POST)
    public @ResponseBody Result<?> exportExcel(@RequestBody Map map, HttpServletRequest request,HttpServletResponse response){
        String Agent = request.getHeader("User-Agent");
        map.put("request",request);
        map.put("response",response);
        map.put("Agent",Agent);
        String userNumber = LoginUserUtil.getUserNumber(request);
        map.put("userNumber",userNumber);
        return excelService.exportExcel(map);
    }

    @RequestMapping("/showExportFields")
    @ResponseBody
    public Result<?> showExportFields( HttpServletRequest request){
        return excelService.showExportFields();
    }

    @GetMapping("/downloadModel")
    public void downloadModel(HttpServletRequest request,HttpServletResponse response){
         String agent = request.getHeader("User-Agent");
        excelService.downloadModel(agent,response);
    }

    @PostMapping("/getUrlMap")
    @ResponseBody
    public Map<String,Object> getUrlMap(@RequestParam Map map, HttpServletRequest request){
        map.put("request",request);
        return excelService.getUrlMap(map);
    }


    /**
     * @Title  querySheetNames
     * @Description 查询导入文件的sheet页名称
     * @author: slq
     * @date:   2020年7月15日 下午4:16:29
     */
    @RequestMapping(value = "/querySheetNames",method = RequestMethod.POST)
    public @ResponseBody Result<?> querySheetNames(@RequestParam(value="file",required = false) MultipartFile file,
                                               HttpServletRequest request){
        try {
            return excelService.querySheetNames(file);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("查询失败！");
        }
    }

   /**
    * @Title  projectCaseImport
    * @Description 项目案例库导入
    * @author: slq
    * @date:   2020年7月15日 上午11:16:22
    */
   @RequestMapping(value = "/projectCaseImport",method = RequestMethod.POST)
   public @ResponseBody Result<?> projectCaseImport(@RequestParam(value="file",required = false) MultipartFile file,
                                              @RequestParam("projectgroupResourceID") String projectgroupResourceID,
                                              @RequestParam("sheetNames") String sheetNames,
                                              HttpServletRequest request){
       String userNumber = LoginUserUtil.getUserNumber(request);
       try {
           return excelService.projectCaseImport(file,projectgroupResourceID,sheetNames,userNumber);
       } catch (Exception e) {
           e.printStackTrace();
           return Result.renderError("导入失败！");
       }
   }
   /**
    * @Title  projectCaseExportExcel
    * @Description 项目案例库导出
    * @author: slq
    * @date:   2020年7月15日 上午11:23:23
    */
   @ApiOperation(value = "项目案例库导出")
   @RequestMapping(value = "/projectCaseExport",method = RequestMethod.POST)
   public @ResponseBody Result<?> projectCaseExportExcel(@RequestBody Map<String, Object> map, HttpServletRequest request,HttpServletResponse response){
       String userNumber = LoginUserUtil.getUserNumber(request);
       map.put("userNumber",userNumber);
       return excelService.projectCaseExport(map, request, response);
   }



    @PostMapping(value = "/getExcelSheets")
    @ResponseBody
    public Result<?> getExcelSheets(@RequestParam(value="file",required = false) MultipartFile file){
        try {
            return excelService.querySheetNames(file);
        } catch (Exception e) {
            return Result.renderError("查询失败！");
        }
    }

    /**
     * <AUTHOR>
     * @description 项目案例库导入
     * @date 2020年11月09日 15:16
     * @param [file, projectgroupResourceID, sheetNames, request]
     * @return com.jettech.dto.Result<?>
     **/
    @PostMapping(value = "/importProjectCase")
    @ResponseBody
    public Result<?> importProjectCase(@RequestParam(value="file",required = false) MultipartFile file,
                                            @RequestParam("projectgroupResourceID") String projectgroupResourceID,
                                       @RequestParam("projectResourceID") String projectResourceID,
                                       @RequestParam("isBuildGroup") String isBuildGroup,
                                            @RequestParam("sheetNames") String sheetNames, HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        try {
            return excelService.importProjectCase(file,projectgroupResourceID,projectResourceID,isBuildGroup,sheetNames,userNumber);
        } catch (Exception e) {
            logger.error("案例导入失败", e);
            return Result.renderError("导入失败！");
        }
    }


    /**
     * @Title: importIdentifyCaseID
     * @description: excel导入(当有案例编号重复时提示)
     * @param  "[file, request]"
     * @return com.jettech.dto.Result<?>
     * @throws
     * <AUTHOR>
     * @date 2021-06-11
     */
    @RequestMapping(value = "/importIdentifyCaseID",method = RequestMethod.POST)
    public @ResponseBody
    Result<?> importIdentifyCaseID(@RequestParam(value="file",required = false) MultipartFile file,
                                   @RequestParam("nodeResourceID") String nodeResourceID,
                                   @RequestParam("nodeType") String nodeType,
                                   @RequestParam("taskResourceID") String taskResourceID,
                                   @RequestParam(value = "committed", required = false) Boolean committed,
                                   HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        try {
            return excelService.importIdentifyCaseID(file, nodeType, nodeResourceID, userNumber, taskResourceID, committed);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("导入失败！");
        }
    }

    @ApiOperation(value = "导出项目案例")
    @RequestMapping(value = "/projectAllCaseExport",method = RequestMethod.POST)
    public @ResponseBody Result<?> projectAllCaseExport(@RequestBody Map<String, Object> map, HttpServletRequest request,HttpServletResponse response){
        String userNumber = LoginUserUtil.getUserNumber(request);
        map.put("userNumber",userNumber);
        return excelService.projectAllCaseExport(map, request, response);
    }
    /**
     * 案例查看页面-导出功能
     * @param taskTradeCaseDto
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "案例查看导出功能")
    @RequestMapping(value = "/exportExcelByDemandCase",method = RequestMethod.POST)
    public @ResponseBody Result<?> exportExcelByDemandCase(@RequestBody TaskTradeCaseDto taskTradeCaseDto, HttpServletRequest request,HttpServletResponse response){
        String userNumber = LoginUserUtil.getUserNumber(request);
        taskTradeCaseDto.setUserNumber(userNumber);
        return excelService.exportExcelByDemandCase(taskTradeCaseDto,request,response);
    }

    /**
     * 下载新建类案例导入模板
     * @param map
     * @param response
     * @return
     */
    @PostMapping("/downProjectCaseTemp")
    public Result downProjectCaseTemp(@RequestBody Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) {
        return excelService.downProjectCaseTemp(map, request, response);
    }
}
