package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.service.iservice.IExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ImportExcelController
 * @projectName jettopro
 * @description: excel导入导出
 * @date 2019/11/411:43
 */
@Api(value="excel导入导出controller",tags={"excel导入导出接口"})
@RestController
@RequestMapping("/excel")
public class ExcelController {

    @Autowired
    private IExcelService excelService;

    /**
      * @Title: importExcel
      * @description: excel导入
      * @param  "[file, request]"
      * @return com.jettech.dto.Result<?>
      * @throws
      * <AUTHOR>
      * @date 2019/11/4 11:55
      */
    @RequestMapping(value = "/import",method = RequestMethod.POST)
    public @ResponseBody Result<?> importExcel(@RequestParam(value="file",required = false) MultipartFile file,
                                               @RequestParam("nodeResourceID") String nodeResourceID,
                                               @RequestParam("nodeType") String nodeType,
                                               @RequestParam(value = "testEnviroment",required = false) String testEnviroment,
                                               HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        try {
            return excelService.importExcelNew(file,nodeType,nodeResourceID,userNumber,testEnviroment);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("导入失败！");
        }
    }

    /**
      * @Title: exportExcel
      * @description: 导出excel(支持批量)
      * @param  "[map]"
      * @return com.jettech.dto.Result<?>
      * @throws
      * <AUTHOR>
      * @date 2019/11/4 15:26
      */
    @ApiOperation(value = "导出excel(支持批量)")
    @RequestMapping(value = "/export",method = RequestMethod.POST)
    public @ResponseBody
    Result<?> exportExcel(@RequestBody Map map, HttpServletRequest request, HttpServletResponse response){
        String Agent = request.getHeader("User-Agent");
        map.put("request",request);
        map.put("response",response);
        map.put("Agent",Agent);
        return excelService.exportExcel(map);
    }

    @GetMapping("/downloadModel")
    public void downloadModel(HttpServletRequest request,HttpServletResponse response){
         String agent = request.getHeader("User-Agent");
        excelService.downloadModel(agent,response);
    }
}
