package com.jettech.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.dto.AsAnalysisModelAttachmentQueryDTO;
import com.jettech.dto.AsAnalysisModelQueryDTO;
import com.jettech.dto.AsAnalysisModelUpdateDTO;
import com.jettech.dto.AsModelUpdateRepeatedCaseDTO;
import com.jettech.service.iservice.IAsAnalysisModelService;
import com.jettech.vo.AsAnalysisModelDetailVO;
import com.jettech.vo.AsAnalysisModelToJsonVO;
import com.jettech.vo.AsAnalysisModelVO;
import com.jettech.vo.AsAnalysisModelVersionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import java.util.List;


/**
* <p>
 * 测试分析模型表 前端控制器
 * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/
@RestController
@RequestMapping("/analysisModel")
@Api(value = "AsAnalysisModelController",tags = "测试分析模型表模块")
@Validated
public class AsAnalysisModelController {
    
    private static final Logger logger = LoggerFactory.getLogger(AsAnalysisModelController.class);

    /**
     * service
     */
    @Autowired
    private IAsAnalysisModelService service;

    /**
     * 分页查询
     *
     * @return Result
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页查询", notes = "分页查询", code = 200, produces = "application/json")
    public Result<IPage<AsAnalysisModelVO>> page(@Validated AsAnalysisModelQueryDTO asAnalysisModelQueryDTO) {
        return this.service.selectPageByDto(asAnalysisModelQueryDTO);
    }

    /**
     * 保存或修改
     *
     * @param dto 参数
     * @return 保存或修改结果
     */
    @PostMapping
    @ApiOperation(value = "保存或修改", notes = "保存或修改", code = 200, produces = "application/json")
    public Result<Boolean> saveOne(@RequestBody @Validated com.jettech.dto.AsAnalysisModelDTO dto, HttpServletRequest request) {
        return this.service.saveOne(dto,request);
    }

    /**
     * 修改(只做修改内容和模型名称)
     * @param dto 修改参数
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改(只做修改内容和模型名称)", notes = "修改(只做修改内容和模型名称)", code = 200, produces = "application/json")
    public Result<Boolean> updateOne(@RequestBody @Validated AsAnalysisModelUpdateDTO dto, HttpServletRequest request) {
        return this.service.updateOne(dto,request);
    }

    /**
     * 模型引用和案例数更新
     * @param dto
     * @return
     */
    @PostMapping("/update/repeatedOrCase")
    @ApiOperation(value = "模型引用和案例数更新", notes = "模型引用和案例数更新", code = 200, produces = "application/json")
    public Result<Boolean> updateRepeatedOrCase(@RequestBody @Validated AsModelUpdateRepeatedCaseDTO dto) {
        return this.service.updateRepeatedOrCase(dto);
    }

    /**
     * 根据主键查询VO
     *
     * @param id 主键
     * @return VO
     */
    @GetMapping
    @ApiOperation(value = "根据主键查询VO详情", notes = "根据主键查询VO详情", code = 200, produces = "application/json")
    @ApiImplicitParam(name = "id",value = "主键id",required = true,dataType = "string",paramType = "query")
    public Result<AsAnalysisModelVO> getById(@RequestParam(name = "id",required = true) String id) {
       return this.service.getById(id);
    }

    /**
     * 根据主键批量删除
     * @param ids 主键
     * @return 删除结果
     */
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "根据主键批量删除", notes = "根据主键批量删除", code = 200, produces = "application/json")
    public Result<Boolean> deleteById(@Validated @NotEmpty @PathVariable("ids") List<String> ids) {
       return this.service.deleteById(ids);
    }

    /**
     * 撤回操作
     * @param id 主键
     * @return
     */
    @GetMapping("withdraw")
    @ApiOperation(value = "撤回", notes = "撤回", code = 200, produces = "application/json")
    @ApiImplicitParam(name = "id",value = "主键id",required = true,dataType = "string",paramType = "query")
    public Result<String> withdraw(@RequestParam(name = "id",required = true) @NotEmpty String id, HttpServletRequest request) {
      return this.service.withdraw(id,request);
    }

    /**
     * 模型内容对比
     * @param id 主键id
     * @return
     */
    @GetMapping("contrast")
    @ApiOperation(value = "模型内容对比", notes = "模型内容对比", code = 200, produces = "application/json")
    @ApiImplicitParam(name = "id",value = "主键id",required = true,dataType = "string",paramType = "query")
    public Result<AsAnalysisModelVersionVO> contrast(@RequestParam(name = "id",required = true) String id) {
        return this.service.contrast(id);
    }

    /**
     * 提交审核
     * @param idList 提交的模型id
     * @param remarks 评审的内容
     * @return
     */
    @GetMapping("submitReview")
    @ApiOperation(value = "提交审核", notes = "提交审核", code = 200, produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList",value = "提交的模型id列表",required = true,dataType = "string",paramType = "query"),
            @ApiImplicitParam(name = "remarks",value = "评审的内容",required = false,dataType = "string",paramType = "query")
    })
    public Result<String> submitReview(@RequestParam List<String> idList, @RequestParam(name = "remarks",required = false) String remarks, HttpServletRequest request) {
        return this.service.submitReview(idList,remarks,request);
    }

    /**
     * 待审核分页查询
     * @param current 页数
     * @param size 条数
     * @param name 测试分析模型名称
     * @param updateUser 提交人
     * @return
     */
    @GetMapping("/reviewPage")
    @ApiOperation(value = "待审核分页查询", notes = "待审核分页查询", code = 200, produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current",value = "页数",required = false,dataType = "Integer",paramType = "query"),
            @ApiImplicitParam(name = "size",value = "条数",required = false,dataType = "Integer",paramType = "query"),
            @ApiImplicitParam(name = "name",value = "测试分析模型名称",required = false,dataType = "string",paramType = "query"),
            @ApiImplicitParam(name = "updateUser",value = "提交人",required = false,dataType = "string",paramType = "query")
    })
    public Result<IPage<AsAnalysisModelVO>> reviewPage(@RequestParam(name = "current",required = false,defaultValue = "1") Integer current, @RequestParam(name = "size",required = false,defaultValue = "10")Integer size,
                                                       @RequestParam(name = "name",required = false)String name,@RequestParam(name = "updateUser",required = false)String updateUser, HttpServletRequest request) {
        return this.service.reviewPage(current,size,name,updateUser,request);
    }

    /**
     * 批量审核
     * @param idList 提交的模型id列表
     * @param remarks 评审的内容
     * @param status 审核状态
     * @return
     */
    @GetMapping("/reviewBatch")
    @ApiOperation(value = "批量审核", notes = "批量审核", code = 200, produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList",value = "提交的模型id列表",required = true,dataType = "string",paramType = "query"),
            @ApiImplicitParam(name = "remarks",value = "评审的内容",required = true,dataType = "string",paramType = "query"),
            @ApiImplicitParam(name = "status",value = "审核状态2-通过,3-已拒绝",required = true,dataType = "integer",paramType = "query")
    })
    public Result<String> reviewBatch(@RequestParam(name = "idList",required = true) List<String> idList,
                                                        @RequestParam(name = "remarks",required = true)String remarks,@RequestParam(name = "status",required = true)@Min(value = 2) @Max(value = 3) Integer status, HttpServletRequest request) {
        if(CollectionUtils.isEmpty(idList)){
            return Result.renderError("提交的模型id列表最少传入一条");
        }
        return this.service.reviewBatch(idList,remarks,status,request);
    }


    /**
     * 评审/修改记录
     * @param id 主键id
     * @return
     */
    @GetMapping("reviewRecords")
    @ApiOperation(value = "评审/修改记录", notes = "评审/修改记录", code = 200, produces = "application/json")
    @ApiImplicitParam(name = "id",value = "主键id",required = true,dataType = "string",paramType = "query")
    public Result<List<AsAnalysisModelDetailVO>> reviewRecords(@RequestParam(name = "id",required = true) String id) {
        return this.service.reviewRecords(id);
    }

    /**
     * xmind解析成json 导入xmind
     * @param multipartFile xmind文件流
     * @return
     */
    @PostMapping(value = "/xmindParse")
    @ApiOperation(value = "xmind解析成json", notes = "xmind解析成json", code = 200, produces = "application/json")
    public Result<AsAnalysisModelVersionVO> xmindParse(@RequestParam(value="file",required = true) MultipartFile multipartFile,@RequestParam(name = "tradeResourceId",required = true)String tradeResourceId){
        return this.service.xmindParse(multipartFile,tradeResourceId);
    }

    /**
     * xmind解析成json(只做解析)
     * @param multipartFile xmind文件流
     * @return
     */
    @PostMapping(value = "/xmindToJson")
    @ApiOperation(value = "xmind解析成json(只做解析)", notes = "xmind解析成json(只做解析)", code = 200, produces = "application/json")
    public Result<AsAnalysisModelToJsonVO> xmindToJson(@RequestParam(value="file",required = true) MultipartFile multipartFile){
        return this.service.xmindToJson(multipartFile);
    }

    /**
     * xmind导出
     * @param id 模型(主键)id
     * @param response
     */
    @GetMapping("/xmindExport")
    @ApiOperation(value = "xmind导出", notes = "xmind导出", code = 200, produces = "application/json")
    @ApiImplicitParam(name = "id",value = "模型(主键)id",required = true,dataType = "string",paramType = "query")
    public void xmindExport(@RequestParam(name = "id",required = true) String id, HttpServletResponse response) {
        this.service.xmindExport(id,response);
    }


    /**
     * 文档管理-引用模型评审通过的模型分页查询
     * @param asAnalysisModelAttachmentQueryDTO 文档的测试模型查询
     * @return
     */
    @GetMapping("/attachment/page")
    @ApiOperation(value = "文档管理-引用模型评审通过的模型分页查询", notes = "文档管理-引用模型评审通过的模型分页查询", code = 200, produces = "application/json")
    public Result<IPage<AsAnalysisModelVO>> attachmentPage(@Validated AsAnalysisModelAttachmentQueryDTO asAnalysisModelAttachmentQueryDTO) {
        return this.service.attachmentPage(asAnalysisModelAttachmentQueryDTO);
    }

    /**
     * 文档模型xmind导出
     * @param id 文档模型(主键)id
     * @param response
     */
    @GetMapping("/attachment/xmindExport")
    @ApiOperation(value = "文档模型xmind导出", notes = "文档模型xmind导出", code = 200, produces = "application/json")
    @ApiImplicitParam(name = "id",value = "文档模型(主键)id",required = true,dataType = "string",paramType = "query")
    public void attachmentXmindExport(@RequestParam(name = "id",required = true) String id, HttpServletResponse response) {
        this.service.attachmentXmindExport(id,response);
    }

    @GetMapping("/generate/{id}")
    @ResponseBody
    public Result generate(HttpServletRequest request,@PathVariable(value = "id") String id) {
        //id=request.getParameter("id");
        try {
            String userNumber=LoginUserUtil.getUserNumber(request);
            return this.service.generateCase(userNumber,id);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return Result.renderError("生成案例失败");
        }

    }
}
