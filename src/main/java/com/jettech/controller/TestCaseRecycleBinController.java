package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.model.TestCaseRecycleBin;
import com.jettech.service.iservice.ITestCaseRecycleBinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


/**
 * @ClassName TestCaseRecycleBinController
 * @description 案例编写（回收站表）Controller层
 * <AUTHOR>
 * @create 2020-05-19
 */
@RestController
@RequestMapping("/testCaseRecycleBin")
public class TestCaseRecycleBinController {
	@Autowired
	private LoginUserUtil loginUserUtil;
	@Autowired
	private ITestCaseRecycleBinService iTestCaseRecycleBinService;

	/**
	 *
	 * @Title: findLeftTreeByTestTaskResourceID
	 * @Description: 根据任务的rid查询该任务维护的系统模块交易范围，展示为左侧树
	 * @param params
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 */
	@PostMapping("/findLeftTreeByTestTaskResourceID")
	public Result<?> findLeftTreeByTestTaskResourceID(HttpServletRequest request,
													  @RequestBody Map<String, String> params) {
		UserVo userVo = loginUserUtil.getLoginUser(request);
		params.put("userNumber", userVo.getUserNumber());
		return iTestCaseRecycleBinService.findLeftTreeByTestTaskResourceID(params);
	}

	/**
     * @Title: findTestCaseRecycleBinPage
     * @Description: 根据左侧树展示右侧回收站案例列表
     * @Return: "com.jettech.dto.Result"
     * @Author: sheng_liqing
     * @Date: 20200520
     */
    @PostMapping("/findTestCaseRecycleBinPage")
    public Result<?> findTestCaseRecycleBinPage(HttpServletRequest request) {
    	String userNumber = LoginUserUtil.getUserNumber(request);
        String rows = request.getParameter("rows");// 默认为10
		if (rows == null|| "".equals(rows))
			rows = "10";
		String page = request.getParameter("page");// 默认为1
		if (page == null|| "".equals(page))
			page = "1";
		String order = request.getParameter("order");
		if (order == null || "".equals(order))
			order = "createTime";
		String sort = request.getParameter("sort");
		if (sort == null|| "".equals(sort))
			sort = "desc";
		/**
		 * 东莞银行增加查看功能
		 */
		//任务ResourceID
		String taskResourceID = request.getParameter("taskResourceID");
		//交易ResourceID
		String tradeResourceID = request.getParameter("tradeResourceID");
		//系统ResourceID
		String testsystemResourceID = request.getParameter("testsystemResourceID");
		//模块ResourceID
		String systemmoduleResourceID = request.getParameter("systemmoduleResourceID");
		PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows),Sort.Direction.fromString(sort), order);
		Page<TestCaseRecycleBin> demandPage = iTestCaseRecycleBinService.findTestCaseRecycleBinPage(pageRequest,taskResourceID,testsystemResourceID,systemmoduleResourceID,tradeResourceID,userNumber);
		return Result.renderSuccess(demandPage);
    }

    /**
	 *
	 * @Title: restoreTestCaseData
	 * @Description: 还原回收站案例数据
	 * @param restoreTestCaseResIDs
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @Date: 20200520
	 */
	@PostMapping("/restoreTestCaseData")
	public Result<?> restoreTestCaseData(HttpServletRequest request,@RequestBody List<String> restoreTestCaseResIDs) {
		UserVo userVo = loginUserUtil.getLoginUser(request);
		if(restoreTestCaseResIDs == null || restoreTestCaseResIDs.isEmpty()) {
			return Result.renderSuccess();
		}
		return iTestCaseRecycleBinService.restoreTestCaseData(restoreTestCaseResIDs,userVo.getUserNumber());
	}

	/**
	 *
	 * @Title: deleteTestCaseRecycleBins
	 * @Description: 删除回收站案例数据
	 * @param restoreTestCaseResIDs
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @Date: 20200520
	 */
	@PostMapping("/deleteTestCaseRecycleBins")
	public Result<?> deleteTestCaseRecycleBins(HttpServletRequest request,@RequestBody List<String> restoreTestCaseResIDs) {
		UserVo userVo = loginUserUtil.getLoginUser(request);
		if(restoreTestCaseResIDs == null || restoreTestCaseResIDs.isEmpty()) {
			return Result.renderSuccess();
		}
		return iTestCaseRecycleBinService.deleteTestCaseRecycleBins(restoreTestCaseResIDs,userVo.getUserNumber());
	}

}
