package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.service.iservice.ITestSystemApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: TestSystemApplyController
 * @description: 系统应用控制层
 * @date 2024/7/15
 */
@Api(value="系统应用controller",tags={"系统应用控制层接口"})
@RestController
@RequestMapping("/testSystemApply")
public class TestSystemApplyController {

    @Autowired
    private ITestSystemApplyService testSystemApplyService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    /**
     * @Title: findTestSystemApply
     * @Description: 系统应用(分页 + 条件查询)
     * @Param: params
     * @Return: "com.jettech.dto.Result"
     * @Author: dwl
     * @Date: 2024/7/15
     */
    @ApiOperation(value = "系统应用(分页 + 条件查询)")
    @PostMapping("/findTestSystemApply")
    public Result findTestSystemApply(@RequestBody Map<String, String> params) {
        return testSystemApplyService.findTestSystemApply(params);
    }

    /**
     * @Title: addTestSystemApply
     * @Description: 新增系统应用
     * @param request
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @PostMapping("/addTestSystemApply")
    @ResponseBody
    public Result<?> addTestSystemApply(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String systemResourceID = params.get("systemResourceID");
        if (StringUtils.isBlank(systemResourceID)) {
            return Result.renderError("传入参数systemResourceID不能为空");
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        String userNumber = user.getUserNumber();
        return testSystemApplyService.addTestSystemApply(params, userNumber);
    }

    /**
     * @Title: updateTestSystemApply
     * @Description: 修改系统应用
     * @param request
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @PostMapping("/updateTestSystemApply")
    @ResponseBody
    public Result<?> updateTestSystemApply(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String resourceID = params.get("resourceID");
        if (StringUtils.isBlank(resourceID)) {
            return Result.renderError("传入参数resourceID不能为空");
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        String userNumber = user.getUserNumber();
        return testSystemApplyService.updateTestSystemApply(params, userNumber);
    }

    /**
     * @Title: deleteTestSystemApply
     * @Description: 删除系统应用
     * @param request
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @PostMapping("/deleteTestSystemApply")
    @ResponseBody
    public Result<?> deleteTestSystemApply(HttpServletRequest request, @RequestParam("resourceID") String resourceID) {
        if (StringUtils.isBlank(resourceID)) {
            return Result.renderError("传入参数resourceID不能为空");
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        String userNumber = user.getUserNumber();
        return testSystemApplyService.deleteTestSystemApply(resourceID, userNumber);
    }

    /**
     * @Title: findTestSystemApplyBySystemResourceID
     * @Description: 通过系统resourceID查询应用
     * @Author: dwl
     * @Date: 2024/7/16
     */
    @PostMapping("/findTestSystemApplyBySystemResourceID")
    public Result findTestSystemApplyBySystemResourceID(@RequestParam("systemResourceID") String systemResourceID) {
        return testSystemApplyService.findTestSystemApplyBySystemResourceID(systemResourceID);
    }

    /**
     * @Title: findApplyRoundByApplyResourceID
     * @Description: 通过应用resourceID查询应用
     * @Author: dwl
     * @Date: 2024/7/16
     */
    @PostMapping("/findApplyRoundByApplyResourceID")
    public Result findApplyRoundByApplyResourceID(@RequestParam("applyResourceID") String applyResourceID) {
        return testSystemApplyService.findApplyRoundByApplyResourceID(applyResourceID);
    }

    /**
     * @Title: findRootDirectoryPath
     * @Description: 返回根目录路径
     * @Author: dwl
     * @Date: 2024/7/16
     */
    @PostMapping("/findRootDirectoryPath")
    public Map<String, Object> findRootDirectoryPath() {
        return testSystemApplyService.findRootDirectoryPath();
    }
}
