package com.jettech.controller;

import com.alibaba.fastjson.JSONObject;
import com.jettech.common.annotation.RepeatSubmit;
import com.jettech.common.constant.TimeLength;
import com.jettech.common.dto.Result;
import com.jettech.common.redis.JedisClient;
import com.jettech.common.util.BinaryDecimalUtil;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.model.TestSystem;
import com.jettech.model.TestSystemUser;
import com.jettech.service.iservice.ITestSystemService;
import com.jettech.service.iservice.ITestSystemUserService;
import com.jettech.util.LongUtil;
import com.jettech.util.jedis.RedisCacheConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: TestSystemController
 * @projectName jettopro
 * @description: 被测系统（管理级）控制层
 * @date 2019/11/418:58
 */
@Api(value="被测系统管理controller",tags={"被测系统（管理级）控制层接口"})
@RestController
@RequestMapping("/testSystem")
public class TestSystemController {

    @Autowired
    private ITestSystemService testSystemService;

    @Autowired
    private ITestSystemUserService iTestSystemUserService;
    
    @Autowired
	private IFeignDataDesignToBasicService feignDataDesignToBasicService;
	@Autowired
	private LoginUserUtil loginUserUtil;

    @Autowired
    private JedisClient jedisUtil;

    /**
     * @param projectResourceID
     * @return com.jettech.dto.Result<?>
     * @Description 通过项目resourceid查询单点案例左侧树
     * <AUTHOR>
     * @date 2019-11-06 18:38
     */
    @GetMapping("/SinglePointLeftTree/{projectResourceID}")
    public Result<?> findSinglePointLeftTreeByProjectResourceID(@PathVariable String projectResourceID) {
        return testSystemService.findSinglePointLeftTreeByProjectResourceID(projectResourceID);
    }
    /**
     * @param demandResourceID
     * @return com.jettech.dto.Result<?>
     * @Description 通过项目resourceid查询单点案例左侧树
     * <AUTHOR>
     * @date 2019-11-06 18:38
     */
    @GetMapping("/SinglePointLeftTreeByKeyWord/{demandResourceID}/{keyWord}")
    public Result<?> findSinglePointLeftTreeByDemandResourceIDAndKeyWord(@PathVariable String demandResourceID,@PathVariable String keyWord) {
        return testSystemService.findSinglePointLeftTreeByDemandResourceIDAndKeyWord(demandResourceID,keyWord);
    }

    /**
     * @Title: findOneLeftTreeByTestSystemResourceID
     * @Description:  案例编写查询单个被测系统的左侧树
     * @Param: "[testSystemResourceID]"
     * @Return: "com.jettech.dto.Result<?>"
     * @Author: xpp
     * @Date: 2020/2/21
     */
    @GetMapping("/findOneLeftTreeByTestSystemResourceID/{testSystemResourceID}/{demandResourceID}")
    public Result<?> findOneLeftTreeByTestSystemResourceID(HttpServletRequest request,@PathVariable String testSystemResourceID
    ,@PathVariable String demandResourceID) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        return testSystemService.findOneLeftTreeByTestSystemResourceID(testSystemResourceID,demandResourceID,userNumber);
    }

    /**
     * @Title: findLeftTreeByUAT
     * @Description:  通过UAT按钮初始化左侧树，增加UAT树节点
     * @Param: "[testEnviroment]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    @GetMapping("/findLeftTreeByUAT/{demandResourceID}")
    public Result findLeftTreeByUAT(@PathVariable("demandResourceID") String demandResourceID){
        return testSystemService.findLeftTreeByUAT(demandResourceID);
    }

    /**
     * @Title: findLeftTreeByUAT
     * @Description:  通过SIT按钮初始化左侧树
     * @Param: "[testEnviroment]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/1/15
     */
    @GetMapping("/findLeftTreeBySIT/{demandResourceID}")
    public Result findLeftTreeBySIT(@PathVariable("demandResourceID") String demandResourceID){
        return testSystemService.findLeftTreeBySIT(demandResourceID);
    }

    /**
     * @Title: customLeftTree
     * @Description:  通过勾选自定义显示案例编写左侧树
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/2/18
     */
    @PostMapping("/customLeftTree")
    public Result customLeftTree(HttpServletRequest request, @RequestBody Map<String, String> params){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return testSystemService.customLeftTree(params, userNumber);
    }

    /**
     * @Title: findCheckedLeftTree
     * @Description:  页面回选已经自定义的左侧树节点
     * @Param: "[params]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/2/25
     */
    @PostMapping("/findCheckedLeftTree")
    public Result findCheckedLeftTree(@RequestBody Map<String, String> params){
        return testSystemService.findCheckedLeftTree(params);
    }

    @PostMapping
    /**
     * @Title saveorUpdateTestSystem
     * @Description 新增或修改被测系统
     * @Params [request, paramsMap]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    public Result saveorUpdateTestSystem(HttpServletRequest request, @RequestBody Map<String,String> data) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        TestSystem testSystem = new TestSystem();
        String resourceID = data.get("resourceID");
        if (!StringUtils.isEmpty(resourceID)) {
            testSystem.setResourceID(Long.valueOf(resourceID));
        }
        testSystem.setNumber(data.get("number"));
        testSystem.setSimpleName(data.get("simpleName"));
        testSystem.setName(data.get("name"));
        testSystem.setDescribes(data.get("describes"));
        testSystem.setManager(data.get("manager"));
        testSystem.setBusPCentralizedDepartment(data.get("busPCentralizedDepartment"));
        testSystem.setDevelopCompany(data.get("developCompany"));
        testSystem.setProjectManager(data.get("projectManager"));
        testSystem.setSITTestManger(data.get("sittestManger"));
        testSystem.setSITTesters(data.get("sittesters"));
        testSystem.setImportant(data.get("important"));
        testSystem.setDevelopPerson(data.get("developPerson"));
        String testMode = data.get("testMode");
        //测试方式数据处理
        testMode = BinaryDecimalUtil.DicValToBin(testMode);
        int val = BinaryDecimalUtil.BinToTen(testMode);
        testSystem.setTestMode(val);
        return testSystemService.saveorUpdateTestSystem(testSystem, userNumber);
    }

    /**
     * @Title: findOneTestSystem
     * @Description: 根据id查询单个被测系统
     * @Param: "[systemResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/17
     */
    @GetMapping("/findOneTestSystem/{systemResourceID}")
    public Result findOneTestSystem(@PathVariable("systemResourceID") Long systemResourceID) {
        return testSystemService.findOneTestSystem(systemResourceID);
    }

    /**
     *
     * <AUTHOR>
     * @description 确认删除被测系统
     * @date 2020年11月25日 14:05
     * @param [request, resourceID]
     * @return com.jettech.dto.Result
     **/
    @DeleteMapping(value = "/confirmDeleteTestSystem/{resourceID}")
    public Result confirmDeleteTestSystem(HttpServletRequest request, @PathVariable String resourceID) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        return testSystemService.confirmDeleteTestSystem(resourceID, userNumber);
    }


    @DeleteMapping(value = "/{resourceID}")
    /**
     * @Title deleteTestSystem
     * @Description 删除被测系统
     * @Params [request, resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result deleteTestSystem(HttpServletRequest request, @PathVariable String resourceID) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        return testSystemService.deleteTestSystemByResourceID(resourceID, userNumber);
    }

    @PostMapping(value = "/checkResourceByTestSystemForDelete/{resourceID}")
    /**
     * @Title checkResourceByTestSystemForDelete
     * @Description 校验 删除时当前被测系统下是否维护数据
     * @Params [request, paramsMap]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result checkResourceByTestSystemForDelete(@PathVariable String resourceID) {
        return testSystemService.findResourceBySystemResourceID(resourceID);
    }


    /**
     * @param "[map,
     *         request]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: updateNodePosition
     * @description: 更新左侧树节点位置
     * <AUTHOR>
     * @date 2019/11/7 14:33
     */
    @PostMapping("/updateNodePosition")
    public @ResponseBody
    Result updateNodePosition(@RequestBody Map map, HttpServletRequest request) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        map.put("userNumber", userNumber);
        return testSystemService.updateNodePosition(map);
    }

    @GetMapping("/findSystemManagement")
    /**
     * @Title findSystemManagement
     * @Description 查询系统负责人（下拉展示）
     * @Params [request]
     * @Return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @Date 2019/11/11
     */
    public Result<?> findSystemManagement(HttpServletRequest request) {
        return testSystemService.findSystemManagement();

    }

    /**
     * @Title: isNotRepeat
     * @Description: 判断系统名称是否重复
     * @Param: "[request, testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/21
     */
    @PostMapping("/whetherToRepeat")
    public Result whetherToRepeat(@RequestBody Map<String, String> paramsMap) {
        return testSystemService.whetherToRepeat(paramsMap);
    }

    /**
     * @Title: simpleNameWhetherToRepeat
     * @Description: 判断系统简称是否重复
     * @Param: "[paramsMap]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/10
     */
    @PostMapping("/simpleNameWhetherToRepeat")
    public Result simpleNameWhetherToRepeat(@RequestBody Map<String, String> paramsMap) {
        return testSystemService.simpleNameWhetherToRepeat(paramsMap);
    }

    /**
     * @return com.jettech.dto.Result<?>
     * @Description 通过项目resourceid和交易下案例已关联个数查询单点案例左侧树和案例个数(manexecute调用)
     * <AUTHOR>
     * @date 2019-11-06 18:38
     */
    @PostMapping("/SinglePointLeftTree")
    public Result<?> SinglePointLeftTree(@RequestBody String data) {
        HashMap map = JSONObject.parseObject(data, HashMap.class);
        Object demandResourceIDObj = map.get("demandResourceID");
        Object relationTradeMapObj = map.get("relationTradeMap");
        Object testPlanType = map.get("testPlanType");
        return testSystemService.SinglePointLeftTree(demandResourceIDObj, relationTradeMapObj, testPlanType);
    }

    /**
     * @Title: initializeTestSystemTable
     * @Description: 系统交易管理初始化被测系统table(分页 + 条件查询)
     * @Param: "[]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    @ApiOperation(value = "系统交易管理初始化被测系统table(分页 + 条件查询)")
    @PostMapping("/initializeTestSystemTable")
    public Result initializeTestSystemTable(@RequestBody Map<String, String> params) {
        return testSystemService.initializeTestSystemTable(params);
    }

    /**
     * @Title: selectTestSystemUser
     * @Description: 返回已选和未选的人员列表
     * @Param: "[resourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    @GetMapping("/selectTestSystemUser/{resourceID}")
    public Result selectTestSystemUser(@PathVariable("resourceID") Long resourceID) {
        return testSystemService.selectTestSystemUser(resourceID);
    }

    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getSITtestUser
     * @description: 返回所有的人员列表，也就是SIT测试人员和SIT测试负责人
     * <AUTHOR>
     * @date 2019/12/12 19:18
     */
    @PostMapping("/getSITtestUser")
    public Result getSITtestUser(HttpServletRequest request) {
        String name = request.getParameter("name");
        String page = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        return testSystemService.getSITtestUser(name,page,pageSize);
    }

    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findTypeAllUser
     * @description: 返回内部机构下所有的人员列表，通过basic查询
     * <AUTHOR>
     * @date 2019/12/12 19:50
     */
    @PostMapping("/findTypeAllUser")
    public Result findTypeAllUser(HttpServletRequest request) {
        String name = request.getParameter("name");
        String page = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        return testSystemService.findTypeAllUser(name,page,pageSize);
    }

    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findOutDept
     * @description: 查询外部机构下所有的子机构，也就是开发公司
     * <AUTHOR>
     * @date 2019/12/12 20:11
     */
    @GetMapping("/findOutDept")
    public Result findOutDept() {
        return testSystemService.findOutDept();
    }

    /**
     * @Title: setSelectTestSystemUser
     * @Description: 被测系统关联人员
     * @Param: "[JettechUserDTOs]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    @PostMapping("/setSelectTestSystemUser")
    public Result setSelectTestSystemUser(HttpServletRequest request, @RequestBody Map<String, Object> params) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        params.put("userNumber", userNumber);
        return testSystemService.setSelectTestSystemUser(params);
    }

    /**
     * @Title: initializeTradeTable
     * @Description: 根据单个被测系统或模块查询关联的交易
     * @Param: "[map]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    @PostMapping("/initializeTradeTable")
    public Result initializeTradeTable(@RequestBody Map<String, String> map) {
        return testSystemService.initializeTradeTable(map);
    }


    @GetMapping("/getTreeBySystemRid/{systemResourceID}")
    public Result getTreeBySystemRid(@PathVariable String systemResourceID,HttpServletRequest request) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        return testSystemService.getTreeBySystemRid(systemResourceID,userNumber);
    }

    /**
     * @Title: whetherToRepeat
     * @Description: 判断模块名称是否重复
     * @Param:
     * @Return: Result
     * @Author: ZhangBo
     * @Date: 2019/11/21
     */
    @PostMapping("/SavedemandUser")
    public String SavedemandUser(HttpServletRequest request) {
        String userId = request.getParameter("userId");
        String systemModelresourceIDs = request.getParameter("systemModelresourceIDs");
        String savedemandUser = testSystemService.SavedemandUser(userId, systemModelresourceIDs);
        return savedemandUser;
    }

    @PostMapping("/getDicValues/{dicName}")
    public Result getDicValues(@PathVariable String dicName) {
        return testSystemService.getDicValues(dicName);
    }

    /**
     * @Title findDataDictionaryByDicName
     * @Description 数据字典查询
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/12/4
     */
    @PostMapping("/findDataDictionaryByDicName")
    public Result findDataDictionaryByDicName(@RequestBody Map<String, String> params) {
        String dicName = params.get("dicName");
        return testSystemService.findDataDictionaryByDicName(dicName);
    }

    /**
     * @param testSystemResourceID
     * @return com.jettech.dto.Result<?>
     * @Description 查询被测系统下一级的模块和交易MAP
     * <AUTHOR>
     * @date 2019-12-12 20:42
     */
    @GetMapping("queryNextLowerLevelMap/{testSystemResourceID}")
    public Result<?> queryNextLowerLevelMap(@PathVariable Long testSystemResourceID) {
        return testSystemService.queryNextLowerLevelMap(testSystemResourceID);
    }

    @PostMapping("/findAllSystem")
    /**
     * @Title findAllSystem
     * @Description 查询所有被测系统
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/12/13
     */
    public Result findAllSystem() {
        return testSystemService.findAllSystem();

    }
    /**
     * @Title findAllSubordinateSystemMap
     * @Description 缺陷归属系统初始化查所有
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2020/3/6
     */
    @PostMapping("/findAllSubordinateSystemMap")
    @ResponseBody
    public Result findAllSubordinateSystemMap(){
        Object r = null;
        Result result  = null;
        String key = RedisCacheConfig.TEST_SYSTEM_ALL;
        r = this.jedisUtil.getObject(key);
        if(r == null)
        {
            result = testSystemService.findSubordinateSystemMap();
            this.jedisUtil.setObject(key, result, 60 * 60 * 10);
        }else{
            result = (Result)r;
        }
        return result;
    }

    @PostMapping("/findAllSystems")
    /**
     * @Title findAllSystems
     * @Description 查询所有被测系统
     * @Params []
     * @Return Result
     * <AUTHOR>
     * @Date 2019/12/24
     */
    public Result findAllSystems() {
        List<TestSystem> findAll = testSystemService.findAll();
        return Result.renderSuccess(findAll);
    }

    @PostMapping("/addTestSystemModul")
    /**
     * @Title findAllSystems
     * @Description 人员添加修改被测系统
     * @Params []
     * @Return Result
     * <AUTHOR>
     * @Date 2019/12/24
     */
    public @ResponseBody
    Boolean addTestSystemModul(HttpServletRequest request) {
        String testSystemResourceIDs = request.getParameter("testSystemResourceIDs");
        String resourceID = request.getParameter("resourceID");
        String userNumber = request.getParameter("userNumber");
        Boolean addTestSystemModul = iTestSystemUserService.addTestSystemModul(testSystemResourceIDs, Long.valueOf(resourceID), userNumber);
        return addTestSystemModul;
    }

    @PostMapping("/deleteTestSystemModul")
    /**
     * @Title findAllSystems
     * @Description 人员删除被测系统与人员中间表
     * @Params []
     * @Return Result
     * <AUTHOR>
     * @Date 2019/12/24
     */
    public Boolean deleteTestSystemModul(HttpServletRequest request) {
        String resourceID = request.getParameter("resourceID");
        String usernumber = request.getParameter("usernumber");
        Boolean addTestSystemModul = iTestSystemUserService.deleteTestSystemModul(resourceID, usernumber);
        return addTestSystemModul;
    }
    
    @PostMapping("/findAllTestSystem")
    /**
     * @Title findAllSystem
     * @Description 查询所有被测系统
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/12/13
     */
    public List<TestSystem> findAllTestSystem(){
        return  testSystemService.findAll();
        

    }
    
    
    @PostMapping("/addTestsystemAndUsers")
    /**
     * @Title addTestsystemAndUsers
     * @Description 添加被测系统与人员
     * @Params []
     * @Return boolean
     * <AUTHOR>
     * @Date 2020/1/3
     */
    public boolean addTestsystemAndUsers(@RequestBody List<TestSystemUser> TestsystemAndUsers, HttpServletRequest request){
    	String userNumber = request.getParameter("userNumber");
    	iTestSystemUserService.addTestsystemAndUsers(TestsystemAndUsers, userNumber);
    	return true;
    }

    /**
     * @param "[]"
     * @return void
     * @throws
     * @Title: downloadModal
     * @description: 模板下载
     * <AUTHOR>
     * @date 2019/12/27 14:37
     */
    @GetMapping("/download")
    public void downloadModal(HttpServletRequest request, HttpServletResponse response) {
        String agent = request.getHeader("User-Agent");
        testSystemService.downloadModal(agent, response);
    }

    /**
     * @param "[file,
     *         request]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: importExcel
     * @description: 被测系统导入excel
     * <AUTHOR>
     * @date 2019/12/27 18:39
     */
    @PostMapping("/importExcel")
    public Result importExcel(@RequestParam(value = "file", required = false) MultipartFile file,
                              HttpServletRequest request) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        try {
            return testSystemService.importExcel(file, userNumber);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("导入失败!");
        }
    }

    /**
     * @Title: getBelongTribe
     * @Description: 获取下拉框选项-->所属部落
     * @Param: "[]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/31
     */
    @GetMapping("/getBelongTribe")
    public Result getBelongTribe() {
        return testSystemService.getBelongTribe();
    }

    /**
     * @Title: getSubordinatTeam
     * @Description: 根据选择的所属部落获取下拉框选项-->所属小队
     * @Param: "[id]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/12/31
     */
    @GetMapping("/getSubordinatTeam/{text}")
    public Result getSubordinatTeam(@PathVariable("text") String text){
        return testSystemService.getSubordinatTeam(text);
    }

    /**
      * @Title: isOperationAuthority
      * @description: 登录用户是否有操作案例库的权限
      * @param "[request]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2020/1/14 11:18
      */
    @PostMapping("/isOperationAuthority")
    public Result isOperationAuthority(HttpServletRequest request){
        return iTestSystemUserService.isOperationAuthority(LoginUserUtil.getUserNumber(request));
    }

    @GetMapping("/findByResourceID/{resourceID}")
    public Result findByResourceID(@PathVariable String resourceID){
        TestSystem ts = testSystemService.findByResourceID(Long.valueOf(resourceID));
        return Result.renderSuccess(ts);
    }
    

    /**
       * @Title findTrades
       * @Description 查询所属模块下的所属交易
       * @param testSystemResourceID
       * @param resourceID
       * @param type
       * @return   Result<?> 
       * <AUTHOR>
       * @data Jan 14, 20208:53:00 AM
       */
      @GetMapping("findTrades/{testSystemResourceID}/{resourceID}/{type}")
      public Result<?> findTrades(@PathVariable Long testSystemResourceID,@PathVariable Long resourceID,@PathVariable String type) {
          return testSystemService.checkData(testSystemResourceID,resourceID,type);
      }

      /**
        * @Title: findTestSystemByDemandResourceID
        * @description: 需求下被测系统
        * @param "[demandResourceID]"
        * @return com.jettech.dto.Result
        * @throws
        * <AUTHOR>
        * @date 2020/2/12 13:28
        */
      @GetMapping("/findTestSystemByDemandResourceID/{demandResourceID}")
      public Result findTestSystemByDemandResourceID(@PathVariable String demandResourceID){
        return  testSystemService.findTestSystemByDemandResourceID(demandResourceID);
      }

      /**
        * @Title: findTestSystemUserByTestSystemResourceID
        * @description: 被测系统查询人员
        * @param "[testSystemResourceID]"
        * @return com.jettech.dto.Result
        * @throws
        * <AUTHOR>
        * @date 2020/2/12 13:55
        */
      @GetMapping("/findTestSystemUserByTestSystemResourceID/{testSystemResourceID}")
      public Result findTestSystemUserByTestSystemResourceID(@PathVariable String testSystemResourceID){
          List<TestSystemUser> list = iTestSystemUserService.findbyTestSystemResourceID(testSystemResourceID);
          return Result.renderSuccess(list);
      }

      /**
        * @Title: exportTestSystem
        * @description: 导出被测系统
        * @param "[]"
        * @return com.jettech.dto.Result
        * @throws
        * <AUTHOR>
        * @date 2020/2/20 16:59
        */
      @ApiOperation(value = "导出被测系统")
      @PostMapping("/exportTestSystem")
      public void exportTestSystem(@RequestBody Map map,
                                    HttpServletRequest request,
                                     HttpServletResponse response){
          String Agent = request.getHeader("User-Agent");
          map.put("request",request);
          map.put("response",response);
          map.put("Agent",Agent);

          testSystemService.exportTestSystem(map);
      }
      /**
        * @Title: findByUserNumber
        * @description: 当前登录是人是否在被测系统下
        * @param "[request]"
        * @return com.jettech.dto.Result
        * @throws
        * <AUTHOR>
        * @date 2020/2/23 14:27
        */
      @GetMapping("/findByUserNumber/{nodeType}/{nodeResourceID}")
      public Result findByUserNumber(@PathVariable String nodeType,
                                     @PathVariable String nodeResourceID,
                                     HttpServletRequest request){
          String userNumber = LoginUserUtil.getUserNumber(request);
          return iTestSystemUserService.findByUserNumber(nodeType,nodeResourceID,userNumber);
      }


    /**
     *
     * @Title: searchTestSystem
     * @Description: 查询关联系统
     * @param request
     * @return
     * <AUTHOR>
     * @date 2020年2月23日 下午4:49:37
     */
    @RequestMapping(value = "/searchTestSystem", method = RequestMethod.POST)
  	@ResponseBody
  	public Result<?> searchTestSystem(HttpServletRequest request){
  		List<Map<String, Object>> list=testSystemService.searchTestSystem();
  		return Result.renderSuccess(list);
  	}
    
    /**
     * 
     * @Title: findTestSystemByUserResourceID 
     * @Description: 查询人员所属系统
     * @return
     * <AUTHOR>
     * @date 2020年3月3日 下午8:58:25
     */
    @RequestMapping(value = "/findTestSystemByUserResourceID", method = RequestMethod.POST)
  	@ResponseBody
    public String findTestSystemByUserResourceID(HttpServletRequest request){
    	String userResourceID= request.getParameter("userResourceID");
    	return testSystemService.findTestSystemByUserResourceID(Long.parseLong(userResourceID));
    }

    /**
     * @Title: findTradeByResourceID
     * @Description:  根据交易id查询系统
     * @Param: "[tradeResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2020/3/7
     */
    @GetMapping("/findTestSystembyTradeByResourceID/{tradeResourceID}")
    public Result findTestSystembyTradeByResourceID(@PathVariable("tradeResourceID") Long tradeResourceID){
        return testSystemService.findTestSystembyTradeByResourceID(tradeResourceID);
    }
    /**
     * @Title findTestSystemMap
     * @Description 查询被测系统返回Map
     * @Params [systemRID]
     * @Return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @Date 2020/3/7
     */
    @PostMapping("/findTestSystemMap")
    public Result findTestSystemMap(HttpServletRequest request){
        String systemRID = request.getParameter("testSystemResourceID");
        Map<String,String> systemMap = testSystemService.findTestSystemMap(LongUtil.parseLong(systemRID));
        return Result.renderSuccess(systemMap);
    }
    
    /**
     * 
     * @Title:findValueAndTextNameByInfoName
     * @Description:测试阶段类型下拉
     * @param 
     * @return Result
     * @author: wu_yancheng
     * @date 2020年4月1日下午2:38:29
     */
    @PostMapping("/findValueAndTextNameByInfoName")
    public Result findValueAndTextNameByInfoName(HttpServletRequest request){
    	String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, Object>> list = feignDataDesignToBasicService.findValueAndTextNameByInfoName("测试阶段", token);
        return Result.renderSuccess(list);
    }
    
    /**
     * 
     * @Title:findLeftTreeByTestStage
     * @Description:通过测试阶段初始化左侧树
     * @param 
     * @return Result
     * @author: wu_yancheng
     * @date 2020年4月1日下午3:00:50
     */
    @GetMapping("/findLeftTreeByTestStage/{demandResourceID}/{testStage}")
    public Result findLeftTreeByTestStage(@PathVariable("demandResourceID") String demandResourceID,@PathVariable("testStage") String testStage){
        return testSystemService.findLeftTreeByTestStage(demandResourceID,testStage);
    }
    
    
    /**
     * 当任务类型是案例设计时，任务范围的左侧系统模块树
    * @Title: getTestTaskTradeTreeBySystemResourceID
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param systemResourceID
    * @param @param request
    * @param @return    参数
    * @return Result    返回类型
    * @throws
    * <AUTHOR>
     */
    @PostMapping(value="/getTestTaskTradeTreeBySystemResourceID")
	public Result<?> getTestTaskTradeTreeBySystemResourceID(@RequestBody Map<String, String> params){
    	
    	 return testSystemService.getTestTaskTradeTreeBySystemResourceID(params);
    };
    /**
     * 当任务类型是案例设计时，任务范围的左侧系统模块树,当点击系统或者模块时展示右侧的交易列表
    * @Title: initTestTaskTradeTable
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param params
    * @param @return    参数
    * @return Result<?>    返回类型
    * @throws
    * <AUTHOR>
     */
    @PostMapping(value="/initTestTaskTradeTable")
    public Result<?> initTestTaskTradeTable(@RequestBody Map<String, String> params){
    	
    	return testSystemService.initTestTaskTradeTable(params); 
    };
    
    /**
     * 根据任务的rid查询该任务维护的系统模块交易范围，展示为左侧树
    * @Title: findLeftTreeByTestTaskResourceID
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param params
    * @param @return    参数
    * @return Result<?>    返回类型
    * @throws
    * <AUTHOR>
     */
    @PostMapping("/findLeftTreeByTestTaskResourceID")
    public Result<?> findLeftTreeByTestTaskResourceID(@RequestBody Map<String, String> params){
    	
        return testSystemService.findLeftTreeByTestTaskResourceID(params);

    }
    
    /**
     * 根据任务的rid查询该任务维护的系统模块交易和手工编写案例，展示为左侧树（手工执行用例范围树结构-来源为任务的勾选保存的案例）
    * @Title: findManExecuteLeftTreeByTestTaskResourceID
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param params
    * @param @return    参数
    * @return Result<?>    返回类型
    * @throws
    * <AUTHOR>
     */
    @PostMapping("/findManExecuteLeftTreeByTestTaskResourceID")
    public Result<?> findManExecuteLeftTreeByTestTaskResourceID(HttpServletRequest request,@RequestBody Map<String, String> params){
    	UserVo userVo = loginUserUtil.getLoginUser(request);
    	params.put("userNumber", userVo.getUserNumber());
        return testSystemService.findManExecuteLeftTreeByTestTaskResourceID(params);

    }
    
    /**
     * 根据交易和任务查询手工执行引用案例列表信息
    * @Title: findExecuteCaseListInfo
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @return    参数
    * @return Result<?>    返回类型
    * @throws
    * <AUTHOR>
     */
    @PostMapping("/findExecuteCaseListInfo")
    public Result<?> findExecuteCaseListInfo(HttpServletRequest request,@RequestBody Map<String, String> params){
    	UserVo userVo = loginUserUtil.getLoginUser(request);
    	params.put("userNumber", userVo.getUserNumber());
    	
    	String rows = params.get("rows");;// 默认为10
		if (rows == null|| "".equals(rows))
			rows = "10";
		String page = params.get("page");// 默认为1
		if (page == null|| "".equals(page))
			page = "1";
		PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
    	return testSystemService.findExecuteCaseListInfo(params,pageRequest);
    }
    /**
     * 
     * @Title: importTrade 
     * @Description:交易导入 
     * @param file
     * @param request
     * @return
     * <AUTHOR>
     * @date 2020年5月26日 下午2:36:49
     */
    @RequestMapping(value = "/importTrade",method = RequestMethod.POST)
    public @ResponseBody Result<?> importTrade(@RequestParam(value="file",required = false) MultipartFile file,HttpServletRequest request){
       if(file.isEmpty()) {
    	   return Result.renderError("请选择文件导入！");
       }
    	String userNumber = LoginUserUtil.getUserNumber(request);
        return testSystemService.importTrade2(file,userNumber);
    }

    /**
     *
     * @Title: findNotRelateUser
     * @Description: 查询未关联的人员
     * @param request
     * @return
     * <AUTHOR>
     * @date 2020年9月4日 下午5:01:10
     */
    @PostMapping("/findNotRelateUser")
    @ResponseBody
    public Result<?> findNotRelateUser(HttpServletRequest request) {
        String name = request.getParameter("name");
        String rows = request.getParameter("rows");// 默认为10
        if (rows == null|| "".equals(rows))
            rows = "10000";
        String page = request.getParameter("page");// 默认为1
        if (page == null|| "".equals(page))
            page = "1";
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        String testSystemResourceID = request.getParameter("testSystemResourceID");
        String userGroupReourceID = request.getParameter("userGroupReourceID");
        String deptName = request.getParameter("deptName");
        return iTestSystemUserService.findNotRelateUser(name,deptName,testSystemResourceID,userGroupReourceID,pageRequest);
    }
    /**
     *
     * @Title: findRelatedUser
     * @Description: 查询已关联的人员
     * @param request
     * @return
     * <AUTHOR>
     * @date 2020年9月4日  下午5:01:23
     */
    @PostMapping("/findRelatedUser")
    @ResponseBody
    public Result<?> findRelatedUser(HttpServletRequest request) {
        String name = request.getParameter("name");
        String rows = request.getParameter("rows");// 默认为10
        if (rows == null|| "".equals(rows))
            rows = "10000";
        String page = request.getParameter("page");// 默认为1
        if (page == null|| "".equals(page))
            page = "1";
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        String testSystemResourceID = request.getParameter("testSystemResourceID");
        String userGroupReourceID = request.getParameter("userGroupReourceID");
        String deptName = request.getParameter("deptName");
        return iTestSystemUserService.findRelatedUser(name,deptName,testSystemResourceID,userGroupReourceID,pageRequest);
    }
    
  
    /**
     *
     * @Title: relateUser
     * @Description: 关联人员
     * @param request
     * @return
     * <AUTHOR>
     * @date 2020年9月4日  下午5:01:23
     */
    @PostMapping("/relateUser")
    @ResponseBody
    public Result<?> relateUser(HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String testSystemResourceID = request.getParameter("testSystemResourceID");
        String userResourceIDs = request.getParameter("userResourceIDs");
        return iTestSystemUserService.relateUser(userResourceIDs,testSystemResourceID,user.getUserNumber());
    }

    /**
     *
     * @Title: cancelRelatedUser
     * @Description: 取消关联人员
     * @param request
     * @return
     * <AUTHOR>
     * @date 2020年9月4日  下午5:01:23
     */
    @PostMapping("/cancelRelatedUser")
    @ResponseBody
    public Result<?> cancelRelatedUser(HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String testSystemResourceID = request.getParameter("testSystemResourceID");
        String userResourceIDs = request.getParameter("userResourceIDs");
        return iTestSystemUserService.cancelRelatedUser(userResourceIDs,testSystemResourceID,user.getUserNumber());
    }


    /**
     * @param projectResourceID
     * @return com.jettech.dto.Result<?>
     * @Description 通过项目resourceid查询单点案例左侧树
     * <AUTHOR>
     * @date 2019-11-06 18:38
     */
    @PostMapping("/singlePointLeftTreeHB")
    public Result<?> SinglePointLeftTreeHB(@RequestBody List<Map<String, Object>> testSystemList) {
        List<Map<String, Object>> list = testSystemService.getSinglePointLeftTree(testSystemList);
        return Result.renderSuccess(list);
    }
    /**
     * 
     *@Description多个系统查询共同关联的人员
     *@param 
     *@return Result<?>
     *<AUTHOR>
     *@Date 2022年12月21日
     */
    @PostMapping("/findRelatedUserOfMultipleSystem")
    @ResponseBody
    @ApiOperation("多个系统查询共同关联的人员")
    public Result<?> findRelatedUserOfMultipleSystem(HttpServletRequest request) {
        String name = request.getParameter("name");
        String rows = request.getParameter("rows");// 默认为10
        if (rows == null|| "".equals(rows))
            rows = "10000";
        String page = request.getParameter("page");// 默认为1
        if (page == null|| "".equals(page))
            page = "1";
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        String testSystemResourceID = request.getParameter("testSystemResourceID");
        String userGroupReourceID = request.getParameter("userGroupReourceID");
        String deptName = request.getParameter("deptName");
        return iTestSystemUserService.findRelatedUserOfMultipleSystem(name,deptName,testSystemResourceID,userGroupReourceID,pageRequest);
    }
    /**
     * 
     *@Description多个系统查询未关联的人员
     *@param 
     *@return Result<?>
     *<AUTHOR>
     *@Date 2022年12月21日
     */
    @PostMapping("/findNotRelatedUserOfMultipleSystem")
    @ResponseBody
    @ApiOperation("多个系统查询未关联的人员")
    public Result<?> findNotRelatedUserOfMultipleSystem(HttpServletRequest request) {
    	String name = request.getParameter("name");
    	String rows = request.getParameter("rows");// 默认为10
    	if (rows == null|| "".equals(rows))
    		rows = "10000";
    	String page = request.getParameter("page");// 默认为1
    	if (page == null|| "".equals(page))
    		page = "1";
    	PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
    	String testSystemResourceID = request.getParameter("testSystemResourceID");
    	String userGroupReourceID = request.getParameter("userGroupReourceID");
    	String deptName = request.getParameter("deptName");
    	return iTestSystemUserService.findNotRelatedUserOfMultipleSystem(name,deptName,testSystemResourceID,userGroupReourceID,pageRequest);
    }
    
//    @PostMapping("/multipleSystemRelateUser")
//    @ResponseBody
//    @ApiOperation("多系统关联人员")
//    public Result<?> multipleSystemRelateUser(HttpServletRequest request) {
//        UserVo user = loginUserUtil.getLoginUser(request);
//        String testSystemResourceID = request.getParameter("testSystemResourceID");
//        String userResourceIDs = request.getParameter("userResourceIDs");
//        return iTestSystemUserService.multipleSystemRelateUser(userResourceIDs,testSystemResourceID,user.getUserNumber());
//    }

}
