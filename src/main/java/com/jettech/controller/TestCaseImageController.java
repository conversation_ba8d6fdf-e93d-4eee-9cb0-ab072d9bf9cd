package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.service.iservice.IRichTextImageService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/testCaseImage")
public class TestCaseImageController {
    private final static Logger logger = LoggerFactory.getLogger(TestCaseImageController.class);

    @Autowired
    private IRichTextImageService richTextImageService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    @PostMapping("/uploadRichTextImageArray")
    public Result<?> uploadRichTextImageArray(@RequestParam(value = "file", required = false) MultipartFile[] file, @RequestParam("fileName") String fileName) {
        try {
            if (file == null) {
                file = new MultipartFile[] {};
            }
            richTextImageService.uploadRichTextImageArray(file, fileName);
            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("富文本上传图片失败！", e);
            return Result.renderError("富文本上传图片失败！");
        }
    }

    @PostMapping("/uploadRichTextImage")
    public Result<String> uploadRichTextImage(@RequestParam(value = "file", required = false) MultipartFile file,
                                  HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String s = richTextImageService.uploadRichTextImage(file, user);
        if(StringUtils.isBlank(s)){
            return Result.renderError("失败");
        }
        return Result.renderSuccess(s);
    }

    @RequestMapping(value = "/getImage", method = {RequestMethod.GET, RequestMethod.POST})
    public void getImage(@RequestParam String filePath, HttpServletResponse response) {
        richTextImageService.getRichTextImage(filePath, response);
    }


    @DeleteMapping("/delImage/{resourceID}")
    public Result<?> delImage(@PathVariable("resourceID") String resourceID, HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        richTextImageService.delRichTextImage(resourceID, user);
        return Result.renderSuccess();
    }
}
