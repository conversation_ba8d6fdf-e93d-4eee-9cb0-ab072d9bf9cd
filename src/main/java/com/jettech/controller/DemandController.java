package com.jettech.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jettech.DTO.DemandItemDto;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.DataDictionaryDTO;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.redis.JedisClient;
import com.jettech.common.util.CleanPathUtil;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.model.Demand;
import com.jettech.model.DemandAccessory;
import com.jettech.model.DemandChangeNodes;
import com.jettech.model.TestProject;
import com.jettech.service.iservice.*;
import com.jettech.util.ExcelUtils;
import com.jettech.util.jedis.RedisCacheConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFDataValidationConstraint;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName DemandController
 * @Description 需求controller
 * @date 2019年12月3日下午8:22:20
 */
@Controller
@RequestMapping("/demand")
@Api(value = "需求管理", tags = "需求管理")
public class DemandController {

    @SuppressWarnings("unused")
    private static final Logger logger = LoggerFactory.getLogger(DemandController.class);
    @Autowired
    private IDemandService demandService;
    @Autowired
    private IDemandChangeNodesService iDemandChangeNodesService;
    @Autowired
    private ITestSystemService testSystemService;
    @Autowired
    private LoginUserUtil loginUserUtil;
    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasicService;
    @Autowired
    private ITestProjectService testProjectService;
    @Autowired
    private IDemandAccessoryService demandAccessoryService;

    @Autowired
    private JedisClient jedisUtil;

    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;
    @Value("${use_file_service}")
    private boolean useFileService;
    /**
     * @param
     * @return Result<?>
     * @Title:findDemandPage
     * @Description:条件查询需求
     * @author: wu_yancheng
     * @date 2019年12月3日下午8:22:36
     */
    @RequestMapping(value = "/findDemandPage", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "需求管理-查询接口", tags = "需求管理-查询接口")
    public Result<?> findDemandPage(HttpServletRequest request) {
        //需求名称
        String name = request.getParameter("name");
        //需求编号
        String number = request.getParameter("number");
        //需求状态，可多选
        String typename = request.getParameter("typename");
        //需求级别，可多选
        String level = request.getParameter("level");
        //所属项目
        String testProjectResourceID = request.getParameter("testProjectResourceID");
        //关联系统
        String testSystem = request.getParameter("testSystem");
        //项目经理
        String projectManagerName = request.getParameter("projectManagerName");
        //测试经理
        String testManagerName = request.getParameter("testManagerName");
        String rows = request.getParameter("rows");// 默认为10
        if (rows == null || "".equals(rows))
            rows = "10";
        String page = request.getParameter("page");// 默认为1
        if (page == null || "".equals(page))
            page = "1";
        String order = request.getParameter("order");
        if (order == null || "".equals(order))
            order = "createTime";
        String sort = request.getParameter("sort");
        if (sort == null || "".equals(sort))
            sort = "desc";
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows),Sort.Direction.fromString(sort), order);
        Page<Map<String, Object>> demandPage = demandService.findDemandPage(pageRequest, name, number, typename, testProjectResourceID, testSystem, projectManagerName, testManagerName, level);
        return Result.renderSuccess(demandPage);
    }


    /**
     * @param
     * @return Result<?>
     * @Title:delectDemand
     * @Description:删除需求
     * @author:ZhangBo
     * @date 2019年12月3日下午8:22:36
     */
    @RequestMapping(value = "/delectDemand", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> delectDemand(HttpServletRequest request) {
        UserVo createUser = loginUserUtil.getLoginUser(request);
        String DemandResourceIDs = request.getParameter("DemandResourceIDs");
        if (DemandResourceIDs == null) {
            DemandResourceIDs = "";
        }
        return demandService.delectDemand(DemandResourceIDs, createUser);


    }

    /**
     * @param [request]
     * @return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @description 确认删除需求
     * @date 2020年11月25日 10:56
     **/
    @RequestMapping(value = "/confirmDelectDemand", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> confirmDelectDemand(HttpServletRequest request) {
        UserVo createUser = loginUserUtil.getLoginUser(request);
        String demandResourceIDs = request.getParameter("DemandResourceIDs");
        if (demandResourceIDs == null) {
            demandResourceIDs = "";
        }
        return demandService.confirmDelectDemand(demandResourceIDs, createUser);
    }


    /**
     * @param
     * @return Result<?>
     * @Title:
     * @Description:
     * @author: wu_yancheng
     * @date 2019年12月4日下午4:22:25
     */
    @RequestMapping(value = "/searchCentralizedDepartment", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> searchAdapterType(HttpServletRequest request) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, Object>> list = feignDataDesignToBasicService.findValueAndTextNameByInfoName("业务归口部门", token);
        return Result.renderSuccess(list);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:
     * @Description:
     * @author: wu_yancheng
     * @date 2019年12月4日下午4:22:25
     */
    @RequestMapping(value = "/searchHostTribe", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> searchHostTribe(HttpServletRequest request) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, String>> list = feignDataDesignToBasicService.findNameAndInfoNameByDirName("所属部落", token);
        return Result.renderSuccess(list);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:
     * @Description:
     * @author: wu_yancheng
     * @date 2019年12月4日下午4:22:25
     */
    @RequestMapping(value = "/searchHostTeam", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> searchHostTeam(HttpServletRequest request) {
        String infoName = request.getParameter("infoName");
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, Object>> list = feignDataDesignToBasicService.findValueAndTextNameByInfoName(infoName, token);
        return Result.renderSuccess(list);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:
     * @Description:
     * @author: wu_yancheng
     * @date 2019年12月4日下午4:22:25
     */
    @RequestMapping(value = "/searchType", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> searchType(HttpServletRequest request) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, Object>> list = feignDataDesignToBasicService.findValueAndTextNameByInfoName("需求状态", token);
//		String[] s = {"未开始", "准备中", "测试中", "已完成"};
//		List<String> excludeLs = Arrays.asList(s);
//		list.forEach(e -> {
//			if (excludeLs.contains(e.get("text"))) {
//				e.put("disabled", true);
//			}
//		});
        return Result.renderSuccess(list);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:addOrUpdateDemand
     * @Description:新增需求
     * @author: wu_yancheng
     * @date 2019年12月4日下午2:23:04
     */
    @RequestMapping(value = "/addDemand", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增需求",tags = "新增需求")
    public Result<?> addDemand(HttpServletRequest request, @RequestParam(value = "files", required = false) MultipartFile[] files, @RequestParam Map<String, Object> param) throws IOException {
        if (files == null) {
            files = new MultipartFile[] {};
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        param.put("userNumber", user.getUserNumber());
        return demandService.addDemand(param, files);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:uploadDemandAccessory
     * @Description:上传附件
     * @author: shan_baoxin
     * @date 2020年9月29日14:31:34
     */
    @RequestMapping(value = "/uploadDemandAccessory", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> uploadDemandAccessory(HttpServletRequest request, @RequestParam(value = "files", required = false) MultipartFile[] files, @RequestParam Map<String, Object> param) throws IOException {
        if (files == null) {
            files = new MultipartFile[] {};
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        param.put("userNumber", user.getUserNumber());
        return demandService.uploadDemandAccessory(param, files);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:deleteDemandAccessory
     * @Description:删除附件
     * @author: shan_baoxin
     * @date 2020年9月29日14:31:34
     */
    @RequestMapping(value = "deleteDemandAccessory", method = RequestMethod.GET)
    @ResponseBody
    public Result<?> deleteDemandAccessory(HttpServletRequest request, @RequestParam Map<String, Object> param) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String resourceIDs = (String) param.get("resourceIDs");
        return demandService.deleteDemandAccessory(resourceIDs, user.getUserNumber());
    }

    /**
     * @param
     * @return Result<?>
     * @Title:findByResourceID
     * @Description:查询附件信息
     * @author: shan_baoxin
     * @date 2020年9月29日14:31:34
     */
    @RequestMapping(value = "findByResourceID", method = RequestMethod.GET)
    @ResponseBody
    public Result<?> findByResourceID(HttpServletRequest request, @RequestParam Map<String, Object> param) {
        String resourceID = (String) param.get("resourceID");
        return demandAccessoryService.findByDefectresourceID(resourceID);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:downloadDemand
     * @Description:下载附件信息
     * @author: shan_baoxin
     * @date 2020年9月29日14:31:34
     */
    @RequestMapping(value = "downloadDemand", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> downloadDemand(HttpServletRequest request, HttpServletResponse response) {
        String resourceID = request.getParameter("resourceID");
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("附件的resourceID为空！");
        }
      //代码检查防止路径操作
        resourceID = CleanPathUtil.pathManipulation(resourceID);

       /* Map<String, Object> mapParam = new HashMap<>();
        mapParam.put("resourceID", resourceID);
        mapParam.put("response", response);*/
        try {
            /*HttpServletResponse response = (HttpServletResponse) mapParam.get("response");
            String resourceID = (String) mapParam.get("resourceID");*/
            byte[] data=null;
            if(useFileService){
                byte[] data1=feignDataDesignToFileService.getContent(Long.parseLong(resourceID));
                data = Base64.getMimeDecoder().decode(data1);

            }else{
                DemandAccessory demandFile = demandAccessoryService.findByResourceID(Long.valueOf(resourceID));
                //时间戳。。
                String extname = demandFile.getExtname();
                String path = demandFile.getPath();
                data = demandAccessoryService.getFileData(resourceID, extname,path);
            }

            String fileName = new String("xxx".getBytes("UTF-8"), "ISO8859-1");
            response.setContentType("application/octet-stream;charset=utf-8");

            response.setContentType(request.getServletContext().getMimeType(fileName));
//            response.setHeader("Content-Disposition", "attachment;filename=" + "XXXX"+"的附件下载.zip");
            response.getOutputStream().write(data);
            return null;
        } catch (Exception e) {
            return Result.renderError("下载异常！");
        }
    }

    /**
     * @param
     * @return Result<?>
     * @Title:updateDemand
     * @Description:修改需求
     * @author: wu_yancheng
     * @date 2019年12月6日下午3:13:04
     */
    @RequestMapping(value = "/updateDemand", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改需求",tags = "修改需求")
    public Result<?> updateDemand(HttpServletRequest request, @RequestParam Map<String, Object> param, @RequestParam(value = "files", required = false) MultipartFile[] files) throws IOException {
        if (files == null) {
            files = new MultipartFile[] {};
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        param.put("userNumber", user.getUserNumber());
        return demandService.updateDemand(param, files);
    }

    /**
     * @return : Result<?> 返回类型
     * @Title: initWorkBenchMyDemand
     * @Description: 初始化工作台我的需求
     * <AUTHOR>
     * @date 2019年12月5日下午4:00:03
     */
    @RequestMapping(value = "/initMyDemand", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> initWorkBenchMyDemand(@RequestBody Map<String, String> params) {
        String rows = params.get("rows");
        String page = params.get("page");
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        logger.info(params.toString());
        Page<Map<String, Object>> result = demandService.initWorkBenchMyDemand(pageRequest, params);
        return Result.renderSuccess(result);
    }

    /**
     * @param params
     * @return
     * @Title: initWorkbenchAlreadyMyDemand
     * @Description: 查询我的已办需求列表
     * <AUTHOR>
     * @date 2020年4月23日 下午3:40:45
     */
    @RequestMapping(value = "/initWorkbenchAlreadyMyDemand", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> initWorkbenchAlreadyMyDemand(@RequestBody Map<String, String> params) {
        String rows = params.get("rows");
        String page = params.get("page");
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        logger.info(params.toString());
        Page<Map<String, Object>> result = demandService.initWorkbenchAlreadyMyDemand(pageRequest, params);
        return Result.renderSuccess(result);
    }

    /**
     * @param params
     * @return
     * @Title: initWorkbenchCreateMyDemand
     * @Description: 查询我创建的需求列表
     * <AUTHOR>
     * @date 2020年4月23日 下午4:11:37
     */
    @RequestMapping(value = "/initWorkbenchCreateMyDemand", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> initWorkbenchCreateMyDemand(HttpServletRequest request) {

        Map<String, String> paramMap = new HashMap<String, String>();
        String userNumber = LoginUserUtil.getUserNumber(request);
        if (org.apache.commons.lang3.StringUtils.isBlank(userNumber)) {
            return Result.renderError("未获取到登录用户");
        }

        String rows = request.getParameter("rows");// 每页显示条数
        if (org.apache.commons.lang3.StringUtils.isBlank(rows)) {
            rows = "10";
        }
        String page = request.getParameter("page");// 当前页
        if (org.apache.commons.lang3.StringUtils.isBlank(page)) {
            page = "1";
        }
        String demandName = request.getParameter("demandName") == null ? "" : request.getParameter("demandName");// 需求名称
        String typename = request.getParameter("typename") == null ? "" : request.getParameter("typename");// 需求状态名称
        String number = request.getParameter("number") == null ? "" : request.getParameter("number");// 需求编号
        String testProjectResourceID = request.getParameter("testProjectResourceID") == null ? ""
                : request.getParameter("testProjectResourceID");// 项目的resourceID
        String planName = request.getParameter("planName") == null ? "" : request.getParameter("planName");// 计划名称
        paramMap.put("rows", rows);// 每页显示条数
        paramMap.put("page", page);// 当前页

        paramMap.put("userNumber",userNumber);// 登录人number
        paramMap.put("demandName", demandName);// 需求名称
        paramMap.put("typename", typename);// 需求状态
        paramMap.put("number", number);// 需求编号
        paramMap.put("planName", planName);// 计划名称
        paramMap.put("testProjectResourceID", testProjectResourceID);// 所属项目
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));

        /*String rows = params.get("rows");
        String page = params.get("page");
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        String userNumber = LoginUserUtil.getUserNumber(request);*/

//        Map<String, Object> map = null;
//        String key = RedisCacheConfig.CREATE_MY_DEMAND + userNumber + params.get("testProjectResourceID");
//        Object redisResult = this.jedisUtil.getObject(key);
//        if(redisResult != null)
//        {
//            map = (Map<String,Object>)redisResult;
//        }else{
//            map = new HashMap<>();
//            Page<Map<String, Object>> result = demandService.initWorkbenchCreateMyDemand(pageRequest, params);
//            int number = demandService.initWorkbenchCreateMyDemandNumber(params);
//            map.put("page", result);
//            map.put("number", number);
//            this.jedisUtil.setObject(key, map, 5 * 60);
//        }
        return Result.renderSuccess(demandService.initWorkbenchCreateMyDemand(pageRequest, paramMap));
    }

    /**
     * @param request
     * @return
     * @Title: initMyDemandToDealWith
     * @Description: 工作台待我处理需求列表（当前用户加入当前需求就显示）
     * <AUTHOR>
     * @date 2020年6月15日
     */
    @RequestMapping(value = "/initMyDemandToDealWith", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> initMyDemandToDealWith(HttpServletRequest request) {
        Map<String, String> paramMap = new HashMap<String, String>();
        String userNumber = LoginUserUtil.getUserNumber(request);
        if (org.apache.commons.lang3.StringUtils.isBlank(userNumber)) {
            return Result.renderError("未获取到登录用户");
        }

        String rows = request.getParameter("rows");// 每页显示条数
        if (org.apache.commons.lang3.StringUtils.isBlank(rows)) {
            rows = "10";
        }
        String page = request.getParameter("page");// 当前页
        if (org.apache.commons.lang3.StringUtils.isBlank(page)) {
            page = "1";
        }
        String demandName = request.getParameter("demandName") == null ? "" : request.getParameter("demandName");// 需求名称
        String typename = request.getParameter("typename") == null ? "" : request.getParameter("typename");// 需求状态名称
        String number = request.getParameter("number") == null ? "" : request.getParameter("number");// 需求编号
        String testProjectResourceID = request.getParameter("testProjectResourceID") == null ? ""
                : request.getParameter("testProjectResourceID");// 项目的resourceID
        String planName = request.getParameter("planName") == null ? ""
                : request.getParameter("planName");// 项目的resourceID
        paramMap.put("rows", rows);// 每页显示条数
        paramMap.put("page", page);// 当前页

        paramMap.put("userNumber", userNumber);// 登录人number
        paramMap.put("demandName", demandName);// 需求名称
        paramMap.put("typename", typename);// 需求状态
        paramMap.put("number", number);// 需求编号
        paramMap.put("testProjectResourceID", testProjectResourceID);// 所属项目
        paramMap.put("planName", planName);// 测试计划名称


//        String rows = params.get("rows");
//        String page = params.get("page");
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
//        Map<String, Object> map = null;
//        String userNumber = LoginUserUtil.getUserNumber(request);
//        String key = RedisCacheConfig.DEAL_WIDTH_DEMAND + userNumber + params.get("testProjectResourceID");
//        Object r = this.jedisUtil.getObject(key);
//        if(r != null)
//        {
//            map = (Map<String,Object>)r;
//        }else{
//            map = new HashMap<String, Object>();
//            Page<Map<String, Object>> result = demandService.initMyDemandToDealWith(pageRequest, params);
//            int number = demandService.initMyDemandNumberToDealWith(params);
//            map.put("page", result);
//            map.put("number", number);
//            this.jedisUtil.setObject(key, map, 5 * 60);
//        }
        return Result.renderSuccess(demandService.initMyDemandToDealWith(pageRequest, paramMap));
    }

    /**
     * @Title exportDemandTemplate
     * @Description 需求模板导出
     * @param request
     * <AUTHOR>
     *
     */

	/*@SuppressWarnings("resource")
	@RequestMapping(value = "/exportDemandTemplate", method = RequestMethod.POST)
	@ResponseBody
	public void exportDemandTemplate(HttpServletRequest request, HttpServletResponse response) {
		try {
			 //定义工作簿
			XSSFWorkbook workBook = null;
			try {
				workBook = new XSSFWorkbook();
			} catch (Exception e) {
				logger.debug(Arrays.toString(e.getStackTrace()), e);
				logger.info("导出模板失败！！！！");
				return;
			}
	        //定义工作表
			workBook.createSheet();
			workBook.setSheetName(0, "需求");
			XSSFCellStyle cellStyle = workBook.createCellStyle();
			XSSFFont font = workBook.createFont();
			font.setFontName("宋体");
			font.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);//粗体显示
			font.setFontHeightInPoints((short) 12);
			cellStyle.setFont(font);

			XSSFSheet sheet = workBook.getSheetAt(0);

			XSSFRow titleRow = sheet.createRow(0);
			XSSFCell cell0 = titleRow.createCell(0);
			cell0.setCellValue("*需求编号");
			cell0.setCellStyle(cellStyle);
			sheet.setColumnWidth(0, 5304);

			XSSFCell cell1 = titleRow.createCell(1);
			cell1.setCellValue("*需求名称");
			cell1.setCellStyle(cellStyle);
			sheet.setColumnWidth(1, 5304);

			XSSFCell cell2 = titleRow.createCell(2);
			cell2.setCellValue("*需求状态");
			cell2.setCellStyle(cellStyle);
			sheet.setColumnWidth(2, 5304);
			List<Map<String, String>> list=feignDataDesignToBasicService.findValueAndTextNameByInfoName("需求状态");
			String[] datas = changeStrings(list);
			if(datas != null && datas.length > 0) {
				XSSFDataValidation validation = setDataValidation(sheet,1,65536,2,2,datas);
				sheet.addValidationData(validation);
			}

			XSSFCell cell3 = titleRow.createCell(3);
			cell3.setCellValue("需求级别");
			cell3.setCellStyle(cellStyle);
			sheet.setColumnWidth(3, 5304);
			List<Map<String, String>> list1=feignDataDesignToBasicService.findValueAndTextNameByInfoName("需求级别");
			String[] datas1 = changeStrings(list1);
			if(datas1 != null && datas1.length > 0) {
				XSSFDataValidation validation = setDataValidation(sheet,1,65536,3,3,datas1);
				sheet.addValidationData(validation);
			}

			XSSFCell cell4 = titleRow.createCell(4);
			cell4.setCellValue("*需求提出人");
			cell4.setCellStyle(cellStyle);
			sheet.setColumnWidth(4, 5304);
			//List<JettechUserDTO> aa =feignDataDesignToBasicService.findAllPerson();
			List<Map<String, String>> list2 =feignDataDesignToBasicService.findAllPersons();
			String[] datas2 = changeStrings(list2);
			if(datas2 != null && datas2.length > 0) {
				XSSFDataValidation validation = setDataValidation(sheet,1,65536,4,4,datas2);
				sheet.addValidationData(validation);
			}

			XSSFCell cell5 = titleRow.createCell(5);
			cell5.setCellValue("需求项目经理");
			cell5.setCellStyle(cellStyle);
			sheet.setColumnWidth(5, 5304);
			List<Map<String, String>> list3=feignDataDesignToBasicService.findTypeAllPersons();
			String[] datas3 = changeStrings(list3);
			if(datas3 != null && datas3.length > 0) {
				XSSFDataValidation validation = setDataValidation(sheet,1,65536,5,5,datas3);
				sheet.addValidationData(validation);
			}

			XSSFCell cell6 = titleRow.createCell(6);
			cell6.setCellValue("*业务归口部门");
			cell6.setCellStyle(cellStyle);
			sheet.setColumnWidth(6, 5304);
			List<Map<String, String>> list4=feignDataDesignToBasicService.findValueAndTextNameByInfoName("业务归口部门");
			String[] datas4 = changeStrings(list4);
			if(datas4 != null && datas4.length > 0) {
				XSSFDataValidation validation = setDataValidation(sheet,1,65536,6,6,datas4);
				sheet.addValidationData(validation);
			}

			XSSFCell cell7 = titleRow.createCell(7);
			cell7.setCellValue("主办部落");
			cell7.setCellStyle(cellStyle);
			sheet.setColumnWidth(7, 5304);
			List<Map<String, String>> list5=feignDataDesignToBasicService.findValueAndTextNameByInfoName("所属部落");
			String[] datas5 = changeStrings(list5);
			if(datas5 != null && datas5.length > 0) {
				XSSFDataValidation validation = setDataValidation(sheet,1,65536,7,7,datas5);
				sheet.addValidationData(validation);
			}

			XSSFCell cell8 = titleRow.createCell(8);
			cell8.setCellValue("主办小队");
			cell8.setCellStyle(cellStyle);
			sheet.setColumnWidth(8, 5304);
			List<Map<String, String>> list6=feignDataDesignToBasicService.findValueAndTextNameByInfoName("所属小队");
			String[] datas6 = changeStrings(list6);
			if(datas6 != null && datas6.length > 0) {
				XSSFDataValidation validation = setDataValidation(sheet,1,65536,8,8,datas6);
				sheet.addValidationData(validation);
			}

			XSSFCell cell9 = titleRow.createCell(9);
			cell9.setCellValue("*创建日期（年/月/日 或 年-月-日）");
			cell9.setCellStyle(cellStyle);
			sheet.setColumnWidth(9, 10424);

//			XSSFDataFormat df = workBook.createDataFormat(); // 此处设置数据格式
//			XSSFCellStyle hssfCellStyleDate = workBook.createCellStyle();
//			hssfCellStyleDate.setDataFormat(df.getFormat("yyyy-MM-dd"));
//			for(int i = 1 ; i<65536; i++) {
//				XSSFRow row = sheet.createRow(i);
//				XSSFCell xssfCell = row.getCell(2);
//				xssfCell.setCellStyle(hssfCellStyleDate);
//			}

			ServletOutputStream outPutStream = response.getOutputStream();
			if (workBook != null) {
				response.setHeader("Cache-Control", "private");
				response.setHeader("Pragma", "private");
				response.setContentType("application/vnd.ms-excel;charset=utf-8");
				response.setHeader("Content-Type", "application/force-download");
		        response.setHeader("content-disposition","attachment;filename=exportDemandTemplate.xlsx");
				workBook.write(outPutStream);
			}
			outPutStream.flush();
			outPutStream.close();
		} catch (Exception e) {
			logger.info(e.getMessage());
		}
	}*/

    /**
     * @param @param  strFormula
     * @param @param  firstRow   起始行
     * @param @param  endRow     终止行
     * @param @param  firstCol   起始列
     * @param @param  endCol     终止列
     * @param @return
     * @return HSSFDataValidation
     * @throws
     * @Title: setDataValidation
     * @Description: 下拉列表元素很多的情况 (255以上的下拉)
     */
    private XSSFDataValidation setDataValidation(XSSFSheet sheet, int firstRow, int endRow,
                                                 int firstCol, int endCol, String[] datas) {

        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
        XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper
                .createExplicitListConstraint(datas);
        XSSFDataValidation validation = null;
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        validation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, addressList);
        validation.createErrorBox("数据校验", "只能为下拉框范围内的值！");
        validation.setEmptyCellAllowed(false);
        validation.setShowErrorBox(true);
        return validation;
    }

    private String[] changeStrings(List<Map<String, String>> list) {

        String[] datas = new String[list.size()];
        int i = 0;
        for (Map<String, String> map : list) {
            datas[i] = map.get("text");
            i++;
        }
        return datas;
    }


    /**
     * @param request
     * @return
     * @Title: findNotRelevanceUser
     * @Description: 查询未关联用户
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @RequestMapping(value = "/findNotRelevanceUser", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> findNotRelevanceUser(HttpServletRequest request) {
        String name = request.getParameter("name");

        String rows = request.getParameter("rows");// 默认为10
        if (rows == null || "".equals(rows))
            rows = "10000";
        String page = request.getParameter("page");// 默认为1
        if (page == null || "".equals(page))
            page = "1";
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        String userGroupReourceID = request.getParameter("userGroupReourceID");
        String testSystemResourceID = request.getParameter("testSystemResourceID");
        String demandResourceID = request.getParameter("demandResourceID");
        String deptResourceID = request.getParameter("deptResourceID");
        return demandService.findDemandNotRelevanceUser(name, testSystemResourceID, demandResourceID, userGroupReourceID,deptResourceID, pageRequest);
    }

    /**
     *
     * @Title: findRelevanceSystem
     * @Description: 查询已关联系统
     * @param request
     * @return
     * <AUTHOR>
     * @date 2019年12月04日
     */
	/*@RequestMapping(value = "/findRelevanceSystem", method = RequestMethod.POST)
	@ResponseBody
	public Result<?> findRelevanceSystem(HttpServletRequest request) {

		String id = request.getParameter("id");
		List<Map<String,Object>> resultList=demandService.findRelevanceSystem(id);

		return Result.renderSuccess(resultList);
	}*/

    /**
     * @param request
     * @return
     * @Title: findRelevanceUser
     * @Description: 查询已关联用户
     * <AUTHOR>
     * @date 2019年12月04日 下午9:28:38
     */
    @RequestMapping(value = "/findRelevanceUser", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> findRelevanceUser(HttpServletRequest request) {
        String name = request.getParameter("name");

        String rows = request.getParameter("rows");// 默认为10
        if (rows == null || "".equals(rows))
            rows = "10000";
        String page = request.getParameter("page");// 默认为1
        if (page == null || "".equals(page))
            page = "1";
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        String testSystemResourceID = request.getParameter("testSystemResourceID");
        String userGroupReourceID = request.getParameter("userGroupReourceID");
        String demandResourceID = request.getParameter("demandResourceID");
        String deptResourceID = request.getParameter("deptResourceID");
        return demandService.findDemandRelevanceUser(name, testSystemResourceID, demandResourceID, userGroupReourceID,deptResourceID, pageRequest);
    }

    /**
     * @param request
     * @return
     * @Title: findUserBySystem
     * @Description: 根据系统查人员
     * <AUTHOR>
     * @date 2019年12月04日 下午9:28:38
     */
    @RequestMapping(value = "/findUserBySystem", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> findUserBySystem(HttpServletRequest request) {

        String id = request.getParameter("id");
        return testSystemService.findUserBySystem(id);
    }


    /**
     * @param request
     * @return
     * @Title: addDemandUser
     * @Description: 给需求关联人员
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @RequestMapping(value = "/addDemandUser", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "关联需求人员",tags = "关联需求人员")
    public Result<?> addDemandUser(HttpServletRequest request) {

        UserVo user = loginUserUtil.getLoginUser(request);
        String demandId = request.getParameter("demandId");
        String ids = request.getParameter("ids");
        String role = request.getParameter("role");
        String msg = "";
        try {
            msg = demandService.addDemandUser(demandId, ids, role, user.getUserNumber());
        } catch (Exception e) {
            logger.info("关联人员失败或者通知发送失败！！");
            return Result.renderError("需求关联人员失败！");
        }
        return Result.renderSuccess(msg);
    }
    /**
     *
     *@Description 多个需求关联一个人
     *@param
     *@return Result<?>
     *<AUTHOR>
     *@Date 2023年1月18日
     */
    @RequestMapping(value = "/moreDemandAddDemandUser", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "关联需求人员",tags = "关联需求人员")
    public Result<?> moreDemandAddDemandUser(HttpServletRequest request) {

        UserVo user = loginUserUtil.getLoginUser(request);
        String demandId = request.getParameter("demandId");
        String ids = request.getParameter("ids");
        String role = request.getParameter("role");
        String msg = "";
        try {
            msg = demandService.moreDemandAddDemandUser(demandId, ids, role, user.getUserNumber());
        } catch (Exception e) {
            logger.info("关联人员失败或者通知发送失败！！");
            return Result.renderError("需求关联人员失败！");
        }
        return Result.renderSuccess(msg);
    }

    /**
     * @param request
     * @return
     * @Title: addDemandSystem
     * @Description: 给需求关联系统
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @RequestMapping(value = "/addDemandSystem", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> addDemandSystem(HttpServletRequest request, @RequestParam List<String> testSystemResourceIDs) {

        UserVo user = loginUserUtil.getLoginUser(request);
        String demandId = request.getParameter("demandId");
        String ids = request.getParameter("ids");
        String msg = demandService.addDemandSystem(testSystemResourceIDs, demandId, ids, user.getUserNumber());
        return Result.renderSuccess(msg);
    }

    /**
     * @param request
     * @return
     * @Title: findNotRelevanceSystem
     * @Description: 查询未关联系统
     */
    @RequestMapping(value = "/getNotRelevanceSystem", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> getNotRelevanceSystem(HttpServletRequest request) {
        String resourceID = request.getParameter("demandResourceID");
        String name = request.getParameter("name");
        if (name == null) {
            name = "";
        }

        if (StringUtils.isEmpty(resourceID)) {
            return Result.renderError("入参resourceID不能为空！");
        }
        List<Map<String, Object>> result = demandService.getNotRelevanceSystem(name, resourceID);
        return Result.renderSuccess(result);
    }

    /**
     * @param request
     * @return
     * @Title: removeDemandSystem
     * @Description: 移除需求关联系统
     */
    @RequestMapping(value = "/removeDemandSystem", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> removeDemandSystem(HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String demandResourceID = request.getParameter("demandResourceID");
        String ids = request.getParameter("ids");
        String msg = demandService.removeDemandSystem(demandResourceID, ids, user.getUserName());
        return Result.renderSuccess(msg);
    }

    /**
     * @param request
     * @return
     * @Title: removeDemandUser
     * @Description: 删除关联人员
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @RequestMapping(value = "/removeDemandUser", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> removeDemandUser(HttpServletRequest request) {

        UserVo user = loginUserUtil.getLoginUser(request);
        String demandId = request.getParameter("demandId");
        String rIds = request.getParameter("rIds");
        //add需求人员关联表的resourceIDs
        String duResourceIDs = request.getParameter("duResourceIDs");
        return demandService.removeDemandUser(demandId, rIds, user.getUserNumber(), duResourceIDs);

    }


    /**
     * @param request
     * @return
     * @Title: findNotRelevanceSystem
     * @Description: 查询已关联系统
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @RequestMapping(value = "/findRelevanceSystem", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> findRelevanceSystem(HttpServletRequest request) {

        String resourceID = request.getParameter("demandResourceID");
        if (StringUtils.isEmpty(resourceID)) {
            return Result.renderError("入参resourceID不能为空！");
        }
        List<Map<String, Object>> result = demandService.findRelevanceSystem(resourceID);
        return Result.renderSuccess(result);
    }

    /**
     * @param request
     * @return
     * @Title: findRelevanceSystemID
     * @Description: 已关联系统ID
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @RequestMapping(value = "/findRelevanceSystemID", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> findRelevanceSystemID(HttpServletRequest request) {

        String resourceID = request.getParameter("demandResourceID");
        if (StringUtils.isEmpty(resourceID)) {
            return Result.renderError("入参resourceID不能为空！");
        }
        List<String> result = testSystemService.findRelevanceSystemID(resourceID);
        return Result.renderSuccess(result);
    }


    /**
     * @param request
     * @return
     * @Title: findRelevanceSystem
     * @Description: 查询所有系统
     * <AUTHOR>
     * @date 2019年12月04日
     */
    @RequestMapping(value = "/findAllSystem", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> findAllSystem(HttpServletRequest request) {
        String name = request.getParameter("name");
        if (name == null) {
            name = "";
        }
        String resourceID = request.getParameter("resourceID");
        if (StringUtils.isEmpty(resourceID)) {
            return Result.renderError("入参resourceID不能为空！");
        }
        List<Map<String, Object>> data = demandService.findAllSystem(name, resourceID);
        return Result.renderSuccess(data);
    }

    /**
     * @param request
     * @Title removeUserFromDemand
     * @Description 从需求中批量移除人员
     * <AUTHOR>
     */

    @RequestMapping(value = "/removeUserFromDemand", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> removeUserFromDemand(HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String demandResIDs = request.getParameter("demandResIDs");
        if (StringUtils.isEmpty(demandResIDs)) {
            return Result.renderError("参数demandResIDs为空！");
        }
        String result = demandService.removeUserFromDemand(demandResIDs, user);
        if ("SUCCESS".equals(result)) {
            return Result.renderSuccess();
        } else {
            return Result.renderError(result);
        }

    }

    /**
     * @param
     * @return com.jettech.dto.Result<?>
     * @Description 查询未上线的需求, 缺陷管理新增缺陷使用
     * <AUTHOR>
     * @date 2019-12-12 20:07
     */
    @GetMapping("queryNotOnLineDemand")
    @ResponseBody
    public Result<?> queryNotOnLineDemand() {
        return demandService.queryNotOnLineDemand();
    }

    /**
     * @param
     * @return Result<?>
     * @Title:findUserByDeptName
     * @Description:查询项目经理
     * @author: wu_yancheng
     * @date 2019年12月12日下午7:48:09
     */
    @RequestMapping(value = "/searchManagementUser", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> searchManagementUser(HttpServletRequest request) {
        String name = request.getParameter("name");
        String page = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        String token = HttpRequestUtils.getCurrentRequestToken();
        return feignDataDesignToBasicService.findUserByDeptName(page, pageSize, name, token);
    }


    @PostMapping("/createFives/{userResourceID}")
    public @ResponseBody
    Result createFives(@PathVariable String userResourceID) {
        return demandService.createFives(userResourceID);
    }

    /**
     * @Title: findDemandsNotOnLine
     * @Description: findDemandsNotOnLine
     * @Param: "[]"
     * @Return: "com.jettech.dto.Result"
     * @Author: li_yajiao
     * @Date: 2019/12/25
     */
    @PostMapping("/findDemandsNotOnLine")
    public @ResponseBody
    Result findDemandsNotOnLine() {
        List<Demand> all = demandService.findDemandsNotOnLine();
        ArrayList<Object> list = new ArrayList<>();
        for (Demand demand : all) {
            HashMap map = new HashMap<>();
            map.put("resourceID", demand.getResourceID());
            map.put("name", demand.getNumber() + "---" + demand.getName());
            list.add(map);
        }
        return Result.renderSuccess(list);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:
     * @Description:
     * @author: wu_yancheng
     * @date 2019年12月4日下午4:22:25
     */
    @RequestMapping(value = "/searchLevel", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> searchLevel(HttpServletRequest request) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<Map<String, Object>> list = feignDataDesignToBasicService.findValueAndTextNameByInfoName("需求级别", token);
        return Result.renderSuccess(list);
    }

    /**
     * @param
     * @return Result<?>
     * @Title:findUserByName
     * @Description:查询项需求提出人
     * @author: wu_yancheng
     * @date 2019年12月12日下午7:48:09
     */
    @RequestMapping(value = "/searchDemandUser", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> searchDemandUser(HttpServletRequest request) {
        String name = request.getParameter("name");
        String page = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        String token = HttpRequestUtils.getCurrentRequestToken();
        return feignDataDesignToBasicService.findUserByName(page, pageSize, name, token);
    }

    /**
     * @param request
     * @Title exportDemandTemplate
     * @Description 需求模板导出
     * <AUTHOR>
     */
    @SuppressWarnings("resource")
    @RequestMapping(value = "/exportDemandTemplate", method = RequestMethod.POST)
    @ResponseBody
    public void imppot(HttpServletResponse response) {
        List<String> getdatas = getdatas("CentralizedBusinessDepartment");
        List<String> provNameList = new ArrayList<String>();

        for (String string : getdatas) {
            provNameList.add(string);
        }
        Map<String, List<String>> siteMap = new HashMap<String, List<String>>();
		/*DataDicDirectoryDTO dataDicDirectory = feignDataDesignToBasicService.findByDirNameFromDataDesign("所属部落");
		List<Map<String,Object>> findNameAndInfoNameByDirRid = feignDataDesignToBasicService.findNameAndInfoNameByDirRid(String.valueOf(dataDicDirectory.getResourceID()));
		List<String> getdatas2 =new ArrayList<>();
		for (int i = 0; i < findNameAndInfoNameByDirRid.size(); i++) {
			Map<String, Object> map = findNameAndInfoNameByDirRid.get(i);
			String string = map.get("text").toString();
			getdatas2.add(string);
		}

		for (String string : provNameList) {
			siteMap.put(string, getdatas2);
		}
		for (String string : getdatas2) {
			List<DataDictionaryDTO> dataDictionaries = feignDataDesignToBasicService.findByInfoNameFromDataDesign(string);
			List<String> arrayList = new ArrayList<>();
			for (DataDictionaryDTO dataDictionary : dataDictionaries) {
				String textName = dataDictionary.getTextName();
				arrayList.add(textName);
			}
			siteMap.put(string, arrayList);
		}*/

        //生成工作簿和存放对应数据的辅助表单
        /*XSSFWorkbook workBook = null;*/
        Workbook book = new HSSFWorkbook();
		/*Sheet hideSheet = book.createSheet("site_sheet");
		book.setSheetHidden(book.getSheetIndex(hideSheet), false);

		int rowId = 0;
		Row proviRow = hideSheet.createRow(rowId++);
		proviRow.createCell(0).setCellValue("部落列表");
		//在第一行添加省信息
		for (int i = 0; i < provNameList.size(); ++i)
		{
			Cell proviCell = proviRow.createCell(i+1);
			proviCell.setCellValue(provNameList.get(i));
		}

		Iterator<String> keyIterator = siteMap.keySet().iterator();
		//接下来每行都是第一个是父区域，后面跟着父区域的下一级子区域
		while (keyIterator.hasNext())
		{
			String key = keyIterator.next();
			List<String> son = siteMap.get(key);

			Row row = hideSheet.createRow(rowId++);
			row.createCell(0).setCellValue(key);
			for (int i = 0; i < son.size(); ++i)
			{
				Cell cell = row.createCell(i + 1);
				cell.setCellValue(son.get(i));
			}

			//每一行都添加名称管理器
			String range = getRange(1, rowId, son.size());
			Name name = book.createName();
			name.setNameName(key);
			String formula = "site_sheet!" + range;
			name.setRefersToFormula(formula);
		}*/
        CellStyle createCellStyle = book.createCellStyle();
        Font font = book.createFont();
        font.setFontName("宋体");
        font.setBold(true);//粗体显示
        font.setFontHeightInPoints((short) 12);
        createCellStyle.setFont(font);
        // book.setSheetHidden(book.getSheetIndex(hideSheet), false);
        //生成和用户交互的表单
        /*XSSFSheet sheet = workBook.getSheetAt(0);*/
        Sheet hidden7 = book.createSheet("hidden7");//生成一个空白表，不然需求页会展示不出来数据；新增空白表再点击开也可以解决
        book.setSheetHidden(book.getSheetIndex(hidden7), true);//设置空白表隐藏
        Sheet sheet1 = book.createSheet("需求");
        Row row0 = sheet1.createRow(0);
        row0.createCell(0).setCellValue("*需求编号");
        row0.createCell(1).setCellValue("*需求名称");
        row0.createCell(2).setCellValue("*需求状态");
        List<String> getdatas3 = getdatas("DemandState");
//1.创建隐藏的sheet页。
        Sheet hidden5 = book.createSheet("hidden5");
        //2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        for (int i = 0, length = getdatas3.size(); i < length; i++) {
            hidden5.createRow(1000 + i).createCell(2).setCellValue(getdatas3.get(i));
        }
        Name category5Name = book.createName();
        category5Name.setNameName("hidden5");
        //3A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        category5Name.setRefersToFormula("hidden5" + "!A1:A" + (getdatas3.size() + 1000));
        //加载叫做“hidden”这个sheet的数据
        DVConstraint constraint5 = DVConstraint.createFormulaListConstraint("hidden5");
        //起始行 终止行 起始列 终止列
        CellRangeAddressList addressList5 = new CellRangeAddressList(1, 1000, 2, 2);
        DataValidation provinceDataValidation1 = new HSSFDataValidation(addressList5, constraint5);
        provinceDataValidation1.createErrorBox("error", "请选择正确需求状态");
        //隐藏sheet的地方
        book.setSheetHidden(book.getSheetIndex(hidden5), true);
        sheet1.addValidationData(provinceDataValidation1);
//		String [] datas=new String[getdatas3.size()];
//		for (int i = 0; i < getdatas3.size(); i++) {
//			datas[i]=getdatas3.get(i);
//		}
//		DVConstraint trpUserSex = DVConstraint.createExplicitListConstraint(datas);
//		CellRangeAddressList trpUserSexlist = new CellRangeAddressList(1,1000,2,2);
//		DataValidation provinceDataValidation1 = new HSSFDataValidation(trpUserSexlist,trpUserSex );
//		provinceDataValidation1.createErrorBox("error", "请选择正确需求状态");
//		sheet1.addValidationData(provinceDataValidation1);
        row0.createCell(3).setCellValue("*需求级别");
        List<String> getdatas4 = getdatas("DemandLevel");
        //1.创建隐藏的sheet页。
        Sheet hidden4 = book.createSheet("hidden4");
        //2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        for (int i = 0, length = getdatas4.size(); i < length; i++) {
            hidden4.createRow(1000 + i).createCell(3).setCellValue(getdatas4.get(i));
        }
        Name category4Name = book.createName();
        category4Name.setNameName("hidden4");
        //3A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        category4Name.setRefersToFormula("hidden4" + "!A1:A" + (getdatas4.size() + 1000));
        //加载叫做“hidden”这个sheet的数据
        DVConstraint constraint4 = DVConstraint.createFormulaListConstraint("hidden4");
        //起始行 终止行 起始列 终止列
        CellRangeAddressList addressList4 = new CellRangeAddressList(1, 1000, 3, 3);
        DataValidation provinceDataValidation2 = new HSSFDataValidation(addressList4, constraint4);
        provinceDataValidation2.createErrorBox("error", "请选择正确需求级别");
        //隐藏sheet的地方
        book.setSheetHidden(book.getSheetIndex(hidden4), true);
        sheet1.addValidationData(provinceDataValidation2);
        row0.createCell(4).setCellValue("*需求提出人");
		/*List<String> getdatas5 = getdatass();
		String[] datas5 = getdatas5.toArray(new String[getdatas5.size()]);
		if(datas5 != null && datas5.length > 0) {
			XSSFDataValidation validation = setDataValidation(sheet,1,65536,7,7,datas5);
			sheet1.addValidationData(validation);
		}*/
        List<String> getdatas5 = getdatass();

        //1.创建隐藏的sheet页。
        Sheet hidden = book.createSheet("hidden");
        //2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        for (int i = 0, length = getdatas5.size(); i < length; i++) {
            hidden.createRow(1000 + i).createCell(4).setCellValue(getdatas5.get(i));
        }
        Name category1Name = book.createName();
        category1Name.setNameName("hidden");
        //3A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        category1Name.setRefersToFormula("hidden" + "!A1:A" + (getdatas5.size() + 1000));
        //加载叫做“hidden”这个sheet的数据
        DVConstraint constraint = DVConstraint.createFormulaListConstraint("hidden");
        //起始行 终止行 起始列 终止列
        CellRangeAddressList addressList = new CellRangeAddressList(1, 1000, 4, 4);
        DataValidation provinceDataValidation3 = new HSSFDataValidation(addressList, constraint);
        provinceDataValidation3.createErrorBox("error", "请选择正确需求提出人");
        //隐藏sheet的地方
        book.setSheetHidden(book.getSheetIndex(hidden), true);

        //最后把数据装到真实sheet的下拉
        sheet1.addValidationData(provinceDataValidation3);


		/*String [] datas2=new String[getdatas5.size()];
		for (int i = 0; i < getdatas5.size(); i++) {
			datas2[i]=getdatas5.get(i);
		}
		DVConstraint all = DVConstraint.createExplicitListConstraint(datas2);
		CellRangeAddressList allpersonlist = new CellRangeAddressList(1,1000,4,4);
		DataValidation provinceDataValidation3 = new HSSFDataValidation(allpersonlist,all );
		provinceDataValidation3.createErrorBox("error", "请选择正确需求提出人");
		sheet1.addValidationData(provinceDataValidation3);*/
        row0.createCell(5).setCellValue("需求项目经理");
        List<String> getdatas6 = getdatasss();
        //2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        Sheet hidden1 = book.createSheet("hidden1");
        for (int i = 0, length = getdatas6.size(); i < length; i++) {
            hidden1.createRow(1000 + i).createCell(5).setCellValue(getdatas6.get(i));
        }
        //3A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        Name category1Name1 = book.createName();
        category1Name1.setNameName("hidden1");
        category1Name1.setRefersToFormula("hidden1" + "!A1:A" + (getdatas6.size() + 1000));
        //加载叫做“hidden”这个sheet的数据
        DVConstraint constraint1 = DVConstraint.createFormulaListConstraint("hidden1");
        //起始行 终止行 起始列 终止列
        CellRangeAddressList addressList1 = new CellRangeAddressList(1, 1000, 5, 5);
        DataValidation provinceDataValidation4 = new HSSFDataValidation(addressList1, constraint1);
        provinceDataValidation4.createErrorBox("error", "请选择正确需求项目经理");
        //隐藏sheet的地方
        book.setSheetHidden(book.getSheetIndex(hidden1), true);

        //最后把数据装到真实sheet的下拉
        sheet1.addValidationData(provinceDataValidation4);

		/*String [] datas3=new String[getdatas6.size()];
		for (int i = 0; i < getdatas6.size(); i++) {
			datas3[i]=getdatas6.get(i);
		}
		DVConstraint allPerson = DVConstraint.createExplicitListConstraint(datas3);
		CellRangeAddressList allTypePersonList = new CellRangeAddressList(1,1000,5,5);
		DataValidation provinceDataValidation4 = new HSSFDataValidation(allTypePersonList,allPerson );
		provinceDataValidation4.createErrorBox("error", "请选择正确需求项目经理");
		sheet1.addValidationData(provinceDataValidation4);*/
        row0.createCell(6).setCellValue("*业务归口部门");
//1.创建隐藏的sheet页。
        Sheet hidden6 = book.createSheet("hidden6");
        //2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        for (int i = 0, length = provNameList.size(); i < length; i++) {
            hidden6.createRow(1000 + i).createCell(6).setCellValue(provNameList.get(i));
        }
        Name category6Name = book.createName();
        category6Name.setNameName("hidden6");
        //3A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        category6Name.setRefersToFormula("hidden6" + "!A1:A" + (provNameList.size() + 1000));
        //加载叫做“hidden”这个sheet的数据
        DVConstraint constraint6 = DVConstraint.createFormulaListConstraint("hidden6");
        //起始行 终止行 起始列 终止列
        CellRangeAddressList addressList6 = new CellRangeAddressList(1, 1000, 6, 6);
        DataValidation provinceDataValidation6 = new HSSFDataValidation(addressList6, constraint6);
        provinceDataValidation6.createErrorBox("error", "请选择正确业务归口部门");
        //隐藏sheet的地方
        book.setSheetHidden(book.getSheetIndex(hidden6), true);
        sheet1.addValidationData(provinceDataValidation6);
//		DVConstraint business  = DVConstraint.createExplicitListConstraint(provNameList.toArray(new String[]{}));
//        CellRangeAddressList businesslist = new CellRangeAddressList(1,1000,6,6);
//        DataValidation provinceDataValidation5 = new HSSFDataValidation(businesslist,business  );
//        provinceDataValidation5.createErrorBox("error", "请选择正确业务归口部门");
//        sheet1.addValidationData(provinceDataValidation5);
		/*String [] datas5=new String[getdatas.size()];
		for (int i = 0; i < getdatas.size(); i++) {
			datas5[i]=getdatas.get(i);
		}*/

        row0.createCell(7).setCellValue("*所属项目");
        List<TestProject> list = testProjectService.findAll();
        List<String> projectNameList = list.stream().map(TestProject::getName).collect(Collectors.toList());

		/*String [] projectdatas=new String[projectNameList.size()];
		for (int i = 0; i < projectNameList.size(); i++) {
			projectdatas[i]=projectNameList.get(i);
		}
		DVConstraint p_dvc = DVConstraint.createExplicitListConstraint(projectdatas);
		CellRangeAddressList p_crlist = new CellRangeAddressList(1,1000,7,7);
		DataValidation p_dv = new HSSFDataValidation(p_crlist,p_dvc );
		p_dv.createErrorBox("error", "请选择所属项目");
		sheet1.addValidationData(p_dv);*/
        Sheet hidden2 = book.createSheet("hidden2");
        for (int i = 0, length = projectNameList.size(); i < length; i++) {
            hidden2.createRow(1000 + i).createCell(7).setCellValue(projectNameList.get(i));
        }
        //3A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        Name category1Name2 = book.createName();
        category1Name2.setNameName("hidden2");
        category1Name2.setRefersToFormula("hidden2" + "!A1:A" + (projectNameList.size() + 1000));
        //加载叫做“hidden”这个sheet的数据
        DVConstraint p_dvc = DVConstraint.createFormulaListConstraint("hidden2");
        //起始行 终止行 起始列 终止列
        CellRangeAddressList p_crlist = new CellRangeAddressList(1, 1000, 7, 7);
        DataValidation p_dv = new HSSFDataValidation(p_crlist, p_dvc);
        p_dv.createErrorBox("error", "请选择正确所属项目");
        //隐藏sheet的地方
        book.setSheetHidden(book.getSheetIndex(hidden2), true);
        //最后把数据装到真实sheet的下拉
        sheet1.addValidationData(p_dv);

        //row0.createCell(8).setCellValue("主办小队");
        //row0.createCell(8).setCellValue("需求创建时间（年-月-日或年/月/日）");
        sheet1.setColumnWidth(0, 5304);
        sheet1.setColumnWidth(1, 5304);
        sheet1.setColumnWidth(2, 5304);
        sheet1.setColumnWidth(3, 5304);
        sheet1.setColumnWidth(4, 5304);
        sheet1.setColumnWidth(5, 5304);
        sheet1.setColumnWidth(6, 5304);
        sheet1.setColumnWidth(7, 5304);
        //sheet1.setColumnWidth(8, 5304);
        row0.getCell(0).setCellStyle(createCellStyle);
        row0.getCell(1).setCellStyle(createCellStyle);
        row0.getCell(2).setCellStyle(createCellStyle);
        row0.getCell(3).setCellStyle(createCellStyle);
        row0.getCell(4).setCellStyle(createCellStyle);
        row0.getCell(5).setCellStyle(createCellStyle);
        row0.getCell(6).setCellStyle(createCellStyle);
        row0.getCell(7).setCellStyle(createCellStyle);
        //row0.getCell(8).setCellStyle(createCellStyle);
        // row0.getCell(9).setCellStyle(createCellStyle);
        //sheet1.setColumnWidth(9, 10424);
		/*String []teame=new String[getdatas2.size()];
		for (int i = 0; i < getdatas2.size(); i++) {
			teame[i]=getdatas2.get(i);
		}*/

        //设置每一个单元格都以前一个单元格的选择作为标准
		/*for (int i = 2; i <=1000;++i)
		{
			//部落规则
			DVConstraint formula =DVConstraint.createExplicitListConstraint(teame);
			CellRangeAddressList rangeAddressList = new CellRangeAddressList(i-1, i-1,7,7);
			DataValidation cacse = new HSSFDataValidation(rangeAddressList, formula);
			cacse.createErrorBox("error", "请选择正确的部落");
			sheet1.addValidationData(cacse);

			//小队规则
			formula = DVConstraint.createFormulaListConstraint("INDIRECT($H$" + i + ")");
			rangeAddressList = new CellRangeAddressList(i-1, i-1,8,8);
			cacse = new HSSFDataValidation(rangeAddressList, formula);
			cacse.createErrorBox("error", "请选择正确的小队");
			sheet1.addValidationData(cacse);
		}*/

        book.setActiveSheet(1);
        try {
            ServletOutputStream outPutStream = response.getOutputStream();
            if (book != null) {
                response.setHeader("Cache-Control", "private");
                response.setHeader("Pragma", "private");
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                response.setHeader("Content-Type", "application/force-download");
                response.setHeader("content-disposition", "attachment;filename=exportDemandTemplate.xls");
                book.write(outPutStream);
            }
            outPutStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private List<String> getdatas(String names) {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<DataDictionaryDTO> findByName = feignDataDesignToBasicService.findByNamesFromDataDesign(names, token);
        String[] datas = new String[findByName.size()];
        for (int i = 0; i < findByName.size(); i++) {
            DataDictionaryDTO dataDictionary = findByName.get(i);
            datas[i] = dataDictionary.getTextName();
        }
        List<String> provNameList = new ArrayList<String>();
        for (String string : datas) {
            provNameList.add(string);
        }
        return provNameList;
    }

    private List<String> getdatass() {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<JettechUserDTO> findAllPerson = feignDataDesignToBasicService.findAllPerson(token);
        String[] datas = new String[findAllPerson.size()];
        for (int i = 0; i < findAllPerson.size(); i++) {
            JettechUserDTO jettechUserDTO = findAllPerson.get(i);
            datas[i] = jettechUserDTO.getUserName();
        }
        List<String> provNameList = new ArrayList<String>();
        for (String string : datas) {
            provNameList.add(string);
        }
        return provNameList;
    }

    private List<String> getdatasss() {
        String token = HttpRequestUtils.getCurrentRequestToken();
        List<JettechUserDTO> findTypeAllUser = feignDataDesignToBasicService.findTypeAllPersons(token);
        String[] datas = new String[findTypeAllUser.size()];
        for (int i = 0; i < findTypeAllUser.size(); i++) {
            JettechUserDTO jettechUserDTO = findTypeAllUser.get(i);
            datas[i] = jettechUserDTO.getUserName();
        }
        List<String> provNameList = new ArrayList<String>();
        for (String string : datas) {
            provNameList.add(string);
        }
        return provNameList;
    }

    /**
     * @param offset   偏移量，如果给0，表示从A列开始，1，就是从B列
     * @param rowId    第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     * <AUTHOR> 2019年12月31日 下午5:17:49
     */
    public static String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        if (colCount <= 25) {
            char end = (char) (start + colCount - 1);
            return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
        } else {
            char endPrefix = 'A';
            char endSuffix = 'A';
            if ((colCount - 25) / 26 == 0 || colCount == 51) {// 26-51之间，包括边界（仅两次字母表计算）
                if ((colCount - 25) % 26 == 0) {// 边界值
                    endSuffix = (char) ('A' + 25);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                }
            } else {// 51以上
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26);
                }
            }
            return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
        }
    }

    /**
     * @param demandResourceID
     * @return Result<?>
     * @Title findDemandRelevanceUser
     * @Description 根据需求resourceID查询需求已关联的人员
     * <AUTHOR>
     * @data Jan 8, 20203:53:42 PM
     */
    @RequestMapping(value = "/findDemandRelevanceUser/{demandResourceID}", method = RequestMethod.GET)
    @ResponseBody
    public Result<?> findDemandRelevanceUser(@PathVariable("demandResourceID") String demandResourceID) {
        if (demandResourceID == null || "".equals(demandResourceID)) {
            logger.debug("传入的需求主键为空!");
            return Result.renderError("传入的需求主键为空!");
        }
        List<Map<String, Object>> resultList = demandService.findDemandRelevanceUser(demandResourceID);

        return Result.renderSuccess(resultList);
    }

    @Autowired
    private HttpServletRequest request;

    @PostMapping("/updateTypeAndTypename")
    @ResponseBody
    public void updateDemandTypeAndTypename(HttpServletRequest request,String demandResourceID,  String type,String typename) {
    	UserVo user = loginUserUtil.getLoginUser(request);
        Demand updateDemand = demandService.findByResourceID(Long.parseLong(demandResourceID));
        DemandChangeNodes demandChangeNodes = new DemandChangeNodes();
        demandChangeNodes.setDemandResourceID(updateDemand.getResourceID());
        demandChangeNodes.setBeforeChangeType(updateDemand.getType());
        demandChangeNodes.setAfterChangeType(type);
       // iDemandChangeNodesService.save(demandChangeNodes, loginUserUtil.getLoginUser(request).getUserNumber());
        iDemandChangeNodesService.save(demandChangeNodes, user.getUserNumber());

        updateDemand.setType(type);
        updateDemand.setTypename(typename);
//        Demand demand = new Demand();
//        demand.setType(type);
//        demand.setTypename(typename);
//        demand.setResourceID(Long.parseLong(demandResourceID));
     //   demandService.updateByResourceID(demand, loginUserUtil.getLoginUser(request).getUserNumber());
        demandService.updateByResourceID(updateDemand, user.getUserNumber());
    }

    @GetMapping("/findDemand")
    @ResponseBody
    public Map<String, Object> findDemand(String demandResourceID) {
        Demand demand = demandService.findByResourceID(Long.parseLong(demandResourceID));
        Map<String, Object> resMp = new HashMap<>();
        if (demand!=null) {
            BeanMap beanMap = BeanMap.create(demand);
            beanMap.forEach((k, v) -> {
                resMp.put((String) k, v);
            });
        }
        return resMp;
    }

    /**
     * @param "[demandResourceID, testEnviroment]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: testPlanRotationPie
     * @description: 报表需求中测试计划轮次占比图(sit, uat)
     * <AUTHOR>
     * @date 2020/2/10 17:27
     */
    @GetMapping("/testPlanRotationPie/{demandResourceID}/{testEnviroment}")
    public Result testPlanRotationPie(@PathVariable String demandResourceID,
                                      @PathVariable String testEnviroment) {
        return demandService.testPlanRotationPie(demandResourceID, testEnviroment);
    }


    @PostMapping("/testVenture")
    @ResponseBody
    public Result testVenture(HttpServletRequest request) {
        String demandResourceID = request.getParameter("demandResourceID");
        return demandService.testVenture(demandResourceID);
    }


    /**
     * @param resourceIDs:传递过来的需求ids字符串 多个用,分隔
     * @param request:
     * @param response:
     * @return void
     * @Description:需求导出excel
     * <AUTHOR>
     * @date 2020/2/19
     */
    @PostMapping("/exportExcel")
    @ResponseBody
    public void export(@RequestParam("resourceIDs") String resourceIDs, HttpServletRequest request, HttpServletResponse response) {
        List<Map<String, String>> demandList = null;
        //查询出所有选中的需求
        if (StringUtils.isEmpty(resourceIDs)) {
            demandList = new ArrayList<>();
            String name = request.getParameter("name");
            String number = request.getParameter("number");
            String typename = request.getParameter("typename");
            //需求级别，可多选
            String level = request.getParameter("level");
            String testProjectResourceID = request.getParameter("testProjectResourceID");
            String testSystem = request.getParameter("testSystem");
            String projectManagerName = request.getParameter("projectManagerName");
            String testManagerName = request.getParameter("testManagerName");

            String page = request.getParameter("page");// 默认为1
            if (page == null || "".equals(page))
                page = "1";
            String order = request.getParameter("order");
            if (order == null || "".equals(order))
                order = "createTime";
            String sort = request.getParameter("sort");
            if (sort == null || "".equals(sort))
                sort = "desc";
            PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.MAX_VALUE, Sort.Direction.fromString(sort), order);
            Page<Map<String, Object>> demandPage = demandService.findDemandPage(pageRequest, name, number, typename, testProjectResourceID, testSystem, projectManagerName, testManagerName, level);
            List<Map<String, Object>> content = demandPage.getContent();

            for (Map<String, Object> stringObjectMap : content) {
                String resourceID = stringObjectMap.get("resourceID").toString();
                resourceIDs += resourceID + ",";
            }
            if (StringUtils.isEmpty(resourceIDs)) {
                demandList = demandService.findDemandIDs("0");
            } else {
                demandList = demandService.findDemandIDs(resourceIDs);
            }

        } else {
            demandList = demandService.findDemandIDs(resourceIDs);
        }
        String selectedFields = request.getParameter("selectedFields");
        //封装集合里的key
        String[] keyName = {};
        ExcelUtils excelUtils = new ExcelUtils();
        // 显示的导出表的标题
        String title = "需求任务";
        //导出的文件名
        String fileName = "所有需求";
        //导出表内所需要的字段
        String[] rowName = selectedFields.split(",");
        //String[] rowName={"需求编号","需求名称","业务归口部门","需求状态","需求级别","需求提出人","主办部落","主办小队","需求项目经理"};
        try {
            excelUtils.exportExcel(title, title, rowName, rowName, demandList, fileName, response, request);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("批量导出需求任务失败");
            Result.renderError();
        }

    }

    @RequestMapping("/showExportFields")
    public @ResponseBody
    Result showExportFields(HttpServletRequest request) {

        HashMap<String, Object> map = new HashMap<>();
        ArrayList<Map<String, String>> selected = new ArrayList<>();
        ArrayList<Map<String, String>> unSelected = new ArrayList<>();
        Map<String, String> number = new HashMap<String, String>();
        number.put("label", "需求编号");
        number.put("key", "number");
        selected.add(number);
        Map<String, String> name = new HashMap<String, String>();
        name.put("label", "需求名称");
        name.put("key", "name");
        selected.add(name);
        Map<String, String> centralizedDepartment = new HashMap<String, String>();
        centralizedDepartment.put("label", "业务归口部门");
        centralizedDepartment.put("key", "centralizedDepartment");
        selected.add(centralizedDepartment);
        Map<String, String> type = new HashMap<String, String>();
        type.put("label", "需求状态");
        type.put("key", "type");
        selected.add(type);
        Map<String, String> level = new HashMap<String, String>();
        level.put("label", "需求级别");
        level.put("key", "level");
        selected.add(level);
        Map<String, String> proposerResourceID = new HashMap<String, String>();
        proposerResourceID.put("label", "需求提出人");
        proposerResourceID.put("key", "proposerResourceID");
        selected.add(proposerResourceID);
		/*Map<String, String> hostTribe = new HashMap<String, String>();
		hostTribe.put("label", "所属项目");
		hostTribe.put("key", "hostTribe");
		selected.add(hostTribe);
		Map<String, String> hostTeam = new HashMap<String, String>();
		hostTeam.put("label", "主办小队");
		hostTeam.put("key", "hostTeam");
		selected.add(hostTeam);*/
        Map<String, String> testProject = new HashMap<String, String>();
        testProject.put("label", "所属项目");
        testProject.put("key", "testProject");
        selected.add(testProject);
        Map<String, String> projectManagerResourceID = new HashMap<String, String>();
        projectManagerResourceID.put("label", "需求项目经理");
        projectManagerResourceID.put("key", "projectManagerResourceID");
        selected.add(projectManagerResourceID);
        Map<String, String> testManagerResourceID = new HashMap<String, String>();
        testManagerResourceID.put("label", "测试经理");
        testManagerResourceID.put("key", "testManagerResourceID");
        selected.add(testManagerResourceID);
        map.put("selected", selected);
        map.put("unSelected", unSelected);
        return Result.renderSuccess(map);
    }

    /**
     * 需求导入
     *
     * @param request
     * @return
     * @Method importExcel
     * @Descripation 导入需求Excel
     * <AUTHOR>
     */
    @PostMapping("/importExcel")
    @ResponseBody
    public Result<?> importExcel(MultipartFile file, HttpServletResponse response, HttpServletRequest request) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        UserVo createUser = loginUserUtil.getLoginUser(request);
        MultipartFile files = multipartRequest.getFile("files");
        Result<Object> result = new Result<>();
        try {
            return demandService.importDemandExcel(files, createUser);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("导入需求功能异常！");
        }
    }


    /**
     * 更新需求的状态（需求状态：未开始、准备中、测试中、已完成、特殊状态（部分上线、已上线、已暂停、已取消））
     *
     * @param @param demandResourceID    参数
     * @return void    返回类型
     * @throws
     * @Title: changeDemandType
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    @PostMapping("/changeDemandType")
    @ResponseBody
    public Result<?> changeDemandType(HttpServletRequest request) {
        Result<?> result = new Result<>();
        String demandResourceID = request.getParameter("demandResourceID");
        String userNumber = request.getParameter("userNumber");
        if (StringUtils.isEmpty(demandResourceID)) {
            result.setMsg("需求的ResourceID为空！");
            return result;
        }
        String str = demandService.changeDemandType(Long.valueOf(demandResourceID), userNumber);
        result.setMsg(str);
        return result;
    }

    /**
     * @Title: findAllDemandToTestTask
     * @Description: //在任务管理模块中，查询所有的需求数据
     * @Param: " [] "
     * @return: " com.jettech.dto.Result "
     * @throws:
     * @Author: xpp
     * @Date: 11:15 2020/6/9
     */
    @GetMapping("/findAllDemandToTestTask/{name}")
    @ResponseBody
    public Result findAllDemandToTestTask(@PathVariable String name) {
        return demandService.findAllDemandToTestTask(name);
    }

    /**
     * @Title: initTaskScopeLeftTree
     * @Description: //根据需求，初始化添加任务范围弹框左侧树
     * @Param: " [demandResourceID] "
     * @return: " com.jettech.dto.Result "
     * @throws:
     * @Author: xpp
     * @Date: 14:24 2020/6/10
     */
    @GetMapping("/initTaskScopeLeftTree/{demandResourceID}")
    @ResponseBody
    public Result initTaskScopeLeftTree(@PathVariable String demandResourceID) {
        return demandService.initTaskScopeLeftTree(demandResourceID);
    }

    /**
     * @param "[]"
     * @return java.util.List<com.jettech.model.Demand>
     * @throws
     * @Title: findByTestProjectResourceID
     * @description: 项目rid查询
     * <AUTHOR>
     * @date 2020/7/9 10:53
     */
    @GetMapping("/findByTestProjectResourceID/{projectResourceID}")
    @ResponseBody
    public Result<List<Demand>> findByTestProjectResourceID(@PathVariable("projectResourceID") String projectResourceID) {
        if (org.apache.commons.lang3.StringUtils.isBlank(projectResourceID)) return Result.renderError();
        List<Demand> list = demandService.findByTestProjectResourceID(Long.valueOf(projectResourceID));
        return Result.renderSuccess(list);
    }

    /***
     * 项目rid查询需求
     * @Method : initDemandTable
     * @Description : 项目rid查询需求
     * @param map : 项目rid
     * @return : com.jettech.dto.Result
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-15 星期三 10:18:24
     *
     */
    @PostMapping("/initDemandTable")
    @ResponseBody
    public Result initDemandTable(@RequestBody HashMap<String, Object> map) {
        return demandService.initDemandTable(map);
    }

    /**
     * @Method: findAllDemand
     * @Description: 查询所有需求
     * @Param: " [] "
     * @return: com.jettech.dto.Result<java.util.List < com.jettech.model.Demand>>
     * @Author: wws
     * @Date: 2020/7/28
     */
    @GetMapping("/findAllDemand")
    @ResponseBody
    public Result<List<Demand>> findAllDemand() {
        return Result.renderSuccess(demandService.findAll());
    }

    /**
     * 需求状态刷新（只更新---未开始、准备中、测试中、已完成---四种状态的需求）
     *
     * @param request userNumber
     * @return
     * <AUTHOR> && bao_qiuxu
     */
    @PostMapping("/refreshDemandType")
    @ResponseBody
    public Result<?> refreshDemandType(HttpServletRequest request) {
        UserVo userVo = loginUserUtil.getLoginUser(request);
        String resourceID = request.getParameter("resourceID");
        try {
            String result = demandService.refreshDemandType(userVo.getUserNumber(), resourceID);
            return Result.renderSuccess(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("需求状态刷新失败！");
        }
    }


    /**
     * 根据名称、编号或项目rid查询需求
     *
     * @param request
     * @return
     */
    @PostMapping("/findDemandItemByCondition")
    @ResponseBody
    public Result<?> findDemandItemByCondition(HttpServletRequest request) {
        String condition = request.getParameter("condition");
        String projectIDs = request.getParameter("projectIDs");
        String projectTypes = request.getParameter("projectTypes");
        String demandTypes = request.getParameter("demandTypes");
        List<Long> prjIDs = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(projectIDs)) {
            for (String s : projectIDs.split(",")) {
                prjIDs.add(Long.valueOf(s));
            }
        }
        List<DemandItemDto> list = demandService.findDemandItemByCondition(condition, prjIDs, projectTypes, demandTypes);
        return Result.renderSuccess(list);
    }

    /**
     * @Method: demandProjectDataDeal
     * @Description: 通过excel中的数据更新需求和项目的关联关系，更新缺陷数据
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/11/11
     */
    @GetMapping("/demandProjectDataDeal")
    @ResponseBody
    public Result demandProjectDataDeal() {
        try {
            return demandService.demandProjectDataDeal();
        } catch (IOException e) {
            e.printStackTrace();
            return Result.renderError();
        }
    }

    /**
      * 根据项目的业务主键查询项目关联的所有需求
      * <AUTHOR>
      *    10:18
      * @update
      * @param [request]     [request]说明
      * @return  com.jettech.dto.Result
      * @exception/throws [异常类型] [异常说明]
      * @see   [类、类#方法、类#成员]
      * @since [起始版本]
      */
    @PostMapping("/findDemandsByProjectResourceID")
    @ResponseBody
    public Result findDemandsByTestProjectResourceID(HttpServletRequest request){

        String projectResourceID =  request.getParameter("projectResourceID");
        if(StringUtils.isEmpty(projectResourceID)){
            return Result.renderError("项目参数为空！");
        }
        String key = RedisCacheConfig.FIND_DEMAND_BY_PROJECT;
        Object dlist = this.jedisUtil.getHsetValue(key, projectResourceID);
        List<Map<String,Object>> demandList = null;
        if(dlist == null)
        {
            demandList = demandService.findDemandsByTestProjectResourceID(projectResourceID);
            this.jedisUtil.setHash(key, projectResourceID, demandList, 10 * 60);
        }else{
            demandList = (List<Map<String,Object>>)dlist;
        }
        if(demandList.isEmpty()){
            return  Result.renderSuccess(new ArrayList<>());
        }
        return  Result.renderSuccess(demandList);
    }

    /**
     * 根据需求的业务主键查询需求的所属项目
     * <AUTHOR>
     *    10:18
     * @update
     * @param [request]     [request]说明
     * @return  com.jettech.dto.Result
     * @exception/throws [异常类型] [异常说明]
     * @see   [类、类#方法、类#成员]
     * @since [起始版本]
     */
    @PostMapping("/findProjectBydemandResourceID")
    @ResponseBody
    public Result findProjectBydemandResourceID(HttpServletRequest request){

        String demandResourceID =  request.getParameter("demandResourceID");
        UserVo userVo = loginUserUtil.getLoginUser(request);
        String userNumber = userVo.getUserNumber();
        if(StringUtils.isEmpty(demandResourceID)){
            return Result.renderError("需求参数为空！");
        }
        //实际上每个需求只可以所属一个项目
        List<Map<String,Object>> testProject = demandService.findProjectBydemandResourceID(demandResourceID,userNumber);
        if(testProject.isEmpty()){
            return  Result.renderSuccess(new ArrayList<>());
        }
        return  Result.renderSuccess(testProject);
    }

    /**
     * @param
     * @return Result<?>
     * @Title: createOrUpdateDemand
     * @Description:富滇新增或修改需求
     * @author: gao_zhenbao
     * @date 2021年1月18日上午11:23:04
     */
    @RequestMapping(value = "/createOrUpdateDemand", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "富滇新增或修改需求",tags = "富滇新增或修改需求")
    public Result<?> createOrUpdateDemand(HttpServletRequest request,@RequestBody JSONObject body) {
        UserVo createUser = new UserVo();
        createUser.setUserNumber("System");
        String list = body.getJSONArray("list").toJSONString();
        List<Map<String,String>> demandList = (List<Map<String,String>>) JSONArray.parse(list);
        return demandService.createOrUpdateDemand(demandList,createUser);
    }
}
