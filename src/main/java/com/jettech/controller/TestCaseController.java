package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.redis.JedisClient;
import com.jettech.common.redis.RedisDistributeLock;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.model.AtTestCase;
import com.jettech.service.iservice.ITestCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/testCase")
public class TestCaseController {

    @Autowired
    private ITestCaseService testCaseService;

    @Autowired
    private JedisClient jedisClient;

    /**
     * @Title: getMaxTestCaseCaseId
     * @Description: 案例资产库：根据最新要求返回最大的案例编号
     * @Param: "[tradeResourceID]"
     * @Return: "java.lang.String"
     * @Author: xpp
     * @Date: 2020/1/13
     */
    @GetMapping("/getMaxTestCaseCaseId/{tradeResourceID}")
    public Result getMaxTestCaseCaseId(@PathVariable("tradeResourceID") Long tradeResourceID) {
        return testCaseService.getMaxTestCaseCaseId(tradeResourceID);
    }

    /**
     * @Title: addTestCase
     * @Description: 新增案例
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @PostMapping()
    public Result addTestCase(HttpServletRequest request, @RequestBody AtTestCase testCase) {
        String requestID = UUID.randomUUID().toString().replaceAll("-", "");
        String userNumber = LoginUserUtil.getUserNumber(request);
        boolean tryGetLock = true;
        Long time = System.currentTimeMillis() + 3000;
        RedisDistributeLock lock = new RedisDistributeLock("addTestCase", 5, jedisClient);

        boolean locked = lock.lock();
        if (locked) {
            try {
                System.out.println("新增案例获取锁成功!请求号:\"" + requestID + "\"");
                Result result = testCaseService.addTestCase(testCase, userNumber);
                //boolean releaseDistributedLock = RedisTool.releaseDistributedLock(RedisPool.getJedis(), "addTestCase", requestID);
                return result;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                lock.release();
            }
        } else {
            System.out.println("新增案例获取锁失败!请求号:\"" + requestID + "\"");
            if (System.currentTimeMillis() > time) {
                return Result.renderError("超时!");
            }
        }

        return Result.renderError("服务器错误!");
    }

    /**
     * @Title: isNotRepeat
     * @Description: 判断案列编号是否重复
     * @Param: "[request, testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/21
     */
    @PostMapping("/isNotRepeat")
    public Result isNotRepeat(@RequestBody AtTestCase testCase) {
        return testCaseService.isNotRepeat(testCase);
    }

    /**
     * @Title: updateTestCase
     * @Description: 修改案例
     * @Param: "[testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @PutMapping()
    public Result updateTestCase(HttpServletRequest request, @RequestBody AtTestCase testCase) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        return testCaseService.updateTestCase(testCase, userNumber);
    }

    /**
     * @Title: findByTestCase
     * @Description: 查询案例详情
     * @Param: "[currentTestCaseResourceID]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @GetMapping("/{currentTestCaseResourceID}")
    public Result findByTestCase(@PathVariable String currentTestCaseResourceID) {
        return testCaseService.findByTestCase(currentTestCaseResourceID);
    }

    /**
     * @Title: deleteTestCase
     * @Description: 删除案例
     * @Param: "[iDList]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/4
     */
    @DeleteMapping("/{testCaseResourceIDs}")
    public Result deleteTestCase(HttpServletRequest request, @PathVariable String testCaseResourceIDs) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        String[] split = testCaseResourceIDs.split(",");
        List<String> iDList = Arrays.asList(split);
        return testCaseService.deleteTestCase(iDList, userNumber);
    }

    @GetMapping("/findCaseTotalBySelectedCase/{caseResourceID}")
    /**
     * @Title findCaseTotalBySelectedCase
     * @Description 根据选中的案例查询所属交易下的案例总数
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/28
     */
    public Result findCaseTotalBySelectedCase(@PathVariable String caseResourceID) {
        return testCaseService.findCaseTotalBySelectedCase(caseResourceID);

    }

    @GetMapping("/findTestCaseMapByResourceID/{caseResourceID}")
    /**
     * @Title findTestCaseMapByResourceID
     * @Description 查询单个案例
     * @Params [caseResourceID]
     * @Return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @Date 2019/11/29
     */
    public Result<?> findTestCaseMapByResourceID(@PathVariable String caseResourceID) {
        return testCaseService.findTestCaseMapByResourceID(caseResourceID);
    }

    @GetMapping("/findTestCaseMapByResourceIDFeginToDataDic/{caseResourceID}")
    /**
     * @Title findTestCaseMapByResourceIDFeginToDataDic
     */
    public Result<?> findTestCaseMapByResourceIDFeginToDataDic(@PathVariable String caseResourceID) {
        return testCaseService.findTestCaseMapByResourceIDFeginToDataDic(caseResourceID);
    }

    /**
     * @param
     * @return com.jettech.dto.Result<?>
     * @Description 查询单点案例左侧树
     * <AUTHOR>
     * @date 2019-11-06 18:38
     */
    @GetMapping("/SinglePointLeftTree")
    public Result<?> findSinglePointLeftTreeByProjectResourceID() {
        return testCaseService.findSinglePointLeftTreeByProjectResourceID();
    }


    /**
     * @param
     * @return Result
     * @Title:copyTestCase
     * @Description:复制需求案例至案例库
     * @author: wu_yancheng
     * @date 2019年12月9日下午5:15:49
     */
    @PostMapping("/copyTestCase")
    public Result<?> copyTestCase(@RequestBody Map<String, Object> map) {
        return testCaseService.copyTestCase(map);
    }

    /**
     * @param "[map]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findByTradeAndOptions
     * @description: 案例详情
     * <AUTHOR>
     * @date 2019/12/11 15:27
     */
    @PostMapping("/findByTradeAndOptions")
    public Result findByTradeAndOptions(@RequestBody HashMap map) {
        return testCaseService.findByTradeAndOptions(map);
    }


    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: updateTestCaseByTrade
     * @description: 洗数据
     * <AUTHOR>
     * @date 2019/12/26 12:54
     */
    @PostMapping("/updateTestCaseByTrade")
    public Result updateTestCaseByTrade() {
        try {
            return testCaseService.updateTestCaseByTrade();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("功能异常!");
        }
    }

    /**
     * @param "[testCaseRids]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: findByTestCaseResourceIDs
     * @description: 根据案例rids查询
     * <AUTHOR>
     * @date 2020/1/13 19:36
     */
    @PostMapping("/findByTestCaseResourceIDs")
    public Result findByTestCaseResourceIDs(@RequestBody List<String> testCaseRids) {
        if (testCaseRids.isEmpty()) return Result.renderError("传递参数为空！");
        List<AtTestCase> list = testCaseService.findByResourceIDIn(testCaseRids);
        return Result.renderSuccess(list);
    }

    /**
     * @param "[request]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: isOperationAuthority
     * @description: 登录用户是否有操作案例库的权限
     * <AUTHOR>
     * @date 2020/1/14 11:07
     */
    @PostMapping("/isOperationAuthority")
    public Result isOperationAuthority() {
//        return testCaseService.isOperationAuthority();
        //此方法暂时不用
        return Result.renderSuccess(true);
    }

    @PostMapping("/syncTestCase4Demand")
    public void syncTestCase4Demand(String demandResourceID) {
        testCaseService.syncTestCase4Demand(demandResourceID);
    }

    /**
     * @Title: findCaseIDByTradeResourceID
     * @description: 查询交易下已经存在的全部案例编号
     * <AUTHOR>
     * @date 2020/6/16
     */
    @SuppressWarnings("unchecked")
    @PostMapping("/findCaseIDByTradeResourceID")
    @ResponseBody
    public Result<List<String>> findCaseIDByTradeResourceID(@RequestParam("tradeResourceID") Long tradeResourceID) {
        List<String> caseIDList = testCaseService.findCaseIDByTradeResourceID(tradeResourceID);
        return Result.renderSuccess(caseIDList);
    }

    //initMyCaseToAssesWithBynumber
    @PostMapping("/initWorkBenchMyCase")
    @ResponseBody
    public Result initMyCaseToAssesWithBynumber(@RequestBody Map<String, String> params) {
        String rows = params.get("rows");
        String page = params.get("page");
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
        Page<Map<String, Object>> result = testCaseService.initMyCaseToAssesWithBynumber(pageRequest, params);
        return Result.renderSuccess(result);
    }
	
 @PostMapping("/findAllFilterTradeResourceID")
    public Result findAll(String demandResourceID) {
        List<AtTestCase> findAll = testCaseService.findAll();
        Map<Long, List<String>> tradeAndcaseIDsMap = findAll.stream().collect(Collectors.groupingBy(AtTestCase::getTradeResourceID,Collectors.mapping(AtTestCase::getCaseId,Collectors.toList())));
        return Result.renderSuccess(tradeAndcaseIDsMap);
        
    }
	 
	 /**
	  * 
	  *@Description  根据交易查询案例库案例
	  *@param 
	  *@return Result<List<AtTestCase>>
	  *<AUTHOR>
	  *@Date 2023年2月8日
	  */
	@PostMapping("/findbyTradeResourceIDList")
	public Result<List<AtTestCase>> findbyTradeResourceIDList(@RequestBody List<Long> tradeResourceIDs ) {
		 List<AtTestCase> findbyTradeResourceIDList = testCaseService.findbyTradeResourceIDList(tradeResourceIDs);
		return Result.renderSuccess(findbyTradeResourceIDList);
	}
	 
	
}

