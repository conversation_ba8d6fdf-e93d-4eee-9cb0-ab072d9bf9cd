package com.jettech.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jettech.DTO.TaskTestCaseDTO;
import com.jettech.DTO.TaskTradeCaseDto;
import com.jettech.common.dto.FileIdName;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.GenerageCaseDto;
import com.jettech.common.dto.datadesign.TestCaseDTO;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.redis.JedisClient;
import com.jettech.common.redis.RedisDistributeLock;
import com.jettech.common.util.*;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.model.TestCase;
import com.jettech.model.TestCaseFile;
import com.jettech.service.iservice.*;
import com.jettech.util.FileCheckUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/testCase")
public class TestCaseController {
	@Value("${use_file_service}")
	private boolean useFileService;
	// 日志对象
	private final static Logger logger = LoggerFactory.getLogger(TestCaseController.class);

	@Autowired
	private ITestCaseService testCaseService;

	@Autowired
	private ITestCaseRecycleBinService testCaseRecycleBinService;

	@Autowired
	private RedisUtils redisUtils;
	@Autowired
	private ITestCaseFileService testCaseFileService;
	@Autowired
	private LoginUserUtil loginUserUtil;

	@Autowired
	private JedisClient jedisClient;
    @Autowired
	private IFeignDataDesignToFileService feignDataDesignToFileService;
	/**
	 * @Title: getTestCaseCaseId
	 * @Description: 新增案例时回显的案例编号
	 * @Param: "[map]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/12/5
	 */
	@PostMapping("/getTestCaseCaseId")
	public Result getTestCaseCaseId(@RequestBody Map<String, String> map) {
		return testCaseService.getTestCaseCaseId(map);
	}

	/**
	 * @Title: getNewTestCaseCaseId
	 * @Description: 案例编写：根据最新要求返回案例编号
	 * @Param: "[tradeResourceID]"
	 * @Return: "java.lang.String"
	 * @Author: xpp
	 * @Date: 2020/1/13
	 */
	@GetMapping("/getNewTestCaseCaseId/{tradeResourceID}")
	public Result getNewTestCaseCaseId(@PathVariable("tradeResourceID") Long tradeResourceID) {
		synchronized (TestCaseController.class) {
			String str = testCaseService.getNewTestCaseCaseId(tradeResourceID);
			return Result.renderSuccess(str);
		}
	}

	/**
	 * @Title: addTestCase
	 * @Description: 新增案例
	 * @Param: "[testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	@PostMapping()
	public Result addTestCase(HttpServletRequest request, @RequestParam Map<String,String> params, @RequestParam(value = "files", required = false) MultipartFile[] files) {
		if (files == null) {
			files = new MultipartFile[] {};
		}
		String checkInfo=FileCheckUtil.check(files);
		if(org.apache.commons.lang3.StringUtils.isNotBlank(checkInfo)){
			return Result.renderError(checkInfo);
		}
		String requestID = UUID.randomUUID().toString().replaceAll("-", "");
		String userNumber = LoginUserUtil.getUserNumber(request);
		TestCase testCase = new TestCase();
		testCase.setCreateUser(params.get("createUser"));
		testCase.setCaseId(params.get("caseId"));
		testCase.setCasetLevel(params.get("casetLevel"));
		testCase.setCheckPoint(params.get("checkPoint") == null?"":params.get("checkPoint"));
		testCase.setComment(params.get("comment"));
		testCase.setComments10(params.get("comments10"));
		testCase.setComments3(params.get("comments3"));
		testCase.setComments7(params.get("comments7"));
		testCase.setComments9(params.get("comments9"));
		testCase.setDataRequirements(params.get("dataRequirements"));
		try {
			testCase.setDemandResourceID(Long.valueOf(params.get("demandResourceID")));
		} catch (Exception e) {
			testCase.setDemandResourceID(null);
		}
		testCase.setExpectedResult(params.get("expectedResult"));
		testCase.setIntent(params.get("intent"));
		testCase.setIsNegative(params.get("isNegative"));
		testCase.setPreconditions(params.get("preconditions"));
		testCase.setReviewStatus(params.get("reviewStatus"));
		testCase.setTestStep(params.get("testStep"));
		try {
			testCase.setTesttaskResourceID(Long.valueOf(params.get("testtaskResourceID")));
		} catch (Exception e) {
			testCase.setTesttaskResourceID(null);
		}
		testCase.setTradeResourceID(Long.valueOf(params.get("tradeResourceID")));
		testCase.setName(params.get("name"));
		try {
			testCase.setVersionResourceID(Long.valueOf(params.get("versionResourceID")));
		} catch (Exception e) {
			testCase.setVersionResourceID(null);
		}
		String testMode = BinaryDecimalUtil.DicValToBin(params.get("testMode"));
		int val = BinaryDecimalUtil.BinToTen(testMode);
		testCase.setTestMode(val);

		/**
		 * updater:wws
		 * 2021年2月2日14:50:47
		 * 添加案例类型，运行条件,备用字段，数据需求
		 */
		testCase.setCaseType(params.get("caseType"));
		testCase.setTimingName(params.get("timingName"));
		testCase.setComments2(params.get("comments2"));
		testCase.setComments1(params.get("comments1"));
		testCase.setComments4(params.get("comments4"));
		testCase.setComments5(params.get("comments5"));
		testCase.setComments6(params.get("comments6"));
		testCase.setComments8(params.get("comments8"));
		testCase.setDataRequirements(params.get("dataRequirements") == null?"":params.get("dataRequirements"));

		//是否场景案例
		testCase.setSceneCase(BooleanUtils.toBoolean(params.get("sceneCase")));
		//是否可被手工执行引用
		testCase.setCommitted(BooleanUtils.toBoolean(params.get("committed")));

		boolean tryGetLock = true;
		Long time = System.currentTimeMillis() + 3000;
		RedisDistributeLock lock = new RedisDistributeLock("addTestCase", "addTestCase", 5000, jedisClient);
		boolean locked = lock.lock();

			if (locked) {
				try {
					System.out.println("新增案例获取锁成功!请求号:\"" + requestID + "\"");
					Result result = testCaseService.addTestCase(testCase,files, userNumber);
					// boolean releaseDistributedLock =
					// RedisTool.releaseDistributedLock(redisPool.getJedis(), "addTestCase",
					// requestID);
					return result;
				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					lock.release();
				}
			} else {
				System.out.println("新增案例获取锁失败!请求号:\"" + requestID + "\"");
				if (System.currentTimeMillis() > time) {
					return Result.renderError("超时!");
				}
			}

		return Result.renderError("服务器错误!");
	}

	/**
	 * @Title: isNotRepeat
	 * @Description: 判断案列编号是否重复
	 * @Param: "[request, testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/21
	 */
	@PostMapping("/isNotRepeat")
	public Result isNotRepeat(@RequestBody TestCase testCase) {
		return testCaseService.isNotRepeat(testCase);
	}

	/**
	 * @Title: updateTestCase
	 * @Description: 修改案例
	 * @Param: "[testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	@PutMapping()
	public Result updateTestCase(HttpServletRequest request, @RequestParam Map<String,String> params,@RequestParam(value = "files", required = false) MultipartFile[] files) {
		if (files == null) {
			files = new MultipartFile[] {};
		}
		String checkInfo=FileCheckUtil.check(files);
		if(org.apache.commons.lang3.StringUtils.isNotBlank(checkInfo)){
			return Result.renderError(checkInfo);
		}
		String userNumber = LoginUserUtil.getUserNumber(request);
		TestCase testCase = new TestCase();
		testCase.setCreateUser(params.get("createUser"));
		testCase.setCaseId(params.get("caseId"));
		testCase.setCasetLevel(params.get("casetLevel"));
		testCase.setCheckPoint(params.get("checkPoint") == null?"":params.get("checkPoint"));
		testCase.setComment(params.get("comment"));
		testCase.setComments10(params.get("comments10"));
		testCase.setComments3(params.get("comments3"));
		testCase.setComments7(params.get("comments7"));
		testCase.setComments9(params.get("comments9"));
		testCase.setName(params.get("name"));
		testCase.setDataRequirements(params.get("dataRequirements"));
//		testCase.setDemandResourceID(params.get("demandResourceID")==null||"".equals(params.get("demandResourceID"))?null:Long.valueOf(params.get("demandResourceID")));
		testCase.setExpectedResult(params.get("expectedResult"));
		testCase.setIntent(params.get("intent"));
		testCase.setIsNegative(params.get("isNegative"));
		testCase.setPreconditions(params.get("preconditions"));
		testCase.setReviewStatus(params.get("reviewStatus"));
		testCase.setTestStep(params.get("testStep"));
//		testCase.setTesttaskResourceID(params.get("testtaskResourceID") == null || "".equals(params.get("testtaskResourceID")) ? null : Long.valueOf(params.get("testtaskResourceID")));
		testCase.setTradeResourceID(Long.valueOf(params.get("tradeResourceID")));
		testCase.setResourceID(Long.valueOf(params.get("resourceID")));
		if (params.get("versionResourceID") != null) {
			testCase.setVersionResourceID(Long.valueOf(params.get("versionResourceID")));
		}
		String testMode = BinaryDecimalUtil.DicValToBin(params.get("testMode"));
		int val = BinaryDecimalUtil.BinToTen(testMode);
		testCase.setTestMode(val);
		/**
		 * updater:wws
		 * 2021年2月2日14:50:47
		 * 添加案例类型，运行条件,备用字段，数据需求
		 */
		testCase.setCaseType(params.get("caseType"));
		testCase.setTimingName(params.get("timingName"));
		testCase.setComments2(params.get("comments2"));
		testCase.setComments1(params.get("comments1"));
		testCase.setComments4(params.get("comments4"));
		testCase.setComments5(params.get("comments5"));
		testCase.setComments6(params.get("comments6"));
		testCase.setComments8(params.get("comments8"));
		testCase.setDataRequirements(params.get("dataRequirements")== null?"":params.get("dataRequirements"));

		JSONObject jsonObject = new JSONObject().fluentPut("sceneCase", params.get("sceneCase")).fluentPut("committed", params.get("committed"));
		//是否场景案例
		testCase.setSceneCase(jsonObject.getBooleanValue("sceneCase"));
		//是否可被手工执行引用
		if(params.get("committed")!=null){
			testCase.setCommitted(jsonObject.getBooleanValue("committed"));
		}
		//20220519 leadsource区分案例到底是在维护类创建的还是在新建类创建的,此处作为判断
		if(!StringUtils.isEmpty(params.get("leadsource"))) {
			int leadsource = 0;
			try {
				leadsource = Integer.valueOf(params.get("leadsource"));
			} catch (NumberFormatException e) {
				return Result.renderError("参数[案例来源]异常");
			}
			testCase.setLeadsource(leadsource);
		}
		return testCaseService.updateTestCase(testCase,files, userNumber);
	}

	/**
	 * @Title: findByTestCase
	 * @Description: 查询案例详情
	 * @Param: "[currentTestCaseResourceID]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	@GetMapping("/{currentTestCaseResourceID}")
	public Result findByTestCase(@PathVariable String currentTestCaseResourceID) {
		return testCaseService.findByTestCase(currentTestCaseResourceID);
	}

	/**
	 * @Title: deleteTestCase
	 * @Description: 删除案例
	 * @Param: "[iDList]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	@PostMapping("/deleteTestCase")
	public Result deleteTestCase(HttpServletRequest request) {
		String userNumber = LoginUserUtil.getUserNumber(request);
		String[] split = request.getParameter("testCaseResourceIds").split(",");
		List<String> iDList = Arrays.asList(split);
		return testCaseService.deleteTestCase(iDList, userNumber);
	}

	/**
	 * @Title: deleteTestCase
	 * @Description: 删除案例
	 * @Param: "[iDList]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/4
	 */
	@DeleteMapping("/{testCaseResourceIDs}")
	public Result deleteTestCase(HttpServletRequest request, @PathVariable String testCaseResourceIDs) {
		String userNumber = LoginUserUtil.getUserNumber(request);
		String[] split = testCaseResourceIDs.split(",");
		List<String> iDList = Arrays.asList(split);
		return testCaseService.deleteTestCase(iDList, userNumber);
	}

	/**
	 * @Title: findByTradeAndOptions
	 * @Description: 根据左侧树展示右侧案例列表
	 * @Param: "[map]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/12/13
	 */
	@PostMapping("/findByTradeAndOptions")
	public Result findByTradeAndOptions(@RequestBody Map map, HttpServletRequest request) {
		String userNumber = LoginUserUtil.getUserNumber(request);
		map.put("userNumber", userNumber);
		return testCaseService.findByTradeAndOptions(map);
	}

	/**
	 * QiaoHongju
	 *
	 * @param moduleResourceID
	 * @param page
	 * @param pageSize
	 * @return
	 */
	@PostMapping("/findBySystemModule")
	public Page<TestCaseDTO> findBySystemModule(Long moduleResourceID, Long demandResourceID, Integer page,
												Integer pageSize) {
		IPage<TestCase> testCasePage = testCaseService.findBySystemModule(moduleResourceID, demandResourceID,
				new Page<>(page, pageSize));

		List<TestCase> testCaseList = testCasePage.getRecords();
		List<TestCaseDTO> testCaseDTOList = new ArrayList<>();
		testCaseList.forEach(e -> {
			TestCaseDTO dto = new TestCaseDTO();
			org.springframework.beans.BeanUtils.copyProperties(e, dto);
			testCaseDTOList.add(dto);
		});

		Page<TestCaseDTO> testCaseDTOPage = new Page<>();
		org.springframework.beans.BeanUtils.copyProperties(testCasePage, testCaseDTOPage);
		testCaseDTOPage.setRecords(testCaseDTOList);
		return testCaseDTOPage;
	}

	@GetMapping("/findCaseTotalBySelectedCase/{caseResourceID}")
	/**
	 * @Title findCaseTotalBySelectedCase
	 * @Description 根据选中的案例查询所属交易下的案例总数
	 * @Params [caseResourceID]
	 * @Return com.jettech.dto.Result
	 * <AUTHOR>
	 * @Date 2019/11/28
	 */
	public Result findCaseTotalBySelectedCase(@PathVariable String caseResourceID) {
		return testCaseService.findCaseTotalBySelectedCase(caseResourceID);

	}

	/**
	 * @Title: findTestPlanTreebyTestCaseResourceID
	 * @Description: 根据案例查询组装测试计划左侧树结构
	 * @Param: "[testCaseResouceIDList]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/27
	 */
	@PostMapping("/findTestPlanTreebyTestCaseResourceID")
	public Result findTestPlanTreebyTestCaseResourceID(@RequestBody List<Map<Long, String>> maps) {
		return testCaseService.findTestPlanTreebyTestCaseResourceID(maps);
	}

	@GetMapping("/findTestCaseMapByResourceID/{caseResourceID}")
	/**
	 * @Title findTestCaseMapByResourceID
	 * @Description 查询单个案例
	 * @Params [caseResourceID]
	 * @Return com.jettech.dto.Result<?>
	 * <AUTHOR>
	 * @Date 2019/11/29
	 */
	public Result<?> findTestCaseMapByResourceID(@PathVariable String caseResourceID) {
		return testCaseService.findTestCaseMapByResourceID(caseResourceID);
	}

	/**
	 * 通过案例列表查询案例 QiaoHongju
	 *
	 * @param
	 * @return 案例
	 */
	@PostMapping("/list")
	public List<Map<String, String>> findTestCaseByResourceIDIn(
			@RequestParam("testCaseResourceIDs") String testCaseResourceIDs) {
		List<String> testCaseResourceIDList = Arrays.asList(testCaseResourceIDs.split(","));
		List<String> list = testCaseResourceIDList.stream().map(Object::toString).collect(Collectors.toList());
		List<TestCase> testCaseList = testCaseService.findByResourceIDIn(list);
		List<Map<String, String>> res = new ArrayList<>();
		testCaseList.forEach(e -> {
			try {
				res.add(BeanUtils.describe(e));
			} catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException ex) {
				ex.printStackTrace();
			}
		});
		return res;
	}

	/**
	 * @Title: findTestPlanTreebyTestCaseResourceID
	 * @Description: 根据案例查询组装测试计划左侧树结构不带案例节点
	 * @Param: "[testCaseResouceIDList]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/11/27
	 */
	@PostMapping("/findTradeTreeTreebyTestCaseResourceID")
	public Result findTradeTreeTreebyTestCaseResourceID(@RequestBody List<Map<Long, String>> maps,
														@RequestParam("testSystemResourceID") String testSystemResourceID) {
		return testCaseService.findTradeTreebyTestCaseResourceID(maps, testSystemResourceID);
	}

	/**
	 * @Title: updateTestCaseByTrade
	 * @Description: 批量更新案例的编号
	 * @Param: "[]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2019/12/25
	 */
	@GetMapping("/updateTestCaseByTrade")
	public Result updateTestCaseByTrade() throws Exception {
		return testCaseService.updateTestCaseByTrade();
	}

	/**
	 * @param "[]"
	 * @return com.jettech.dto.Result
	 * @throws @Title: InitAssetsLeftTree
	 * @description: 引用案例资产库左侧树
	 * <AUTHOR>
	 * @date 2020/1/13 10:49
	 */
	@GetMapping("/InitAssetsLeftTree")
	public Result InitAssetsLeftTree() {
		return testCaseService.InitAssetsLeftTree();
	}

	/**
	 * @param "[]"
	 * @return com.jettech.dto.Result
	 * @throws @Title: InitAssetsTestCaseTables
	 * @description: 初始化资产库案例列表
	 * <AUTHOR>
	 * @date 2020/1/13 10:58
	 */
	@PostMapping("/InitAssetsTestCaseTables")
	public Result InitAssetsTestCaseTables(@RequestBody HashMap map) {
		return testCaseService.InitAssetsTestCaseTables(map);
	}

	/**
	 * @param "[map, request]"
	 * @return com.jettech.dto.Result
	 * @throws @Title: QuoteTestCase
	 * @description: 案例引用
	 * <AUTHOR>
	 * @date 2020/1/13 19:51
	 */
	@PostMapping("/QuoteTestCase")
	public Result QuoteTestCase(@RequestBody Map map, HttpServletRequest request) {
		try {
			map.put("userNumber", LoginUserUtil.getUserNumber(request));
			return testCaseService.QuoteTestCase(map);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * @Title findTestCaseByResourceIDs
	 * @Description 批量查询案例
	 * @Params [collect]
	 * @Return com.jettech.dto.Result
	 * <AUTHOR>
	 * @Date 2020/1/15
	 */
	@PostMapping("/findTestCaseByResourceIDs")
	public Result findTestCaseByResourceIDs(@RequestBody List<String> collect) {
		List<TestCase> byResourceIDIn = testCaseService.findByResourceIDIn(collect);
		return Result.renderSuccess(byResourceIDIn);
	}

	@GetMapping("/findTestCaseByTradeResourceIDsPage")
	public Page<TestCaseDTO> findTestCaseByTradeResourceIDsPage(String[] tradeResourceIDs, String demandResourceID,
																String testEnviroment, Integer page, Integer pageSize) {
		IPage<TestCase> testCasePage = testCaseService.findTestCaseByTradeResourceIDsAndDemandResourceIDPage(
				Arrays.asList(tradeResourceIDs), demandResourceID, testEnviroment, new Page<>(page, pageSize));
		Page<TestCaseDTO> testCaseDTOPage = new Page<>();
		org.springframework.beans.BeanUtils.copyProperties(testCasePage, testCaseDTOPage);
		List<TestCaseDTO> collect = testCasePage.getRecords().stream().map(e -> {
			TestCaseDTO dto = new TestCaseDTO();
			org.springframework.beans.BeanUtils.copyProperties(e, dto);
			return dto;
		}).collect(Collectors.toList());
		testCaseDTOPage.setRecords(collect);
		return testCaseDTOPage;
	}



	/**
	 *
	 * @Title: findTestcasequoteExportedWord
	 * @Description: 查询导出引用执行案例到word文档需要数据
	 * @param map
	 * @return Result<?> 返回类型
	 * <AUTHOR>
	 * @date 2020年3月13日下午4:25:21
	 */
	@PostMapping("/findTestcasequoteExportedWord")
	@ResponseBody
	public Result<?> findTestcasequoteExportedWord(@RequestBody HashMap<String, Object> map) {
		return testCaseService.findTestcasequoteExportedWord(map);
	}

	/**
	 * 根据交易和任务的rid查询当前任务当前交易下的手工编写案例
	 *
	 * @Title: findTestCaseByTaskAndTradeResourceID
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  map
	 * @param @return 参数
	 * @return List<TestCase> 返回类型
	 * @throws <AUTHOR>
	 */
	@PostMapping("/findTestCaseByTaskAndTradeResourceID")
	@ResponseBody
	public Result<?> findTestCaseByTaskAndTradeResourceID(@RequestBody HashMap<String, Object> map) {

		return testCaseService.findTestCaseByTaskAndTradeResourceID(map);
	}

	/**
	 * @Title: findCaseIDByTradeResourceID
	 * @description: 查询交易下已经存在的全部案例编号
	 * <AUTHOR>
	 * @date 2020/6/16
	 */
	@SuppressWarnings("unchecked")
	@PostMapping("/findCaseIDByTradeResourceID")
	@ResponseBody
	public Result<?> findCaseIDByTradeResourceID(@RequestParam("tradeResourceID") Long tradeResourceID) {
		List<String> idList = new ArrayList<String>();
		List<String> caseIDList = testCaseService.findCaseIDByTradeResourceID(tradeResourceID);
		List<String> caseIDInRecycleBinList = testCaseRecycleBinService.findCaseIDByTradeResourceID(tradeResourceID);
		idList.addAll(caseIDInRecycleBinList);
		idList.addAll(caseIDList);
		return Result.renderSuccess(idList);
	}



	/**
	 *
	 * @Title: commitTestCase
	 * @Description: 提交案例
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年6月9日 下午5:55:32
	 */
	@PostMapping("/commitTestCase")
	@ResponseBody
	public Result<?> commitTestCase(@RequestBody HashMap<String,String> map, HttpServletRequest request){
		String testTaskResourceID=map.get("testTaskResourceID");
		//1 代表点击提交成果按钮；  2 代表点击提交并关闭任务按钮
		String state = map.get("state");
		if(testTaskResourceID==null || "".equals(testTaskResourceID)) {
			return Result.renderError("测试任务参数为空！");
		}
		String userNumber = LoginUserUtil.getUserNumber(request);
		return testCaseService.commitTestCase(testTaskResourceID,state,userNumber);
	}

	/**
	 *
	 * @Title: findProjectTestCase
	 * @Description: 分页查询项目案例
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 下午5:45:27
	 */
	@PostMapping("/findProjectTestCase")
	@ResponseBody
	public Result<PageImpl<TestCaseDTO>> findProjectTestCase(HttpServletRequest request){

		String testSystem=request.getParameter("testSystem");
		String trade=request.getParameter("trade");
		String caseId=request.getParameter("caseId");
		String intent=request.getParameter("intent");
		String caseType=request.getParameter("caseType");
		String casetLevel=request.getParameter("casetLevel");
		String nodeType=request.getParameter("nodeType");
		String resourceID=request.getParameter("resourceID");//树节点-项目组resourceID
		//demandFlag是否关联需求，0-未关联需求的，1-关联需求的
		String demandFlag=request.getParameter("demandFlag");
		String caseFinalResult = request.getParameter("caseFinalResult");
		String userNumber = LoginUserUtil.getUserNumber(request);

		if (resourceID == null|| "".equals(resourceID)) {
			return Result.renderError("节点参数resourceID为空");
		}

		String tagResourceIDs=request.getParameter("tagResourceIDs");

		String rows = request.getParameter("pageSize");// 默认为10
		if (rows == null|| "".equals(rows))
			rows = "10";
		String page = request.getParameter("page");// 默认为1
		if (page == null|| "".equals(page))
			page = "1";
		PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
		PageImpl<TestCaseDTO> pages=testCaseService.findProjectTestCase(pageRequest,testSystem,trade,caseId,intent
				,caseType,casetLevel,resourceID,nodeType,demandFlag,tagResourceIDs, caseFinalResult, userNumber);
		return Result.renderSuccess(pages);
	}

	/**
	 *
	 * @Title: findTestCaseType
	 * @Description: 查询案例类型
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 下午6:22:16
	 */
	@GetMapping("/findTestCaseType")
	@ResponseBody
	public Result<?> findTestCaseType(HttpServletRequest request){
		return Result.renderSuccess(redisUtils.getHashEntries("案例类型"));
	}

	/**
	 *
	 * @Title: findTestCaseLevel
	 * @Description:查询案例级别
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 下午6:22:29
	 */
	@GetMapping("/findTestCaseLevel")
	@ResponseBody
	public Result<?> findTestCaseLevel(HttpServletRequest request){
		return Result.renderSuccess(redisUtils.getHashEntries("案例级别"));
	}

	/**
	 * @Title: addProjectCaseLibTestCase
	 * @Description: 新增项目案例库案例（用的表还是ds_testcase,通过leadsource字段区分，案例来源（默认0，项目案例为1））
	 * @Param: "[testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: cao_jinbao
	 * @Date: 2020/7/15
	 */
	@PostMapping("/addProjectCaseLibTestCase")
	public Result addProjectCaseLibTestCase(HttpServletRequest request, @RequestBody TestCase testCase) {
		String requestID = UUID.randomUUID().toString().replaceAll("-", "");
		String userNumber = LoginUserUtil.getUserNumber(request);
		boolean tryGetLock = true;
		Long time = System.currentTimeMillis() + 3000;
		RedisDistributeLock lock = new RedisDistributeLock("addProjectCaseLibTestCase", "addProjectCaseLibTestCase", 5000, jedisClient);
		boolean locked = lock.lock();

			if (locked) {
				try {
					System.out.println("新增案例获取锁成功!请求号:\"" + requestID + "\"");
					return testCaseService.addProjectCaseLibTestCase(testCase, userNumber);
				} catch (Exception e) {
					e.printStackTrace();
					return Result.renderError("新增案例异常!");
				} finally {
					lock.release();
				}
			} else {
				System.out.println("新增项目案例获取锁失败!请求号:\"" + requestID + "\"");
				if (System.currentTimeMillis() > time) {
					return Result.renderError("超时!");
				}
			}

		return Result.renderError("服务器错误!");
	}

	/**
	 * @Title: updateProjectCaseLibTestCase
	 * @Description: 修改项目案例库案例
	 * @Param: "[testCase]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: cao_jinbao
	 * @Date: 2020/7/15
	 */
	@PostMapping("/updateProjectCaseLibTestCase")
	public Result updateProjectCaseLibTestCase(HttpServletRequest request, @RequestBody TestCase testCase) {
		String userNumber = LoginUserUtil.getUserNumber(request);
		try {

			return Result.renderSuccess(testCaseService.updateProjectCaseLibTestCase(testCase, userNumber));
		} catch (Exception e) {
			e.printStackTrace();
			return Result.renderError("更新失败！");
		}
	}

	/**
	 * @Method: deleteBatch
	 * @Description: //批量删除案例
	 * @Param: " [] "
	 * @return:
	 * @Author: wws
	 * @Date: 2020/7/15
	 */

	/**
	 * @Method: deleteBatch
	 * @Description: //批量删除案例
	 * @Param: " [resourceIDs, request] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/7/15
	 */
	@PostMapping("/deleteBatch")
	public Result deleteBatch(HttpServletRequest request){
		String userNumber = LoginUserUtil.getUserNumber(request);
		String resourceIDs = request.getParameter("resourceIDs");
		if(StringUtils.isEmpty(resourceIDs)){
			return Result.renderError("请选择要删除的案例！");
		}
		String[] split = resourceIDs.split(",");
		List<String> rids = Arrays.asList(split);
		List<TestCase> lists = testCaseService.findByResourceIDIn(rids);
		if(lists !=null && !lists.isEmpty()){
			testCaseService.deleteInBatch(lists,userNumber);
		}
		return Result.renderSuccess();
	}


	/**
	 * @Method: relationDemand
	 * @Description: //案例关联需求
	 * @Param: " [demandResourceID, testCaseResourceID, request] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/7/15
	 */


	@PostMapping("/relationDemand")
	public Result relationDemand(HttpServletRequest request){
		String userNumber = LoginUserUtil.getUserNumber(request);
		String demandResourceID = request.getParameter("demandResourceID");
		if(StringUtils.isEmpty(demandResourceID)){
			return Result.renderError("请选择要关联的需求！");
		}
		String testCaseResourceIDs = request.getParameter("testCaseResourceIDs");
		if(StringUtils.isEmpty(demandResourceID)){
			return Result.renderError("请选择要关联需求的案例！");
		}
		return testCaseService.relationDemand(demandResourceID,testCaseResourceIDs,userNumber);
	}

	/**
	 * @Method: findByResourceIDs
	 * @Description: rid查询案例
	 * @Param: " [resourceIDs] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/7/16
	 */
	@PostMapping("/findByResourceIDs")
	public Result findByResourceIDs(@RequestBody HashMap<String, String> dataMap){
		String[] strArr=dataMap.get("resourceIDs").split(",");
		List<String> rids = Arrays.asList(dataMap.get("resourceIDs").split(","));

		List<TestCase> list = testCaseService.findByResourceIDIn(rids);
		HashMap<String,TestCase> resourceIdCaseMap=new HashMap();
		if(!CollectionUtils.isEmpty(list)){

			for(TestCase testCase:list){
				resourceIdCaseMap.put(testCase.getResourceID()+"",testCase);
			}
		}
		List<TestCase> list1=new ArrayList<>();
		if(strArr!=null&&strArr.length>0){
			for(String str:strArr){
				list1.add(resourceIdCaseMap.get(str));
			}
		}
		return Result.renderSuccess(list1);
	}

	/**
	 * @Method: findAllTaskTestCase
	 * @Description: 查询所有需求案例
	 * @Param: " [] "
	 * @return: com.jettech.dto.Result<java.util.List<com.jettech.dto.TaskTestCaseDTO>>
	 * @Author: wws
	 * @Date: 2020/7/28
	 */
	@PostMapping("/findAllTaskTestCase")
	public Result<List<TaskTestCaseDTO>> findAllTaskTestCase(){
		return testCaseService.findAllTaskTestCase();
	}

	/**
	 *
	 * @Title: findInfoByTestCaseResourceID
	 * @Description: 根据当前案例的rid查找案例所属的系统模块交易和所属需求所属项目(手工执行页面用)
	 * @param @param resourceIDs
	 * @param @param request
	 * @param @return    参数
	 * @return Result    返回类型
	 * @throws
	 * <AUTHOR>
	 */
	@PostMapping("findInfoByTestCaseResourceID")
	public Result findInfoByTestCaseResourceID(HttpServletRequest request){
		String resourceID = request.getParameter("resourceID");
		String taskResourceID=request.getParameter("taskResourceID");
		String testProjectResourceID = request.getParameter("testProjectResourceID");
		return testCaseService.findInfoByTestCaseResourceID(resourceID,taskResourceID,testProjectResourceID);
	}

	/**
	 *
	 * @Title: findAnotherInfoByTestCaseResourceID
	 * @Description: 根据当前案例的rid查找案例所属的系统模块交易和所属项目(案例执行库页面用)--没有所属需求
	 * @param @param resourceIDs
	 * @param @param request
	 * @param @return    参数
	 * @return Result    返回类型
	 * @throws
	 * <AUTHOR>
	 */
	@PostMapping("findAnotherInfoByTestCaseResourceID")
	public Result findAnotherInfoByTestCaseResourceID(HttpServletRequest request){
		//performcase对应的tasecaseResouceID
		String resourceID = request.getParameter("resourceID");

		return testCaseService.findAnotherInfoByTestCaseResourceID(resourceID);
	}

	@PostMapping("/initWorkBenchMyCase")
	@ResponseBody
	public Result initMyCaseToAssesWithBynumber(HttpServletRequest request){
		Map<String, String> paramMap = new HashMap<String, String>();
		String userNumber = LoginUserUtil.getUserNumber(request);
		if (org.apache.commons.lang3.StringUtils.isBlank(userNumber)) {
			return Result.renderError("未获取到登录用户");
		}

		String rows = request.getParameter("rows");// 每页显示条数
		if (org.apache.commons.lang3.StringUtils.isBlank(rows)) {
			rows = "10";
		}
		String page = request.getParameter("page");// 当前页
		if (org.apache.commons.lang3.StringUtils.isBlank(page)) {
			page = "1";
		}
		String demandName = request.getParameter("demandName") == null ? "" : request.getParameter("demandName");// 需求名称
		String caseResult = request.getParameter("caseResult") == null ? "" : request.getParameter("caseResult");// 案例状态
		String number = request.getParameter("number") == null ? "" : request.getParameter("number");// 需求编号
		String testProjectResourceID = request.getParameter("testProjectResourceID") == null ? ""
				: request.getParameter("testProjectResourceID");// 项目的resourceID
		String caseType = request.getParameter("caseType") == null ? ""
				: request.getParameter("caseType");// 案例类型
		paramMap.put("rows", rows);// 每页显示条数
		paramMap.put("page", page);// 当前页
		//根据项目id条件查询如果不存在不走查询
		if(org.apache.commons.lang3.StringUtils.isEmpty(testProjectResourceID)){
			return Result.renderSuccess();
		}
		paramMap.put("userNumber",userNumber);// 登录人number
		paramMap.put("demandName", demandName);// 需求名称
		paramMap.put("caseResult", caseResult);// 案例状态
		paramMap.put("number", number);// 需求编号
		paramMap.put("testProjectResourceID", testProjectResourceID);// 所属项目
		paramMap.put("caseType", caseType);// 案例类型

//		String rows = params.get("rows");
//		String page = params.get("page");
		PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
		PageImpl<Map<String, Object>> result = testCaseService.initMyCaseToAssesWithBynumber(pageRequest,paramMap);
		return Result.renderSuccess(result);
	}

	@PostMapping("/notarizeCase")
	@ResponseBody
	public Result notarizeCase(HttpServletRequest request){
		Map<String, String> paramMap = new HashMap<String, String>();
		String userNumber = LoginUserUtil.getUserNumber(request);
		paramMap.put("userNumber",userNumber);
		String resourceID = request.getParameter("resourceID") == null ? "" : request.getParameter("resourceID");// 需求名称
		paramMap.put("resourceID",resourceID);

//		String userNumber = params.get("userNumber");
		return testCaseService.updateCaseStatus(paramMap,userNumber);
	}
	/**
	 *
	 * @Title: findTestCaseDetailsForDefect
	 * @Description:点击缺陷所属案例编号查询任务案例的信息
	 * @param @param params
	 * @param @return    参数
	 * @return Result    返回类型
	 * @throws
	 * <AUTHOR>
	 */
	@PostMapping("/findTestCaseDetailsForDefect")
	@ResponseBody
	public Result<?> findTestCaseDetailsForDefect(HttpServletRequest request){

		String testCaseResourceID = request.getParameter("testCaseResourceID");

		if(StringUtils.isEmpty(testCaseResourceID)) {
			return Result.renderError("参数有误！");
		}

		return testCaseService.findTestCaseDetailsForDefect(testCaseResourceID);
	}


	/**
	 *
	 * @Title: findTestCaseFileByResourceID
	 * @Description:根据案例resourceId查询附件信息
	 * @param @param params
	 * @param @return    参数
	 * @return Result    返回类型
	 * @throws
	 * <AUTHOR>
	 */
	@GetMapping("findTestCaseFileByResourceID")
	public Result findTestCaseFileByResourceID(@RequestParam("testCaseResourceID") Long testCaseResourceID){
		if(testCaseResourceID==null){
			return Result.renderError("参数错误!");
		}
		if(useFileService){
			Result<List<FileIdName>> data=feignDataDesignToFileService.getFileIdList(testCaseResourceID, ObjectTypeEnum.SCRIPT.getValue());
			return data;
		}
		List<TestCaseFile> testCaseFileList=testCaseFileService.findTestCaseFileByResourceID(testCaseResourceID);
		//feignDataDesignToFileServiceServiceImpl.getFileIdList();

		return Result.renderSuccess(testCaseFileList);
	}


	/**
	 * @param
	 * @return Result<?>
	 * @Title:downloadDemand
	 * @Description:下载附件信息
	 * @author: shan_baoxin
	 */

	@PostMapping("/downloadCaseFile")
	@ResponseBody
	public Result<?> downloadCaseFile( HttpServletResponse response,HttpServletRequest request) throws Exception {
		String resourceID = request.getParameter("resourceID");

		if(resourceID==null || "".equals(resourceID)) {
			return Result.renderError("附件的resourceID为空！");
		}
		if(useFileService){
			byte[] data1=feignDataDesignToFileService.getContent(Long.parseLong(resourceID));
			byte[] data = Base64.getMimeDecoder().decode(data1);

			/*try {
				feignResp2ServletResp(feignResponse, response);
			}catch (Exception e){
				e.printStackTrace();
			}*/
			response.setHeader("Content-Disposition", "attachment;filename=" + "XXXX" + "的附件下载.zip");

			response.setHeader("Content-disposition",
					"attachment; filename=" + encodeDownloadFilename("test", request.getHeader("User-Agent")));
			response.getOutputStream().write(data);

			return Result.renderSuccess();
		}else{
			//代码检查防止路径操作
			//resourceID = CleanPathUtil.pathManipulation(resourceID);

			TestCaseFile findByResourceID = testCaseFileService.findByResourceID(Long.valueOf(resourceID));
			if(findByResourceID==null) {
				Result.renderError("附件的相关信息不正确！");
			}
			Map<String,Object> mapParam = new HashMap<>();
			mapParam.put("resourceID", resourceID);
			mapParam.put("response", response);
			try {
				testCaseFileService.downloadCaseFile(mapParam);
				return Result.renderSuccess();
			} catch (Exception e) {
				e.printStackTrace();
				return Result.renderError("下载附件失败！");
			}
		}

	}

	/**
	 * @param
	 * @return Result<?>
	 * @Title:deleteCaseFile
	 * @Description:删除附件
	 * @author: shan_baoxin
	 */
	@GetMapping(value = "deleteCaseFile")
	public Result<?> deleteCaseFile(HttpServletRequest request, @RequestParam Map<String, Object> param) {
		UserVo user = loginUserUtil.getLoginUser(request);
		String resourceIDs = (String) param.get("resourceIDs");
        if(useFileService){
        	return feignDataDesignToFileService.deleteFile(resourceIDs);
		}
		return testCaseFileService.deleteCaseFile(resourceIDs, user.getUserNumber());
	}

	@GetMapping("/findTaskTree")
	public Result findTaskTree() {
		return Result.renderSuccess(testCaseService.findTaskTree());
	}

	@GetMapping("/findTradeTree/{taskResourceIDs}")
	public Result findTradeTree(@PathVariable String taskResourceIDs) {
		return Result.renderSuccess(testCaseService.findTradeTree(taskResourceIDs));
	}

	@PostMapping("/findTaskTradeCasePage")
	public Result findTaskTradeCasePage(@RequestBody TaskTradeCaseDto taskTradeCaseDto) {
		return Result.renderSuccess(testCaseService.findTaskTradeCasePage(taskTradeCaseDto));
	}

	@PostMapping("/findCaseQuote")
	public Result findCaseQuote(@RequestBody TaskTradeCaseDto taskTradeCaseDto) {
		return Result.renderSuccess(testCaseService.findCaseQuote(taskTradeCaseDto));
	}

	/**
	 * @Title: findApiScriptById
	 * @Description: 查询api脚本,通过脚本id
	 * @author: dingwenlong
	 * @date: 2021年10月13日
	 * @param tradeFlowId
	 * @return
	 */
	@PostMapping("/findApiScriptById")
	public Result findApiScriptById(@RequestParam(value = "tradeFlowId", required = false) String tradeFlowId) {
		if (StringUtils.isEmpty(tradeFlowId)) {
			return Result.renderError("参数api脚本id为空");
		}
		try {
			return Result.renderSuccess(testCaseService.findApiScriptById(tradeFlowId));
		} catch (Exception e) {
			logger.error("查询api脚本异常: ", e);
			e.printStackTrace();
			return Result.renderError("查询api脚本异常: " + e.getMessage());
		}
	}


	@PostMapping("/findProjectCasePage")
	public Result findProjectCase(@RequestBody Map<String,Object> params) {
		return Result.renderSuccess(testCaseService.findProjectCasePage(params));
	}

	@PostMapping("/findTaskCase")
	public List<TestCase> findCaseByTask(@RequestParam("taskResourceIDs") String taskResourceIDs) {
		return testCaseService.findTaskCase(taskResourceIDs);
	}

	/**
	 * @Title: findAllUser
	 * @Description:  查询所有人员
	 * @Return: "com.jettech.dto.Result<?>"
	 * @Author: dwl
	 * @Date: 2021/11/1
	 */
	@GetMapping("/findAllUser")
	public @ResponseBody Result<?> findAllUser() {
		List<Map<String,String>> resultList = testCaseService.findAllUser();
		return  Result.renderSuccess(resultList);
	}
	@PostMapping("/generateCase")
	public Result generateCase(@RequestBody GenerageCaseDto generateCaseDto){
		try {
			return testCaseService.generateCase(generateCaseDto);
		} catch (Exception e) {
			logger.error("分析模型生成案例失败", e);
			return Result.renderError("保存案例失败");
		}
	}

	@GetMapping("/InitModelTestCaseTables")
	public Result InitModelTestCaseTables(@RequestParam Map<String, String> params) {
		return Result.renderSuccess(testCaseService.InitModelTestCaseTables(params));
	}

	public String encodeDownloadFilename(String filename, String agent)
			throws IOException {
		if (agent.contains("Firefox")) { // 火狐浏览器
			filename = "=?UTF-8?B?"
					+ Base64Utils.encodeToString(filename.getBytes(StandardCharsets.UTF_8))
					+ "?=";
			filename = filename.replaceAll("\r\n", "");
		} else { // IE及其他浏览器
			filename = URLEncoder.encode(filename, "utf-8");
			filename = filename.replace("+", "");
		}
		filename = new String(filename.getBytes("UTF-8"), "iso-8859-1");
		return filename;
	}

}
