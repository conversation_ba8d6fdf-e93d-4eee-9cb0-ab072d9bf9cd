package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.service.iservice.IApplyRroundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ApplyRroundController
 * @description: 应用轮次控制层
 * @date 2024/7/15
 */
@Api(value="应用轮次controller",tags={"应用轮次控制层接口"})
@RestController
@RequestMapping("/applyRround")
public class ApplyRroundController {

    @Autowired
    private IApplyRroundService applyRroundService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    /**
     * @Title: findApplyRround
     * @Description: 应用轮次(分页 + 条件查询)
     * @Param: params
     * @Author: dwl
     * @Date: 2024/7/15
     */
    @ApiOperation(value = "应用轮次(分页 + 条件查询)")
    @PostMapping("/findApplyRround")
    public Result findApplyRround(@RequestBody Map<String, String> params) {
        return applyRroundService.findApplyRround(params);
    }

    /**
     * @Title: addApplyRround
     * @Description: 新增应用轮次
     * @param request
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @PostMapping("/addApplyRround")
    @ResponseBody
    public Result<?> addApplyRround(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String applyResourceID = params.get("applyResourceID");
        if (StringUtils.isBlank(applyResourceID)) {
            return Result.renderError("传入参数applyResourceID不能为空");
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        String userNumber = user.getUserNumber();
        return applyRroundService.addApplyRround(params, userNumber);
    }

    /**
     * @Title: updateApplyRround
     * @Description: 修改应用轮次
     * @param request
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @PostMapping("/updateApplyRround")
    @ResponseBody
    public Result<?> updateApplyRround(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String resourceID = params.get("resourceID");
        if (StringUtils.isBlank(resourceID)) {
            return Result.renderError("传入参数resourceID不能为空");
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        String userNumber = user.getUserNumber();
        return applyRroundService.updateApplyRround(params, userNumber);
    }

    /**
     * @Title: deleteTestSystemApply
     * @Description: 删除应用轮次
     * @param request
     * <AUTHOR>
     * @date 2024年7月15日
     */
    @PostMapping("/deleteApplyRround")
    @ResponseBody
    public Result<?> deleteApplyRround(HttpServletRequest request, @RequestParam("resourceID") String resourceID) {
        if (StringUtils.isBlank(resourceID)) {
            return Result.renderError("传入参数resourceID不能为空");
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        String userNumber = user.getUserNumber();
        return applyRroundService.deleteApplyRround(resourceID, userNumber);
    }

}
