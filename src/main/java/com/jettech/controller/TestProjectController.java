package com.jettech.controller;

import com.jettech.common.dto.FileIdName;
import com.jettech.common.dto.FileInfo;
import com.jettech.common.dto.Result;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.redis.JedisClient;
import com.jettech.common.util.*;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.model.ProjectGroup;
import com.jettech.model.TestProject;
import com.jettech.service.impl.ProjectGroupServiceImpl;
import com.jettech.service.iservice.IProjectGroupService;
import com.jettech.service.iservice.ITestProjectService;
import com.jettech.util.jedis.RedisCacheConfig;
import com.jettech.view.TestProjectView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Title: TestProjectController.java
 * Description:项目管理
 *
 * <AUTHOR>
 * @date 2020年4月20日
 */
@RestController
@RequestMapping("/testProject")
@Api(value = "项目管理 ", tags = "项目管理 ")
public class TestProjectController {

    @Autowired
    private ITestProjectService testProjectService;
    @Autowired
    private LoginUserUtil loginUserUtil;
    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasicService;

    @Autowired
    private JedisClient jedisUtil;
    @Value("${use_file_service}")
    private boolean useFileService;
    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;
    @Autowired
    private IProjectGroupService projectGroupService;

    /**
     * @param request
     * @return
     * @Title: findTestProject
     * @Description: 分页查询项目
     * <AUTHOR>
     * @date 2020年4月21日 下午12:14:42
     */
    @PostMapping("/findTestProject")
    @ResponseBody
    public Result<?> findTestProject(HttpServletRequest request) {

        String name = request.getParameter("name");
        String status = request.getParameter("status");
        String number = request.getParameter("number");
        String projectType = request.getParameter("projectType");
        /**
         * updater:wws
         * 添加字段：测试方式
         * 2021年3月24日15:18:47
         */
        String testMode = request.getParameter("testMode");//测试方式
        if (testMode == null || "".equals(testMode))
            testMode = "0";
        String rows = request.getParameter("rows");// 默认为10
        if (rows == null || "".equals(rows))
            rows = "10";
        String page = request.getParameter("page");// 默认为1
        if (page == null || "".equals(page))
            page = "1";
        String order = request.getParameter("order");
        if (order == null || "".equals(order))
            order = "createTime";
        String sort = request.getParameter("sort");
        if (sort == null || "".equals(sort))
            sort = "desc";

        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows), Sort.Direction.fromString(sort), order);
        Page<TestProject> pages = testProjectService.findTestProject(pageRequest, name, status, number, projectType, testMode);

        // 返回项目类型字典值名称
        Map<String, String> testProjectTypeMap = new HashMap<>();
        Result result = feignDataDesignToBasicService.findByName("TESTPROJECTTYPE", HttpRequestUtils.getCurrentRequestToken());//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list = (List<Map<String, String>>) result.getObj();
        list.stream().forEach(x -> {
            testProjectTypeMap.put(x.get("value"), x.get("textName"));
        });

        List<Map<String, Object>> resultList = translateData(pages.getContent(), testProjectTypeMap);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("content", resultList);
        resultMap.put("pageable", pages.getPageable());
        resultMap.put("sort", pages.getSort());
        resultMap.put("total", pages.getTotalElements());
        return Result.renderSuccess(resultMap);
    }

    /**
     * @param request
     * @return
     * @Title: findTestProjectAndChildren
     * @Description: 项目搜索优化
     */
    @GetMapping("/findTestProjectAndChildren")
    @ResponseBody
    public Result findTestProjectAndChildren(HttpServletRequest request) {

        String name = request.getParameter("name");
        String status = request.getParameter("status");
        String number = request.getParameter("number");
        String projectType = request.getParameter("projectType");
        String testMode = request.getParameter("testMode");
        UserVo user = loginUserUtil.getLoginUser(request);
        String userNumber = user.getUserNumber();

        String rows = request.getParameter("rows");// 默认为10
        if (rows == null || "".equals(rows))
            rows = "10";
        String page = request.getParameter("page");// 默认为1
        if (page == null || "".equals(page))
            page = "1";
        String order = request.getParameter("order");
        if (order == null || "".equals(order))
            order = "createTime";
        String sort = request.getParameter("sort");
        if (sort == null || "".equals(sort))
            sort = "desc";
        PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows), Sort.Direction.fromString(sort), order);
        return testProjectService.findTestProjectAndChildren(userNumber, name, status, pageRequest, number, projectType, testMode);

    }

    /**
     * @param list
     * @param testProjectTypeMap
     * @return
     * @Title: translateData
     * @Description:处理数据
     * <AUTHOR>
     * @date 2020年4月21日 下午6:33:59
     */
    private List<Map<String, Object>> translateData(List<TestProject> list, Map<String, String> testProjectTypeMap) {

        Map<Long, String> parentMap = new HashMap<>();
        List<TestProject> parentList = testProjectService.findAllParent();
        for (TestProject testProject : parentList) {
            parentMap.put(testProject.getResourceID(), "");
        }

        Result result = feignDataDesignToBasicService.findByName("TESTMODE", null);
        List<Map<String, Object>> tmList = (List<Map<String, Object>>) result.getObj();
        Map<String, String> tmMap = tmList.stream().collect(Collectors.toMap(x -> x.get("value").toString(), x -> x.get("textName").toString(), (k1, k2) -> k2));
        List<Map<String, Object>> resultList = new ArrayList<>();
        //添加进度数据
        Map<String, Integer> tpmap = new HashMap<String, Integer>();
        tpmap = testProjectService.selectProjectProgress(list);
        for (TestProject tp : list) {
            Boolean hasChildren = false;//是否有子项目
            if (!parentMap.isEmpty() && parentMap.containsKey(tp.getResourceID())) {
                hasChildren = true;
            }
            String startDate = tp.getStartDate() != null ? DateUtil.getDateStr(tp.getStartDate(), DateUtil.FMT_YYYY_MM_DD) : "";
            String endDate = tp.getEndDate() != null ? DateUtil.getDateStr(tp.getEndDate(), DateUtil.FMT_YYYY_MM_DD) : "";
            Map<String, Object> map = new HashMap<>();
            //增加创建时间，创建人，修改时间，修改人展示
            /*
             * 2023-03-06
             */
            String createTime = tp.getCreateTime() != null ? DateUtil.getDateStr(tp.getCreateTime(), DateUtil.FMT_DEFAULT) : "";
            String editTime = tp.getEditTime() != null ? DateUtil.getDateStr(tp.getEditTime(), DateUtil.FMT_DEFAULT) : "";
            map.put("createTime", createTime);
            map.put("createUser", tp.getCreateUser());
            map.put("editTime", editTime);
            map.put("editUser", tp.getEditUser());
            map.put("resourceID", tp.getResourceID());
            map.put("name", tp.getName());
            map.put("number", tp.getNumber());
            map.put("status", tp.getStatus());
            map.put("statusName", tp.getStatusName());
            map.put("startDate", startDate);
            map.put("endDate", endDate);
            map.put("managerResourceID", tp.getManagerResourceID());
            map.put("ManagerName", tp.getManagerName());
            map.put("describeInfo", tp.getDescribeInfo());
            map.put("hasChildren", hasChildren);
            map.put("parentResourceID", tp.getParentResourceID());
            map.put("projectType", tp.getProjectType());
            //测试方式
            List<String> testModeList = BinaryDecimalUtil.TenToDicVal(tp.getTestMode());
            String testModeValue = "";
            for (String s : testModeList) {
                testModeValue = testModeValue + (tmMap.get(s) == null ? "" : tmMap.get(s).toString()) + ",";
            }

            map.put("testModeKey", testModeList);
            map.put("testModeValue", "".equals(testModeValue) ? "" : testModeValue.substring(0, testModeValue.length() - 1));
            map.put("projectTypeName", tp.getProjectType() == null ? "" : testProjectTypeMap.get(tp.getProjectType().toString()));
            /* 处理进度数据 */
            if (tpmap != null && !tpmap.isEmpty()) {
                String progress = tp.getResourceID().toString();
                map.put("progress", tpmap.get(progress) == 0 ? 0 : tpmap.get(progress) + "%");
            } else {
                map.put("progress", 0);
            }
            resultList.add(map);
        }
        return resultList;
    }

    /**
     * @return
     * @Title: findTestProjectStatus
     * @Description:查询项目状态
     * <AUTHOR>
     * @date 2020年4月21日 下午2:55:44
     */
    @GetMapping("/findTestProjectStatus")
    public Result<?> findTestProjectStatus() {
        String token = HttpRequestUtils.getCurrentRequestToken();
        return feignDataDesignToBasicService.findTextNameAndValueByName("TESTPROJECTSTATUS", token);
    }

    /**
     * @param request
     * @return
     * @Title: validateNumber
     * @Description:验证项目编号是否重复
     * <AUTHOR>
     * @date 2020年4月21日 下午12:42:19
     */
    @PostMapping("/validateNumber")
    @ResponseBody
    public Result<?> validateNumber(HttpServletRequest request) {
        String number = request.getParameter("number");
        String resourceID = request.getParameter("resourceID");
        return testProjectService.validateNumber(number, resourceID);
    }

    /**
     * @param request
     * @return
     * @Title: addAndUpdateTestProject
     * @Description: 新增修改项目
     * <AUTHOR>
     * @date 2020年4月21日 下午1:48:46
     */
    @PostMapping("/addAndUpdateTestProject")
    @ResponseBody
    public Result<?> addAndUpdateTestProject(@RequestParam(value="files",required = false) MultipartFile[] files, HttpServletRequest request) {
        if (files == null) {
            files = new MultipartFile[] {};
        }
        UserVo user = loginUserUtil.getLoginUser(request);
        String parentResourceID = request.getParameter("parentResourceID");
        String resourceID = request.getParameter("resourceID");
        String name = request.getParameter("name");
        String number = request.getParameter("number");
        String status = request.getParameter("status");
        String statusName = request.getParameter("statusName");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        String managerResourceID = request.getParameter("managerResourceID");
        String managerName = request.getParameter("managerName");
        String describeInfo = request.getParameter("describeInfo");
        String projectType = request.getParameter("projectType");
        String testMode = request.getParameter("testMode");
        Map<String, InputStream> fileMap = new HashMap<>();
        if(files!=null){
            for (MultipartFile file : files) {
                try {
                    fileMap.put(file.getOriginalFilename(), file.getInputStream());
                } catch (IOException e) {
                    return Result.renderError("文件异常！");
                }
            }
        }
        return testProjectService.addAndUpdateTestProject(parentResourceID, resourceID, name, number, status, statusName,
                startDate, endDate, managerResourceID, managerName, describeInfo, fileMap, user.getUserNumber(), projectType, testMode, files);
    }

    /**
     * @param request
     * @return
     * @Title: validateDeleteTestProject
     * @Description: 删除项目前验证下是否关联用户和需求
     * <AUTHOR>
     * @date 2020年4月21日 下午2:07:34
     */
    @PostMapping("/validateDeleteTestProject")
    @ResponseBody
    public Result<?> validateDeleteTestProject(HttpServletRequest request) {
        String resourceIDs = request.getParameter("resourceIDs");
        return testProjectService.validateDeleteTestProject(resourceIDs);
    }

    /**
     * @param request
     * @return
     * @Title: deleteTestProject
     * @Description: 批量删除项目
     * <AUTHOR>
     * @date 2020年4月21日 下午2:06:27
     */
    @PostMapping("/deleteTestProject")
    @ResponseBody
    public Result<?> deleteTestProject(HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String resourceIDs = request.getParameter("resourceIDs");
        return testProjectService.deleteTestProject(resourceIDs, user.getUserNumber());
    }

    /**
     * @param [request]
     * @return com.jettech.dto.Result<?>
     * <AUTHOR>
     * @description 确认删除项目
     * @date 2020年11月24日 17:02
     **/
    @PostMapping("/confirmDeleteTestProject")
    @ResponseBody
    public Result<?> confirmDeleteTestProject(HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String resourceIDs = request.getParameter("resourceIDs");
        return testProjectService.confirmDeleteTestProject(resourceIDs, user.getUserNumber());
    }

    /**
     * @param request
     * @return
     * @Title: findAllUser
     * @Description: 查询所有人员
     * <AUTHOR>
     * @date 2020年4月21日 下午4:27:44
     */
    @GetMapping("/findAllUser")
    @ResponseBody
    public Result<?> findAllUser(HttpServletRequest request) {
        return testProjectService.findAllUser();
    }

    /**
     * @param request
     * @return
     * @Title: findAttachmentList
     * @Description: 获取附件列表
     * <AUTHOR>
     * @date 2020年4月21日 下午7:17:16
     */
    @PostMapping("/findAttachmentList")
    @ResponseBody
    public Result<?> findAttachmentList(HttpServletRequest request) {
        String resourceID = request.getParameter("resourceID");
        if (useFileService) {
            Result<List<FileIdName>> recode = feignDataDesignToFileService.getFileIdList(Long.parseLong(resourceID), ObjectTypeEnum.PROJECT.getValue());
            List<FileIdName> fileIdNames = recode.getObj();
            List<String> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(fileIdNames)) {
                for (FileIdName item : fileIdNames) {
                    list.add(item.getName());
                }
            }
            return Result.renderSuccess(list);
        } else {
            return testProjectService.findAttachmentList(resourceID);
        }
    }

    /**
     * @param request
     * @return
     * @Title: deleteAttachmentList
     * @Description: 删除附件
     * <AUTHOR>
     * @date 2020年4月21日 下午7:17:39
     */
    @PostMapping("/deleteAttachment")
    @ResponseBody
    public Result<?> deleteAttachmentList(HttpServletRequest request) {
        String resourceID = request.getParameter("resourceID");
        String fileName = request.getParameter("fileName");
        if (useFileService) {
            Result<List<FileInfo>> record = feignDataDesignToFileService.getFileInfoList(Long.parseLong(resourceID), ObjectTypeEnum.PROJECT.getValue());
            List<FileInfo> fileInfos = record.getObj();
            if (!CollectionUtils.isEmpty(fileInfos)) {
                for (FileInfo item : fileInfos) {
                    if (item.getOriginalFilename().equals(fileName)) {
                        return feignDataDesignToFileService.deleteFile(item.getResourceId() + "");

                    }
                }
            }
        } else {
            return testProjectService.deleteAttachmentList(resourceID, fileName);

        }
        return null;
    }

    /**
     * @param request
     * @return
     * @Title: downloadAttachmentList
     * @Description: 下载附件
     * <AUTHOR>
     * @date 2020年4月21日 下午7:18:08
     */
    @PostMapping("/downloadAttachment")
    @ResponseBody
    public Result<?> downloadAttachmentList(HttpServletRequest request, HttpServletResponse response) {
        String resourceID = request.getParameter("resourceID");
        String fileName = request.getParameter("fileName");
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("项目的resourceID为空！");
        }
        if (fileName == null || "".equals(fileName)) {
            return Result.renderError("附件名称为空！");
        }
        try {
            byte[] data = null;
            //传入的参数是项目的resourceId 蛋疼
            if (useFileService) {
                Result<List<FileInfo>> record = feignDataDesignToFileService.getFileInfoList(Long.parseLong(resourceID), ObjectTypeEnum.PROJECT.getValue());
                List<FileInfo> fileInfos = record.getObj();
                if (!CollectionUtils.isEmpty(fileInfos)) {
                    for (FileInfo item : fileInfos) {
                        if (item.getOriginalFilename().equals(fileName)) {
                            data = feignDataDesignToFileService.getContent(item.getResourceId());

                        }
                    }
                }
            } else {
                data = testProjectService.getFileData(resourceID, fileName);
            }

            fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");
            response.setContentType("application/octet-stream;charset=utf-8");

            response.setContentType(request.getServletContext().getMimeType(fileName));
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
//            response.getOutputStream().write(data);
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            //接口得到的文件数组需要转码。原因没有弄清楚
            byte[] decodedBytes = Base64.getMimeDecoder().decode(data);
            //String data1 = new String(decodedBytes);
            response.getOutputStream().write(decodedBytes);
            return null;
        } catch (Exception e) {
            return Result.renderError("下载异常！");
        }
    }

    /**
     * @param request
     * @return
     * @Title: previewAttachment
     * @Description: 预览附件
     * <AUTHOR>
     * @date 2020年4月21日 下午7:18:15
     */
    @PostMapping("/previewAttachment")
    @ResponseBody
    public Result<?> previewAttachment(HttpServletRequest request) {
        String resourceID = request.getParameter("resourceID");
        String fileName = request.getParameter("fileName");
        return testProjectService.previewAttachment(resourceID, fileName);
    }

    /**
     * @param request
     * @return
     * @Title: findAllTestProject
     * @Description: 查询所有项目name和resourceID
     * <AUTHOR>
     * @date 2020年4月22日 下午3:37:19
     */
    @GetMapping("/findAllTestProjectNameAndResourceID")
    @ResponseBody
    public Result<?> findAllTestProjectNameAndResourceID(HttpServletRequest request) {
        return testProjectService.findAllTestProjectNameAndResourceID();
    }

    /**
     * @param
     * @return
     * @Title: findNoCloseTestProject
     * @Description: 查询非关闭的项目name和resourceID列表
     * <AUTHOR>
     * @date 2020年12月29日 下午3:37:19
     */
    @GetMapping("/findNoCloseTestProjectNameAndResourceID")
    @ResponseBody
    @ApiOperation(value = "查询非关闭的项目name和resourceID列表", tags = "查询非关闭的项目name和resourceID列表")
    public Result<?> findNoCloseTestProjectNameAndResourceID() {
        return testProjectService.findNoCloseTestProjectNameAndResourceID();
    }

    /**
     * 查询项目下的所有需求及需求中的案例、缺陷
     * bao_qiuxu
     */
    @GetMapping("/findDemandByTestProjectResourceID/{resourceID}")
    @ResponseBody
    public Result<?> findDemandByTestProjectResourceID(@PathVariable String resourceID) {
        return testProjectService.findDemandByTestProjectResourceID(resourceID);
    }


    /**
     * @param request
     * @return List<Map < String, Object>>
     * @Title: findAllTestProjectByUser
     * @Description: 查询工作台当前用户所有项目name和resourceID
     * <AUTHOR>
     * @date 2020年6月19日
     */
    @GetMapping("/findAllTestProjectByUser")
    @ResponseBody
    public Result<List<Map<String, Object>>> findAllTestProjectByUser(HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String key = RedisCacheConfig.FIND_ALL_PROJECT_BY_USER;
        String userNumber = user.getUserNumber();
        String field = userNumber;
        Result result = null;
        Object r = this.jedisUtil.getHsetValue(key, field);
        if (r == null) {
            result = testProjectService.findAllTestProjectByUser(userNumber);
            this.jedisUtil.setHash(key, field, result, 6 * 60);
        } else {
            result = (Result<List<Map<String, Object>>>) r;
        }

        return result;
    }

    /**
     * @param request
     * @return
     * @Title: findChildTestProject
     * @Description: 查询子项目
     * <AUTHOR>
     * @date 2020年7月13日 下午5:32:45
     */
    @PostMapping("/findChildTestProject")
    @ResponseBody
    public Result findChildTestProject(HttpServletRequest request) {
        String resourceID = request.getParameter("resourceID");
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("参数resourceID为空！");
        }
        List<TestProject> resultList = testProjectService.findChildTestProjectByResourceID(resourceID);

        // 返回项目类型字典值名称
        Map<String, String> testProjectTypeMap = new HashMap<>();
        Result result = feignDataDesignToBasicService.findByName("TESTPROJECTTYPE", HttpRequestUtils.getCurrentRequestToken());//findByNamesFromDataDesign("CASETYPE");
        List<Map<String, String>> list = (List<Map<String, String>>) result.getObj();
        list.stream().forEach(x -> {
            testProjectTypeMap.put(x.get("value"), x.get("textName"));
        });
        return Result.renderSuccess(translateData(resultList, testProjectTypeMap));
    }


    /**
     * @param "[]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: loadParentDemand
     * @description: 加载父节点项目
     * <AUTHOR>
     * @date 2020/7/13 17:18
     */
    @PostMapping("/loadParentTestProject")
    @ResponseBody
    public Result loadParentTestProject(HttpServletRequest request) {

        String name = request.getParameter("name");
        return testProjectService.loadParentTestProject(name);
    }

    /**
     * @param "[resourceID]"
     * @return com.jettech.dto.Result
     * @throws
     * @Title: getChildrenTestProject
     * @description: 父节点查询子节点项目
     * <AUTHOR>
     * @date 2020/7/13 17:36
     */
    @GetMapping("/getChildrenTestProject/{resourceID}")
    public Result getChildrenTestProject(@PathVariable String resourceID) {
        return testProjectService.getChildrenTestProject(resourceID);
    }

    /**
     * @param request
     * @return
     * @Title: findProjectAndGroupTree
     * @Description: 查询项目和项目组树
     * <AUTHOR>
     * @date 2020年7月14日 下午3:52:43
     */
    @GetMapping("/findProjectAndGroupTree/{groupType}")
    @ResponseBody
    public Result findProjectAndGroupTree(HttpServletRequest request, @PathVariable String groupType) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        return testProjectService.findProjectAndGroupTree(userNumber, groupType);
    }

    /**
     * @param request
     * @return
     * @Title: addTestProjectValidate
     * @Description: 新增项目前校验
     * <AUTHOR>
     * @date 2020年7月20日 上午10:42:47
     */
    @PostMapping("/addTestProjectValidate")
    @ResponseBody
    public Result<String> addTestProjectValidate(HttpServletRequest request) {
        String resourceID = request.getParameter("resourceID");
        if (resourceID == null || "".equals(resourceID)) {
            return Result.renderError("参数resourceID为空！");
        }
        return testProjectService.addTestProjectValidate(Long.valueOf(resourceID));
    }

    /**
     * @Method: initTestProjectTree
     * @Description: 加载项目树
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/7/20
     */
    @GetMapping("/initTestProjectTree")
    @ResponseBody
    public Result initTestProjectTree() {
        return testProjectService.initTestProjectTree();
    }

    /**
     * @Method: initProjectTree
     * @Description: 缺陷流程配置左侧树初始化
     * @Param: " [name] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/8/17
     */
    @GetMapping("/initProjectTree")
    @ResponseBody
    public Result initProjectTree(@RequestParam("name") String name) {
        return testProjectService.initProjectTree(name);
    }


    /**
     * @Title: findProjectTree
     * @Description: 查询项目树
     * <AUTHOR>
     * @date 2020年8月21日
     */
    @GetMapping("/findProjectTree/{groupType}")
    @ResponseBody
    public Result findProjectTree(HttpServletRequest request, @PathVariable String groupType) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        String key = RedisCacheConfig.FIND_PROJECT_TREE;
        String field = userNumber + groupType;
        Object r = this.jedisUtil.getHsetValue(key, field);
        List<Map<String, Object>> tree = null;
        if (null == r) {
            tree = testProjectService.findProjectTree(userNumber, groupType);
            this.jedisUtil.setHash(key, field, tree, 15 * 60);
        } else {
            tree = (List<Map<String, Object>>) r;
        }
        return Result.renderSuccess(tree);
    }

    /**
     * @Title: findTestProjectByResourceID
     * @Description: 查询项目
     * <AUTHOR>
     * @date 2020年8月21日
     */
    @GetMapping("/findTestProjectByResourceID")
    @ResponseBody
    public Result findTestProjectByResourceID(@RequestParam("resourceID") String resourceID) {
        return Result.renderSuccess(testProjectService.findByResourceID(Long.valueOf(resourceID)).getName());
    }


    @PostMapping("/findAllProject")
    public Result findAllProject() {
        List<TestProject> all = testProjectService.findAll();
        return Result.renderSuccess(all);
    }

    /**
     * 查询节点数据
     *
     * @param resourceID
     * @param resourceID
     * @return
     */
    @GetMapping("/getNodeList")
    public Result getNodeList(@RequestParam("nodeType") String nodeType, @RequestParam("resourceID") String resourceID, @RequestParam("userNumber") String userNumber) {
        return Result.renderSuccess(testProjectService.getNodeList(nodeType, resourceID, "1", userNumber));
    }

    /**
     * @Method: findProjectByName
     * @Description: 通过项目名称查询项目
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/9/22
     */
    @PostMapping("/findProjectByName")
    public Result findProjectByName(@RequestParam String projectName) {
        List<TestProject> list = testProjectService.findByName(projectName);
        return Result.renderSuccess(list);
    }

    /**
     * 根据条件查询项目
     *
     * @param request
     * @return
     */
    @PostMapping("/findProjectByCondition")
    public Result findTestProjectByCondition(HttpServletRequest request) {
        String name = request.getParameter("name");
        String status = request.getParameter("status");
        String number = request.getParameter("number");
        String projectType = request.getParameter("projectType");
        String testMode = request.getParameter("testMode")!=null ? request.getParameter("testMode"):null;
        List<TestProject> list = testProjectService.findTestProjectByCondition(name, status, number, projectType,testMode);
        return Result.renderSuccess(list);
    }

    /**
     * @Method: findParentProjectByProjectResourceID
     * @Description: 项目rid查询父节点项目rid
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/10/26
     */
    @GetMapping("/findParentProjectByProjectResourceID/{projectResourceID}")
    public Result findParentProjectByProjectResourceID(@PathVariable String projectResourceID) {
        return testProjectService.findParentProjectByProjectResourceID(projectResourceID);
    }

    /**
     * @Method: findChildByParentRid
     * @Description: 根节点查询子项目
     * @Param: " [] "
     * @return: com.jettech.dto.Result
     * @Author: wws
     * @Date: 2020/10/27
     */
    @GetMapping("/findChildByParentRid/{parentProjectResourceID}")
    public Result findChildByParentRid(@PathVariable String parentProjectResourceID) {
        return testProjectService.findChildByParentRid(parentProjectResourceID);
    }

    @GetMapping("/findByResourceID")
    public Result findByResourceID(@RequestParam("resourceID") String resourceID) {
        return Result.renderSuccess(testProjectService.findByResourceID(Long.valueOf(resourceID)));
    }

    /**
     * @param request
     * @return
     * @Title: createTestProject
     * @Description: 富滇银行新增项目
     * <AUTHOR>
     * @date 2021年1月15日 下午1:48:46
     */
    @PostMapping("/createTestProject")
    @ResponseBody
    public Result<?> createTestProject(HttpServletRequest request) {
        UserVo createUser = new UserVo();
        createUser.setUserNumber("System");
        TestProjectView testProjectView = getTestProjectView(request);
        return testProjectService.createTestProject(testProjectView, createUser.getUserNumber());
    }

    /**
     * @param request
     * @return
     * @Title: updateTestProject
     * @Description: 富滇银行修改项目
     * <AUTHOR>
     * @date 2021年1月15日 下午1:48:46
     */
    @PostMapping("/updateTestProject")
    @ResponseBody
    public Result<?> updateTestProject(HttpServletRequest request) {
        UserVo createUser = new UserVo();
        createUser.setUserNumber("System");
        TestProjectView testProjectView = getTestProjectView(request);
        return testProjectService.updateTestProject(testProjectView, createUser.getUserNumber());
    }

    public TestProjectView getTestProjectView(HttpServletRequest request) {

        String name = request.getParameter("name");
        String number = request.getParameter("number");
        String status = request.getParameter("status");
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");
        String managerName = request.getParameter("managerName");
        String describeInfo = request.getParameter("describeInfo");
        String projectType = request.getParameter("projectType");

        return new TestProjectView(name, number, status, startDate, endDate, managerName, describeInfo, projectType);
    }

    @PostMapping("/findProjectSelectDataByDataAuth")
    public Result findAllByDataAuth(HttpServletRequest request) {
        String pageNumber = request.getParameter("pageNumber");
        String pageSize = request.getParameter("pageSize");
        String projectName = request.getParameter("projectName");
        String disable = request.getParameter("disable");
        Map<String, String> params = new HashMap<>();
        params.put("pageNumber", pageNumber);
        params.put("pageSize", pageSize);
        params.put("projectName", projectName);
        params.put("disable", disable);

        Map<String, Object> map = testProjectService.findProjectSelectDataByDataAuth(params);
        return Result.renderSuccess(map);
    }

    @GetMapping("/findAllProjectTree")
    public Result findAllProjectTree() {
        return testProjectService.findAllProjectTree();
    }

    /**
     * @param
     * @return Result
     * @Description 项目查询（报工处使用）
     * <AUTHOR>
     * @Date 2023年1月6日
     */
    @GetMapping("/findProjectTreeOfreport")
    @ResponseBody
    public Result findProjectTreeOfreport(HttpServletRequest request) {
        String userNumber = LoginUserUtil.getUserNumber(request);
        String key = RedisCacheConfig.FIND_PROJECT_TREE;
        String field = userNumber + "wreport";
        Object r = this.jedisUtil.getHsetValue(key, field);
        List<Map<String, Object>> tree = null;
        if (null == r) {
            tree = testProjectService.findProjectTreeOfreport(userNumber, "");
            this.jedisUtil.setHash(key, field, tree, 15 * 60);
        } else {
            tree = (List<Map<String, Object>>) r;
        }
        return Result.renderSuccess(tree);
    }

    /**
     * @param request
     * @return
     * @Title: validateName
     * @Description:验证项目名称是否重复（全局唯一）
     * <AUTHOR>
     * @date 2023年2月1日
     */
    @PostMapping("/validateName")
    @ResponseBody
    public Result<?> validateName(HttpServletRequest request) {
        String name = request.getParameter("name");
        String resourceID = request.getParameter("resourceID");
        return testProjectService.validateName(name, resourceID);
    }

    @ResponseBody
    @PostMapping("/getProjectGroupByResourceIdList")
    public List<ProjectGroup> getProjectGroupByResourceIdList(@RequestBody List<String> projectGroupResourceIDList) {
        List<ProjectGroup> list = new ArrayList<>();
        List<ProjectGroup> groups = projectGroupService.findByResourceIDIn(projectGroupResourceIDList);
        Set<Long> projectResourceIdSet = groups.stream().map(ProjectGroup::getTestProjectResourceID).collect(Collectors.toSet());
        projectResourceIdSet.forEach(projectResourceId -> {
            list.addAll(projectGroupService.findByTestProjectResourceID(projectResourceId, "1"));
        });

        return list;
    }
}
