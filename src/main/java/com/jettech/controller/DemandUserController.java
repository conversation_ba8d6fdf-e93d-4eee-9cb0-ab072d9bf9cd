package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.model.DemandUser;
import com.jettech.service.iservice.IDemandUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * @ClassName DemandUserController
 * @Description 需求与人员controller
 * <AUTHOR>
 * @date 2019年12月4日
 */
@Controller
@RequestMapping("/demanduser")
public class DemandUserController {

    private static final Logger logger = LoggerFactory.getLogger(DemandUserController.class);
    @Autowired
    private IDemandUserService demandUserService;
    @Autowired
	private LoginUserUtil loginUserUtil;
    @Autowired
	private IFeignDataDesignToBasicService feignDataDesignToBasicService;
    
    /**
     * @Title getUserByDemandResourceID
     * @Description 根据需求id查询人员
     * @param demandResourceID
     * @return   Result 
     * <AUTHOR>
     * @data Dec 5, 20194:00:10 PM
     */
    @GetMapping("/getUserByDemandResourceID/{demandResourceID}")
    @ResponseBody
     public Result getUserByDemandResourceID(@PathVariable Long demandResourceID) {
     	List<Map<String, String>> resultMaps = demandUserService.getUserByDemandResourceID(demandResourceID);
     	return Result.renderSuccess(resultMaps);
     }
    
    /**
	 * 
	 * @Title:joinDemand
	 * @Description:加入需求
	 * @param 
	 * @return Result<?>
	 * @author: wu_yancheng
	 * @date 2019年12月6日下午3:13:04
	 */
	@RequestMapping(value = "/joinDemand", method = RequestMethod.POST)
	@ResponseBody
	public Result<?> joinDemand(HttpServletRequest request){
		UserVo user = loginUserUtil.getLoginUser(request);
		
		String demandResourceIDs = request.getParameter("demandResourceIDs");
		String token = HttpRequestUtils.getCurrentRequestToken();
		JettechUserDTO userDTO = feignDataDesignToBasicService.findByNumber(user.getUserNumber(),token);
		
		Long userResourceID = userDTO.getResourceID();
		if(demandResourceIDs!=null) {
			List<String> demandRidList = Arrays.asList(demandResourceIDs.split(","));
			return demandUserService.joinDemand(demandRidList,String.valueOf(userResourceID) , user.getUserNumber());
		}else {
			return Result.renderError("请选择需求");
		}
		
		
	}
	
    /**
     * @Title workbenchGetUserByDemandResourceID
     * @Description 根据需求id查询人员
     * @param "demandResourceID"
     * @return   Result 
     * <AUTHOR>
     * @data Dec 5, 20194:00:10 PM
     */
    @RequestMapping(value = "/workbenchGetUserByDemandResourceID", method = RequestMethod.POST)
	@ResponseBody
     public Result workbenchGetUserByDemandResourceID(@RequestBody Map<String, String> params) {
    	String demandResourceID = params.get("demandResourceID");
     	List<Map<String, String>> resultMaps = demandUserService.getUserByDemandResourceID(Long.parseLong(demandResourceID));
     	return Result.renderSuccess(resultMaps);
     }

    /**
     *
     * @Title:findUserByDemandRIDAndRole
     * @Description:根据需求id和角色查询人员
     * @param
     * @return Result
     * @author: wu_yancheng
     * @date 2020年2月12日下午2:50:34
     */
    @RequestMapping(value = "/findUserByDemandRIDAndRole", method = RequestMethod.POST)
    @ResponseBody
     public Result findUserByDemandRIDAndRole(HttpServletRequest request) {
    	String demandResourceID = request.getParameter("demandResourceID");
     	List<String> list = demandUserService.findUserByDemandRIDAndRole(demandResourceID,"测试");
     	return Result.renderSuccess(list);
     }

     /**
       * @Title: findUserByDemandResourceID
       * @description: 通过需求查询关联用户
       * @param "[demandResourceID]"
       * @return com.jettech.dto.Result
       * @throws
       * <AUTHOR>
       * @date 2020/2/12 16:04
       */
     @GetMapping("/findUserByDemandResourceID/{demandResourceID}")
     public @ResponseBody Result findUserByDemandResourceID(@PathVariable String demandResourceID){
		 List<DemandUser> list = demandUserService.findBydemandResourceID(Long.valueOf(demandResourceID));
		 return Result.renderSuccess(list);
	 }

     /**
      * @Title: findDevUserByDemandResourceID
      * @Description: 根据需求查询关联的属性是开发的人员
      * @Param: "[demandResourceID]"
      * @Return: "com.jettech.dto.Result"
      * @Author: xpp
      * @Date: 2020/2/12
      */
     @GetMapping("/findDevUserByDemandResourceID/{demandResourceID}")
     public  @ResponseBody Result findDevUserByDemandResourceID(@PathVariable String demandResourceID){
     	 // 先查询需求下关联的所有人员
		 List<Map<String, Object>> userList = demandUserService.findUserByDemandResourceID(demandResourceID);
		 // 筛选出关联人员中属于开发的人员IDs
		 List<String> userResourceIDList = userList.stream()
				 .filter(s -> s.get("role").equals("开发人员"))
				 .map(s -> String.valueOf(s.get("resourceID")))
				 .collect(Collectors.toList());
		 return Result.renderSuccess(userResourceIDList);
	 }
     /**
      * @Description 通过需求rid和人员rid查询人员与需求关联表DTO
      * <AUTHOR>
      * @date 2020-02-18 10:58
       * @param demandResourceID
      * @param userResourceID
      * @return com.jettech.dto.Result
      */
     @GetMapping("findDemandUserMapByDemandResourceIDAndUserResourceID/{demandResourceID}/{userResourceID}")
	 @ResponseBody
     public Result findDemandUserMapByDemandResourceIDAndUserResourceID(@PathVariable String demandResourceID,@PathVariable String userResourceID){
    	return demandUserService.findDemandUserMapByDemandResourceIDAndUserResourceID(demandResourceID,userResourceID);
	 }

	/**
	 * @Title: findDemandRelevanceUser
	 * @Description:  根据需求查询关联人员,消除重复值
	 * @Param: "[demandResourceID]"
	 * @Return: "com.jettech.dto.Result<?>"
	 * @Author: xpp
	 * @Date: 2020/2/25
	 */
	@GetMapping("/findDemandRelevanceUser/{testTaskResourceID}")
	public @ResponseBody
	Result<?> findDemandRelevanceUser(@PathVariable("testTaskResourceID")String testTaskResourceID) {
		if(testTaskResourceID==null || "".equals(testTaskResourceID)) {
			logger.debug("传入的任务主键为空!");
			return Result.renderError("传入的任务主键为空!");
		}
		List<Map<String,String>> resultList=demandUserService.getUserByTestTaskResourceID(testTaskResourceID);
		return  Result.renderSuccess(resultList);
	}
	/***
	 *
	 * @Method : getUserByDemandResourceIDAndUserGroupResourceIDs
	 * @Description : 通过需求resourceID和角色resourceIDs查询人员
	 * @param demandResourceID : 需求resourceID
	 * @param userGroupResourceIDs : 角色resourceIDs
	 * @return : com.jettech.dto.Result<java.util.Map<java.lang.String,java.lang.String>>
	 * <AUTHOR> Hansiwei.
	 * @CreateDate : 2020-07-13 周一 17:53:25
	 *
	 */
	@PostMapping("/getUserByDemandResourceIDAndUserGroupResourceIDs")
	@ResponseBody
	public Result<Map<String,String>> getUserByDemandResourceIDAndUserGroupResourceIDs(@RequestParam("demandResourceID")String demandResourceID, @RequestParam("userGroupResourceIDs")List<String> userGroupResourceIDs){
		return Result.renderSuccess(demandUserService.getUserByDemandResourceIDAndUserGroupResourceIDs(demandResourceID,userGroupResourceIDs));
	}
}
