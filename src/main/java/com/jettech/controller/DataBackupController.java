package com.jettech.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jettech.DTO.DataBackupHistoryDTO;
import com.jettech.common.dto.Result;
import com.jettech.common.util.DateUtil;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.ParamConfig;
import com.jettech.common.util.UserVo;
import com.jettech.model.DataBackupHistory;
import com.jettech.model.Demand;
import com.jettech.service.iservice.IDataBackupHistoryService;
import com.jettech.service.iservice.IDataBackupService;
import com.jettech.service.iservice.IDemandService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dataBackup")
public class DataBackupController {

    @Autowired
    private LoginUserUtil loginUserUtil;

    @Autowired
    private ParamConfig paramConfig;

    @Autowired
    private IDataBackupService dataBackupService;

    @Autowired
    private IDemandService demandService;

    @Autowired
    private IDataBackupHistoryService dataBackupHistoryService;

    @PostMapping("/page")
    public Result page(@RequestBody Map<String, Object> params) {
        int pageSize = params.get("pageSize") != null ? (int) params.get("pageSize") : 10;
        int pageNumber = params.get("pageNumber") != null ? (int) params.get("pageNumber") : 0;
        Page pageResult = PageHelper.startPage(pageNumber, pageSize);
        List<DataBackupHistory> dataBackupHistoryList = dataBackupHistoryService.findByParams(params);
        List<DataBackupHistoryDTO> dataBackupHistoryDTOList = JSON.parseArray(JSON.toJSONString(dataBackupHistoryList), DataBackupHistoryDTO.class);
        pageResult.clear();
        pageResult.addAll(dataBackupHistoryDTOList);
        dataBackupHistoryDTOList.stream().forEach(item -> {
            if (item.getStatus() == 0) {
                item.setStatusText("未归档");
            }
            if (item.getStatus() == -1) {
                item.setStatusText("失败");
            }
            if (item.getStatus() == 1) {
                item.setStatusText("成功");
            }
        });
        return Result.renderSuccess(pageResult.toPageSerializable());
    }

    /**
     * @param params
     * @return
     */
    @PostMapping("/backupDemandData")
    public Result backupDemandData(@RequestBody Map<String, Object> params,
                                   HttpServletRequest request) {
        UserVo createUser = loginUserUtil.getLoginUser(request);
        if (params.get("demandResourceIDs") == null) {
            return Result.renderError("缺少需求RID参数！");
        }
        if (params.get("name") == null || StringUtils.isEmpty(params.get("name").toString())) {
            return Result.renderError("缺少档案名称参数！");
        }
        String demandResourceIDs = params.get("demandResourceIDs").toString();
        List<Demand> demandList = this.demandService.findByResourceIDIn(Arrays.asList(demandResourceIDs.split(",")));
        if (demandList.isEmpty()) {
            return Result.renderError("需求不存在！");
        }

        boolean clear = false;
        if (params.get("clear") != null) {
            try {
                clear = (boolean) params.get("clear");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //0:延时执行   1:立即执行
        int type = 0;
        if (params.get("type") != null) {
            try {
                type = (int) params.get("type");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        DataBackupHistory dataBackupHistory = new DataBackupHistory();
        dataBackupHistory.setClear(clear);
        dataBackupHistory.setType(type);
        dataBackupHistory.setName(params.get("name").toString());
        dataBackupHistory.setFileName(params.get("name").toString() + "_" + DateUtil.getDateStr(new Date(), DateUtil.FMT_YYYYMMDDHHMMSS) + "_backup.sql");
        dataBackupHistory.setFilePath(paramConfig.getAttachmentPath());
        dataBackupHistory.setDemandResourceID(demandList.stream().map(item -> item.getResourceID().toString()).collect(Collectors.joining(",")));
        dataBackupHistory.setDemandName(demandList.stream().map(Demand::getName).collect(Collectors.joining(",")));
        dataBackupHistory.setComment(params.get("comment") != null ? params.get("comment").toString() : "");

        if (type == 1) {
            int status = this.dataBackupService.dataBackup(demandList.stream().map(Demand::getResourceID).collect(Collectors.toList()), dataBackupHistory.getFilePath(), dataBackupHistory.getFileName(), clear);
            dataBackupHistory.setStatus(status);
            dataBackupHistory.setBackupTime(new Date());
        }
        dataBackupHistoryService.save(dataBackupHistory, createUser.getUserNumber());
        return Result.renderSuccess();
    }

    @PostMapping("/download")
    public Object download(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        if (params.get("resourceID") == null) {
            return Result.renderError("缺少RID参数！");
        }
        DataBackupHistory dataBackupHistory = this.dataBackupHistoryService.findByResourceID(Long.parseLong(params.get("resourceID").toString()));
        if (dataBackupHistory == null) {
            return Result.renderError("归档不存在！");
        }
        File file = new File(dataBackupHistory.getFilePath() + File.separator + dataBackupHistory.getFileName());
        InputStream inputStream=null;
        if (file.exists()) {
            try {
                 inputStream = new FileInputStream(file);
                byte[] data = new byte[(int) file.length()];
                inputStream.read(data);
                inputStream.close();
                response.setHeader("Content-Disposition", "attachment;filename=" + dataBackupHistory.getFilePath() + File.separator + dataBackupHistory.getFileName());
                response.getOutputStream().write(data);
            } catch (Exception e) {
                e.printStackTrace();
            }finally {
                try {
                    if(inputStream!=null){
                        inputStream.close();
                    }
                }catch (Exception e){

                }
            }
        } else {
            return null;
        }
        return null;
    }

    @DeleteMapping("/del/{ids}")
    public Result del(HttpServletRequest request, @PathVariable(value = "ids") String ids) {
        UserVo user = loginUserUtil.getLoginUser(request);
        int num = this.dataBackupHistoryService.del(ids, user);
        if (num > 0) {
            return Result.renderSuccess();
        } else {
            return Result.renderError();
        }
    }
}
