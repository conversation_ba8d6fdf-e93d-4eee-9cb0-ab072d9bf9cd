package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.EnvironmentPageDTO;
import com.jettech.common.util.ExcelUtils;
import com.jettech.common.util.HttpRequestUtils;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.feign.IFeignAssetsToBasic;
import com.jettech.model.Environment;
import com.jettech.service.iservice.IEnvironmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName EnvironmentController
 * @description 被测系统环境信息记录Controller层
 * <AUTHOR>
 * @create 20200630
 */
@Api(value="被测系统环境信息记录controller",tags={"被测系统环境信息记录接口"})
@RestController
@RequestMapping("/environment")
@SuppressWarnings("unchecked")
public class EnvironmentController {
	private static final Logger logger = LoggerFactory.getLogger(EnvironmentController.class);
	@Autowired
	private IEnvironmentService environmentService;

	/**
	 * @Title findEnvironmentPage
	 * @Description 分页查询环境信息
	 * @author: slq
	 * @date: 2020年6月30日 上午11:40:30
	 */
	@RequestMapping(value = "/findEnvironmentPage", method = RequestMethod.POST)
	@ResponseBody
	public Result<Page<Environment>> findEnvironmentPage(HttpServletRequest request, @RequestBody EnvironmentPageDTO environment){
		String rows = environment.getRows();// 默认为10
		if (rows == null|| "".equals(rows))
			rows = "10";
		String page = environment.getPage();// 默认为1
		if (page == null|| "".equals(page))
			page = "1";
		String order = environment.getOrder();
		if (order == null || "".equals(order))
			order = "createTime";
		String sort = environment.getSort();
		if (sort == null|| "".equals(sort))
			sort = "desc";
		PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows),Sort.Direction.fromString(sort), order);
		Page<Environment> environmentPage = environmentService.findEnvironmentPage(pageRequest,environment);
		return Result.renderSuccess(environmentPage);
		
	}
	/**
	 * @Title  addOrUpdateEnvironment     
	 * @Description  新建或修改环境   
	 * @author: slq    
	 * @date:   2020年6月30日 下午4:43:39
	 */
	@RequestMapping(value = "/addOrUpdateEnvironment", method = RequestMethod.POST)
	@ResponseBody
	public Result<String> addOrUpdateEnvironment(HttpServletRequest request, @RequestBody Environment environment){
		String userNumber = LoginUserUtil.getUserNumber(request);
		if(StringUtils.isEmpty(environment.getName())) {
			return Result.renderError("环境名称不允许为空！");
		}
		if(StringUtils.isEmpty(environment.getType())) {
			return Result.renderError("环境类型不允许为空！");
		}
		if(StringUtils.isEmpty(environment.getState())) {
			return Result.renderError("环境状态不允许为空！");
		}
		if(StringUtils.isEmpty(environment.getIp())) {
			return Result.renderError("IP不允许为空！");
		}
		if(StringUtils.isEmpty(environment.getSystemName())) {
			return Result.renderError("系统名称不允许为空！");
		}
		String result = environmentService.addOrUpdateEnvironment(environment,userNumber);
		if("修改成功！".equals(result) || "新建成功！".equals(result)) {
			return Result.renderSuccess(result);
		}else {
			return Result.renderError(result);
		}
		
	}
	
	/**
	 * @Title  deleteEnvironments     
	 * @Description 删除环境   
	 * @author: slq    
	 * @date:   2020年6月30日 下午5:11:02
	 */
	@RequestMapping(value = "/deleteEnvironments", method = RequestMethod.POST)
	@ResponseBody
	public Result<String> deleteEnvironments(HttpServletRequest request, @RequestBody List<String> environmentResID){
		String userNumber = LoginUserUtil.getUserNumber(request);
		if(environmentResID == null || environmentResID.isEmpty()) {
			Result.renderSuccess();
		}
		String result = environmentService.deleteEnvironments(environmentResID,userNumber);
		return Result.renderSuccess(result);
	}
	/**
	 * @Title  batchExportExcel     
	 * @Description  根据查询条件的结果批量导出环境   
	 * @author: slq    
	 * @date:   2020年6月30日 下午5:24:26
	 */
	@ApiOperation(value = "根据查询条件的结果批量导出环境")
	@PostMapping("/batchExportExcel")
	@ResponseBody
	public void batchExportExcel(@RequestBody Map map, HttpServletRequest request, HttpServletResponse response){
		String Agent = request.getHeader("User-Agent");
		map.put("request",request);
		map.put("response",response);
		map.put("Agent",Agent);
        environmentService.exportExcel(map);
	}
	/**
	 * @Title  findUpdateEnvironmentByResID     
	 * @Description 查询要修改的环境信息   
	 * @author: slq    
	 * @date:   2020年7月1日 上午11:51:04
	 */
	@RequestMapping(value = "/findUpdateEnvironmentByResID", method = RequestMethod.POST)
	@ResponseBody
	public Result<EnvironmentPageDTO> findUpdateEnvironmentByResID(HttpServletRequest request, @RequestParam("resourceID") String resourceID){
		EnvironmentPageDTO result = environmentService.findUpdateEnvironmentByResID(resourceID);
		return Result.renderSuccess(result);
	}
}
