package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.datadesign.DemandToTradeDTO;
import com.jettech.common.dto.datadesign.SystemModuleTradeDTO;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.model.Trade;
import com.jettech.service.iservice.ITradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName TradeController
 * @description 交易Controller
 * <AUTHOR>
 * @create 2019-11-04 19:07
 */
@RestController
@RequestMapping("/trade")
public class TradeController {

    @Autowired
    private ITradeService tradeService;

    @PostMapping
    /**
     * @Title saveOrUpdateTrade
     * @Description 新增或修改交易
     * @Params [request, params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/5
     */
    public Result saveOrUpdateTrade(HttpServletRequest request, @RequestBody Map<String,String> params){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return tradeService.saveOrUpdateTrade(params,userNumber);

    }

    @PostMapping(value="/checkResourceByTradeForDelete/{resourceID}")
    /**
     * @Title checkResourceByTradeForDelete
     * @Description 校验 删除时交易下是否维护数据
     * @Params [resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result checkResourceByTradeForDelete(@PathVariable String resourceID){
        return tradeService.findResourceByTrade(resourceID);
    }
    @DeleteMapping(value = "/{resourceID}")
    /**
     * @Title deleteTrade
     * @Description 删除交易
     * @Params [request, resourceID]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2019/11/6
     */
    public Result deleteTrade(HttpServletRequest request,@PathVariable String resourceID){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return tradeService.deleteTradeByResourceID(resourceID,userNumber);

    }

    /**
     * <AUTHOR>
     * @description 确认删除交易
     * @date 2020年11月25日 15:33
     * @param [request, resourceID]
     * @return com.jettech.dto.Result
     **/
    @DeleteMapping(value = "/confirmDeleteTrade/{resourceID}")
    public Result confirmDeleteTrade(HttpServletRequest request,@PathVariable String resourceID){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return tradeService.confirmDeleteTrade(resourceID,userNumber);

    }


    /**
     * @Title: whetherToRepeat
     * @Description: 判断交易名称是否重复
     * @Param: "[request, testCase]"
     * @Return: "com.jettech.dto.Result"
     * @Author: xpp
     * @Date: 2019/11/21
     */
    @PostMapping("/whetherToRepeat")
    public Result whetherToRepeat(@RequestBody Map<String, String> paramsMap) {
        return tradeService.whetherToRepeat(paramsMap);
    }

    /**
     * 通过交易反向查找测试系统树
     * @param tradeResourceIDs 交易RID
     * @return 反向树
     */
    @PostMapping("/reverseTradeTree")
    public Result getTestProjectTree(@RequestBody List<String> tradeResourceIDs) {
        return Result.renderSuccess(tradeService.getTestProjectTree(tradeResourceIDs));
    }

    /**
      * @Title: importTradeExcel
      * @description: 导入交易
      * @param "[file, moduleResouimportTraderceID, request]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2019/12/9 9:55
      */
    @PostMapping("/importTrade")
    public Result importTradeExcel(@RequestParam(value="file",required = false) MultipartFile file,
                                   @RequestParam("moduleResourceID") String moduleResourceID,
                                   @RequestParam("testSystemResourceID") String testSystemResourceID,
                                   HttpServletRequest request) throws IOException {
        String userNumber = LoginUserUtil.getUserNumber(request);
        return tradeService.importTradeExcel(file,moduleResourceID,testSystemResourceID,userNumber);
    }

    /**
      * @Title: downloadModal
      * @description: 下载模板
      * @param "[request, response]"
      * @return void
      * @throws
      * <AUTHOR>
      * @date 2019/12/9 11:21
      */
    @GetMapping("/downloadModal")
    public void downloadModal(HttpServletRequest request,HttpServletResponse response){
        String agent = request.getHeader("User-Agent");
        tradeService.downloadModel(agent,response);
    }

    /**
      * @Title: getTradeListAndObj
      * @description: 案例资产库服务接口，返回节点对象，交易list
      * @param "[]"
      * @return java.util.Map<java.lang.String,java.lang.Object>
      * @throws
      * <AUTHOR>
      * @date 2019/12/26 12:06
      */
    @PostMapping("/getTradeListAndObj")
    public Map<String,Object> getTradeListAndObj(@RequestParam("nodeType")String nodeType,
                                                 @RequestParam("nodeResourceID")String nodeResourceID){
        return tradeService.getTradeListAndObj(nodeType,nodeResourceID);
    }

    /**
      * @Title: findAllTrade
      * @description: 查询所有病变
      * @param "[]"
      * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
      * @throws
      * <AUTHOR>
      * @date 2019/12/26 12:48
      */
    @PostMapping("/findAllTrade")
    public Result findAllTrade(){
        List<Trade> all = tradeService.findAll();
        return Result.renderSuccess(all);
    }

    /**
      * @Title: findByTestSystemResourceID
      * @description: 被测系统查询交易
      * @param "[testSystemResourceID]"
      * @return com.jettech.dto.Result
      * @throws
      * <AUTHOR>
      * @date 2020/1/15 20:12
      */
    @GetMapping("/findByTestSystemResourceID/{testSystemResourceID}")
    public Result findByTestSystemResourceID(@PathVariable String testSystemResourceID){
        List<Trade> list = tradeService.findbyTestSystemResourceID(testSystemResourceID, null,null,null );
        return Result.renderSuccess(list);
    }

    /**
     * 系统交易管理-根据系统导出系统交易excel
    * @Title: exportTestSystemTradeExcel
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param request
    * @param @return    参数
    * @return Result<?>    返回类型
    * @throws
    * <AUTHOR>
     */
   @RequestMapping(value = "/exportTestSystemTradeExcel",method = RequestMethod.POST)
   public @ResponseBody Result<?> exportTestSystemTradeExcel( HttpServletRequest request,HttpServletResponse response){
       String testSystemRids = request.getParameter("testSystemRids");
       if(testSystemRids == null || "".equals(testSystemRids)) {
			return Result.renderError("参数为空！");
		}
       Map<String,Object> mapData = new HashMap<>();
       String Agent = request.getHeader("User-Agent");
       mapData.put("request",request);
       mapData.put("response",response);
       mapData.put("Agent",Agent);
       String userNumber = LoginUserUtil.getUserNumber(request);
       mapData.put("userNumber",userNumber);
       mapData.put("testSystemRids",testSystemRids);
       return tradeService.exportTestSystemTradeExcel2(mapData);
   }

   /**
    * 交易类型数据字典查询
   * @Title: getTradeTypeDictionary
   * @Description: TODO(这里用一句话描述这个方法的作用)
   * @param @param request
   * @param @return    参数
   * @return Result    返回类型
   * @throws
   * <AUTHOR>
    */
   @RequestMapping(value = "/getTradeTypeDictionary",method = RequestMethod.POST)
   public Result getTradeTypeDictionary(HttpServletRequest request){

	   return tradeService.getTradeTypeDictionary();
   }
   @PostMapping("/getDemandToTradeMapperByDemandNameAndTestSystemName")
   public Result<List<DemandToTradeDTO>> getDemandToTradeMapperByDemandNameAndTestSystemName(@RequestParam("demandName")String demandName, @RequestParam("testSystemName")String testSystemName){


		return Result.renderSuccess(tradeService.getDemandToTradeMapperByDemandNameAndTestSystemName(demandName, testSystemName));
	}


   /**
    * 案例编写、手工执行系通过任务查找任务下维护的统模块交易树
   * @Title: initTestTaskTradeTree
   * @Description: TODO(这里用一句话描述这个方法的作用)
   * @param @return    参数
   * @return Result<?>    返回类型
   * @throws
   * <AUTHOR> 2020-06-09
    */
   @RequestMapping(value = "/initTestTaskTradeTree",method = RequestMethod.POST)
   public Result<?> initTestTaskTradeTree(HttpServletRequest request){
	   //当前登录用户
	   String userNumber = LoginUserUtil.getUserNumber(request);
	   //当前所选任务
	   String  taskResourceID = request.getParameter("taskResourceID");
	   return tradeService.initTestTaskTradeTree(userNumber,taskResourceID);
   }

    @PostMapping(value = "/initTradeTreeByDemand")
    public Result<?> initTradeTreeByPlan(Long demandResourceID){
        return tradeService.initTradeTreeByDemand(demandResourceID);
    }

    /**
     * 项目案例执行 案例引用 系统模块交易树
     * @Title: initTradeTree
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param @return    参数
     * @return Result<?>    返回类型
     * @throws
     * <AUTHOR> 2020-06-09
     */
    @RequestMapping(value = "/initTradeTree",method = RequestMethod.POST)
    public Result<?> initTradeTree(){
        return tradeService.initTradeTree();
    }

    /**
     * @Title findTradeBySystemRIDOrModuleRID
     * @Description 添加范围弹框根据系统或者模块查询交易列表
     * @Params [params]
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2020/6/10
     */
    @PostMapping("/findTradeBySystemRIDOrModuleRID")
    public Result findTradeBySystemRIDOrModuleRID(@RequestBody Map<String, String> params){
        return tradeService.findTradeBySystemRIDOrModuleRID(params);
    }

    /**
     * @Title findTradeByResourceID
     * @Description 查询要修改的交易信息
     * <AUTHOR>
     * @Date 2020/6/24
     */
    @SuppressWarnings("unchecked")
	@PostMapping("/findTradeByResourceID")
    public Result<Trade> findTradeBySystemRIDOrModuleRID(HttpServletRequest request){
    	String  tradeResourceID = request.getParameter("tradeResourceID");
    	if(StringUtils.isEmpty(tradeResourceID)) {
    		 return Result.renderError("入参【tradeResourceID】为空");
    	}
    	
    	Trade findByResourceID=	tradeService.findTradeAndanagerByResourceID(Long.parseLong(tradeResourceID));
        return Result.renderSuccess(findByResourceID);
      //  return Result.renderSuccess(tradeService.findByResourceID(Long.parseLong(tradeResourceID)));
    }

    /**
    * @Title: findTradeByModuleResourceID
    * @Description: 根据模块查询交易
    * @param @return    参数
    * @return Result<?>    返回类型
    * @throws
    * <AUTHOR> 2020-07-16
     */
    @PostMapping("/findTradeByModuleResourceID")
    public Result findTradeByModuleResourceID(HttpServletRequest request){
    	String  moduleResourceID = request.getParameter("moduleResourceID");
        return tradeService.findTradeByModuleResourceID(moduleResourceID);
    }
	/**
	 * 
	* @Title: findTradeByResourceIDs
	* @Description: 获取交易信息
	* @param @param tradeRidList
	* @param @return    参数
	* @return Result<?>    返回类型
	* @throws
	* <AUTHOR>
	 */
    @PostMapping("/findTradeByResourceIDs")
    public Result findTradeByModuleResourceID(HttpServletRequest request,@RequestBody List<String>tradeRidList){
    	
        return Result.renderSuccess(tradeService.findByResourceIDIn(tradeRidList));
    }

    /**
     * @Title: findTradeByModuleResourceIDs
     * @Description: 根据模块查询交易
     * @param @return    参数
     * @return Result<?>    返回类型
     * @throws
     * <AUTHOR> 2020-07-16
     */
    @PostMapping("/findTradeByModuleResourceIDs")
    public Result findTradeByModuleResourceIDs(HttpServletRequest request){
        String  moduleResourceID = request.getParameter("moduleResourceID");
        String  taskResourceID = request.getParameter("taskResourceID");
        return tradeService.findTradeByModuleResourceIDs(moduleResourceID,taskResourceID);
    }

    /**
     * @Title: findTradeBySystemAndTask
     * @Description: 根据系统任务查询交易
     * @param @return    参数
     * @return Result<?>    返回类型
     * @throws
     * <AUTHOR> 2020-07-16
     */
    @PostMapping("/findTradeBySystemAndTask")
    public Result findTradeBySystemAndTask(HttpServletRequest request){
        String  systemResourceID = request.getParameter("systemResourceID");
        String  taskResourceID = request.getParameter("taskResourceID");
        return tradeService.findTradeBySystemAndTask(systemResourceID,taskResourceID);
    }

    /**
     * 获取交易系统模块
     * @param tradeResourceIDs
     * @return
     */
    @GetMapping("/getTradeSystemModule/{tradeResourceIDs}")
    public Result<List<SystemModuleTradeDTO>> getTradeSystemModule(@PathVariable String tradeResourceIDs) {
        if (StringUtils.isEmpty(tradeResourceIDs)) {
            return Result.renderError("参数为空！");
        }
        try {
            List<Long> tradeResourceIDList = Arrays.asList(tradeResourceIDs.split(",")).stream().map(item -> Long.parseLong(item)).collect(Collectors.toList());
            return Result.renderSuccess(tradeService.getTradeSystemModule(tradeResourceIDList));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.renderError("获取交易系统模块失败！");
        }
    }

    /**
     * 查询任务下选择的交易
     * @param testTaskResourceID
     * @return
     */
    @PostMapping("/findSelectedTradeByTaskResourceID")
    public Result<Trade> findSelectedTradeByTaskResourceID(String testTaskResourceID) {
        List<Trade> tradeList = tradeService.findSelectedTradeByTaskResourceID(testTaskResourceID);
        return Result.renderSuccess(tradeList);
    }
}
