package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.service.iservice.IProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
/**
 * @Description 项目controller
 * <AUTHOR>
 * @date 2019-11-06 10:14
 * @return
 */
@RestController
@RequestMapping("/project")
public class ProjectController {

    @Autowired
    private IProjectService projectService;
    /**
     * @Description 查询用户拥有的项目
     * <AUTHOR>
     * @date 2019-11-06 10:15
      * @param request
     * @return com.jettech.dto.Result<?>
     */
    @GetMapping
    public Result<?> findTestProjectByUser(HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return projectService.findTestProjectByUserNumber(userNumber);
    }
}
