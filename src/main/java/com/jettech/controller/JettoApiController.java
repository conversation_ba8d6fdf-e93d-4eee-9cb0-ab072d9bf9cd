package com.jettech.controller;

import com.jettech.DTO.jettoApi.env.ApiEnvPage;
import com.jettech.DTO.jettoApi.env.ApiEnvQueryDto;
import com.jettech.DTO.jettoApi.page.ApiPageDto;
import com.jettech.DTO.jettoApi.result.ApiResponseStatus;
import com.jettech.DTO.jettoApi.result.base.ApiResultRoot;
import com.jettech.common.dto.Result;
import com.jettech.service.iservice.JettoApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/jettoapi")
public class JettoApiController {

    @Autowired
    private JettoApiService jettoApiService;

    /**
     * 查询分页环境列表
     * @param apiEnvQueryDto
     * @return
     */
    @PostMapping("/envInfo/findEnvByPage")
    public Result<?> findEnvByPage(@RequestBody ApiEnvQueryDto apiEnvQueryDto){

        if(apiEnvQueryDto.getReqPage()==null){
            apiEnvQueryDto.setReqPage(new ApiPageDto());
        }

        if(apiEnvQueryDto.getReqPage().getGoPage()==null){
            apiEnvQueryDto.getReqPage().setGoPage(1);
        }
        if(apiEnvQueryDto.getReqPage().getPageRows()==null){
            apiEnvQueryDto.getReqPage().setPageRows(Integer.MAX_VALUE);
        }

        Result<?> responseInfo = transformAipResult(jettoApiService.findEnvByPage(apiEnvQueryDto));
        return responseInfo;
    }

    private Result<?> transformAipResult(ApiResultRoot<ApiEnvPage> apiResultRoot){
        if(apiResultRoot.getHead().getResult() == ApiResponseStatus.SUCCESS){
            return new Result(true, apiResultRoot.getHead().getResult(), apiResultRoot.getHead().getMessage(), apiResultRoot.getBody().getPageList());
        }else {
            return Result.renderError(apiResultRoot.getHead().getMessage());
        }
    }
}
