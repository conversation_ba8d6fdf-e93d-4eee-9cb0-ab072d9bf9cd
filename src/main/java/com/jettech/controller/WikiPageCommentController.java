package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.model.WikiPageComment;
import com.jettech.service.iservice.IWikiPageCommentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/wikiPageComment")
public class WikiPageCommentController {

    private static final Logger logger = LoggerFactory.getLogger(WikiPageCommentController.class);

    @Autowired
    private LoginUserUtil loginUserUtil;

    @Autowired
    private IWikiPageCommentService wikiPageCommentService;

    @GetMapping("/{pageResourceId}")
    public Result pageComments(@PathVariable("pageResourceId") Long pageResourceId) {
        try {
            return Result.renderSuccess(this.wikiPageCommentService.listCommentsByPageId(pageResourceId));
        } catch (Exception e) {
            logger.error("获取评论数据失败！", e);
            return Result.renderError("获取评论数据失败！");
        }
    }

    @PostMapping
    public Result addComment(@RequestBody WikiPageComment comment, HttpServletRequest request) {
        try {
            UserVo user = this.loginUserUtil.getLoginUser(request);
            this.wikiPageCommentService.addComment(comment, user.getUserNumber());
            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("评论失败！", e);
            return Result.renderError("评论失败！");
        }
    }

    @PutMapping
    public Result editContentById(@RequestBody WikiPageComment comment, HttpServletRequest request) {
        try {
            UserVo user = this.loginUserUtil.getLoginUser(request);
            this.wikiPageCommentService.editContentById(comment,user.getUserNumber());
            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("修改评论失败！", e);
            return Result.renderError("修改评论失败！");
        }
    }

    @DeleteMapping(value = "/{resourceID}")
    public Result clearContentById(@PathVariable Long resourceID, HttpServletRequest request) {
        try {
            UserVo user = this.loginUserUtil.getLoginUser(request);
            this.wikiPageCommentService.clearContentById(resourceID, user.getUserNumber());
            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("修改评论失败！", e);
            return Result.renderError("修改评论失败！");
        }
    }
}
