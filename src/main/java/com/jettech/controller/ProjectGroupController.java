package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.model.ProjectGroup;
import com.jettech.service.iservice.IProjectGroupService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @title: ProjectGroupController
 * @projectName jettomanager
 * @description: 项目组controller
 * @date 2020/7/1319:20
 */
@RestController
@RequestMapping("/projectGroup")
public class ProjectGroupController {

    @Autowired
    private IProjectGroupService projectGroupService;


    /** 
      * 新增项目组
      * @Title: saveProjectGroup
      * @param projectGroup :项目实体对象
      * @return com.jettech.dto.Result wws
      * @throws
      * <AUTHOR>
      * @date 2020/7/13 19:44 
      */
    @PostMapping("/saveProjectGroup")
    public Result saveProjectGroup(ProjectGroup projectGroup,
                                   @RequestParam("isSmallPoints") String isSmallPoints,
                                   HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        if(StringUtils.isNotBlank(isSmallPoints)){
            if(isSmallPoints.equals("0")){
                projectGroup.setSmallPoints(false);
            }else{
                projectGroup.setSmallPoints(true);
            }
        }
        return projectGroupService.saveProjectGroup(projectGroup,userNumber);
    }



    /***
     * 返显数据
     * @Method : findByResourceID
     * @Description : 返显数据
     * @param resourceID : 被查询rid
     * @return : com.jettech.dto.Result<com.jettech.model.ProjectGroup> 实体对象
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-13 星期一 20:02:24
     *
     */
    @GetMapping("/findByResourceID/{resourceID}")
    public Result<ProjectGroup> findByResourceID(@PathVariable String resourceID){
        ProjectGroup pg = projectGroupService.findByResourceID(Long.valueOf(resourceID));
        return Result.renderSuccess(pg);
    }

    /***
     * 修改项目组
     * @Method : updateProjectGroup
     * @Description : 想改项目组
     * @return : com.jettech.dto.Result
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 10:48:48
     *
     */
    @PostMapping("/updateProjectGroup")
    public Result updateProjectGroup(ProjectGroup pg,
                                     @RequestParam("isSmallPoints") String isSmallPoints, @RequestParam("updatePlace") String updatePlace,
                                     HttpServletRequest request){
        if(StringUtils.isNotBlank(isSmallPoints)) {
            if (isSmallPoints.equals("0")) {
                pg.setSmallPoints(false);
            } else {
                pg.setSmallPoints(true);
            }
            //iuyhiuhiuyh
        }
        if(org.springframework.util.StringUtils.isEmpty(updatePlace)) {
        	return Result.renderError("参数updatePlace为空！");
        }
        String userNumber = LoginUserUtil.getUserNumber(request);
        return projectGroupService.updateProjectGroup(pg,userNumber,updatePlace);
    }


    /***
     * 删除项目组
     * @Method : deleteProjectGroup
     * @Description : 删除项目组
     * @param resourceID : 被删除的rid
     * @param type : datadesign,perform
     * @return : com.jettech.dto.Result
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 14:18:23
     *
     */
    @DeleteMapping("/deleteProjectGroup/{resourceID}/{type}")
    public Result deleteProjectGroup(@PathVariable String resourceID,
                                     @PathVariable String type,
                                     HttpServletRequest request){
        String userNumber = LoginUserUtil.getUserNumber(request);
        return  projectGroupService.deleteProjectGroup(resourceID,type,userNumber);
    }


    /***
     * Excel导入项目组
     * @Method : importProjectGroup
     * @Description : Excel导入项目组
     * @param file : 要导入的Excel文件
     * @param testProjectResourceID : 项目的ResourceID
     * @return : com.jettech.dto.Result
     * <AUTHOR> zhangsheng
     * @CreateDate : 2020-09-17 下午 13:51:23
     *
     */
    @PostMapping("/importProjectGroup")
    public Result importProjectGroup(@RequestParam(value="file",required = false) MultipartFile file,
                                     @RequestParam("testProjectResourceID") Long testProjectResourceID,
                                     @RequestParam("groupType") String groupType, HttpServletRequest request){
        if(file.isEmpty()) {
            return Result.renderError("请选择文件导入！");
        }
        if(null == testProjectResourceID) {
            return Result.renderError("testProjectResourceID不能为null！");
        }
        if(StringUtils.isBlank(groupType)) {
            return Result.renderError("groupType不能为null！");
        }
        String userNumber = LoginUserUtil.getUserNumber(request);
        return projectGroupService.importProjectGroup(file,testProjectResourceID,userNumber, groupType);
    }



    /***
     * Excel导入项目组 模板下载
     * @Method : exportProjectGroup
     * @Description : Excel导入项目组 模板下载
     * @param HttpServletResponse
     * @return : void
     * <AUTHOR> zhangsheng
     * @CreateDate : 2020-09-21 下午 11:51:23
     *
     */
    @RequestMapping(value = "/exportProjectGroup", method = RequestMethod.GET)
    @ResponseBody
    public void exportProjectGroup(HttpServletResponse response) {
        // 生成导入Excel的模板，生成xls格式文件
        Workbook book = new HSSFWorkbook();

       // sheet1设置字体格式
        CellStyle cellStyleWithColor = this.getCellStyle(book, true);
        CellStyle cellStyleNoColor = this.getCellStyle(book, false);

        Sheet sheet1 = book.createSheet("填写说明");
        // 第一行
        Row row0 = sheet1.createRow(0);
        row0.createCell(0).setCellValue("");
        row0.setRowStyle(cellStyleNoColor);  // 设置文字格式
        row0.createCell(1).setCellValue("填写说明：");
        row0.getCell(1).setCellStyle(cellStyleWithColor);
        // 第二行
        Row row1 = sheet1.createRow(1);
        row1.setHeightInPoints(60f);
        row1.setRowStyle(cellStyleNoColor);  // 设置文字格式
        row1.createCell(0).setCellValue("");
        String content = "1、左侧选择末级项目可批量导入左侧树结构\r\n2、模板默认五级结构，可在后面追加六级结构、七级结构等\r\n3、每级结构依次维护内容，如果中间结构为空后面数据不读取\r\n4、每行有内容的最后一级结构导入系统作为末级节点";
        row1.createCell(1).setCellValue(content);
        row1.getCell(1).setCellStyle(cellStyleNoColor);
        sheet1.setColumnWidth(1, 50 * 512);
        sheet1.setColumnWidth(0, 10 * 512);

        // sheet2页内容
        Sheet sheet2 = book.createSheet("左侧树结构");
        Row row = sheet2.createRow(0);
        String[] rowName = new String[]{"一级结构","二级结构","三级结构","四级结构","五级结构"};
        for (int i = 0; i < rowName.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(rowName[i]);
            cell.setCellStyle(this.getCellStyle(book, true));
            sheet2.setColumnWidth(i, 10 * 512);  // 设置单元格高度和宽度
        }

        try {
            ServletOutputStream outPutStream = response.getOutputStream();
            if (book != null) {
                response.setHeader("Cache-Control", "private");
                response.setHeader("Pragma", "private");
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                response.setHeader("Content-Type", "application/force-download");
                response.setHeader("content-disposition","attachment;filename=exportDemandTemplate.xls");
                book.write(outPutStream);
            }
            outPutStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 返回单元格样格式样式
     * @param book
     * @param hasColor
     * @return
     * by zhangsheng
     */
    private CellStyle getCellStyle(Workbook book, boolean hasColor) {
        CellStyle createCellStyle = book.createCellStyle();
        Font font = book.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);
        createCellStyle.setFont(font);
        createCellStyle.setWrapText(true);
        if (hasColor) {
            font.setBold(true);//粗体显示
            createCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);//设置前景填充样式
            createCellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.PALE_BLUE.getIndex());//前景填充色
        }
        return createCellStyle;
    }

    @GetMapping("/findAll")
    public Result findAll(){
        List<ProjectGroup> list = projectGroupService.findAll();
        return Result.renderSuccess(list);
    }
    
	/**
	 * 
	* @Title: findPerformCaseLeadInfo
	* @Description: 获取项目案例的所属项目和项目组的层级结构
	* @param @param projectGroupResourceID
	* @param @return    参数
	* @return Result<?>    返回类型
	* @throws
	* <AUTHOR>
	 */
	@PostMapping(value = "/findPerformCaseLeadInfo")
	Result<?> findPerformCaseLeadInfo(@RequestParam("projectGroupResourceID") String projectGroupResourceID){
		try {
			String result =  projectGroupService.findPerformCaseLeadInfo(projectGroupResourceID);
			return Result.renderSuccess(result);
		} catch (Exception e) {
			 e.printStackTrace();
			return Result.renderError();
		}
	};

}
