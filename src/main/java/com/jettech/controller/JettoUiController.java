package com.jettech.controller;

import com.jettech.DTO.jettoUi.query.DeviceVo;
import com.jettech.DTO.jettoUi.response.ResponseInfo;
import com.jettech.DTO.jettoUi.response.ResponseUtils;
import com.jettech.common.dto.Result;
import com.jettech.feign.IFeignJettoUiMauto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/jettoui")
public class JettoUiController {

    @Autowired
    private IFeignJettoUiMauto feignJettoUIiMauto;

    @RequestMapping("/device/listByName")
    public Result<?> listDeviceByName(@RequestParam(required = false) String userName){
        return transformAipResult(feignJettoUIiMauto.listDeviceByName(userName));
    }

    @RequestMapping("/device/query/list")
    public Result<?> queryList(@RequestBody DeviceVo deviceVo){
        return transformAipResult(feignJettoUIiMauto.queryList(deviceVo));
    }

    @RequestMapping("/device/getQuartzTaskInfo")
    public Result<?> getQuartzTaskInfo(@RequestBody List<Integer> deviceIdList){
        return transformAipResult(feignJettoUIiMauto.getQuartzTaskInfo(deviceIdList));
    }

    @PostMapping("/caseinfo/updateOneVars")
    public Result<?> updateCaseVars(@RequestBody Map<String, Object> caseInfoDto,
                                    @RequestHeader(required = false) String authorization,
                                    @RequestHeader(required = false) String usernumber){
        return transformAipResult(feignJettoUIiMauto.updateCaseVars(caseInfoDto, authorization, usernumber));
    }

    private Result<?> transformAipResult(ResponseInfo<?> responseInfo){
        if(ResponseUtils.isOk(responseInfo)){
            return new Result<>(true, responseInfo.getCode(), responseInfo.getMessage(), responseInfo.getData());
        }else {
            return new Result<>(false, responseInfo.getCode(), responseInfo.getMessage());
        }
    }
}
