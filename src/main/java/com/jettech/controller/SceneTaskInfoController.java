package com.jettech.controller;

import com.jettech.DTO.SceneTaskInfoDTO;
import com.jettech.common.dto.Result;
import com.jettech.model.SceneTaskInfo;
import com.jettech.service.iservice.ISceneTaskInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/scenetask")
public class SceneTaskInfoController {
    @Autowired
    private ISceneTaskInfoService sceneTaskInfoService;

    @GetMapping("/findByTestTaskResourceID")
    public Result<?> findByTestTaskResourceID(Long testTaskResourceID) {
        List<SceneTaskInfo> sceneTaskInfoList = sceneTaskInfoService.findByTestTaskResourceID(testTaskResourceID);
        return Result.renderSuccess(sceneTaskInfoList);
    }

    @PostMapping("/save")
    public Result<?> save(@RequestBody SceneTaskInfoDTO sceneTaskInfoDTO) {
        SceneTaskInfo sceneTaskInfo = sceneTaskInfoService.saveSceneTask(sceneTaskInfoDTO);
        return Result.renderSuccess(sceneTaskInfo);
    }

    @PostMapping("/update")
    public Result<?> update(@RequestBody SceneTaskInfoDTO sceneTaskInfoDTO) {
        SceneTaskInfo sceneTaskInfo = sceneTaskInfoService.updateSceneTask(sceneTaskInfoDTO);
        return Result.renderSuccess(sceneTaskInfo);
    }

    @GetMapping("/deleteById")
    public Result<?> deleteById(Long id) {
        sceneTaskInfoService.deleteById(id);
        return Result.renderSuccess();
    }
}
