package com.jettech.controller;

import com.alibaba.fastjson.JSONObject;
import com.jettech.DTO.SceneCaseExecDTO;
import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.model.SceneCaseContain;
import com.jettech.service.iservice.ISceneCaseContainService;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.service.iservice.ITestCasequoteService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 场景案例
 */
@RestController
@RequestMapping("/sceneCase")
public class SceneCaseContainController {

    @Autowired
    private ISceneCaseContainService sceneCaseContainService;
    @Autowired
    private ITestCaseService testCaseService;
    @Autowired
    private ITestCasequoteService testCasequoteService;

    /**
     * 查询交易下的场景案例
     * @param params
     * @return
     */
    @PostMapping("/findSceneCaseByTradeID")
    public Result<?> findSceneCaseByTradeID(@RequestBody JSONObject params){
        return Result.renderSuccess(testCaseService.findSceneCaseByTradeID(params));
    }

    /**
     * 查询场景案例下的引用案例
     * @param params
     * @return
     */
    @PostMapping("/findBySceneCaseResourceID")
    public Result<?> findBySceneCaseResourceID(@RequestBody JSONObject params){
        return Result.renderSuccess(sceneCaseContainService.findBySceneCaseResourceID(params));
    }

    /**
     * 检查场景案例下参数是否已选择
     * @param testCaseResourceIDList
     * @return
     */
    @PostMapping("/checkInitParamBySceneCaseList")
    public Result<?> checkInitParamBySceneCaseList(@RequestBody List<Long> testCaseResourceIDList) {
        return sceneCaseContainService.checkInitParamBySceneCaseList(testCaseResourceIDList);
    }

    /**
     * 场景案例引用其他案例
     * @param sceneCaseContainList
     * @return
     */
    @PostMapping("/save")
    public Result<?> save(@RequestBody List<SceneCaseContain> sceneCaseContainList, HttpServletRequest request){
        return Result.renderSuccess(sceneCaseContainService.save(sceneCaseContainList, LoginUserUtil.getUserNumber(request)));
    }

    /**
     * 更新引用的案例(参数或顺序等)
     * @param sceneCaseContainList
     * @return
     */
    @PostMapping("/update")
    public Result<?> update(@RequestBody List<SceneCaseContain> sceneCaseContainList, HttpServletRequest request){
        return Result.renderSuccess(sceneCaseContainService.update(sceneCaseContainList, LoginUserUtil.getUserNumber(request)));
    }

    /**
     * 场景案例删除引用案例
     * @param sceneCaseContain
     * @param request
     * @return
     */
    @PostMapping("/deleteById")
    public Result<?> deleteById(@RequestBody SceneCaseContain sceneCaseContain, HttpServletRequest request){

        sceneCaseContainService.delete(sceneCaseContain, LoginUserUtil.getUserNumber(request));

        return Result.renderSuccess();
    }

    /**
     * 场景案例批量删除引用案例
     * @param idList
     * @param request
     * @return
     */
    @PostMapping("/deleteByIds")
    public Result<?> deleteById(@RequestBody List<Long> idList, HttpServletRequest request){

        if(CollectionUtils.isEmpty(idList)){
            return Result.renderError("id列表不能为空");
        }

        List<SceneCaseContain> list = idList.stream().filter(Objects::nonNull).map(id -> {
            SceneCaseContain sceneCaseContain = new SceneCaseContain();
            sceneCaseContain.setId(id);
            return sceneCaseContain;
        }).collect(Collectors.toList());

        if(list.isEmpty()){
            return Result.renderError("案例不存在：" + idList);
        }

        sceneCaseContainService.deleteInBatch(list, LoginUserUtil.getUserNumber(request));
        return Result.renderSuccess();
    }

    /**
     * 查询执行的案例和场景案例
     * @param sceneCaseExecDTO
     * @return
     */
    @PostMapping("/findTestCaseQuoteDTOList")
    public Result<?> findTestCaseQuoteDTOList(@RequestBody SceneCaseExecDTO sceneCaseExecDTO){

        return Result.renderSuccess(sceneCaseContainService.findTestCaseQuoteDTOList(sceneCaseExecDTO));
    }


    @RestControllerAdvice(assignableTypes = SceneCaseContainController.class)
    public static class exceptionHandle {
        @ExceptionHandler(Exception.class)
        public Result<?> handleException(Exception exception, HttpServletRequest httpServletRequest){
            if(exception instanceof RuntimeException && StringUtils.isNotBlank(exception.getMessage())){
                System.err.println(exception.getMessage());
            }else {
                exception.printStackTrace();
            }
            return Result.renderError(exception.getMessage());
        }
    }

    @GetMapping("/isSceneQuote")
    public Result<?> isSceneQuote(@RequestParam("testCaseResourceID") String testCaseResourceID){

        if(StringUtils.isBlank(testCaseResourceID)){
            return Result.renderError("案例ResourceID不能为空");
        }

        boolean isQuote = sceneCaseContainService.isSceneQuote(testCaseResourceID) || testCasequoteService.isQuote(testCaseResourceID);

        return isQuote ? Result.renderError("测管存在该案例引用关系, 禁止删除") : Result.renderSuccess();
    }
}
