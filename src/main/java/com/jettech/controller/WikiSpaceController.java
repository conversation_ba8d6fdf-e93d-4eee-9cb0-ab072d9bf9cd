package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.WikiSpacePageDTO;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.model.WikiSpace;
import com.jettech.service.iservice.IWikiSpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(value = "知识空间controller",tags = "知识空间接口")
@RestController
@RequestMapping("/wikiSpace")
public class WikiSpaceController {

    private static final Logger logger = LoggerFactory.getLogger(WikiSpaceController.class);

    @Autowired
    private IWikiSpaceService wikiSpaceService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    @PostMapping("/space")
    public Result space(@RequestBody WikiSpacePageDTO dto) {
        try {
            return Result.renderSuccess(this.wikiSpaceService.getSpacePage(dto));
        } catch (Exception e) {
            logger.error("获取知识空间数据失败！", e);
            return Result.renderError("获取知识空间数据失败！");
        }
    }

    @GetMapping
    public Result spaceList() {
        try {
            return Result.renderSuccess(this.wikiSpaceService.getSpaceList(null));
        } catch (Exception e) {
            logger.error("获取知识空间数据失败！", e);
            return Result.renderError("获取知识空间数据失败！");
        }
    }

    @ApiOperation(value = "知识空间添加/修改")
    @PostMapping("/submit")
    public Result submit(@RequestBody WikiSpace wikiSpace, HttpServletRequest request) {
        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            String res = this.wikiSpaceService.addOrUpdateSpace(wikiSpace, user.getUserNumber());
            return Result.renderSuccess(res);
        } catch (Exception e) {
            logger.error("保存知识空间失败！", e);
            return Result.renderError("保存知识空间失败！");
        }
    }

    @PostMapping("/delete")
    public Result submit(@RequestBody List<WikiSpace> wikiSpaceList, HttpServletRequest request) {
        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            wikiSpaceList.stream().forEach(space -> {
                space.setDelFlag(true);
            });
            this.wikiSpaceService.update(wikiSpaceList, user.getUserNumber());
            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("删除知识空间失败！", e);
            return Result.renderError("删除知识空间失败！");
        }
    }
}
