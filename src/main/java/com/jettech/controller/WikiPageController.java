package com.jettech.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.WikiPageCopyDTO;
import com.jettech.common.dto.assets.WikiPageDTO;
import com.jettech.common.enums.ObjectTypeEnum;
import com.jettech.common.page.PageUtil;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.feign.IFeignDataDesignToFileService;
import com.jettech.model.WikiPage;
import com.jettech.model.WikiPageUserTemConfig;
import com.jettech.service.iservice.IWikiPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.anumbrella.seaweedfs.core.FileTemplate;
import net.anumbrella.seaweedfs.core.file.FileHandleStatus;
import net.anumbrella.seaweedfs.core.http.StreamResponse;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Base64;
import java.util.List;
import java.util.Map;
@Api(value = "知识空间页面controller",tags = "知识空间页面接口")
@RestController
@RequestMapping("/wikiPage")
public class WikiPageController {

    private static final Logger logger = LoggerFactory.getLogger(WikiPageController.class);

    @Autowired
    private IWikiPageService wikiPageService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    @Autowired(required = false)
    private FileTemplate fileTemplate;

    @Value("${use_file_service}")
    private boolean useFileService;

    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;

    @ApiOperation(value = "知识空间页面添加")
    @PostMapping
    public Result addPage(@RequestBody WikiPage page, HttpServletRequest request) {
        String textContentString = page.getContent();
        if (!StringUtils.isEmpty(textContentString)) {
            // 先将结束标签替换为换行
            String textContentReplaceEnd = textContentString.replaceAll("<//?[^>]+>", "\n");
            // 再替换开始标签
            String textContentReplaceStart = textContentReplaceEnd.replaceAll("</?[^>]+>", "");
            // 最后进行特殊符号的转义进行还原
            page.setTextContent(StringEscapeUtils.unescapeHtml(textContentReplaceStart));
        }

        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            if (!StringUtils.isEmpty(page.getTitle())) {
                page.setTitle(page.getTitle().trim());
            }
            wikiPageService.addPage(page, user.getUserNumber());
            return Result.renderSuccess("添加页面信息成功！");
        } catch (Exception e) {
            logger.error("添加页面信息失败！", e);
            return Result.renderError("添加页面信息失败！");
        }
    }
    @ApiOperation(value = "知识空间页面修改")
    @PutMapping
    public Result editPage(@RequestBody WikiPage page, HttpServletRequest request) {
        String textContentString = page.getContent();
        if (!StringUtils.isEmpty(textContentString)) {
            // 先将结束标签替换为换行
            String textContentReplaceEnd = textContentString.replaceAll("<//?[^>]+>", "\n");
            // 再替换开始标签
            String textContentReplaceStart = textContentReplaceEnd.replaceAll("</?[^>]+>", "");
            // 最后进行特殊符号的转义进行还原
            page.setTextContent(StringEscapeUtils.unescapeHtml(textContentReplaceStart));
        }

        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            if (!StringUtils.isEmpty(page.getTitle())) {
                page.setTitle(page.getTitle().trim());
            }
            this.wikiPageService.editPage(page, user.getUserNumber());
            return Result.renderSuccess("更新页面信息成功！");
        } catch (Exception e) {
            logger.error("更新页面信息失败！", e);
            return Result.renderError("更新页面信息失败！");
        }
    }

    @ApiOperation(value = "检查空间下是否存在同名页面")
    @PostMapping("/checkPageName")
    public Result checkPageName(@RequestBody WikiPage page) {
        boolean check = this.wikiPageService.checkPageName(page);
        if(!check){
            return Result.renderError("该空间下或其回收站存在同名页面！");
        }
        return Result.renderSuccess();
    }

    @PostMapping("/getDraftPageList")
    public Result getDraftPageList(@RequestBody WikiPageDTO dto) {
        return Result.renderSuccess(this.wikiPageService.getDraftPageList(dto));
    }

    @PostMapping("/draftPage")
    public Result draftPage(@RequestBody WikiPageDTO dto) {
        return Result.renderSuccess(this.wikiPageService.draftPage(dto));
    }

    @GetMapping("/getPage/{resourceID}")
    public Result getPage(@PathVariable("resourceID") Long resourceID, HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        return Result.renderSuccess(this.wikiPageService.getPage(resourceID, user.getUserNumber()));
    }

    @GetMapping(value = "/space/{spaceId}")
    public Result getSpacePageList(@PathVariable("spaceId") Long spaceId) {
        return Result.renderSuccess(this.wikiPageService.listPageTreeBySpace(spaceId, false));
    }

    @PostMapping("/collectPage")
    public Result collectPage(@RequestBody WikiPageUserTemConfig wikiPageUserTemConfig, HttpServletRequest request) {
        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            this.wikiPageService.collectPage(wikiPageUserTemConfig, user.getUserNumber());
            if (wikiPageUserTemConfig.getCollect().equals(0)) {
                return Result.renderSuccess("取消成功！");
            } else {
                return Result.renderSuccess("收藏成功！");
            }
        } catch (Exception e) {
            if (wikiPageUserTemConfig.getCollect().equals(0)) {
                logger.error("取消失败！", e);
                return Result.renderError("取消失败！");
            } else {
                logger.error("收藏失败！", e);
                return Result.renderError("收藏失败！");
            }

        }
    }

    @PostMapping("/copyPageById")
    public Result copyPageById(@RequestBody WikiPageCopyDTO dto, HttpServletRequest request) {
        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            this.wikiPageService.copyPageById(dto, user.getUserNumber());
            return Result.renderSuccess("复制页面成功！");
        } catch (Exception e) {
            logger.error("复制页面失败！", e);
            return Result.renderError("复制页面失败！");
        }
    }

    @PostMapping("/movePageByParam")
    public Result movePageByParam(@RequestBody WikiPageCopyDTO dto, HttpServletRequest request) {
        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            this.wikiPageService.editSpaceIdByPageId(dto, user.getUserNumber());
            return Result.renderSuccess("移动页面成功！");
        } catch (Exception e) {
            logger.error("移动页面失败！", e);
            return Result.renderError("移动页面失败！");
        }
    }

    @DeleteMapping(value = "/{resourceID}")
    public Result delPage(@PathVariable Long resourceID,
                          @RequestParam Boolean delChildren,
                          HttpServletRequest request) {
        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            this.wikiPageService.delPage(resourceID, delChildren, user.getUserNumber());
            return Result.renderSuccess("删除页面成功！");
        } catch (Exception e) {
            logger.error("删除页面失败！", e);
            return Result.renderError("删除页面失败！");
        }
    }

    @PostMapping("/listPageUserByParam")
    public Result listPageUserByParam(@RequestBody WikiPageUserTemConfig config, HttpServletRequest request) {
        UserVo user = loginUserUtil.getLoginUser(request);
        return Result.renderSuccess(this.wikiPageService.listPageUserByParam(config, user.getUserNumber()));
    }

    @PostMapping(value = "/recycle")
    public Result getPageInRecycle(@RequestBody WikiPageDTO wikiPageDTO) {
        try {
            PageHelper.startPage(wikiPageDTO.getPageNumber(), wikiPageDTO.getPageSize());
            List<WikiPage> wikiPageList = this.wikiPageService.listRecyclePageBySpace(wikiPageDTO);
            return Result.renderSuccess(PageUtil.getPageResult(new PageInfo<>(wikiPageList)));
        } catch (Exception e) {
            logger.error("获取回收站数据失败！", e);
            return Result.renderError("获取回收站数据失败！");
        }
    }

    @DeleteMapping(value = "/recycle/{pageResourceID}")
    public Result deleteAllRecyclePage(@PathVariable Long pageResourceID) {
        try {
            this.wikiPageService.clearRecyclePage(pageResourceID);
            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("清空回收站失败！", e);
            return Result.renderError("清空回收站失败！");
        }
    }

    @PostMapping(value = "/importWord/{resourceID}")
    public Result importWord(HttpServletRequest request, @RequestParam MultipartFile file,
                             @PathVariable Long resourceID, @RequestParam Boolean asChild, @RequestParam Boolean delChildren,
                             @RequestParam Integer titleConfictValue) {
        try {
            String uploadPath = "wordToHtml";

            // 创建图片存放路径
            File filePath = new File(uploadPath);
            if (!filePath.exists()) {
                filePath.mkdirs();
            }

            String fileName = file.getOriginalFilename();

            Map<String, String> contentMap = Maps.newHashMapWithExpectedSize(2);
            if (fileName.endsWith(".doc") || fileName.endsWith(".wps")) {
                // 2003word
//                contentMap = Word2HtmlUtils.word2Html(file.getInputStream(), uploadPath);
            } else if (fileName.endsWith(".docx")) {
                // 2007word
//                contentMap = Word2HtmlUtils.word2007ToHtml(file.getInputStream(), uploadPath);
            } else {
                return Result.renderError("文件格式错误！");
            }

            if (contentMap.containsKey("html")) {
//                String content = handleImagePath(request, contentMap.get("html"));
//                contentMap.put("html", content);
            }

//            pageService.importWord(id, asChild, delChildren, titleConfictValue, contentMap, file);


            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("导入失败，原因:{}", e.getMessage(), e);
            return Result.renderError("导入失败！");
        }
    }

    @PostMapping(value = "/uploadImg")
    public Result uploadImg(@RequestParam MultipartFile file) {
        try {
            if (useFileService) {
                Result result = feignDataDesignToFileService.uploadSFile(file, null, ObjectTypeEnum.RICHIMG.getValue(), true);
                if (result != null) {
                    return Result.renderSuccess(result.getObj());
                } else {
                    return Result.renderError();
                }
            } else {
                FileHandleStatus status = fileTemplate.saveFileByStream(file.getOriginalFilename(),file.getInputStream());
                if(status != null && StringUtils.isNotBlank(status.getFileId())) {
                    return Result.renderSuccess(status.getFileId());
                } else {
                    return Result.renderError();
                }
            }
//            String filePath = this.wikiPageService.uploadImg(file);
//            return Result.renderSuccess(filePath);
        } catch (Exception e) {
            logger.error("上传图片文件异常：{}", e);
            return Result.renderError("上传图片文件失败！");
        }
    }

    @RequestMapping(value = "img", method = {RequestMethod.GET, RequestMethod.POST})
    public void img(@RequestParam String filePath, HttpServletResponse response) {
        try {
            if (useFileService) {
                byte[] data = feignDataDesignToFileService.getContent(Long.parseLong(filePath));
                data = Base64.getMimeDecoder().decode(data);
                response.getOutputStream().write(data);
            } else {
                StreamResponse streamResponse = fileTemplate.getFileStream(filePath);
                InputStream is = streamResponse.getInputStream();
                if (is != null) {
                    long length = streamResponse.getLength();
                    OutputStream os  = response.getOutputStream();
                    response.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
                    int tempLength;
                    for(byte[] buffer = new byte[1024]; (tempLength = is.read(buffer)) > -1; length += (long)tempLength) {
                        os.write(buffer, 0, tempLength);
                    }
                    os.flush();
                }
            }

            /*OutputStream os = null;
            InputStream is = streamResponse.getInputStream();
            if (is != null) {
                try {
                    os = response.getOutputStream();
                    int count = 0;
                    byte[] buffer = new byte[1024];
                    while ((count = is.read(buffer)) != -1)
                        os.write(buffer, 0, count);
                    os.flush();
                } catch (Exception e) {
                    logger.error("预览图片失败！", e);
                } finally {
                    if (os != null) {
                        try {
                            os.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if (is != null) {
                        try {
                            is.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }*/

    } catch (Exception e) {
            logger.error("获取图片失败:" + filePath, e);
        }
    }

    @GetMapping(value = "/exportWord/{resourceID}")
    public void exportWord(HttpServletResponse response, @PathVariable Long resourceID) {
        this.wikiPageService.exportWord(resourceID, response);
    }

    @PostMapping("/restore/{pageResourceID}")
    public Result restore(@PathVariable Long pageResourceID, HttpServletRequest request) {
        try {
            UserVo user = loginUserUtil.getLoginUser(request);
            this.wikiPageService.restore(pageResourceID,user.getUserNumber());
            return Result.renderSuccess();
        } catch (Exception e) {
            logger.error("还原文档失败！", e);
            return Result.renderError("还原文档失败！");
        }
    }
}
