package com.jettech.controller;

import com.jettech.common.dto.Result;
import com.jettech.feign.IFeignDataDesignToFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
@RestController
@RequestMapping("/test")
public class TestController {
    @Autowired
    private IFeignDataDesignToFileService feignDataDesignToFileService;

    @GetMapping("/test")
    @ResponseBody
    public String  test(HttpServletRequest request){
        return "test";
    }
}
