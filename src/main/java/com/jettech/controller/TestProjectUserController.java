package com.jettech.controller;

import com.jettech.DTO.ProjectUserDTO;
import com.jettech.common.dto.Result;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.model.TestProjectUser;
import com.jettech.service.iservice.ITestProjectUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 项目和人员关联表
* @ClassName: TestProjectUserController
* @Description: TODO(这里用一句话描述这个类的作用)
* <AUTHOR>
* @date 2020年4月20日
*
 */
@Controller
@RequestMapping("/testProjectUser")
public class TestProjectUserController {

    private static final Logger logger = LoggerFactory.getLogger(TestProjectUserController.class);
    @Autowired
    private ITestProjectUserService testProjectUserService;
    @Autowired
	private LoginUserUtil loginUserUtil;
    
    /**
	 * 
	 * @Title: findNotRelateUser 
	 * @Description: 查询未关联的人员
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:01:10
	 * @update cao_jinbao 2020年6月23日(修改为分页，前端懒加载用)
	 */
	@PostMapping("/findNotRelateUser")
	@ResponseBody
	public Result<?> findNotRelateUser(HttpServletRequest request) {
		String name = request.getParameter("name");
		
		String rows = request.getParameter("rows");// 默认为10
		if (rows == null|| "".equals(rows))
			rows = "10000";
		String page = request.getParameter("page");// 默认为1
		if (page == null|| "".equals(page))
			page = "1";
		PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
		String testProjectResourceID = request.getParameter("testProjectResourceID");
		String deptName = request.getParameter("deptName");
		String userGroupReourceID = request.getParameter("userGroupReourceID");
		return testProjectUserService.findNotRelateUser(name,deptName,testProjectResourceID,userGroupReourceID,pageRequest);
	}
	/**
	 * 
	 * @Title: findRelatedUser 
	 * @Description: 查询已关联的人员
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:01:23
	 * @update cao_jinbao 2020年6月23日(修改为分页，前端懒加载用)
	 */
	@PostMapping("/findRelatedUser")
	@ResponseBody
	public Result<?> findRelatedUser(HttpServletRequest request) {
		String name = request.getParameter("name");
		String rows = request.getParameter("rows");// 默认为10
		if (rows == null|| "".equals(rows)) 
			rows = "10000";
		String page = request.getParameter("page");// 默认为1
		if (page == null|| "".equals(page))
			page = "1";
		PageRequest pageRequest = PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(rows));
		String testProjectResourceID = request.getParameter("testProjectResourceID");
		String deptName = request.getParameter("deptName");
		String userGroupReourceID = request.getParameter("userGroupReourceID");
		return testProjectUserService.findRelatedUser(name,deptName,testProjectResourceID,userGroupReourceID,pageRequest);
	}
	
	/**
	 * 
	 * @Title: relateUser 
	 * @Description: 关联人员
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:36:33
	 */
	@PostMapping("/relateUser")
	@ResponseBody
	public Result<?> relateUser(HttpServletRequest request) {
		UserVo user = loginUserUtil.getLoginUser(request);
		String testProjectResourceID = request.getParameter("testProjectResourceID");
		String userResourceIDs = request.getParameter("userResourceIDs");
		return testProjectUserService.relateUser(userResourceIDs,testProjectResourceID,user.getUserNumber());
	}
	
	/**
	 * 
	 * @Title: cancelRelatedUser 
	 * @Description: 取消关联人员
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:37:11
	 */
	@PostMapping("/cancelRelatedUser")
	@ResponseBody
	public Result<?> cancelRelatedUser(HttpServletRequest request) {
		UserVo user = loginUserUtil.getLoginUser(request);
		String testProjectResourceID = request.getParameter("testProjectResourceID");
		String userResourceIDs = request.getParameter("userResourceIDs");
		return testProjectUserService.cancelRelatedUser(userResourceIDs,testProjectResourceID,user.getUserNumber());
	}

	@PostMapping("/findAllProjectUser")
	@ResponseBody
	public Result findAllProjectUser(){
		List<TestProjectUser> list = testProjectUserService.findAll();
		return Result.renderSuccess(list);
	}


	/**
	 * @Method: findALlProjectAndUser
	 * @Description: 项目和用户的关联关系（带用户名称和number）
	 * @Param: " [] "
	 * @return: com.jettech.dto.Result
	 * @Author: wws
	 * @Date: 2020/9/23
	 */
	@GetMapping("/findALlProjectAndUser")
	@ResponseBody
	public Result findALlProjectAndUser(){
		List<ProjectUserDTO> list = testProjectUserService.findALlProjectAndUser();
		return Result.renderSuccess(list);
	}
}
