package com.jettech.controller;

import com.jettech.DTO.VersionInfoDto;
import com.jettech.common.dto.Result;
import com.jettech.common.util.BinaryDecimalUtil;
import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import com.jettech.feign.IFeignDataDesignToBasicService;
import com.jettech.model.TestCaseVersion;
import com.jettech.service.iservice.ITestCaseVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/25
 **/

@RestController
@RequestMapping("/testCaseVersion")
public class TestCaseVersionController {

    @Autowired
    private ITestCaseVersionService testCaseVersionService;

    @Autowired
    private IFeignDataDesignToBasicService basicService;

    @Autowired
    private LoginUserUtil loginUserUtil;

    @PostMapping("/page")
    public Result page(@RequestBody VersionInfoDto dto) {
        return Result.renderSuccess(testCaseVersionService.page(dto));
    }

    @GetMapping("/get/{id}")
    public Result get(@PathVariable(value = "id") Long id) {
        TestCaseVersion testCaseVersion = testCaseVersionService.find(id);
        if (testCaseVersion.getTestMode() != null) {
            //测试方式
            List<String> testModeList = BinaryDecimalUtil.TenToDicVal(testCaseVersion.getTestMode());
            testCaseVersion.setTestModeKey(testModeList);
        }
        return Result.renderSuccess(testCaseVersion);
    }

    @DeleteMapping("/del/{ids}")
    public Result del(HttpServletRequest request, @PathVariable(value = "ids") String ids) {
        UserVo user = loginUserUtil.getLoginUser(request);
        String[] idsArr = ids.split(",");
        if (idsArr != null && idsArr.length > 0) {
            List<TestCaseVersion> list = new ArrayList<>();
            Arrays.stream(idsArr).forEach(x -> {
                TestCaseVersion info = new TestCaseVersion();
                info.setId(Long.parseLong(x));
                list.add(info);
            });
            testCaseVersionService.deleteInBatch(list, user.getUserNumber());
        }
        return Result.renderSuccess();
    }
}
