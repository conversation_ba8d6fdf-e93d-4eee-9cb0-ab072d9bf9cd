package com.jettech.config;

import cn.com.obase.ObaseDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;

@ConditionalOnExpression("'${spring.datasource.url}'.startsWith('jdbc:obase')")
@Configuration
public class ObaseConfig {
    private static final Logger logger = LoggerFactory.getLogger(ObaseConfig.class);

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.initialSize:20}")
    private String initialSize;

    @Value("${spring.datasource.minIdle:20}")
    private String minIdle;

    @Value("${spring.datasource.maxActive:300}")
    private String maxActive;

    @Value("${spring.datasource.period:30}")
    private String period;

    @Value("${spring.datasource.maxWait:1000}")
    private String maxWait;

    @Value("${spring.datasource.keepAlive:true}")
    private String keepAlive;

    @Value("${spring.datasource.minEvictableIdleTimeMillis:1800000}")
    private String minEvictableIdleTimeMillis;

    @Value("${spring.datasource.timeBetweenEvictionRunsMillis:60000}")
    private String timeBetweenEvictionRunsMillis;

    @Value("${spring.datasource.validationQueryTimeout:1}")
    private String validationQueryTimeout;

    @Value("${spring.datasource.testWhileIdle:true}")
    private String testWhileIdle;

    @Value("${spring.datasource.testOnBorrow:true}")
    private String testOnBorrow;

    @Value("${spring.datasource.testOnReturn:false}")
    private String testOnReturn;

    @Value("${spring.datasource.filters:}")
    private String filters;

    @Value("${spring.datasource.connectionProperties:}")
    private String connectionProperties;

    @Value("${spring.datasource.connIpMapping:}")
    private String connIpMapping;

    @Bean
    public DataSource obaseDataSource() {
        logger.info("start init obase data source");

        ObaseDataSource datasource = new ObaseDataSource();
        HashMap<String, String> dataSourceConfig = new HashMap<String, String>();

        dataSourceConfig.put("url", url);
        dataSourceConfig.put("username", username);
        dataSourceConfig.put("password", password);
        dataSourceConfig.put("initialSize", initialSize);
        dataSourceConfig.put("minIdle", minIdle);
        dataSourceConfig.put("maxActive", maxActive);
        dataSourceConfig.put("period", period);
        dataSourceConfig.put("maxWait", maxWait);
        dataSourceConfig.put("keepAlive", keepAlive);
        dataSourceConfig.put("minEvictableIdleTimeMillis", minEvictableIdleTimeMillis);
        dataSourceConfig.put("timeBetweenEvictionRunsMillis", timeBetweenEvictionRunsMillis);
        dataSourceConfig.put("validationQueryTimeout", validationQueryTimeout);
        dataSourceConfig.put("testWhileIdle", testWhileIdle);
        dataSourceConfig.put("testOnBorrow", testOnBorrow);
        dataSourceConfig.put("testOnReturn", testOnReturn);
        dataSourceConfig.put("filters", filters);
        dataSourceConfig.put("connectionProperties", connectionProperties);
        dataSourceConfig.put("connIpMapping", connIpMapping);
        datasource.setDataSourceConfig(dataSourceConfig);

        try {
            datasource.init();
        } catch (SQLException e) {
            logger.error("init obase data source error: " + e.getMessage());
        }

        logger.info("end init obase data source");

        return datasource;
    }
}
