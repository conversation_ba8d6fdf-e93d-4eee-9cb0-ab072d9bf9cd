package com.jettech.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;

@EnableTransactionManagement
@Configuration
public class MybatisPlusConfig {

    /**
     * 分页插件
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor page = new PaginationInterceptor();             
            page.setDialectType("mysql");             
            return page; 
    }
    
    /**
     * 解决查询返回结果含null没有对应字段值问题
     */
    @Bean 
    public ConfigurationCustomizer configurationCustomizer() {
    	return new ConfigurationCustomizer() { 
    		@Override public void customize(org.apache.ibatis.session.Configuration configuration) {
    			configuration.setCacheEnabled(true); 
    			configuration.setCallSettersOnNulls(true);
    		} 
    	}; 
    }


    
}
