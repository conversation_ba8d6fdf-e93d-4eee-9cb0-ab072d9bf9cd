package com.jettech.config;

import net.anumbrella.seaweedfs.core.FileSource;
import net.anumbrella.seaweedfs.core.FileTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "seaweed.enabled", havingValue = "true")
public class SeaweedConfig {

    private static Logger logger = LoggerFactory.getLogger(SeaweedConfig.class);

    @Value("${seaweed.ip}")
    private String ip;
    @Value("${seaweed.port}")
    private Integer port;
    @Value("${seaweed.timeout}")
    private Integer timeout;

    @Bean
    public FileTemplate fileTemplate() {
        FileTemplate fileTemplate = null;
        FileSource fileSource = new FileSource();
        fileSource.setHost(ip);
        fileSource.setPort(port);
        fileSource.setConnectionTimeout(timeout);
        try {
            fileSource.startup();
            fileTemplate = new FileTemplate(fileSource.getConnection());
        } catch (Exception e) {
            logger.error("seaweed文件服务器初始化失败", e);
        }
        return fileTemplate;
    }
}
