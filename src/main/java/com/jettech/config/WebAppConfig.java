package com.jettech.config;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jettech.common.util.ParamConfig;
import com.jettech.common.util.ParamUtil;
import com.jettech.init.ProjectInit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.time.*;
import java.util.List;

@Configuration
@EnableWebMvc
public class WebAppConfig extends WebMvcConfigurerAdapter {

    @Autowired
    private ParamConfig paramConfig;
	@Bean
	public ProjectInit init() {
		initParam();
		ProjectInit init = new ProjectInit(); 
		return init;
	}
    /**
     * 
     * @Description 初始化ParamUtil
     * <AUTHOR>
     * @Date 2019年6月4日
     */
    private void initParam(){
		ParamUtil.setTrpUrl(paramConfig.getTrpUrl());
		ParamUtil.setSnowFlakeWorkerId(paramConfig.getSnowFlakeWorkerId());
		ParamUtil.setSnowFlakedataId(paramConfig.getSnowFlakedataId());
        ParamUtil.setBasicUrl(paramConfig.getBasicUrl());
    	
    }
    // 静态资源拦截
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/resources/**").addResourceLocations("/resources/");
    }

	// 编码问题
	@Bean
	public HttpMessageConverter<String> responseBodyConverter() {
		StringHttpMessageConverter converter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
		return converter;
	}
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        //定义Json转换器
        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter =
                new MappingJackson2HttpMessageConverter();
        //定义对象映射器
        ObjectMapper objectMapper = new ObjectMapper();
        //定义对象模型
        SimpleModule simpleModule = new SimpleModule();
        //添加对长整型的转换关系
        simpleModule.addSerializer(BigInteger.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeToTimestampSerializer());
        simpleModule.addSerializer(LocalDate.class, new LocalDateToTimestampSerializer());
        //将对象模型添加至对象映射器
        objectMapper.registerModule(simpleModule);
        //将对象映射器添加至Json转换器
        jackson2HttpMessageConverter.setObjectMapper(objectMapper);

        //在转换器列表中添加自定义的Json转换器
        converters.add(jackson2HttpMessageConverter);
        //添加utf-8的默认String转换器
        converters.add(new StringHttpMessageConverter(Charset.forName("UTF-8")));
    }

    class LocalDateToTimestampSerializer extends JsonSerializer<LocalDate> {

        @Override
        public void serialize(LocalDate localDate, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            long timestamp = localDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
            jsonGenerator.writeNumber(timestamp);
        }
    }

    class LocalDateTimeToTimestampSerializer extends JsonSerializer<LocalDateTime> {

        @Override
        public void serialize(LocalDateTime localDateTime, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            long timestamp = localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
            jsonGenerator.writeNumber(timestamp);
        }
    }
}
