package com.jettech.dao.idao;

import java.util.List;

import com.jettech.model.BaseModel;


/**
 * 持久层基�?接口
 * <AUTHOR>
 * @param <T>
 */
public interface IBaseDao<T extends BaseModel> {
	/**
	 * 根据业务主键查询
	 * @param resourceID 业务主键
	 * @return 
	 */
	public T findByResourceID(Long resourceID);
	/**
	 * 根据业务主键批量查询
	 * @param resourceIDs 批量业务主键
	 * @return 
	 */
	public List<T> findByResourceIDIn(List<String> resourceIDs);
	/**
	 * 根据ID集合查询
	 * @param idList  ID集合
	 * @return 
	 */
	public List<T> findByIdIn(List<Integer> idList);
	/**
	 * 根据ID集合查询
	 * @param idList  ID集合
	 * @return 
	 */
	public T findById(Integer id);
	/**
	 * 单个保存数据
	 * @param model 保存对象 
	 * @return 
	 */
	public T save(T model);
	/**
	 * 批量保存数据
	 * @param modelList �?批保存对�? 
	 * @return 
	 */
	public List<T> save(List<T> modelList);
	/**
	 * 更新�?批数�?
	 * @param modelList �?批更新对�? 
	 * @return 
	 */
	public List<T> update(List<T> modelList);
	/**
	 * 更新单个对象
	 * @param model 单个更新对象 
	 * @return 
	 */
	public T update(T model); 
	/**
	 * 根据ID查询
	 * @param id 查询 
	 * @return 
	 */
	public T find(Integer id);
	/**
	 * 查询�?有数�?
	 * @return 
	 */
	public List<T> findAll();
	/**
	 * @return 当前总量
	 */
	public long count();
	/**
	 * 删除对象
	 * @param model 删除对象 
	 */
	public void delete(T model);
	/**
	 * 批量删除
	 * @param modelList 
	 */
	public void deleteInBatch(List<T> modelList); 
	
}
