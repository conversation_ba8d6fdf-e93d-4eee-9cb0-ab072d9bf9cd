package com.jettech.feign;

import com.jettech.common.config.FeignConfiguration;
import com.jettech.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "jettomanager-trp", configuration = FeignConfiguration.class)
public interface IFeignAssetsToTrp {

	/**
	 *
	 * @Title: findTestProjectByUserNumber
	 * @Description: 查询人员所属的所有项目
	 * <AUTHOR>
	 * @date 2019年11月4日
	 */

	@PostMapping(value = "/testProject/findTestProjectByUserNumber")
	public Result<?> findTestProjectByUserNumber(@RequestParam("userNumber") String userNumber,
												 @RequestParam("token") String token);
}
