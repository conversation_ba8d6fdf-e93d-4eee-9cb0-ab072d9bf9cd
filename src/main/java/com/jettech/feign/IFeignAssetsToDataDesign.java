package com.jettech.feign;

import com.jettech.common.config.FeignConfiguration;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.assets.GenerageCaseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@FeignClient(name = "jettomanager-datadesign", configuration = FeignConfiguration.class)
public interface IFeignAssetsToDataDesign {

	/**
	 * @Title: findSinglePointLeftTreeByProjectResourceID
	 * @description: 用例管理左侧树查询
	 * @param "[projectResourceID]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2019/12/3 17:25
	 */
	@GetMapping("/testSystem/SinglePointLeftTree/{projectResourceID}")
	public Result findSinglePointLeftTreeByProjectResourceID(
			@PathVariable(value = "projectResourceID") String projectResourceID, @RequestParam("token") String token);

	@PostMapping("/excel/getUrlMap")
	HashMap<String, Object> getUrl_Map(@RequestParam Map<String, Object> map);

	/**
	 * @Title: getTestCaseCaseId
	 * @description: 获取案例编号
	 * @param "[map]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2019/12/6 9:55
	 */
	@PostMapping("/testCase/getTestCaseCaseId")
	Result getTestCaseCaseId(Map<String, String> map);

	/**
	 * @Title: downloadModel
	 * @description: 模板下载
	 * @param "[]"
	 * @return void
	 * @throws <AUTHOR>
	 * @date 2019/12/16 16:04
	 */
	@GetMapping("/excel/downloadModel")
	void downloadModel(@RequestParam("token") String token);

	/**
	 * @Title: getTradeListAndObj
	 * @description: 查询交易，和对象
	 * @param "[nodeType, nodeResourceID]"
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 * @throws <AUTHOR>
	 * @date 2019/12/26 12:46
	 */
	@PostMapping("/trade/getTradeListAndObj")
	Map<String, Object> getTradeListAndObj(@RequestParam("nodeType") String nodeType,
			@RequestParam("nodeResourceID") String nodeResourceID, @RequestParam("token") String token);

	/**
	 * @Title: findAllTrade
	 * @description: 查询所有交易
	 * @param "[]"
	 * @return void
	 * @throws <AUTHOR>
	 * @date 2019/12/26 12:45
	 */
	@PostMapping("/trade/findAllTrade")
	Result findAllTrade(@RequestParam("token") String token);

	/**
	 * @Title: getNewTestCaseCaseId
	 * @Description: 案例编写：根据最新要求返回案例编号
	 * @Param: "[tradeResourceID]"
	 * @Return: "java.lang.String"
	 * @Author: xpp
	 * @Date: 2020/1/13
	 */
	@GetMapping("/testCase/getNewTestCaseCaseId/{tradeResourceID}")
	public Result getNewTestCaseCaseId(@PathVariable("tradeResourceID") Long tradeResourceID,
			@RequestParam("token") String token);

	/**
	 * @Title: isOperationAuthority
	 * @description: 登录用户是否有操作案例库的权限
	 * @param "[userNumber]"
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2020/1/14 11:10
	 */
	@PostMapping("/testSystem/isOperationAuthority")
	Result isOperationAuthority(@RequestParam("token") String token);

	/**
	 * @Title: findTestSystemUserByUserNumber
	 * @description: 查询当前登录用户是否和被测系统有关联
	 * @param "[userNumber]"
	 * @param nodeType
	 * @param nodeResourceID
	 * @return com.jettech.dto.Result
	 * @throws <AUTHOR>
	 * @date 2020/2/23 14:21
	 */
	@GetMapping("/testSystem/findByUserNumber/{nodeType}/{nodeResourceID}")
	Result findTestSystemUserByUserNumber(@PathVariable("nodeType") String nodeType,
			@PathVariable("nodeResourceID") String nodeResourceID, @RequestParam("token") String token);

	/**
	 * @Title: findTradeByResourceID
	 * @Description: 根据交易id查询系统
	 * @Param: "[tradeResourceID]"
	 * @Return: "com.jettech.dto.Result"
	 * @Author: xpp
	 * @Date: 2020/3/7
	 */
	@GetMapping("/testSystem/findTestSystembyTradeByResourceID/{tradeResourceID}")
	Result findTestSystembyTradeByResourceID(@PathVariable("tradeResourceID") Long tradeResourceID,
			@RequestParam("token") String token);
	/**
	 * @Title: findCaseIDByTradeResourceID
	 * @description: 查询交易下已经存在的全部案例编号
	 * <AUTHOR>
	 * @date 2020/6/16 
	 */
	@PostMapping("/testCase/findCaseIDByTradeResourceID")  
	public Result findCaseIDByTradeResourceID(@RequestParam("tradeResourceID") Long tradeResourceID,
			@RequestParam("token") String token);

	@PostMapping("/testCase/generateCase")
	public Result generateCase(@RequestBody GenerageCaseDto generateCaseDto);

	/**
	 * @Title: getTradeSystemModule
	 * @Description: 根据交易id查询系统、模块、交易
	 * @Param: "[tradeResourceID]"
	 * @Return: "com.jettech.dto.Result"
	 */
	@GetMapping("/trade/getTradeSystemModule/{tradeResourceIDs}")
	Result getTradeSystemModule(@PathVariable("tradeResourceIDs") Long tradeResourceID,
											 @RequestParam("token") String token);
}
