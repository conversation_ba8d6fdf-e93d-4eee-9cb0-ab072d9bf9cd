package com.jettech.feign;

import com.jettech.common.config.FeignConfiguration;
import com.jettech.common.dto.Result;
import com.jettech.common.vo.AttachmentAnalysisModelVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "jettomanager-attachmentreview", configuration = FeignConfiguration.class)
public interface IFeignAssetsToAttachment {

	@GetMapping("/attachment/model/details")
	@ApiOperation(value = "文档模型详情", notes = "文档模型详情", code = 200, produces = "application/json")
	@ApiImplicitParam(name = "id",value = "文档模型(主键)id",required = true,dataType = "string",paramType = "query")
	Result<AttachmentAnalysisModelVO> details(@RequestParam(name = "id",required = true) String id);
}
