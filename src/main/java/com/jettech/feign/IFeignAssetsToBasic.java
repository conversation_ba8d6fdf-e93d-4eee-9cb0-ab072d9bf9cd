package com.jettech.feign;

import com.jettech.common.config.FeignConfiguration;
import com.jettech.common.dto.Result;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.vo.UserRoleVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.Size;
import java.util.List;

@FeignClient(name = "jettomanager-basic", configuration = FeignConfiguration.class)
public interface IFeignAssetsToBasic {
	/**
	 *
	 * @Title: findByNumber
	 * @Description: 根据userNumber查询用户
	 * @param userNumber
	 * @return JettechUserDTO
	 */
	@PostMapping(value = "/user/findByNumber")
	public JettechUserDTO findByNumber(@RequestParam("userNumber") String userNumber,
									   @RequestParam("token") String token);

	/**
	 * @Title findAllUser
	 * @Description 查询所有用户
	 * @Params []
	 * @Return java.util.List<com.jettech.dto.trp.JettechUserDTO>
	 * <AUTHOR>
	 * @Date 2019/11/11
	 */
	@PostMapping(value = "/user/findAllPerson")
	public List<JettechUserDTO> findAllPerson(@RequestParam("token") String token);

	/**
	 * @Title findDefectTypeParentType
	 * @Description 数据字典查询
	 * @Params [params]
	 * @Return com.jettech.dto.Result
	 * <AUTHOR>
	 * @Date 2019/12/4
	 */
	@PostMapping(value = "/dataDictionary/findByName")
	Result findByName(@RequestParam("dicName") String dicName, @RequestParam("token") String token);
    
    /**
	 * 
	 * @Title: findFieldDataByFieldType 
	 * @Description: 查询下拉框字段的下拉数据
	 * @return
	 * <AUTHOR>
	 * @date 2020年5月25日 下午5:27:45
	 */
	@GetMapping(value="/fieldconfiguration/findFieldDataByFieldType")
	Result<?> findFieldDataByFieldType(@RequestParam("token") String token);
	
	/**
	 * 
	 * @Title: findFieldsByFieldType 
	 * @Description:查询案例配置多选框字段
	 * @return
	 * <AUTHOR>
	 * @date 2020年5月29日 上午10:35:51
	 */
	@GetMapping(value="/fieldconfiguration/findFieldsByFieldType")
	Result<?> findFieldsByFieldType(@RequestParam("token") String token);

		/**
	 * 
	 * @Title: findFieldTypeIsDate 
	 * @Description: 查询案例配置字段为日期的
	 * @return
	 * <AUTHOR>
	 * @date 2020年6月5日
	 */
	@GetMapping(value="/fieldconfiguration/findFieldTypeIsDate")
	Result<?> findFieldTypeIsDate(@RequestParam("token") String token);
	/**
	 * 
	 * @Title: findAllTestCaseExportFields 
	 * @Description:查询所有案例配置字段
	 * @return
	 * <AUTHOR>
	 * @date 2020年6月12日 
	 */
	@GetMapping(value="/fieldconfiguration/findAllTestCaseFields")
	Result<?> findAllTestCaseFields(@RequestParam("token") String token);

	/**
	 *
	 * @Title: initModuleMapping
	 * @Description: 初始化日志记录的map
	 * @return
	 */
	@GetMapping("/fieldconfiguration/initModuleMapping/{project}")
	Result<?> initModuleMapping(@PathVariable(value = "project") String project);
	
	/**   
	 * @Title: initTestCaseConfigData   
	 * @Description: 查询案例配置字段信息 
	 * @author: dingwenlong
	 * @date: 2021年10月8日 
	 * @return 
	 */
	@GetMapping("/fieldconfiguration/initTestCaseConfigData")
	Result initTestCaseConfigData();

	/**
	 * 查询指定父级部门下所有的指定角色人员
	 * @param roleIdList 角色id
	 * @param parentDeptResourceIDList 父级部门id
	 * @param deptResourceIDList 部门id
	 * @return
	 */
	@GetMapping("/user/findUserRole")
	@ApiOperation(value = "查询指定部门下所有的指定角色人员", notes = "查询指定父级部门下所有的指定角色人员", code = 200, produces = "application/json")
	Result<List<UserRoleVO>> findUserRole(@RequestParam(name = "roleIdList",required = true) @Size.List(@Size(min = 1,message = "角色id最少传入一条")) List<String> roleIdList,
										  @RequestParam(name = "parentDeptResourceIDList",required = false) List<String> parentDeptResourceIDList, @RequestParam(name = "deptResourceIDList",required = false) List<String> deptResourceIDList);

}
