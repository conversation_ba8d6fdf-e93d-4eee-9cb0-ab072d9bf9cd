package com.jettech.feign;

import com.jettech.common.config.FeignConfiguration;
import com.jettech.common.dto.FileIdName;
import com.jettech.common.dto.FileInfo;
import com.jettech.common.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

//fallback = FeignDataDesignToFileServiceServiceImpl.class
@FeignClient(name = "jettomanager-file", configuration = FeignConfiguration.class)
public interface IFeignDataDesignToFileService {
    @PostMapping(value = "file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result upload(@RequestPart MultipartFile[] files, @RequestParam Long objectResourceId, @RequestParam Integer objectType, @RequestParam boolean thumb);

    @RequestMapping(value = "file/getFileIdList", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<FileIdName>> getFileIdList(@RequestParam Long objectResourceId, @RequestParam Integer objectType);

    @RequestMapping(value = "file/downloadFile", method = RequestMethod.POST)
    public byte[] downloadFile(@RequestParam  Long resourceId);

    @RequestMapping(value = "file/deleteFile", method = RequestMethod.POST)
    @ResponseBody
    public Result<?> deleteFile(@RequestParam  String resourceIds) ;

    @RequestMapping(value = "file/test", method = RequestMethod.POST)
    @ResponseBody
    public String test(@RequestParam  String str) ;

    @RequestMapping(value = "file/getContent", method = RequestMethod.POST)
    public byte[] getContent(@RequestParam Long resourceId);

    @RequestMapping(value = "file/getFileInfo", method = RequestMethod.POST)
    @ResponseBody
    public FileInfo getFileInfo(Long resourceId);

    @PostMapping(value = "file/updateFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result updateFile(@RequestPart MultipartFile[] files,@RequestParam Long objectResourceId, @RequestParam Integer objectType,@RequestParam boolean thumb);

    @RequestMapping(value = "file/getFileInfoList", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<FileInfo>> getFileInfoList(@RequestParam  Long objectResourceId, @RequestParam Integer objectType);
    @RequestMapping(value = "file/deleteFileByResourceIds", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<FileInfo>> deleteFileByResourceIds(@RequestParam String objectResourceIds,@RequestParam Integer objectType);
    @PostMapping(value = "file/uploadSFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result uploadSFile(@RequestPart MultipartFile file, @RequestParam Long objectResourceId, @RequestParam Integer objectType, @RequestParam boolean thumb);

}
