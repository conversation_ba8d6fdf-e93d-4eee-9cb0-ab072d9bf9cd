package com.jettech.typehandler;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.StringTypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

public class UpperTypeHandler extends StringTypeHandler {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        super.setNonNullParameter(ps, i, Optional.ofNullable(parameter).map(String::toUpperCase).orElse(null), jdbcType);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String result = super.getNullableResult(rs, columnName);
        return result == null ? null : result.toUpperCase();
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String result = super.getNullableResult(rs, columnIndex);
        return result == null ? null : result.toUpperCase();
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String result = super.getNullableResult(cs, columnIndex);
        return result == null ? null : result.toUpperCase();
    }

    @Override
    public void setParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        super.setParameter(ps, i, Optional.ofNullable(parameter).map(String::toUpperCase).orElse(null), jdbcType);
    }

    @Override
    public String getResult(ResultSet rs, String columnName) throws SQLException {
        String result = super.getResult(rs, columnName);
        return result == null ? null : result.toUpperCase();
    }

    @Override
    public String getResult(ResultSet rs, int columnIndex) throws SQLException {
        String result = super.getResult(rs, columnIndex);
        return result == null ? null : result.toUpperCase();
    }

    @Override
    public String getResult(CallableStatement cs, int columnIndex) throws SQLException {
        String result = super.getResult(cs, columnIndex);
        return result == null ? null : result.toUpperCase();
    }
}
