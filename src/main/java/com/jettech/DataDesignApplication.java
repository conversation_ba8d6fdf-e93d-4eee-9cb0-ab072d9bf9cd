package com.jettech;

import org.apache.logging.log4j.core.lookup.MainMapLookup;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.jettech.common.util.Utils.getLog4j2Args;

@SpringBootApplication()
@RestController
@MapperScan("com.jettech.mapper")
@ComponentScan(value = "com.jettech",excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,classes = {com.jettech.interceptor.DataAuthAutoConfiguration.class})})
@EnableFeignClients(basePackages = { "com.jettech.feign" })
@EnableScheduling
public class DataDesignApplication {

	public static void main(String[] args) {
		// 设置Log4j2的运行参数
		String[] log4j2Args = getLog4j2Args(DataDesignApplication.class, args);
		MainMapLookup.setMainArguments(log4j2Args);
		SpringApplication.run(DataDesignApplication.class, args);
	}

	@Bean
	public ExecutorService executorService() {

		return Executors.newFixedThreadPool(5);
	}
}
