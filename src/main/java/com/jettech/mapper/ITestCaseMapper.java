package com.jettech.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.AtTestCase;

/**
 * @ClassName ITestCaseMapper
 * @description ITestCaseMapper
 * <AUTHOR>
 * @create 2019-11-04 19:17
 */
@Mapper
public interface ITestCaseMapper extends BaseMapper<AtTestCase> {
    /**
     * @Title: findbyTradeResourceID
     * @Description: 根据当前交易查询其下的所有案例
     * @Param: "[tradeResourceID]"
     * @Return: "java.util.List<com.jettech.model.TestCase>"
     * @Author: xpp
     * @Date: 2019/11/5
     */
    List<AtTestCase> findbyTradeResourceID(Long tradeResourceID);
    /**
     * @Title findbyTradeResourceIDList
     * @Description 根据交易集合查询案例
     * @Params [collect]
     * @Return [List<TestCase>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    List<AtTestCase> findbyTradeResourceIDList(@Param("list")List<Long> collect);
    /**
     * @Description 查询符合条件的案例总数
     * <AUTHOR>
     * @date 2019-11-06 18:09
     * @param tradeResourceID
     * @param maintainer
     * @param isNegative
     * @param caseType
     * @return int
     */
    int findCountByTradeResourceID(@Param("tradeResourceIDs")List<String> tradeResourceID, @Param("maintainer")String maintainer,
                                   @Param("isNegative")String isNegative, @Param("caseType")String caseType, @Param("testEnvironment")String testEnvironment);
    /**
     * @Description 查询符合条件的案例数据带分页
     * <AUTHOR>
     * @date 2019-11-06 18:23
     * @param tradeResourceID
     * @param startIndex
     * @param pageSizeInt
     * @param maintainer
     * @param isNegative
     * @param caseType
     * @return java.util.List<com.jettech.model.TestCase>
     */
    List<Map<String,String>> findByTradeAndOptions(@Param("tradeResourceIDs")List<String> tradeResourceID, @Param("startIndex")int startIndex,
                                         @Param("pageSizeInt")int pageSizeInt, @Param("maintainer")String maintainer,
                                         @Param("isNegative")String isNegative, @Param("caseType")String caseType, @Param("testEnvironment")String testEnvironment);

    /** 
      * @Title: findByCaseIds
      * @description: 通过案例编号查询
      * @param "[caseIds]"
      * @return java.util.List<com.jettech.model.TestCase>
      * @throws
      * <AUTHOR>
      * @date 2019/11/11 12:11 
      */
    List<AtTestCase> findByCaseIds(@Param("list") List<String> caseIds);

    /**
     * @Title: findByCaseIdsAndTradeResourceID
     * @description: 通过案例编号和交易ResourceID查询
     * @param "[caseIds]"
     * @return java.util.List<com.jettech.model.TestCase>
     * @throws
     * <AUTHOR>
     * @date 2019/11/15 17:57
     */
    List<AtTestCase> findByCaseIdsAndTradeResourceID(@Param("list")List<String> caseIds, @Param("tradeResourceID")String tradeResourceID);
    /**
     * @Description 通过交易resourceid查询交易下案例数量
     * <AUTHOR>
     * @date 2019-11-27 18:44
     * @param t
     * @return java.lang.Integer
     */
    Integer findCountNumberByTradeResourceID(@Param("tradeResourceID")String t);
    
    /**
     * 
     * @Title:
     * @Description:
     * @param 
     * @return Integer
     * @author: wu_yancheng
     * @date 2019年12月19日下午5:47:44
     */
    Integer findCountByCaseID(@Param("caseID")String caseID,@Param("tradeResourceID")String tradeResourceID);

    /**
     * @Title: findMaxTestCasebyTradeResourceID
     * @Description:  根据交易查询其下案例编号最大的案例
     * @Param: "[tradeResourceID]"
     * @Return: "com.jettech.model.AtTestCase"
     * @Author: xpp
     * @Date: 2019/12/25
     */
    List<AtTestCase> findMaxTestCasebyTradeResourceID(@Param("tradeResourceID") Long tradeResourceID);

    List<AtTestCase> findByDemandResourceIDAndTestEnviroment(@Param("demandResourceID") String demandResourceID,
                                                             @Param("env") String env);

    void replace(List<AtTestCase> atTestCases);
    /**
     * 根据交易和测试环境查询案例
    * @Title: findbyTradeResourceIDAndTestEnviroment
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param valueOf
    * @param @param testEnviroment
    * @param @return    参数
    * @return List<AtTestCase>    返回类型
    * @throws
    * <AUTHOR>
     */
	List<AtTestCase> findbyTradeResourceIDAndTestEnviroment(@Param("tradeRsourceID")Long tradeRsourceID, @Param("testEnviroment")String testEnviroment, @Param("maintainer")String maintainer,
															@Param("isNegative")String isNegative, @Param("caseType")String caseType);
    
    /**
     * 
     * @Title: findByCaseResourceID 
     * @Description: 
     * @param resourceID
     * @return
     * <AUTHOR>
     * @date 2020年5月29日 上午11:25:23
     */
    public Map<String, Object> findByCaseResourceID(@Param("resourceID")Long resourceID);
    /**
   	 * 
   	 * @Title findTestCaseDictionaryData
   	 * @Description 通过字典的name查询案例的字典数据
   	 * @return List<Map<String,Object>> 返回类型
   	 * <AUTHOR>
   	 */
	List<Map<String, Object>> findTestCaseDictionaryData(@Param("dicName")String dicName);
	/**
	 * 
	 * @Title: findAllUsersNameAndNumber
	 * @Description: 查询所有人员的name和number
	 * <AUTHOR>
	 */
	List<Map<String, String>> findAllUsersNameAndNumber();
	/**
	 * 
	 * @Title findTestSystemByTradeResourceID
	 * @Description 查询交易对应的系统
	 * @return Map<String,Object> 返回类型
	 * <AUTHOR>
	 */
	Map<String, Object> findTestSystemByTradeResourceID(@Param("tradeResourceID")Long tradeResourceID);
	/**
	 * @Title: findCaseIDByTradeResourceID
	 * @description: 查询交易下已经存在的全部案例编号
	 * <AUTHOR>
	 * @date 2020/6/16
	 */
	List<String> findCaseIDByTradeResourceID(@Param("tradeResourceID")Long tradeResourceID);

	List<Map<String, Object>> initMyCaseToAssesWithBynumber(@Param("userNumber") String userNumber, @Param("params") Map<String, String> params);

	/**
	 * <AUTHOR>
	 * @description 根据字典中infoName查询字典值
	 * @date 2021年01月21日 9:55
	 * @param [nameDescription]
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 **/
	List<Map<String, Object>> findTestCaseDictionaryDataByInfoName(@Param("nameDescription") String nameDescription);
	List<String> findTradeBytypeResourceID(@Param("type")String type,@Param("resourceID") String resourceID);
}
