package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.DTO.TaskTestCaseDTO;
import com.jettech.DTO.TaskTradeCaseDto;
import com.jettech.common.dataauth.DataAuth;
import com.jettech.common.dto.datadesign.TestCaseDTO;
import com.jettech.model.TestCase;
import com.jettech.view.TaskTradeCaseView;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ITestCaseMapper
 * @description ITestCaseMapper
 * <AUTHOR>
 * @create 2019-11-04 19:17
 */
@Mapper
public interface ITestCaseMapper extends BaseMapper<TestCase> {
	/**
	 * @Title: findbyTradeResourceID
	 * @Description: 根据当前交易查询其下的所有案例
	 * @Param: "[tradeResourceID]"
	 * @Return: "java.util.List<TestCase>"
	 * @Author: xpp
	 * @Date: 2019/11/5
	 */
	List<TestCase> findbyTradeResourceID(Long tradeResourceID);

	/**
	 * @Title findbyTradeResourceIDList
	 * @Description 根据交易集合查询案例
	 * @Params [collect]
	 * @Return [List<TestCase>]
	 * <AUTHOR>
	 * @Date 2019/11/6
	 */
	List<TestCase> findbyTradeResourceIDList(@Param("list") List<Long> collect);

	/**
	 * @Description 查询符合条件的案例总数
	 * <AUTHOR>
	 * @date 2019-11-06 18:09
	 * @param tradeResourceID
	 * @param maintainer
	 * @param isNegative
	 * @param caseType
	 * @return int
	 */
	int findCountByTradeResourceID(@Param("tradeResourceID") String tradeResourceID,
			@Param("createUser") String createUser, @Param("isNegative") String isNegative,
			@Param("caseType") String caseType, @Param("demandResourceID") String demandResourceID,
			@Param("caseId") String caseId);

	/**
	 * @Description 查询符合条件的案例数据带分页
	 * <AUTHOR>
	 * @date 2019-11-06 18:23
	 * @param tradeResourceID
	 * @param startIndex
	 * @param pageSizeInt
	 * @param maintainer
	 * @param isNegative
	 * @param caseType
	 * @return java.util.List<TestCase>
	 */
	List<TestCase> findByTradeAndOptions(@Param("tradeResourceID") String tradeResourceID,
			@Param("startIndex") int startIndex, @Param("pageSizeInt") int pageSizeInt,
			@Param("createUser") String createUser, @Param("isNegative") String isNegative,
			@Param("caseType") String caseType, @Param("demandResourceID") String demandResourceID,
			@Param("caseId") String caseId);

	/**
	 * @Title: findByCaseIds
	 * @description: 通过案例编号查询
	 * @param "[caseIds]"
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2019/11/11 12:11
	 */
	List<TestCase> findByCaseIds(@Param("list") List<String> caseIds);

	/**
	 * @Title: findByCaseIdsAndTradeResourceID
	 * @description: 通过案例编号和交易ResourceID查询
	 * @param "[caseIds]"
	 * @param
	 * @param demandResourceID
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2019/11/15 17:57
	 */
	List<TestCase> findByCaseIdsAndTradeResourceID(@Param("list") List<String> caseIds,
			@Param("tradeResourceID") String tradeResourceID, @Param("demandResourceID") String demandResourceID);

	/**
	 * @Description 通过交易resourceid查询交易下案例数量
	 * <AUTHOR>
	 * @date 2019-11-27 18:44
	 * @param t
	 * @param type
	 * @return java.lang.Integer
	 */
	Integer findCountNumberByTradeResourceID(@Param("tradeResourceID") String t,
			@Param("demandResourceID") String demandResourceID, @Param("type") String type);

	/**
	 * @Title findTestCaseMapByResourceID
	 * @Description 根据demandresourceid查询案例
	 * @Params [caseResourceID]
	 * @Return List<TestCase>
	 * <AUTHOR>
	 * @Date 2019/12/04
	 */
	List<TestCase> findBydemandResourceID(@Param("demandResourceID") Long demandResourceID);

	/**
	 * @Title: findMaxTestCasebyTradeResourceID
	 * @Description: 根据交易查询当前交易下案例编号的最大值
	 * @Param: "[resourceID]"
	 * @Return: "TestCase"
	 * @Author: xpp
	 * @Date: 2019/12/24
	 */
	List<TestCase> findMaxTestCasebyTradeResourceID(@Param("tradeResourceID") Long resourceID);

	/**
	 * @param userNumber
	 *
	 * @Title: findCountNumberByDemandRids @Description:
	 *         根据需求查询需求下手工编写案例数 @param @param demandResourceIDs @param @return
	 *         参数 @return List<Map<String,Object>> 返回类型 @throws
	 */
	List<Map<String, Object>> findCountNumberByDemandRids(@Param("demandResourceIDs") List<String> demandResourceIDs,
			@Param("userNumber") String userNumber);

	IPage<TestCase> findBySystemModule(IPage<TestCase> testCaseDTOPage,
			@Param("moduleResourceID") Long moduleResourceID, @Param("demandResourceID") Long demandResourceID);

	/**
	 * @Title countByTestCaseResourceID
	 * @Description 统计测试案例下的缺陷数量
	 * @param resourceIDList
	 * @return List<Map<String,Object>>
	 * <AUTHOR>
	 * @data Jan 10, 20203:19:35 PM
	 */
	List<Map<String, Object>> countByTestCaseResourceID(@Param("resourceIDList") List<String> resourceIDList);

	/**
	 * @Title findTotalNumberByDemandRids
	 * @Description 查询需求下的案例总数
	 * @param demandResourceIDs
	 * @return List<Map<String,Object>>
	 * <AUTHOR>
	 * @data Jan 10, 20205:47:05 PM
	 */
	List<Map<String, Object>> findTotalNumberByDemandRids(@Param("demandResourceIDs") List<String> demandResourceIDs);

	/**
	 * @param userNumber
	 * @Title: findtestCaseTableByTradeAndOptions
	 * @Description: 根据交易查询案例和根据搜索选项查询案例列表
	 * @Param: "[tradeResourceID, startIndex, pageSizeInt, createUser, isNegative,
	 *         caseType, demandResourceID, caseId, testEnviroment]"
	 * @Return: "java.util.List<TestCase>"
	 * @Author: xpp
	 * @Date: 2020/1/16
	 */
	List<Map<String,Object>> findtestCaseTableByTradeAndOptions(@Param("tradeResourceID") String tradeResourceID,@Param("testTaskResourceID")String testTaskResourceID,
			@Param("startIndex") int startIndex, @Param("pageSizeInt") int pageSizeInt,
			@Param("createUser") String createUser, @Param("isNegative") String isNegative,
			@Param("caseType") String caseType,	@Param("caseId") String caseId,	@Param("userNumber") String userNumber);

	/**
	 * @param userNumber
	 * @Title: findTestCaseCountByTradeResourceID
	 * @Description: 根据交易查询案例和根据搜索选项查询案例返回案例总数
	 * @Param: "[tradeResourceID, createUser, isNegative, caseType,
	 *         demandResourceID, caseId, testEnviroment]"
	 * @Return: "int"
	 * @Author: xpp
	 * @Date: 2020/1/16
	 */
	int findTestCaseCountByTradeResourceID(@Param("tradeResourceID") String tradeResourceID,@Param("testTaskResourceID")String testTaskResourceID,
			@Param("createUser") String createUser, @Param("isNegative") String isNegative,
			@Param("caseType") String caseType,	@Param("caseId") String caseId,	@Param("userNumber") String userNumber);



	/**
	 * @Title: findByTradeRidAndTestEnviroment
	 * @description: 通过交易rid和测试环境查询案例
	 * @param "[tradeResourceID, testEnviroment]"
	 * @param demandResourceID
	 * @param userNumber
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2020/2/11 11:18
	 */
	List<TestCase> findByTradeRidAndTestEnviroment(@Param("tradeResourceID") Long tradeResourceID,
			@Param("demandResourceID") String demandResourceID, @Param("testEnviroment") String testEnviroment,
			@Param("userNumber") String userNumber);

	/**
	 * @Title: findByTradeResourceIDAndDemandResourceID
	 * @description: 交易rid和需求rid查询
	 * @param "[tradeResourceID, demandResourceID]"
	 * @return java.util.List<TestCase>
	 * @throws <AUTHOR>
	 * @date 2020/2/25 10:48
	 */
	List<TestCase> findByTradeResourceIDAndDemandResourceID(@Param("tradeResourceID") Long tradeResourceID,
			@Param("demandResourceID") String demandResourceID);

	/**
	 * 查询手工执行任务下选中保存的案例所属交易Rid
	 *
	 * @param userNumber
	 * @param hasQuoteSearchCaseRids
	 * @param testPlanResourceID
	 * @Title: findTradeRidsBySelectedTestCaseByTaskResourceID
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return List<Trade> 返回类型
	 * @throws <AUTHOR>
	 */
	List<String> findTradeRidsBySelectedTestCaseByTaskResourceID(@Param("taskResourceID") String taskResourceID,
			@Param("userNumber") String userNumber,
			@Param("hasQuoteSearchCaseRids") List<String> hasQuoteSearchCaseRids);

	/**
	 * 查询手工执行任务下选中保存的案例所属交易Rid
	 *
	 * @param userNumber
	 * @param hasQuoteSearchCaseRids
	 * @param testPlanResourceID
	 * @Title: findTradeRidsBySelectedTestCaseByTaskResourceID
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return List<Trade> 返回类型
	 * @throws <AUTHOR>
	 */
	List<String> findTradeRidsBySelectedTestCaseByTaskResourceID2(@Param("taskResourceID") String taskResourceID,
			@Param("userNumber") String userNumber,
			@Param("hasQuoteSearchCaseRids") List<String> hasQuoteSearchCaseRids,
			@Param("testPlanResourceID") String testPlanResourceID);

	/**
	 * 根据交易和任务的rid查询当前任务当前交易下的手工编写案例
	 *
	 * @Title: findTestCaseByTaskAndTradeResourceID
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  map
	 * @param @return 参数
	 * @return List<TestCase> 返回类型
	 * @throws <AUTHOR>
	 */
	List<Map<String, Object>> findTestCaseByTaskAndTradeResourceID(@Param("taskResourceID") String taskResourceID,
			@Param("tradeResourceID") String tradeResourceID);

	/**
	 * 通过交易和任务查询保存的案例
	 *
	 * @param userNumber
	 * @param hasQuoteSearchCaseRids
	 * @Title: findTestCasesByTaskAndTrades
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  trades
	 * @param @param  taskResourceID
	 * @param @return 参数
	 * @return List<TestCase> 返回类型
	 * @throws <AUTHOR>
	 */
	List<TestCase> findTestCasesByTaskAndTrades(@Param("tradeResourceID") String tradeResourceID,
			@Param("taskResourceID") String taskResourceID, @Param("userNumber") String userNumber,
			@Param("hasQuoteSearchCaseRids") List<String> hasQuoteSearchCaseRids);

	/**
	 * 通过字典的name查询案例的字典数据
	 *
	 * @Title: findTestCaseDictionaryData
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  dicName
	 * @param @return 参数
	 * @return List<Map<String,Object>> 返回类型
	 * @throws <AUTHOR>
	 */
	List<Map<String, Object>> findTestCaseDictionaryData(@Param("dicName") String dicName);

	/**
	 * 查询所有人员的name和number
	 *
	 * @Title: findAllUsersNameAndNumber
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return List<Map<String,String>> 返回类型
	 * @throws <AUTHOR>
	 */
	List<Map<String, String>> findAllUsersNameAndNumber();

	/**
	 * 案例资产库：根据最新要求返回最大的案例编号(at_testCase表)
	 *
	 * @Title: getAssetMaxTestCaseCaseId
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @return 参数
	 * @return String 返回类型
	 * @throws <AUTHOR>
	 */
	String getAssetMaxTestCaseCaseId(@Param("tradeResourceID") Long tradeResourceID);

	/**
	 * 分页查询案例信息
	 *
	 * @Title: findCaseInfoPage
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param  caseResourceIDs
	 * @param @return 参数
	 * @return List<Map<String,Object>> 返回类型
	 * @throws <AUTHOR>
	 */
	List<Map<String, Object>> findCaseInfoPage(@Param("caseResourceIDs") List<Long> caseResourceIDs);

	/**
     *
     * @Title: findByCaseResourceID
     * @Description:
     * @param resourceID
     * @return
     * <AUTHOR>
     * @date 2020年5月29日 上午11:25:23
     */
    public Map<String, Object> findByCaseResourceID(@Param("resourceID")Long resourceID);

	/**
	 * @param demandResourceIDs
	 * @return List<Map < String, Object>>
	 * @Title findTotalZXNumberByDemandRids
	 * @Description 查询需求下的所有执行案例
	 * <AUTHOR>
	 */
	List<Map<String, Object>> findTotalZXNumberByDemandRids(@Param("demandResourceIDs")List<String> demandResourceIDs);
	/**
	 * @Title: findCaseIDByTradeResourceID
	 * @description: 查询交易下已经存在的全部案例编号
	 * <AUTHOR>
	 * @date 2020/6/16
	 */
	List<String> findCaseIDByTradeResourceID(@Param("tradeResourceID")Long tradeResourceID);

	/**
     *
     * @Title: findByTestTaskResourceIDAndNotCommitted
     * @Description: 查询任务下没有提交的案例
     * @param testTaskResourceID
     * @return
     * <AUTHOR>
     * @date 2020年6月9日 下午6:15:00
     */
    public List<TestCase> findByTestTaskResourceIDAndNotCommitted(@Param("testTaskResourceID")Long testTaskResourceID);

    /**
     *
     * @Title: findTestPhaseByTestTaskResourceID
     * @Description: 查询测试阶段
     * @param testtaskResourceID
     * @return
     * <AUTHOR>
     * @date 2020年6月23日 下午6:49:43
     */
    public String findTestPhaseByTestTaskResourceID(@Param("testtaskResourceID")Long testtaskResourceID);
    /**
     * @Title findByTradeRidAndTaskRid
     * @description 查询交易下某一任务的案例
     * @date 20200628
     * <AUTHOR>
     */
	List<TestCase> findByTradeRidAndTaskRid(@Param("tradeResourceID")Long tradeResourceID, @Param("taskResourceID")Long taskResourceID,@Param("maintainer") String maintainer,
											@Param("isNegative") String isNegative,
											@Param("caseType") String caseType,
											@Param("caseId") String caseId,
											@Param("testMode") String testMode,
											@Param("testCaseResourceIDs") String testCaseResourceIDs,
											@Param("leadsource") String leadsource);

	/**
	 *
	 * @Title: findProjectTestCase
	 * @Description: 查询项目案例
	 * @param testSystem
	 * @param trade
	 * @param caseId
	 * @param intent
	 * @param caseType
	 * @param casetLevel
	 * @param demandFlag
	 * @param testCaseRids
	 * @param caseFinalResult
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 下午5:27:56
	 */
	List<TestCaseDTO> findProjectTestCase(@Param("testSystem")String testSystem,
										  @Param("trade")String trade,
										  @Param("caseId")String caseId,
										  @Param("intent")String intent,
										  @Param("caseType")String caseType,
										  @Param("casetLevel")String casetLevel,
										  @Param("resourceIDlist")List<String> resourceIDlist,
										  @Param("demandFlag")String demandFlag,
										  @Param("testCaseRids") List<String>  testCaseRids,
										  @Param("caseFinalResult")String caseFinalResult);


	/***
	 * 通过项目组的rid查询案例
	 * @Method : findByProjectGroupResourceIDs
	 * @Description : 通过项目组的rid查询案例
	 * @param pgResourceIDList : 项目rids
	 * @return : java.util.List<com.jettech.model.TestCase>
	 * <AUTHOR> Wws.
	 * @CreateDate : 2020-07-14 星期二 17:52:28
	 *
	 */
    List<TestCase> findByProjectGroupResourceIDs(@Param("list") List<String> pgResourceIDList,
												 @Param("testSystem")String testSystem,@Param("trade")String trade,@Param("caseId")String caseId,
												 @Param("intent")String intent,@Param("caseType")String caseType,@Param("casetLevel")String casetLevel,
												 @Param("demandFlag")String demandFlag);

    /**
     * @Method: findAllTaskTestCase
     * @Description: 查询所有需求案例
     * @Param: " [] "
     * @return: java.util.List<com.jettech.DTO.TaskTestCaseDTO>
     * @Author: wws
     * @Date: 2020/7/29
     */
    List<TaskTestCaseDTO> findAllTaskTestCase();
    /**
     * //查询案例执行库是否有数据
     * @param listS
     * @return
     * caojinbao
     */
	int findPerformCaseByProjectGroupResourceIDs(@Param("list") List<String> listS);
	/**
	 *
	* @Title: findInfoByTestCaseResourceIDNoModule
	* @Description: 根据当前案例的rid查找案例所属的系统模块交易和所属需求所属项目（交易直接挂在被测系统下）
	* @param @param resourceID
	* @param @return    参数
	* @return Map<String,Object>    返回类型
	* @throws
	* <AUTHOR>
	 */
	Map<String, Object> findInfoByTestCaseResourceIDNoModule(@Param("resourceID") String resourceID);
	/**
	 *
	* @Title: findInfoByTestCaseResourceIDHasModule
	* @Description:根据当前案例的rid查找案例所属的系统模块交易和所属需求所属项目（交易直接挂在所属模块下）
	* @param @param resourceID
	* @param @return    参数
	* @return Map<String,Object>    返回类型
	* @throws
	* <AUTHOR>
	 */
	Map<String, Object> findInfoByTestCaseResourceIDHasModule(@Param("resourceID") String resourceID, @Param("taskResourceID") String taskResourceID);
	/**
	 *
	* @Title: countAllPerformCaseAddToExecute
	* @Description: 校验当前需求下的项目案例是否都都添加到项目案例执行表
	* @param @param demandResourceID
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	int countAllPerformCaseAddToExecute(@Param("demandResourceID")Long demandResourceID);
	/**
	 *
	* @Title: countAllTestCaseAddToExecute
	* @Description:校验当前需求下的手工执行案例的数据是否都都添加到手工案例引用表
	* @param @param demandResourceID
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	int countAllTestCaseAddToExecute(@Param("demandResourceID")Long demandResourceID);
	/**
	 *
	* @Title: findPerformcaseByCaseResourceID
	* @Description: 项目案例表里当前案例非成功取消状态的案例
	* @param @param ceseRids
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	Integer findPerformcaseByCaseResourceID(@Param("ceseRids")List<Long> ceseRids,@Param("flag")String flag);
	/**
	 *
	* @Title: findTestCaseByCaseResourceID
	* @Description: 手工执行案例引用表里当前案例非成功取消状态的案例
	* @param @param ceseRids
	* @param @return    参数
	* @return int    返回类型
	* @throws
	* <AUTHOR>
	 */
	Integer findTestCaseByCaseResourceID(@Param("ceseRids") List<Long> ceseRids,@Param("flag")String flag);
	/**
	 * 根据需求的查询案例表信息
	 *
	 * @param demandResourceID
	 * @return List<String, Object>
	 * <AUTHOR>
	 */
    List<Map<String, Object>> findCaseInfoByDemandResourceID(@Param("demandResourceID") Long demandResourceID);

	/**
	 * 根据交易id列表以及任务等条件查询案例
	 * @param tradeResIds
	 * @param testTaskResId
	 * @param startIndex
	 * @param pageSizeInt
	 * @param maintainer
	 * @param isNegative
	 * @param caseType
	 * @param caseId
	 * @param tagResourceIDs
	 * @param userNumber
	 * @return
	 */
	List<Map<String, Object>> findByTradeResIdsAndTaskResIdPage(@Param("tradeResIds")List<Long> tradeResIds, @Param("testTaskResId") String testTaskResId,
																@Param("maintainer") String maintainer,
																@Param("isNegative") String isNegative,
																@Param("caseType") String caseType,
																@Param("caseId") String caseId,
																@Param("caseName") String caseName,
																@Param("intent") String intent,
																@Param("testMode") String testMode,
																@Param("tagResourceIDs")String tagResourceIDs,
																@Param("userNumber") String userNumber,
																@Param("coverage") String coverage);

	/**
	 * 根据tradeId更新tradeName
	 * @param tradeName
	 * @param tradeResourceID
	 * @return Result
	 * by zhangsheng
	 */
    void updateTestCaseTradeNameByTradeId(@Param("tradeName") String tradeName,@Param("tradeResourceID") Long tradeResourceID);

	/**
	 * 根据SystemId更新SystemName
	 * @param systemName
	 * @param systemResourceId
	 * @return Result
	 * by zhangsheng
	 */
    void updateTestCaseSystemNameBySystemId(@Param("systemName") String systemName, @Param("systemResourceId") Long systemResourceId);

	/**
	 * 根据testSystemResourceID 将ds_testcase表中的模块根据name替换
	 * @param oldName
	 * @param newName
	 * @param testSystemResourceID
	 * @return
	 */
	void updateTestCaseSystemModuleNameByNameAndSystemId(@Param("oldName") String oldName, @Param("newName") String newName, @Param("testSystemResourceID") Long testSystemResourceID);
	/**
	 * 查询项目案例库案例信息
	 *
	 * @param performCaseResourceID
	 * @return
	 */
    Map<String, Object> findPerformCaseInfo(@Param("performCaseResourceID")String performCaseResourceID);

	List<Map<String, Object>> initMyCaseToAssesWithBynumber(@Param("userNumber") String userNumber, @Param("params") Map<String, String> params);

	/**
	 * <AUTHOR>
	 * @description 根据caseid和tradeResid查询案例
	 * @date 2020年11月09日 10:04
	 * @param caseId
     * @return java.util.List<com.jettech.model.TestCase>
	 **/
	List<TestCase> findByCaseIdAndTradeResId(@Param("caseId") String caseId, @Param("tradeRresourceID") Long tradeRresourceID);

	/**
	 * <AUTHOR>
	 * @description 通过infoname查询字典值
	 * @date 2020年12月15日 14:22
	 * @param [type]
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 **/
    List<Map<String, Object>> findTestCaseDictionaryDataByInfoName(@Param("infoName") String infoName);

	/**
	 * <AUTHOR>
	 * @description 根据需求resourceid查询需求下的案例
	 * @date 2021年01月06日 10:04
	 * @param [demandsResourceIDList]
	 * @return java.util.List<com.jettech.model.TestCase>
	 **/
    List<TestCase> findBydemandResourceIDs(@Param("demandsResourceIDList") List<Long> demandsResourceIDList);


	/**
	 * 根据资源id查询标签关联表数据
	 *
	 * @param relationResourceIDs
	 * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
	 * <AUTHOR>
	 * 10:10
	 * @update
	 * @exception/throws [异常类型] [异常说明]
	 * @see [类、类#方法、类#成员]
	 * @since [起始版本]
	 */
	List<Map<String, Object>> findRelationResourceTags(@Param("relationResourceIDs") List<String> relationResourceIDs,@Param("relationType") int relationType);

	@DataAuth(mainTable = "testproject", codes = {"allProject", "relatedProject", "myProject"})
	List<Map<String, Object>> findTaskTree();

	List<Map<String, Object>> findTradeTree(@Param("taskResourceIDs") List<String> taskResourceIDs);

	List<TaskTradeCaseView> findTaskTradeCase(TaskTradeCaseDto dto);

	List<String> findCaseQuote(TaskTradeCaseDto dto);

	List<String> queryExecuteScopeResourceIdList(@Param("tradeResIds")List<Long> tradeResIds);

	List<Map<String, Object>> queryUiScriptByIds(@Param("scriptIdList")List<String> scriptIdList);

	List<Map<String, Object>> queryApiScriptByIds(@Param("tradeFlowIdList")List<String> tradeFlowIdList);

	/**
	 * @Title: findApiScriptById
	 * @Description: 查询api脚本,通过脚本id
	 * @author: dingwenlong
	 * @date: 2021年10月13日
	 * @param valueOf
	 * @return
	 */
	Map<String, Object> findApiScriptById(@Param("tradeFlowId")Long tradeFlowId);

    List<TestCase> findProjectCase(Map<String,Object> params);

	List<TestCase> findByProjectGroupResourceID(@Param("projectGroupResourceID") Long projectGroupResourceID);

    List<TestCase> findByTradeResourceIdAndleadSource(@Param("params") Map<String, String> params);

	List<TaskTradeCaseView> findTaskTradeCaseExport(TaskTradeCaseDto dto);
}
