package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.DTO.VersionInfoDto;
import com.jettech.model.VersionInfo;
import com.jettech.view.VersionInfoView;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/20
 **/
@Mapper
public interface IVersionInfoMapper extends BaseMapper<VersionInfo> {

    List<VersionInfoView> findByCondition(VersionInfoDto versionInfoDto);
}
