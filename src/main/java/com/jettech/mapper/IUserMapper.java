package com.jettech.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.User;
@Mapper
public interface IUserMapper extends BaseMapper<User> {
	/**
	 * @desc 根据用户名称查询用户
	 * @param userName
	 * <AUTHOR>
     * @date: 2019年6月5日 上午10:41:37
	 */
	User findByUserName(@Param("userName")String userName);
	/**
	 * @desc 根据用户编码查询用户
	 * @param userName
	 * <AUTHOR>
     * @date: 2019年6月5日 上午10:41:37
	 */
	User findByNumber(@Param("number")String number);

}
