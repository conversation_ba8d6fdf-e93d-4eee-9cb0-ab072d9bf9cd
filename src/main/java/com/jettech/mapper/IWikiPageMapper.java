package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.common.dto.assets.WikiPageDTO;
import com.jettech.model.WikiPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IWikiPageMapper extends BaseMapper<WikiPage> {

    WikiPage getPage(@Param("resourceID") Long resourceID, @Param("loginUserNumber") String loginUserNumber);

    List<WikiPage> listPageBySpace(@Param("spaceId") Long spaceId, @Param("draft") boolean draft);

    List<WikiPage> listAllPageBySpace(@Param("spaceId") Long spaceId);

    List<WikiPage> listDraftPage(@Param("wikiPageDTO") WikiPageDTO wikiPageDTO);

    List<WikiPage> listRecyclePageBySpace(@Param("wikiPageDTO") WikiPageDTO wikiPageDTO);
}
