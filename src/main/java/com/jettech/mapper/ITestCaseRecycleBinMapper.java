package com.jettech.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.TestCaseRecycleBin;

/**
 * @ClassName ITestCaseRecycleBinMapper
 * @description 案例编写（回收站表）Mapper层
 * <AUTHOR>
 * @create 2020-05-19
 */
@Mapper
public interface ITestCaseRecycleBinMapper extends BaseMapper<TestCaseRecycleBin> {
	/**
	 * @Title: findByTestTaskResourceID
	 * @Description: 查询任务和测试计划信息
	 * @param taskResourceID
	 * @param 参数
	 * @return Map<String,Object> 返回类型
	 * <AUTHOR>
	 */
	Map<String, String> findByTestTaskResourceID(@Param("taskResourceID") String taskResourceID);

	/**
	 * 
	 * @Title: findTradeResIDsByTestTaskResourceIDAndTradeResID
	 * @Description: 查询任务和交易下回收站的案例并返回交易ResourceID
	 * @param taskResourceID
	 * @param tradeResourceIDs
	 * @return List<String> 返回类型
	 * <AUTHOR>
	 * @date 20200520
	 */
	List<String> findTradeResIDsByTestTaskResourceIDAndTradeResID(@Param("taskResourceID") String taskResourceID,
			@Param("tradeResourceIDs") List<Long> tradeResourceIDs);

	/**
	 * @Title: findTestCaseRecycleBinPage
	 * @Description: 根据左侧树展示右侧回收站案例列表
	 * @Return: Page<TestCaseRecycleBin>
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	List<TestCaseRecycleBin> findTestCaseRecycleBinPage(@Param("taskResourceID") String taskResourceID,
			@Param("tradeResIds") List<Long> tradeResIds, @Param("userNumber") String userNumber);

	/**
	 * 获取早于某创建时间的数据
	 * 
	 * @param createTime
	 */
	List<TestCaseRecycleBin> findByLECreateTime(Date createTime);
	
	/**
	 * @Title: findMaxTestCasebyTradeResourceID
	 * @Description: 根据交易查询当前交易下案例编号的最大值
	 * @Param: "[resourceID]"
	 * @Return: "TestCase"
	 * @Author: xpp
	 * @Date: 2019/12/24
	 */
	TestCaseRecycleBin findMaxTestCasebyTradeResourceID(@Param("tradeResourceID") Long resourceID);
	
	/**
	 * @Title: findByTradeResourceID
	 * @Description: 根据TradeResourceID查询回收站案例
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	List<TestCaseRecycleBin> findByTradeResourceID(@Param("tradeResourceID")Long tradeResourceID);
	/**
	 * @Title: findByTradeResourceID
	 * @Description: 根据TradeResourceID查询回收站案例CaseID
	 * @Author: sheng_liqing
	 * @Date: 20200520
	 */
	List<String> findCaseIDByTradeResourceID(@Param("tradeResourceID")Long tradeResourceID);

	List<TestCaseRecycleBin> findCaseByTradeResourceIDList(@Param("tradeResourceIDList")List<Long> mlist);
    /**
     * <pre>findBytestsystemResourceidAndtaskResourceID(根据系统ResourceID，模块ResourceID，交易ResourceID查询回收站内信息)   
     * 创建人：赵凯
     * 创建时间：2023年3月09日 下午4:21:31    
     * 修改人：赵凯       
     * 修改时间：2023年3月09日 下午4:21:31    
     * 修改备注：  (东莞银行)
     * @param testSystemResourceID
     * @param testTaskResourceID
     * @param systemModuleResourceID
     * @return</pre>
     */
	List<Long> findBytestsystemResourceidAndtaskResourceID(@Param("testSystemResourceID")String testSystemResourceID, @Param("testTaskResourceID")String testTaskResourceID, @Param("systemModuleResourceID")String systemModuleResourceID);

}
