package com.jettech.mapper;

import com.jettech.model.AsAnalysisModelDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.vo.AsAnalysisModelDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <p>
    * 测试分析模型审核表 Mapper 接口
    * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/
@Mapper
public interface AsAnalysisModelDetailMapper extends BaseMapper<AsAnalysisModelDetail> {

    List<AsAnalysisModelDetailVO> reviewRecords(@Param("id") String id);
}
