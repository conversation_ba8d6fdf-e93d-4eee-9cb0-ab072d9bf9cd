package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.RichTextImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * rich_text_image表对应的mapper接口RichTextImage
 */
@Mapper
public interface IRichTextImageMapper extends BaseMapper<RichTextImage> {
    /**
     * 根据图片路经查找
     *
     * @param imagePath 图片路径
     * @return RichTextImage
     */
    RichTextImage findByImagePath(@Param("imagePath") String imagePath);
}
