package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.DataBackupHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IDataBackupHistoryMapper extends BaseMapper<DataBackupHistory> {

    @Select({
            "<script>SELECT  id,name,fileName,filePath,clear,type,backupTime,status,demandResourceID,demandName,url," +
                    "`comment`" +
                    ",version,createTime,editTime,createUser,editUser,resourceID  FROM data_backup_history where 1=1 <if test=\"name != null and name != ''\">and name LIKE concat('%',#{name},'%')</if>"+
                    "<if test=\"demandName != null and demandName != ''\">and demandName LIKE concat('%',#{demandName},'%')</if> order by id desc </script>"
    })
    List<DataBackupHistory> selectDataBackHistoryList(@Param("name")String name, @Param("demandName")String demandName);

    @Select({
            "<script>SELECT  id,name,fileName,filePath,clear,type,backupTime,status,demandResourceID,demandName,url," +
                    "`comment`" +
                    ",version,createTime,editTime,createUser,editUser,resourceID  FROM data_backup_history where id in " +
                    "<foreach collection=\"list\" item=\"item\" separator=\",\" open=\"(\" close=\")\">#{item}</foreach></script>"
    })
    List<DataBackupHistory> findById(List<Integer> idList);
}
