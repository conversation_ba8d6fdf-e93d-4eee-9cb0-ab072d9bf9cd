package com.jettech.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IDataBackupMapper {

    Map<String, String> getCreateTableSql(@Param("tableName") String tableName);

    List<Map<String, Object>> queryTableData(@Param("tableName") String tableName, @Param("params") Map<String, Object> params);

    int countTableData(@Param("tableName") String tableName, @Param("params") Map<String, Object> params);

    List<Map<String, Object>> getAllTable();

    int deleteTableData(@Param("tableName") String tableName, @Param("params") Map<String, Object> params);
}
