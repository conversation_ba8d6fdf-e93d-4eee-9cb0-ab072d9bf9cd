package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.dto.AsAnalysisModelAttachmentQueryDTO;
import com.jettech.dto.AsAnalysisModelQueryDTO;
import com.jettech.dto.AsModelUpdateRepeatedCaseDTO;
import com.jettech.model.AsAnalysisModel;
import com.jettech.vo.AsAnalysisModelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <p>
    * 测试分析模型表 Mapper 接口
    * </p>
*
* <AUTHOR>
* @since 2023-05-05
*/
@Mapper
public interface AsAnalysisModelMapper extends BaseMapper<AsAnalysisModel> {

    IPage<AsAnalysisModelVO> selectPageByDto(IPage<AsAnalysisModel> page, @Param("asAnalysisModelQueryDTO") AsAnalysisModelQueryDTO asAnalysisModelQueryDTO);

    IPage<AsAnalysisModelVO> reviewPage(IPage<AsAnalysisModel> page, @Param("name")String name, @Param("updateUser")String updateUser,@Param("number")String number,@Param("status")String status);

    IPage<AsAnalysisModelVO> attachmentPage(IPage<AsAnalysisModelVO> page, @Param("attachmentQueryDTO") AsAnalysisModelAttachmentQueryDTO asAnalysisModelAttachmentQueryDTO, @Param("status")String status);

    int updateRepeatedOrCase(@Param("dto") AsModelUpdateRepeatedCaseDTO dto);
}
