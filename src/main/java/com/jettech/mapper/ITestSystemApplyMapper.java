package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.TestSystemApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @title: ITestSystemApplyMapper
 * @description: 系统应用mapper
 * @date 2024/7/15
 */
@Mapper
@Component
public interface ITestSystemApplyMapper extends BaseMapper<TestSystemApply> {


    List<TestSystemApply> findTestSystemApply(@Param("params") Map<String, String> params);

    String findMaxApplyNumber(@Param("sysResourceID") Long sysResourceID, @Param("systemName") String systemName);
}
