package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.common.dataauth.DataAuth;
import com.jettech.model.TestProject;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Mapper
@Component
public interface ITestProjectMapper extends BaseMapper<TestProject>{

	/**
	 *
	 * @Title: findTestProject
	 * @Description: 分页查询项目
	 * @param pageRequest
	 * @param name
	 * @param status
	 * @param number
     * @param projectTypeList
     * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 上午11:52:01
	 */
	@DataAuth(mainTable = "testproject", codes = {"allProject", "relatedProject", "myProject"})
	public List<TestProject> findTestProject(@Param("name") String name, @Param("status") String status,
											 @Param("number") String number, @Param("projectTypeList") List<Integer> projectTypeList,
											 @Param("testMode") String testMode);

	/**
	 *
	 * @Title: findByNumber
	 * @Description: 根据项目编号查询项目
	 * @param number
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午12:56:03
	 */
	public TestProject findByNumber(@Param("number")String number) ;

	/**
	 *
	 * @Title: findByName
	 * @Description: 根据项目名称查询项目
	 * @param name
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月22日 下午8:17:52
	 */
	public List<TestProject> findByName(@Param("name")String name);
	/**
	 *
	 * @Title: findAllTestProjectByUser
	 * @Description: 查询工作台当前用户所有项目name和resourceID
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2020年6月19日
	 */
	public List<TestProject> findAllTestProjectByUser(@Param("userNumber")String userNumber);

	/**
	 *
	 * @Title: findAllParent
	 * @Description: 查询所有父项目
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月13日 下午5:07:55
	 */
	public  List<TestProject> findAllParent();

	/**
	 *
	 * @Title: findChildTestProjectByResourceID
	 * @Description: 查询子项目
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月13日 下午5:35:09
	 */
	public List<TestProject> findChildTestProjectByResourceID(@Param("resourceID")String resourceID);


	/**
	  * @param name
	 * @Title: loadParentDemand
	  * @description: 加载所有父级节点的项目
	  * @param "[]"
	  * @return com.jettech.dto.Result
	  * @throws
	  * <AUTHOR>
	  * @date 2020/7/13 17:08
	  */
	@DataAuth(mainTable = "testproject", codes = {"allProject", "relatedProject", "myProject"})
	List<TestProject> loadParentTestProject(@Param("name")String name);

	/**
	  * @Title: getChildrenTestProject
	  * @description: 父节点查询所属项目
	  * @param "[resourceID]"
	  * @return java.util.List<com.jettech.model.TestProject>
	  * @throws
	  * <AUTHOR>
	  * @date 2020/7/13 17:48
	  */
    List<TestProject> getChildrenTestProject(@Param("parentResourceID") String resourceID);

    /**
     *
     * @Title: findByUserNumber
     * @Description:查询用户关联的项目
     * @return
     * <AUTHOR>
     * @date 2020年7月14日 下午2:04:51
     */
    public List<TestProject> findByUserNumber(@Param("userNumber") String userNumber,@Param("status")String status);

    /**
     *
     * @Title: findByRootResourceIDIn
     * @Description: 查询跟节点下的所有子节点
     * @param resourceIDs
     * @return
     * <AUTHOR>
     * @date 2020年7月14日 下午2:12:36
     */
    public List<TestProject> findByRootResourceIDIn(@Param("resourceIDs") List<String> resourceIDs, @Param("status")String status);

	/**
	 *  查询项目是否有子项目
	 * @param resourceIDs
	 * @return
	 */
	public List<TestProject> findParentbyResourceIDIn(@Param("resourceIDs") List<String> resourceIDs);


	/**
	 * 根据条件查询所有项目
	 * @param name
	 * @param status
	 * @param number
	 * @param projectTypeList
	 * @return
	 */
	@DataAuth(mainTable = "testproject", codes = {"allProject", "relatedProject", "myProject"})
	List<TestProject> findTestProjectByCondition(@Param("name") String name, @Param("status") String status,
												 @Param("number") String number, @Param("projectTypeList") List<Integer> projectTypeList,@Param("testMode") String testMode);

	/**
	 * @Method: findByRootResourceID
	 * @Description: rootrid查询
	 * @Param: " [rootResourceID] "
	 * @return: java.util.List<com.jettech.model.TestProject>
	 * @Author: wws
	 * @Date: 2020/10/27
	 */
    List<TestProject> findByRootResourceID(@Param("rootResourceID") String rootResourceID);

    List<String> findEndStateBytestProjectResourceID(@Param("testProjectResourceID") String testProjectResourceID);

	@DataAuth(mainTable = "testproject", codes = {"allProject", "relatedProject", "myProject"})
    List<TestProject> findAllByDataAuth();

	List<Map<String, Object>> findTradeFlowCaseFolder(@Param("testProjectResourceId") Long testProjectResourceId);

	@DataAuth(mainTable = "testproject", codes = {"allProject", "relatedProject", "myProject"})
	List<TestProject> findAllByDataAuthByCondition(@Param("projectName") String projectName);
}
