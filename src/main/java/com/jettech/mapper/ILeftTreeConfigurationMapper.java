package com.jettech.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.LeftTreeConfiguration;

/**
 * <AUTHOR>
 * @ClassName ILeftTreeConfigurationMapper
 * @Description TODO
 * @Date 2020-02-21 16:14
 * @Version V1.0
 */
@Mapper
public interface ILeftTreeConfigurationMapper extends BaseMapper<LeftTreeConfiguration> {
    /**
     * @Title: findLeftTreeConfigurationByDemandResrouceIDAndType
     * @Description:  根据当前需求和类型查询自定义左侧树
     * @Param: "[demandResourceID, leftType]"
     * @Return: "java.lang.String"
     * @Author: xpp
     * @Date: 2020/2/21
     */
    LeftTreeConfiguration findLeftTreeConfigurationByDemandResrouceIDAndType(@Param("demandResourceID") String demandResourceID,
                                                              @Param("leftType") String leftType);
}
