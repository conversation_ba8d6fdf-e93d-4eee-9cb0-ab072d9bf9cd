package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.DTO.SceneCaseContainDTO;
import com.jettech.model.SceneCaseContain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface ISceneCaseContainMapper extends BaseMapper<SceneCaseContain> {

    int insertSelective(SceneCaseContain sceneCaseContain);

    List<SceneCaseContainDTO> selectByCondition(@Param("params") Map<String, Object> params);

    int selectMaxId();
}