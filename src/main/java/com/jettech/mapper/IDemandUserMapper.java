package com.jettech.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.DemandUser;

/**
 *
 * @ClassName IDemandUserMapper
 * @Description 需求与人员mapper
 * <AUTHOR>
 * @date 2019年12月4日
 */
@Mapper
public interface IDemandUserMapper extends BaseMapper<DemandUser> {


    List<Map<String,Object>> findUserNotINDemandUser(@Param("demandResourceID")String demandResourceID);

    List<Map<String,Object>> findUserByDemandResourceID(@Param("demandResourceID")String demandResourceID);
	/**
	 *
	 * @Title: addDemandUser
	 * @Description: 给需求关联人员
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2019年12月04日
	 */
    List<DemandUser> findByUserResourceIDAndDemandResourceID(@Param("userResourceID")String userResourceID, @Param("demandResourceID")String demandResourceID);

	List<DemandUser> findBydemandResourceID(@Param("demandResourceID")Long demandResourceID);
	/**
	 * @Title findUserResIDAnddemandResIDList
	 * @Description 根据人员和需求查询
	 * @param userResID 
	 * @param demandResIDList
	 * <AUTHOR>
	 *
	 */
	List<DemandUser> findUserResIDAnddemandResIDList(@Param("userResID")String userResID, @Param("demandResIDList")List<String> demandResIDList);
	/**
	 *
	 * @Title:joinDemand
	 * @Description:用户加入需求
	 * @param
	 * @return void
	 * @author: wu_yancheng
	 * @date 2019年12月9日上午10:55:57
	 */
    List<DemandUser> findDemandUserBydemandResourceIDs(@Param("demandResourceID") List<String> demandRids);
    /**
	 * @Title findUserResIDAndDemandResIDListAndDemandType
	 * @Description 根据人员，需求ResourceID和需求类型查询
	 * @param userResID 
	 * @param demandResIDList
	 * <AUTHOR>
	 * @param typeName 
	 * @param type 
	 *
	 */
	List<DemandUser> findUserResIDAndDemandResIDListAndDemandType(@Param("userResID")String userResID, @Param("demandResIDList")List<String> demandResIDList,
			@Param("type")String type, @Param("typeName")String typeName);

	/**
	 * @Title: findByUserResourceID
	 * @description: 用户rid查询需求
	 * @param "[userResourceID]"
	 * @return java.util.List<Demand>
	 * @throws
	 * <AUTHOR>
	 * @date 2019/12/25 11:52
	 */
    List<DemandUser> findByUserResourceID(String userResourceID);
    /**
     *
     * @Title:findUserByDemandRIDAndRole
     * @Description:根据需求id和角色查询人员
     * @param
     * @return List<Map<String,Object>>
     * @author: wu_yancheng
     * @date 2020年2月12日下午2:39:50
     */
    List<String> findUserByDemandRIDAndRole(@Param("demandResourceID")String demandResourceID,@Param("role")String role );
	/**
	 * @Description 通过需求rid和人员rid查询人员与需求关联表DTO
	 * <AUTHOR>
	 * @date 2020-02-18 11:05
	  * @param demandResourceID
	 * @param userResourceID
	 * @return DemandUser
	 */
	List<DemandUser> findDemandUserMapByDemandResourceIDAndUserResourceID(@Param("demandResourceID")String demandResourceID, @Param("userResourceID")String userResourceID);

	 /**
     * 
     * @Title: findByTestTaskResourceID 
     * @Description: 根据任务查询需求关联的人员
     * @param testTaskResourceID
     * @return
     * <AUTHOR>
     * @date 2020年6月24日 下午6:03:33
     */
    public List<DemandUser> findByTestTaskResourceID(@Param("testTaskResourceID")Long testTaskResourceID);

	/***
	 *
	 * @Method : getUserByDemandResourceIDAndUserGroupResourceIDs
	 * @Description : 通过需求resourceID和角色resourceIDs查询人员
	 * @param demandResourceID : 需求resourceID
	 * @param userGroupResourceIDs : 角色resourceIDs
	 * @return : java.util.List<DemandUser>
	 * <AUTHOR> Hansiwei.
	 * @CreateDate : 2020-07-13 周一 17:53:25
	 *
	 */
    List<DemandUser> findBydemandResourceIDAndUserGroupResourceIDs(@Param("demandResourceID")String demandResourceID, @Param("userGroupResourceIDs")List<String> userGroupResourceIDs);

	/**
	 * 根据testManagerResourceID,demandResourceID,role查询关联表中的数据
	 * @param oldTestManagerResourceID
	 * @param demandResourceID
	 * @param roleName
	 * @return DemandUser
	 */
    DemandUser findByTestManagerResourceIDAndDemandResourceIDAndRole(@Param("oldTestManagerResourceID") Long oldTestManagerResourceID, @Param("demandResourceID") Long demandResourceID, @Param("roleName") String roleName);
}
