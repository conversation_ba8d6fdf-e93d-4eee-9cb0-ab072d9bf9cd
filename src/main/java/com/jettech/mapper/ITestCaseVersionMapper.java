package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.DTO.VersionInfoDto;
import com.jettech.model.TestCaseVersion;
import com.jettech.view.TestCaseVersionView;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @ClassName ITestCaseMapper
 * @description ITestCaseMapper
 * <AUTHOR>
 * @create 2019-11-04 19:17
 */
@Mapper
public interface ITestCaseVersionMapper extends BaseMapper<TestCaseVersion> {

    List<TestCaseVersionView> findByCondition(VersionInfoDto dto);
}
