package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.common.dto.assets.EnvironmentPageDTO;
import com.jettech.model.Environment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * @ClassName IEnvironmentMapper
 * @description 被测系统环境信息记录Mapper层
 * <AUTHOR>
 * @create 20200630
 */
@Mapper
public interface IEnvironmentMapper extends BaseMapper<Environment> {
	/**
	 * @Title findEnvironmentPage
	 * @Description 按条件分页查询环境信息
	 * @author: slq
	 * @date: 2020年6月30日 上午11:40:30
	 */
	List<Environment> findEnvironmentPage(@Param("name") String name, @Param("ip") String ip,
			@Param("systemName") String systemName, @Param("type") String type, @Param("state") String state,
			@Param("systemManager") String systemManager, @Param("department") String department,
		    @Param("envUserName") String envUserName, @Param("systemComponents") String systemComponents);
	/**
	 * @Title  findByNameAndType     
	 * @Description  根据环境名称和环境类型查询环境（唯一性校验）
	 * @author: slq    
	 * @date:   2020年6月30日 下午4:59:27
	 */
	Environment findByNameAndType(@Param("name")String name, @Param("type")String type);
	/**
	 * @Title  findByEnvironment     
	 * @Description  查询环境信息
	 * @author: slq    
	 * @date:   2020年6月30日 下午5:34:14
	 */
	List<Map<String, String>> findByEnvironment(@Param("name") String name, @Param("ip") String ip,
			@Param("systemName") String systemName, @Param("type") String type, @Param("state") String state,
			@Param("systemManager") String systemManager, @Param("department") String department,
			@Param("envUserName") String envUserName, @Param("systemComponents") String systemComponents);
	/**
	 * @Title  findUpdateEnvironmentByResID     
	 * @Description 查询要修改的环境信息   
	 * @author: slq    
	 * @date:   2020年7月1日 上午11:51:04
	 */
	EnvironmentPageDTO findUpdateEnvironmentByResID(@Param("resourceID")String resourceID);

	/**
	 * 查询环境信息列表
	 * @param resourceIdList
	 * @return
	 */
	List<Map<String, String>> findByEnvironmentByResourceIds(List<String> resourceIdList);
}
