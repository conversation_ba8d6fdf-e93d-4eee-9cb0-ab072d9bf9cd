package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.PerFormCase;
import com.jettech.model.TestCasequote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-10-22 16:10
 */
@Mapper
public interface IPerFormCaseMapper extends BaseMapper<PerFormCase> {

    List<PerFormCase> findByTestcaseResourceID(@Param("testcaseResourceID")String resourceID);

    List<PerFormCase> findByGroupResourceIDs(@Param("list") List<String> projectGroups);
}
