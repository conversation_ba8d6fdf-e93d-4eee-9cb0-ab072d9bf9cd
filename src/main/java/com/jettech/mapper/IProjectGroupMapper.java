package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.ProjectGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: IProjectGroupMapper
 * @projectName jettomanager
 * @description: 项目组mapper
 * @date 2020/7/1319:31
 */
@Mapper
public interface IProjectGroupMapper  extends BaseMapper<ProjectGroup> {

	/**
     *
     * @Title: findByTestProjectResourceID
     * @Description:查询项目下的项目组
     * @param resourceID
     * @return
     * <AUTHOR>
     * @date 2020年7月14日 下午1:59:59
     */
    public List<ProjectGroup> findByTestProjectResourceID(@Param("resourceID")Long resourceID,@Param("groupType")String groupType);

    /***
     * 父节点查询
     * @Method : findByParentResourceID
     * @Description : 父节点查询
     * @param resourceID : parentRid
     * @return : java.util.List<com.jettech.model.ProjectGroup>
     * <AUTHOR> Wws.
     * @CreateDate : 2020-07-14 星期二 11:00:45
     *
     */
    List<ProjectGroup> findByParentResourceID(@Param("resourceID")Long resourceID,@Param("groupType")String groupType);

    /**
     * @Method: findByProjectRidsAndIsSmallPoint
     * @Description: 项目rid查询末节点
     * @Param: " [projectResourceID] "
     * @return: java.util.List<com.jettech.model.ProjectGroup>
     * @Author: wws
     * @Date: 2020/9/17
     */
    List<String> findByProjectRidsAndIsSmallPoint(@Param("projectResourceIDs") List<String> projectResourceIDs,@Param("groupType") String groupType);


    /**
     * @Method: findByParentIdAndName
     * @Description: 根据节点名称和父节点id查询projectGroup
     * @Param: " [name, projectResourceID, testProjectResourceID] "
     * @return: ProjectGroup
     * @Author: zhangsheng
     * @Date: 2020/9/18
     */
    List<ProjectGroup> findByParentIdAndName(@Param("name") String name, @Param("parentResourceID") Long parentResourceID,
                                             @Param("testProjectResourceID") Long testProjectResourceID, @Param("groupType") String groupType);

    /**
     * 根据testPorjectId,parentId,name,groupType 判断同一节点下名称是否重复
     * @param projectGroup
     * @return List<ProjectGroup>
     * by zhangsheng
     */
    List<ProjectGroup> findByTestProjectIdAndParentIdAndName(@Param("projectGroup") ProjectGroup projectGroup);

    List<Map<String, String>> findDefectByGroupResourceIDs(@Param("projectGroups") List<String> projectGroups);

    List<ProjectGroup> findByProjectRIDsAndGroupType(@Param("projectResourceIDs") List<String> projectResourceIDs, @Param("groupType") String groupType);
}
