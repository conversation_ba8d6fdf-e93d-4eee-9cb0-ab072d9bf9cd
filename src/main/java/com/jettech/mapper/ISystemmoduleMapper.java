package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.SystemModule;
import com.jettech.model.TreeNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ISystemmoduleMapper
 * @projectName jettopro
 * @description: 系统模块(被测资产管理) mapper
 * @date 2019/11/419:37
 */
@Mapper
@Component
public interface ISystemmoduleMapper extends BaseMapper<SystemModule> {
    /**
     * @Title verifyModuleNameNotRepeatedOfSystem   /  verifyModuleNameNotRepeatedOfModule
     * @Description 校验父节点（被测系统或者模块）下模块名称唯一
     * @Params [name, nodeResourceID, parentResourceID]
     * @Return java.util.List<SystemModule>
     * <AUTHOR>
     * @Date 2019/11/5
     */
    List<SystemModule> verifyModuleNameNotRepeatedOfSystem(@Param("name")String name, @Param("resourceID")String resourceID, @Param("testSystemResourceID")String testSystemResourceID);
    List<SystemModule> verifyModuleNameNotRepeatedOfModule(@Param("name")String name, @Param("resourceID")String resourceID, @Param("parentResourceID")String parentResourceID);
    List<SystemModule> verifyModuleSimpleNameNotRepeatedOfModule(@Param("name")String name, @Param("resourceID")String resourceID, @Param("parentResourceID")String parentResourceID);

    /**
     * @Description 通过被测系统resourceID查询模块
     * <AUTHOR>
     * @date 2019-11-06 14:02
     * @param strings
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    List<Map<String, Object>> findByTestSystemResourceIDs(@Param("list") List<String> strings);
    /**
     * @Title: findByParentResourceID
     * @description:   父节点查询
     * @param "ParentResourceID"
     * @return java.util.List<SystemModule>
     * @throws
     * <AUTHOR>
     * @date 2019/11/5 19:11
     */
    List<SystemModule> findByParentResourceID(@Param("parentResourceID") String parentResourceID);
    /**
     * @Title findbyTestSystemResourceID
     * @Description 查询当前被测系统下所有的模块（包含子模块）
     * @Params [testSystemResourceID]
     * @Return [ List<SystemModule>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    List<SystemModule> findbyTestSystemResourceID(@Param("testSystemResourceID")String testSystemResourceID);

    /**
     * @Title findbyTestSystemResourceID2
     * @Description 查询当前被测系统下所有的模块（包含子模块）
     * @Params [testSystemResourceID]
     * @Return [ List<SystemModule>]
     * <AUTHOR>
     * @Date 2019/11/6
     */
    List<SystemModule> findbyTestSystemResourceID2(@Param("testSystemResourceID")String testSystemResourceID);
    /**
     * @Description 查询该被测系统下一级的数据
     * <AUTHOR>
     * @date 2019-12-12 20:48
      * @param testSystemResourceID
     * @return java.util.List<java.util.HashMap<java.lang.String,java.lang.Object>>
     */
    List<HashMap<String, Object>> findNextLowerLevelMapByTestSystemResourceID(@Param("testSystemResourceID")Long testSystemResourceID);

    /**
     * @Title: findbySystemModuleName
     * @Description:
     * @Param: "[systemModuleName]"
     * @Return: "java.util.List<SystemModule>"
     * @Author: xpp
     * @Date: 2020/2/17
     */
    List<SystemModule> findbySystemModuleName(@Param("systemModuleName") String systemModuleName);
    /**
     *
    * @Title: findUserUnderModuleTaskTradeCount
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @return    参数
    * @return Map<Long,String>    返回类型
    * @throws
    * <AUTHOR>
     */
	List<Map<Long, String>> findUserUnderModuleTaskTradeCount(@Param("userNumber")String userNumber, @Param("taskResourceID")String taskResourceID, @Param("flag")String flag);
	  /**
     * 查询交易列表系统或者模块下面的交易总数（flag-system系统/flag-module-模块）
    * @Title: findUnderSystemORModuleTradeCount
    * @Description: TODO(这里用一句话描述这个方法的作用)
    * @param @param flag
    * @param @return    参数
    * @return List<Map<Long,String>>    返回类型
    * @throws
    * <AUTHOR>
     */
	List<Map<Long, String>> findUnderSystemORModuleTradeCount(@Param("flag")String flag);

	/**
	 *
	 * @Title: findByNameAndTestSystemResourceIDAndParentResourceID
	 * @Description: 根据系统和模块名称查询模块
	 * @param name
	 * @param testSystemResourceID
	 * @return
	 * <AUTHOR>
	 * @date 2020年5月27日 下午12:00:07
	 */
	public SystemModule findByNameAndTestSystemResourceIDAndParentResourceID(@Param("name")String name,@Param("testSystemResourceID")Long testSystemResourceID,@Param("parentResourceID")Long parentResourceID);
    /**
     *
     * @Method : findByDemandResourceID
     * @Description : 通过需求resourceID查询模块树节点数据
     * @param demandResourceID : 需求resourceID
     * @return : java.util.List<com.jettech.model.TreeNode>
     * <AUTHOR> Hansiwei.
     * @CreateDate : 2020-06-24 周三 14:28:45
     *
     */
    List<TreeNode> findByDemandResourceID(@Param("demandResourceID")String demandResourceID);
    /**
     * 根据模块获取上级模块
     *
     * @param moduleResourceID
     * @return
     */
    SystemModule findParentModuleByRid(@Param("moduleResourceID") Long moduleResourceID);

    List<SystemModule> findByTestSystemResIdIn(@Param("systemResIdList") List<String> systemResIdList);
}
