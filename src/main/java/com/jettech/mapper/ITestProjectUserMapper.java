package com.jettech.mapper;

import java.util.List;
import java.util.Map;

import com.jettech.DTO.ProjectUserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.TestProjectUser;

/**
 * 项目人员关联表Mapper
* @ClassName: ITestProjectUserMapper
* @Description: TODO(这里用一句话描述这个类的作用)
* <AUTHOR>
* @date 2020年4月20日
*
 */
@Mapper
public interface ITestProjectUserMapper extends BaseMapper<TestProjectUser>{
	
	/**
	 * 
	 * @Title: findByTestProjectResourceID
	 * @Description: 查询项目关联的人员
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午3:01:07
	 */
	public List<TestProjectUser>findByTestProjectResourceID(@Param("resourceID")Long resourceID);

	/**
	 * 
	 * @Title: findNotRelateUser 
	 * @Description: 查询未关联人员
	 * @param name
	 * @param deptName
     * @param testProjectResourceID
     * @param userGroupReourceIDs
     * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:13:44
	 */
	public List<Map<String, Object>>findNotRelateUser(@Param("name") String name, @Param("deptName") String deptName,
													  @Param("testProjectResourceID") Long testProjectResourceID,
													  @Param("userGroupReourceIDs") List<String> userGroupReourceIDs);
    
	/**
	 * 
	 * @Title: findRelatedUser 
	 * @Description: 查询已关联人员
	 * @param deptName
	 * @param testProjectResourceID
	 * @param userGroupReourceIDs
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:30:06
	 */
	public List<Map<String, Object>>findRelatedUser(@Param("name") String name, @Param("deptName") String deptName,
													@Param("testProjectResourceID") Long testProjectResourceID,
													@Param("userGroupReourceIDs") List<String> userGroupReourceIDs);
	
	/**
	 * 
	 * @Title: findByTestProjectResourceIDAndUserResourceIDIn 
	 * @Description: 根据项目和人员查询关联关系
	 * @param testProjectResourceID
	 * @param userResourceIDs
	 * @return
	 * <AUTHOR>
	 * @date 2020年4月21日 下午5:52:21
	 */
	public List<TestProjectUser> findByTestProjectResourceIDAndUserResourceIDIn(@Param("testProjectResourceID")Long testProjectResourceID,@Param("userResourceIDs")List<Long> userResourceIDs);


	/**
	 * 
	 * @Title: findByTestProjectResourceIDIN 
	 * @Description: 查询项目与人员关联关系
	 * @param resourceIDList
	 * @return
	 * <AUTHOR>
	 * @date 2020年7月14日 上午9:53:14
	 */
	public List<TestProjectUser> findByTestProjectResourceIDIN(@Param("resourceIDList")List<Long> resourceIDList);


	/**
	 * @Method: findALlProjectAndUser
	 * @Description: 项目和用户的关联关系（带用户名称和number）
	 * @Param: " [] "
	 * @return: java.util.List<com.jettech.model.TestProjectUser>
	 * @Author: wws
	 * @Date: 2020/9/23
	 */
    List<ProjectUserDTO> findALlProjectAndUser();
}
