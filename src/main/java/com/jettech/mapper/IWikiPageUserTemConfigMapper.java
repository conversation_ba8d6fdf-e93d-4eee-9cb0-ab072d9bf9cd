package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.WikiPage;
import com.jettech.model.WikiPageUserTemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IWikiPageUserTemConfigMapper extends BaseMapper<WikiPageUserTemConfig> {

    int deleteByParam(Map<String,Object> map);

    List<WikiPage> listPageUserByParam(@Param("config") WikiPageUserTemConfig config);
}
