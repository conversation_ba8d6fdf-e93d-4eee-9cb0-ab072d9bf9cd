package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.DTO.TestSystemDTO;
import com.jettech.common.dataauth.DataAuth;
import com.jettech.model.TestSystem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ITestSystemMapper
 * @projectName jettopro
 * @description: 被测系统（管理级）mapper
 * @date 2019/11/419:03
 */
@Mapper
@Component
public interface ITestSystemMapper extends BaseMapper<TestSystem> {
    /**
     * @param projectResourceID
     * @return java.util.List<TestSystem>
     * @Description 通过被测项目resourceID查询被测系统
     * <AUTHOR>
     * @date 2019-11-06 10:56
     */
    @DataAuth(mainTable = "at_testsystem", codes = {"allSystem", "relatedSystem", "relatedDemandSystem", "mySystem"})
    List<Map<String, Object>> findByTestProjectResourceID(@Param("demandResourceID") String demandResourceIDObj);

    /**
     * @Title verifySystemNameNotRepeated
     * @Description 校验项目下被测系统名称唯一
     * @Params
     * @Return
     * <AUTHOR>
     * @Date 2019/11/5
     */
    List<TestSystem> verifySystemNameNotRepeated(@Param("name") String name, @Param("nodeResourceID") Long nodeResourceID);

    List<Map<String, Object>> findUserBySystem(@Param("testSystemResourceID") Long testSystemResourceID);

    /**
     * @Title: findByTestSystemAndSearch
     * @Description: 系统交易管理初始化被测系统table(分页 + 条件查询)
     * @Param: "[params]"
     * @Return: "java.util.List<TestSystem>"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    @DataAuth(mainTable = "at_testsystem", codes = {"allSystem", "relatedSystem", "relatedDemandSystem", "mySystem"})
    List<TestSystemDTO> findByTestSystemAndSearch(@Param("params") Map<String, String> params);

    /**
     * @Title: selectTestSystemUser
     * @Description: 查询当前被测系统下已选人员
     * @Param: "[resourceID]"
     * @Return: "java.util.List<java.util.Map<java.lang.String,java.lang.Object>>"
     * @Author: xpp
     * @Date: 2019/12/4
     */
    List<Map<String, Object>> selectTestSystemUser(@Param("resourceID") Long resourceID);

    List<TestSystem> findByusernumber(@Param("number") String number);

    /**
     * @Title: verifySystemSimpleNameNotRepeated
     * @Description: 判断系统简称是否重复
     * @Param: "[simpleName, resourceID]"
     * @Return: "boolean"
     * @Author: xpp
     * @Date: 2019/12/10
     */
    List<TestSystem> verifySystemSimpleNameNotRepeated(@Param("simpleName") String simpleName, @Param("resourceID") Long resourceID);

    /**
     * @param demandResourceID
     * @return java.util.List<TestSystem>
     * @Description /过需求resourceid查询已关联的被测系统实体类
     * <AUTHOR>
     * @date 2019-12-12 20:33
     */
    List<TestSystem> findTestSystemByDemandResourceID(@Param("demandResourceID") Long demandResourceID);

    /**
     * @param request
     * @return
     * @Title: findRelevanceSystem
     * @Description: 查询所有系统
     * <AUTHOR>
     * @date 2019年12月04日
     */
    List<Map<String, Object>> findAllSystem(@Param("name") String name);

    /**
     * @param
     * @return
     * @Title: findRelevanceSystemID
     * @Description: 已关联系统ID
     * <AUTHOR>
     * @date 2019年12月04日
     */
    List<String> findRelevanceSystemID(@Param("resourceID") String resourceID);

    /**
     * @param userResourceID
     * @return
     * @Title: findTestSystemByUserResourceID
     * @Description: 查询人员所属系统
     * <AUTHOR>
     * @date 2020年3月3日 下午9:02:30
     */
    public String findTestSystemByUserResourceID(@Param("userResourceID") Long userResourceID);

    /**
     * @Title findAllSubordinateSystemMap
     * @Description 缺陷归属系统初始化查所有
     * @Params []
     * @Return com.jettech.dto.Result
     * <AUTHOR>
     * @Date 2020/3/6
     */
    List<TestSystem> findSubordinateSystemMap();

    /**
     * @Title findTestSystemMap
     * @Description 查询被测系统返回Map
     * @Params [systemRID]
     * @Return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Date 2020/3/7
     */
    Map<String, String> findTestSystemMap(@Param("systemRID") Long systemRID);

    /**
     * 查找当前人当前任务i下勾选的案例rid
     *
     * @param @param  tradeRids
     * @param @param  userNumber
     * @param @param  taskResourceID
     * @param @return 参数
     * @return List<String>    返回类型
     * @throws
     * @Title: findTestTaskCaseResourceIDsInTrades
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    List<String> findTestTaskCaseResourceIDsInTrades(@Param("tradeRids") List<String> tradeRids, @Param("userNumber") String userNumber, @Param("taskResourceID") String taskResourceID);

    /**
     * 查询任务和测试计划信息
     *
     * @param @param  taskResourceID
     * @param @return 参数
     * @return Map<String, Object>    返回类型
     * @throws
     * @Title: findByTestTaskResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    Map<String, String> findByTestTaskResourceID(@Param("taskResourceID") String taskResourceID);

    /**
     * 查询当前任务下引用人在引用表中的案例引用
     *
     * @param testPlanResourceID
     * @param @param             taskResourceID
     * @param @param             userNumber
     * @param @param             startTimeda
     * @param @param             endTimeda
     * @param @param             testCaseState
     * @param @return            参数
     * @return List<Map < String, String>>    返回类型
     * @throws
     * @Title: findCaseQuoteListByInfomation
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    List<Map<String, String>> findCaseQuoteListByInfomation(@Param("taskResourceID") String taskResourceID, @Param("userNumber") String userNumber, @Param("startTime") Date startTimeda,
                                                            @Param("endTime") Date endTimeda, @Param("testCaseState") String testCaseState);

    /**
     * 查找当前交易下有引用关系的引用案例
     *
     * @param @param  taskResourceID
     * @param @param  userNumber
     * @param @param  object
     * @param @param  object2
     * @param @param  testCaseState
     * @param @param  testPlanResourceID
     * @param @param  tradeResourceID
     * @param @return 参数
     * @param caseTypeList
     * @return List<Map < String, String>>    返回类型
     * @throws
     * @Title: findCaseQuoteListByTradeAndTaskPage
     * @Description: 手工执行，根据条件查询案例
     * <AUTHOR>
     * @param caseName 
     */
    List<Map<String, Object>> findCaseQuoteListByTradeInformation(@Param("taskResourceID") String taskResourceID, @Param("tradeResIDs") List<String> tradeResIDs,
                                                                  @Param("testCaseStates") List<String> testCaseStates, @Param("caseTypeList") List<String> caseTypeList,
                                                                  @Param("timeStart") Date timeStart,
                                                                  @Param("timeEnd") Date timeEnd,
                                                                  @Param("tagResourceIDs") String tagResourceIDs,
                                                                  @Param("begin")Integer begin, @Param("pageSize")Integer pageSize,@Param("caseName") String caseName, @Param("caseId") String caseId);

    Integer findCaseQuoteListByTradeInformationCount(@Param("taskResourceID") String taskResourceID, @Param("tradeResIDs") List<String> tradeResIDs,
                                                     @Param("testCaseStates") List<String> testCaseStates, @Param("caseTypeList") List<String> caseTypeList,
                                                     @Param("timeStart") Date timeStart,
                                                     @Param("timeEnd") Date timeEnd,
                                                     @Param("tagResourceIDs") String tagResourceIDs,@Param("caseName") String caseName);

    /**
     * 查看当前案例下是否有缺陷
     *
     * @param @param  caseRid
     * @param @return 参数
     * @return int    返回类型
     * @throws
     * @Title: findBugsByCaseResourceID
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * <AUTHOR>
     */
    int findBugsByCaseResourceID(@Param("caseRid") String caseRid);

    /**
     * @param name
     * @return
     * @Title: findByName
     * @Description: 根据名称查询系统
     * <AUTHOR>
     * @date 2020年5月27日 上午10:42:57
     */
    public TestSystem findByName(@Param("name") String name);

    /**
     * @Title findRelevanceSystemByDemandResourceID
     * @Description 查询当前需求是否关联系统
     * @author: slq
     * @date: 2020年8月17日 上午11:51:24
     */
    int findRelevanceSystemByDemandResourceID(@Param("demandResourceID") String demandResourceID);

    @DataAuth(mainTable = "at_testsystem", codes = {"allSystem", "relatedSystem", "relatedDemandSystem", "mySystem"})
    List<TestSystem> findAllByDataAuth();

    void deleteTradeFolderBySystem(@Param("resourceID") String resourceID);

    List<Map<String, Object>> countQuoteRecord(@Param("quoteResourceIDList") List<Long> quoteResourceIDList,
                                               @Param("timeStart") Date timeStart,
                                               @Param("timeEnd") Date timeEnd);

    List<Map<String, Object>> countCaseResultFile(@Param("recordResourceIDList") List<Long> recordResourceIDList);
    /**
     * @Description 通过被测项目resourceID查询被测系统（左连接查询）
     * @param demandResourceID
     * @return
     */
    @DataAuth(mainTable = "at_testsystem", codes = {"allSystem", "relatedSystem", "relatedDemandSystem", "mySystem"})
	List<Map<String, Object>> findByTestProjectResourceIDAndLeft(@Param("demandResourceID")String demandResourceID);
}
