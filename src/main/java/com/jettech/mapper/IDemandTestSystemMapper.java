package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.common.dataauth.DataAuth;
import com.jettech.model.DemandTestSystem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 *
 * @ClassName IDemandTestSystemMapper
 * @Description 需求与被测系统Mapper
 * <AUTHOR>
 * @date 2019年12月4日
 */
@Mapper
public interface IDemandTestSystemMapper extends BaseMapper<DemandTestSystem> {


    /**
     *
     * @Title: addDemandSystem
     * @Description: 给需求关联系统
     * @param request
     * @return
     * <AUTHOR>
     * @date 2019年12月04日
     */
    DemandTestSystem findByTestSystemResourceIDAndDemandResourceID(@Param("testSystemResourceID")String testSystemResourceID,@Param("demandResourceID") String demandResourceID);
    /**
     *
     * @Title: findNotRelevanceSystem
     * @Description: 查询已关联系统
     * @param request
     * @return
     * <AUTHOR>
     * @date 2019年12月04日
     */
    List<Map<String,Object>> findRelevanceSystem(@Param("resourceIDs")List<Long> resourceIDs);
	List<DemandTestSystem> findBydemandResourceID(@Param("demandResourceID")Long demandResourceID);

    /**
     * @Title: findBytestSystemResourceID
     * @Description: 根据系统查询与需求的关联实体
     * @Param: "[resourceID]"
     * @Return: "java.util.List<DemandTestSystem>"
     * @Author: xpp
     * @Date: 2019/12/9
     */
    List<DemandTestSystem> findBytestSystemResourceID(@Param("testSystemResourceID") Long resourceID);
    /**
	 *
	 * @Title: findNotRelevanceSystem
	 * @Description: 查询未关联系统
	 * @param request
	 * @return
	 */
	@DataAuth(mainTable = "at_testsystem", codes = {"allSystem", "relatedSystem", "relatedDemandSystem", "mySystem"})
	List<Map<String, Object>> getNotRelevanceSystem(@Param("name")String name,@Param("resourceID")String resourceID);


	/**
	 * @Method: findByDemandResourceIDIn
	 * @Description: 批量需求rid查询
	 * @Param: " [demandRdis] "
	 * @return: java.util.List<com.jettech.model.DemandTestSystem>
	 * @Author: wws
	 * @Date: 2020/11/10
	 */
    List<DemandTestSystem> findByDemandResourceIDIn(@Param("list") List<String> demandRdis);
}
