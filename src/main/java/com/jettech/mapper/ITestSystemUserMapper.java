package com.jettech.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.TestSystemUser;

/**
 * <AUTHOR>
 * @title: ITestSystemUserMapper
 * @projectName jettopro
 * @description: 系统和用户关联关系mapper
 * @date 2019/12/515:17
 */
@Mapper
public interface ITestSystemUserMapper extends BaseMapper<TestSystemUser>{

    /**
     * @Title: findbyTestSystemResourceID
     * @Description: 根据被测系统查询关联关系
     * @Param: "[testSystemResourceID]"
     * @Return: "java.util.List<TestSystemUser>"
     * @Author: xpp
     * @Date: 2019/12/5
     */
    List<TestSystemUser> findbyTestSystemResourceID(@Param("testSystemResourceID") String testSystemResourceID);

	List<TestSystemUser> findUserResourceID(@Param("userResourceID")List<String> userResourceID);
    /**
     * @Title: isOperationAuthority
     * @description: 登录用户是否有操作案例库的权限
     * @param "[userNumber]"
     * @return TestSystemUser
     * @throws
     * <AUTHOR>
     * @date 2020/1/14 11:22
     */
    List<TestSystemUser> isOperationAuthority(@Param("userNumber")String userNumber);
    /**
     * 通过系统查询系统下面人员表所有系统人员
     * findbyTestSystemResourceIDsIn
     * @param testSystemResourceIDs
     * @return
     * cao_jinbao
     */
	List<TestSystemUser> findbyTestSystemResourceIDsIn(@Param("testSystemResourceIDs")List<String> testSystemResourceIDs);

	/** 
	  * @Title: findByTestSystemResourceIDAndUserNumber
	  * @description: 被测系统rid和usernumber查询
	  * @param "[testSystemResourceID, userNumber]"
	  * @return TestSystemUser
	  * @throws
	  * <AUTHOR>
	  * @date 2020/2/23 14:42 
	  */
    TestSystemUser findByTestSystemResourceIDAndUserNumber(@Param("testSystemResourceID")Long testSystemResourceID, @Param("userResourceID")Long userResourceID);
	/**
	 * @param
	 * @param userGroupReourceIDs
	 * @param name
	 * @param deptName
	 * @param testSystemResourceID
	 * @param userGroupReourceID
	 * @return
	 * @Title: findNotRelateUser
	 * @Description: 查询未关联的人员
	 * <AUTHOR>
	 * @date 2020年9月9日 下午5:05:58
	 */
	List<Map<String, Object>>findNotRelateUser(@Param("name") String name, @Param("deptName") String deptName, @Param("testSystemResourceID") Long testSystemResourceID, @Param("userGroupReourceIDs") List<String> userGroupReourceIDs);

	/**
	 * @param name
	 * @param testSystemResourceID
	 * @param
	 * @return
	 * @Title: findRelatedUser
	 * @Description: 查询已关联人员
	 * <AUTHOR>
	 * @param userGroupReourceID 
	 * @date 2020年9月9日 下午5:28:52
	 */
	List<Map<String, Object>> findRelatedUser(@Param("name")String name, @Param("deptName") String deptName,@Param("testSystemResourceID")Long testSystemResourceID, @Param("userGroupReourceIDs") List userGroupReourceIDs);

	void deletebytestSystemtouser(@Param("testSystemResourceID")Long testSystemResourceID, @Param("userResourceIDs")List<Long> userResourceIDs);
	/**
     * 
     *@Description多个系统查询共同关联的人员
     *@param 
     *@return Result<?>
     *<AUTHOR>
     *@Date 2022年12月21日
     */
	List<Map<String, Object>> findRelatedUserOfMultipleSystem(@Param("name")String name,@Param("deptName") String deptName,
			@Param("testSystemResourceIDs")List<String> testSystemResourceIDs, @Param("userGroupReourceIDs")List<String> userGroupReourceIDs, @Param("num")int num);
	/**
     * 
     *@Description多个系统查询未关联的人员
     *@param 
     *@return Result<?>
     *<AUTHOR>
     *@Date 2022年12月21日
     */
	List<Map<String, Object>> findNotRelatedUserOfMultipleSystem(@Param("name")String name,@Param("deptName") String deptName,
			@Param("testSystemResourceIDs")List<String> testSystemResourceIDs, @Param("userGroupReourceIDs")List<String> userGroupReourceIDs, @Param("num")int num);
}
