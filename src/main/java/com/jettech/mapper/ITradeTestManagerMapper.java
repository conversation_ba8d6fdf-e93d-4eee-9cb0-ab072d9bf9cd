package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.bean.DemandToTradeDO;
import com.jettech.common.dto.datadesign.SystemModuleTradeDTO;
import com.jettech.model.Trade;
import com.jettech.model.TradeAndTestManager;
import com.jettech.model.TreeNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ITradeMapper
 * @description
 * <AUTHOR>
 * @create 2019-11-04 19:29
 */
@Mapper
public interface ITradeTestManagerMapper extends BaseMapper<TradeAndTestManager> {
   /**
    * 
    *@Description 查询交易的归属测试经理
    *@param 
    *@return List<TradeAndTestManager>
    *<AUTHOR>
    *@Date 2023年2月2日
    */
    
	List<TradeAndTestManager> findByTradeResourceID(Long tradeResourceID);

    List<TradeAndTestManager> findByTradeResourceIDIn(@Param("ridList")List<Long> ridList);
   
}
