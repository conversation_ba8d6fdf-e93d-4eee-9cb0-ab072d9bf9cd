package com.jettech.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jettech.model.TestCaseRecycleBin;
import com.jettech.model.TestCasequote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-10-22 15:07
 */
@Mapper
public interface ITestCasequoteMapper extends BaseMapper<TestCasequote> {
    TestCasequote findByTestcaseResourceID(@Param("resourceID") String resourceID);

    List<TestCasequote> findByCaseResourceID(@Param("resourceID")String resourceID);

    /**
     * <AUTHOR>
     * @description 根据案例resourceid查询案例的执行记录
     * @date 2021年01月06日 10:18
     * @param [testcaseResourceIDList]
     * @return java.util.List<com.jettech.model.TestCasequote>
     **/
    List<TestCasequote> findByCaseResourceIDs(@Param("testcaseResourceIDList") List<Long> testcaseResourceIDList);
}
