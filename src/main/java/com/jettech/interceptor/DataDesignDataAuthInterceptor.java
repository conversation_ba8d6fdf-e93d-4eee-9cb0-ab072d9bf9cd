package com.jettech.interceptor;

import com.jettech.common.dataauth.DataAuthInterceptor;
import com.jettech.common.dataauth.MenuStructureItemDto;
import com.jettech.common.dataauth.MenuStructureItemView;
import com.jettech.common.dataauth.RequestHelper;
import com.jettech.common.dto.trp.JettechUserDTO;
import com.jettech.common.enums.HttpHeaders;
import com.jettech.feign.IFeignDataDesignToBasicService;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 **/
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})
})
public class DataDesignDataAuthInterceptor extends DataAuthInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(DataDesignDataAuthInterceptor.class);

    private IFeignDataDesignToBasicService feignToBasicService;

    public DataDesignDataAuthInterceptor(IFeignDataDesignToBasicService feignToBasicService) {
        this.feignToBasicService = feignToBasicService;
    }

    @Override
    public List<MenuStructureItemView> getMenuStructureItemViewList(String[] codes, String mainTable) {
//        DataAuthRequestUtil util = new DataAuthRequestUtil(this.zuulUrl);
//        return util.getMenuStructureItemViewList(codes, mainTable);
        try {
            String userNumber = URLDecoder.decode(RequestHelper.getRequest().getHeader(HttpHeaders.USERNUMBER.getValue()),"UTF-8") ;
            MenuStructureItemDto dto = new MenuStructureItemDto();
            dto.setCodes(codes);
            dto.setMainTable(mainTable);
            dto.setLoginUserNumber(userNumber);
            return feignToBasicService.findUserDataAuth(dto);
        } catch (Exception e) {
            logger.error("数据权限获取权限数据失败：",e);
            return null;
        }
    }

    @Override
    public JettechUserDTO getJettechUserDTO() {
//        DataAuthRequestUtil util = new DataAuthRequestUtil(this.zuulUrl);
//        return util.getJettechUserDTO();
        try {
            String userNumber = URLDecoder.decode(RequestHelper.getRequest().getHeader(HttpHeaders.USERNUMBER.getValue()),"UTF-8") ;
            return feignToBasicService.findByNumber(userNumber);
        } catch (Exception e) {
            logger.error("数据权限获取用户信息失败：",e);
            return null;
        }
    }
}
