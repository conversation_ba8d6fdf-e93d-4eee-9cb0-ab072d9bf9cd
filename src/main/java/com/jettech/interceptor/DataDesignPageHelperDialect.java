//package com.jettech.interceptor;
//
//import com.jettech.common.dataauth.DataAuthDialect;
//import com.jettech.common.dataauth.DataAuthRequestUtil;
//import com.jettech.common.dataauth.MenuStructureItemView;
//import com.jettech.common.dto.trp.JettechUserDTO;
//
//import java.util.List;
//import java.util.Properties;
//
///**
// * <AUTHOR>
// * @Date 2021/5/11
// **/
//public class DataDesignPageHelperDialect extends DataAuthDialect {
//
//    private String zuulUrl;
//
//    @Override
//    public void setProperties(Properties properties) {
//        this.zuulUrl = properties.getProperty("zuul-url");
//    }
//
//    @Override
//    public List<MenuStructureItemView> getMenuStructureItemViewList(String[] codes, String mainTable) {
//        DataAuthRequestUtil util = new DataAuthRequestUtil(this.zuulUrl);
//        return util.getMenuStructureItemViewList(codes, mainTable);
//    }
//
//    @Override
//    public JettechUserDTO getJettechUserDTO() {
//        DataAuthRequestUtil util = new DataAuthRequestUtil(this.zuulUrl);
//        return util.getJettechUserDTO();
//    }
//}
