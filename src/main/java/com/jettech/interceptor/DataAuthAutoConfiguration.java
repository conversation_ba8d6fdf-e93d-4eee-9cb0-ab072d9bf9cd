package com.jettech.interceptor;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import com.jettech.feign.IFeignDataDesignToBasicService;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Iterator;
import java.util.List;

@Configuration
@ConditionalOnBean({SqlSessionFactory.class})
@AutoConfigureAfter({PageHelperAutoConfiguration.class})
public class DataAuthAutoConfiguration {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Autowired
    private IFeignDataDesignToBasicService feignDataDesignToBasicService;

    public DataAuthAutoConfiguration() {
    }

    @PostConstruct
    public void addPageInterceptor() {
        DataDesignDataAuthInterceptor interceptor = new DataDesignDataAuthInterceptor(feignDataDesignToBasicService);
        Iterator var3 = this.sqlSessionFactoryList.iterator();
        while(var3.hasNext()) {
            SqlSessionFactory sqlSessionFactory = (SqlSessionFactory)var3.next();
            sqlSessionFactory.getConfiguration().addInterceptor(interceptor);
        }

    }
}
