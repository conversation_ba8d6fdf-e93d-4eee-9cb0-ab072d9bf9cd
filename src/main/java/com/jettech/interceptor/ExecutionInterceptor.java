package com.jettech.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.alibaba.fastjson.JSON;
import com.jettech.execution.bean.Response;
import com.jettech.execution.handle.MD5Util;

/**
 * 执行拦截器
 * <AUTHOR>
 *
 */
@Component
public class ExecutionInterceptor implements HandlerInterceptor
{
	
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception 
	{
		String time = request.getHeader("time");
		String sign = request.getHeader("sign");
		if(null == time || "".equals(time.trim()) || null == sign || "".equals(sign))
		{
			response.setCharacterEncoding("UTF-8");
		    response.setContentType("application/json; charset=utf-8");
			response.getWriter().append(JSON.toJSONString(new Response().failure("验签参数出问题")));
			return false;
		}
		String localSign =  MD5Util.getMD5(time);
		if(localSign.equals(sign))
		{
			return true;
		}
		response.setCharacterEncoding("UTF-8");
	    response.setContentType("application/json; charset=utf-8");
		response.getWriter().append(JSON.toJSONString(new Response().failure("验签出错")));
		return false;
	}
}
