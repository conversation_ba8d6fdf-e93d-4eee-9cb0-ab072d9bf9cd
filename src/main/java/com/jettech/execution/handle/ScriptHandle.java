package com.jettech.execution.handle;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

/**
 * 脚本处理器
 * <AUTHOR>
 *
 */
@Component
public class ScriptHandle {
	
	/**
	 * 处理步骤1
	 * @param content
	 * @return
	 */
	public String handleFirstOneStep(String content)
	{
		//把<p></p>替换掉，成为正常格式的脚本
		content = StringUtil.handleHtml_p(content);
		//处理掉注释和换行符
		content = StringUtil.handleAnotation(content);
		return content;
	}
	
	/**
	 * 处理步骤2
	 * @param content
	 * @return
	 */
	public String[] handleSecondStep(String content,List<Map<String,String>> elementDataList)
	{
		String[] objs = null;
		Map<String,String[]> dataMap = new HashMap<>();
		for(Map<String,String> elementMap:elementDataList)
		{
			String[] datas = new String[2];
			datas[0] = elementMap.get("value");
			datas[1] = elementMap.get("defaultValue");
			dataMap.put(elementMap.get("name"), datas);
		}
		objs = StringUtil.handleTemplateParams(content, dataMap);
		return objs;
	}
	
	/**
	 * 处理模板和脚本,这里有一些步骤需要做操作:
	 * 1、翻译模板参数
	 * 2、替换关键字里面的参数内容，再把替换的内容整合回模板格式里面
	 * 3、生成函数脚本
	 * 4、替换函数和一些案例字段成为最终脚本
	 * @param template
	 * @param script
	 * @param funcList
	 * @param keyWordList  name content businessPrimitives
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public String[] handleTemplateAndScript(String template,Map<String,Object> script
			,List<String> funcList,List<Map<String,Object>> keyWordList,Map<String,Object> testCase)
	{
		String result[] = new String[2];
		result[0] = "true";
		//步骤1
		Object[] objs = StringUtil.parseTemplateValues(template);
		List<String> keyList = (List<String>) objs[0];
		List<List<String>> valueList = (List<List<String>>)objs[1];
		//步骤2
		String[] keyWordRes = packagingMainScript(keyList,valueList,keyWordList);
		if("false".equals(keyWordRes[0]))
		{
			return keyWordRes;
		}
		//步骤3
		String funcScript = StringUtil.packagingScriptFunc(funcList);
		//步骤 4
		testCase.put("script", keyWordRes[1]);
		testCase.put("function", funcScript);
		
		String finalScript = script.get("startScript").toString() + StringUtil.LINE + script.get("endScript").toString();
		String pkResult[] = StringUtil.packagingFinalScript(finalScript, testCase);
		if("false".equals(pkResult[0]))
		{
			return pkResult;
		}
		result[1] = pkResult[1];
		return result;
	}
	
	/**
	 * 分装主要的脚本
	 * @param keyList
	 * @param valueList
	 * @param keyWordList name content businessPrimitives
	 * @return
	 */
	private String[] packagingMainScript(List<String> keyList,
			List<List<String>> valueList,
			List<Map<String,Object>> keyWordList)
	{
		String[] result = new String[2];
		result[0] = "true";
		Map<String,String> keyMap = new HashMap<>();
		//把需要的key整合起来
		for(String key : keyList)
		{
			if(keyMap.get(key) == null)
			{
				keyMap.put(key, key);
			}
		}
		//把关键字从数据库的对象种整合成可用信息
		//参数名称信息
		Map<String,List<String>> paramMap = new HashMap<>();
		//脚本信息
		Map<String,String> scriptMap = new HashMap<>();
		for(Map<String,Object> keyWordMap:keyWordList)
		{
			String name = keyWordMap.get("name").toString();
			if(keyMap.get(name) != null)
			{
				String businessPri = keyWordMap.get("businessPrimitives").toString();
				boolean flag = StringUtil.setKeyAndParams(businessPri, paramMap);
				if(!flag)
				{
					result[0] = "false";
					result[1] = "关键字表为:"+name+"获取脚本的表达式businessPrimitives出现问题";
					return result;
				}
				scriptMap.put(name, keyWordMap.get("content") == null?"":keyWordMap.get("content").toString());
			}
		}
		//开始生成主要脚本信息
		StringBuilder rstr = new StringBuilder();
		String line = null;
		for(int i=0;i<keyList.size();++i)
		{
			String key = keyList.get(i);
			List<String> lvalue =valueList.get(i);
			line = scriptMap.get(key);//获取脚本
			List<String> plist = paramMap.get(key);
			//开始替换脚本内容
			rstr.append(StringUtil.getRealScript(line,plist,lvalue));
			if(i<keyList.size() -1)
			{
				rstr.append(StringUtil.LINE);
			}
		}
		
		result[1] = rstr.toString();
		return result;
	}
}
