package com.jettech.execution.handle;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具
 * <AUTHOR>
 *
 */
public class StringUtil {
	
	public static final String LINE = System.getProperty("line.separator");
	
	/**<p>和</p>标签*/
	public static final String HTML_P_FIRST_REX = "[<]{1}\\s*[pP]{1}\\s*[>]{1}";
	
	public static final String HTML_P_END_REX = "[<]{1}[/]{1}\\s*[pP]{1}\\s*[>]{1}";
	
	/**p标签的pattern*/
	public static final Pattern P_FIRST_HTML = Pattern.compile(HTML_P_FIRST_REX);
	
	public static final Pattern P_END_HTML = Pattern.compile(HTML_P_END_REX);
	//==========================================下列为模板参数正则
	
	/**模板参数正则表达式*/
	public static final String TEMPLATE_PARAM_REX = "[$]{1}(.+)[$]{1}";
	
	public static final Pattern P_TEMPLATE_PARAM = Pattern.compile(TEMPLATE_PARAM_REX);	
	//==========================================下列为大括号参数替换正则
	/**参数正则表达式*/
	public static final String PARAM_REX = "[{]{1}([a-zA-Z0-9\\$\\.]+)[}]{1}";
	
	public static final Pattern P_PARAM = Pattern.compile(PARAM_REX);
	
	
	public static Map<String,String> KEY_MAP = new HashMap<>();
	
	static 
	{
		KEY_MAP.put("empty", "");
		KEY_MAP.put("blank", " ");
		KEY_MAP.put("equals", "=");
		KEY_MAP.put("doller", "$");
		KEY_MAP.put("yuan", "￥");
		KEY_MAP.put("ltb", "{");
		KEY_MAP.put("gtb", "}");
	}
	
	/**
	 * 替换p
	 * @param content
	 * @return
	 */
	public static final String handleHtml_p(String content)
	{
		StringBuffer sb = null;
		if(null == content || "".equals(content.trim()))
		{
			return null;
		}
		sb = new StringBuffer();
		Matcher m = P_FIRST_HTML.matcher(content);
		while(m.find())
		{
			m.appendReplacement(sb, "");
		}
		m.appendTail(sb);
		StringBuffer sb2 = new StringBuffer();
		m = P_END_HTML.matcher(sb.toString());
		while(m.find())
		{
			m.appendReplacement(sb2, StringUtil.LINE);
		}
		m.appendTail(sb2);
		return sb2.toString();
	}
	
	/**
	 * 处理注释
	 * @param content
	 * @return
	 */
	public static final String handleAnotation(String content)
	{
		if(null == content || "".equals(content.trim()))
		{
			return null;
		}
		String[] lines = content.split(StringUtil.LINE);
		StringBuilder sb = new StringBuilder();
		String result = null;
		if(lines != null)
		{
			for(int i=0;i<lines.length;++i)
			{
				String line = lines[i];
				if(!line.trim().startsWith("#") && !"".equals(line.trim()))
				{
					sb.append(line);
					if(i != lines.length -1)
					{
						sb.append(StringUtil.LINE);
					}
				}
			}
			result = sb.toString();
			if(result.endsWith(StringUtil.LINE))
			{
				result = result.substring(0, result.length() - 2);
			}
		}
		return result;
	}
	
	/**
	 * 替换模板参数
	 * @param content
	 * @param dataMap
	 * @return
	 */
	public static final String[] handleTemplateParams(String content,Map<String,String[]> dataMap)
	{
		String[] objs = new String[2];
		objs[0] = "true";
		if(null == content || "".equals(content.trim()))
		{
			objs[0] = "false";
			objs[1] = "模板内容为空";
			return objs;
		}
		if(null == dataMap || 0 == dataMap.size())
		{
			objs[0] = "false";
			objs[1] = "要素数据为空";
			return objs;
		}
		
		Stack<Integer> dollerStack = new Stack<>();
		int index = -1;
		int begin = 0;
		int popBegin = 0;
		int end = 0;
		StringBuilder sb = new StringBuilder();
		do {
			index = content.indexOf("$", index+1);
			if(index != -1)
			{
				dollerStack.push(index);
			}
			if(dollerStack.size() >=2)
			{
				end = dollerStack.pop();
				begin = dollerStack.pop();
				String key = content.substring(begin+1, end);
				String startStr = content.substring(popBegin, begin);
				popBegin = end+1;
				sb.append(startStr);
				String[] values = dataMap.get(key);
				String replaceValue = null;
				if(null == values)
				{
					objs[0] = "false";
					objs[1] = "模板的key:"+key+" 在要素数据里面找不到对应的数据";
					return objs;
				}else {
					if(values[0] == null || "".equals(values[0].trim()))
					{
						replaceValue = values[1];
					}else
					{
						replaceValue = values[0];
					}
					if(P_TEMPLATE_PARAM.matcher(replaceValue).matches())
					{
						replaceValue = values[1];
					}
				}
				sb.append(replaceValue);
			}
		}while(index != -1);
		if(popBegin<content.length())
		{
			sb.append(content.substring(popBegin, content.length()));
		}
		if(dollerStack.size() != 0)
		{
			objs[0] = "false";
			objs[1] = "模板以$符号的参数格式有问题，需要的$符号可以以{doller}替代";
			return objs;
		}
		objs[1] = sb.toString();
		
		return objs;
	}
	
	/**
	 * 翻译参数
	 * @param content
	 * @return
	 */
	public static final Object[] parseTemplateValues(String content)
	{
		if(null == content || "".equals(content.trim()))
			return null;
		Object[] objs = new Object[2];
		List<String> list = new ArrayList<>();
		List<List<String>> paramList = new ArrayList<>();
		
		String lines[] = content.split(StringUtil.LINE);
		if(null != lines && 0 != lines.length)
		{
			for(String line : lines)
			{
				line = line.trim();
				if(!"".equals(line))
				{
					String key = null;
					int index = line.indexOf("==");
					if(-1 == index)
					{
						continue;
					}
					key = line.substring(0, index);
					list.add(key);
					List<String> valueList = new ArrayList<>();
					
					int begin = index +2;
					index = line.indexOf("==", index+2);
					
					while(index != -1)
					{
						String value = line.substring(begin, index);
						valueList.add(value);
						begin = index+2;
						index = line.indexOf("==", index+2);
					}
					paramList.add(valueList);
				}
			}
		}
		
		objs[0] = list;
		objs[1] = paramList;
		return objs;
	}
	
	/**
	 * 从字符串中获取key列表
	 * @param content
	 * @return
	 */
	public static boolean setKeyAndParams(String content,Map<String,List<String>> paramMap)
	{
		if(content == null || "".equals(content.trim()))
		{
			return false;
		}
		int index = content.indexOf("==");
		if(-1 == index)
		{
			return false;
		}
		String key = content.substring(0, index);
		Matcher m = P_PARAM.matcher(content);
		List<String> keyList = new ArrayList<>();
		while(m.find())
		{
			String keyValue = m.group(1);
			keyList.add(keyValue);
		}
		paramMap.put(key, keyList);
		
		return true;
	}
	
	/**
	 * 把脚本模板替换成真正的内容
	 * @param scriptLine
	 * @param paramMap
	 * @param lvalue
	 * @return
	 */
	public static final String getRealScript(String scriptLine,List<String> paramList,List<String> valueList)
	{
		Map<String,String> pmap = new HashMap<>();
		//如果参数个数和值的个数不匹配，那么直接返回空字符串
		if(paramList.size() != valueList.size())
		{
			return "";
		}
		for(int i=0;i<paramList.size();++i)
		{
			String key = paramList.get(i);
			String value = valueList.get(i);
			pmap.put(key, value);
		}
		StringBuffer sb = new StringBuffer();
		Matcher m  = P_PARAM.matcher(scriptLine);
		while(m.find())
		{
			String key = m.group(1);
			String value = KEY_MAP.get(key.toLowerCase());
			if(value != null)
			{
				m.appendReplacement(sb, value);
			}else {
				m.appendReplacement(sb, pmap.get(key));
			}
			
		}
		m.appendTail(sb);
		return sb.toString();
	}
	
	/**
	 * 封装函数列表成为脚本
	 * @param funcList
	 * @return
	 */
	public static final String packagingScriptFunc(List<String> funcList)
	{
		 StringBuilder sb = new StringBuilder();
		 if(null != funcList && 0 != funcList.size())
		 {
			 for(int i=0;i<funcList.size();++i)
			 {
				 sb.append(funcList.get(i));
				 if(i <funcList.size() - 1)
				 {
					 sb.append(StringUtil.LINE);
				 }
			 }
		 }
		 return sb.toString();
	}
	
	/**
	 * 封装最终的脚本
	 * @param scriptTemplate
	 * @param testCaseMap
	 * @return
	 */
	public static final String[] packagingFinalScript(String scriptTemplate,Map<String,Object> testCaseMap)
	{
		String[] objs = new String[2];
		objs[0] = "true";
		boolean scriptKey = false;
		StringBuffer sb = new StringBuffer();
		Matcher m  = P_PARAM.matcher(scriptTemplate);
		while(m.find())
		{
			String key = m.group(1);
			String value = KEY_MAP.get(key.toLowerCase());
			if(null != value)
			{
				m.appendReplacement(sb, value);
			}else {
				Object valueObj = testCaseMap.get(key);
				if(null == valueObj)
				{
					objs[0] = "false";
					objs[1] = "模板关键字:"+key+"不存在于案例表字段名称里面";
					return objs;
				}
				if("script".equals(key.toLowerCase()))
				{
					scriptKey = true;
				}
				m.appendReplacement(sb, testCaseMap.get(key).toString());
			}
		}
		m.appendTail(sb);
		if(!scriptKey)
		{
			objs[0] = "false";
			objs[1] = "模板缺少主体关键字{script}";
			return objs;
		}
		objs[1] = sb.toString();
		return objs;
	}
	
	/**
	 * 截取==里面的数据
	 * @param line
	 * @return
	 */
	public static List<String> getLineValues(String line)
	{
		List<String> list = new ArrayList<>();
		int index = line.indexOf("==");
		int begin = 0;
		while(index != -1)
		{
			list.add(line.substring(begin, index));
			begin = index + 2;
			index = line.indexOf("==", index);
		}
		return list;
	}
}
