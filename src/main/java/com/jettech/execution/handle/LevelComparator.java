package com.jettech.execution.handle;

import java.util.Comparator;
import java.util.Map;

/**
 * 水平匹配
 * <AUTHOR>
 *
 */
public class LevelComparator implements Comparator<Map<String,Object>> {

	@Override
	public int compare(Map<String, Object> o1, Map<String, Object> o2) {
		double value1 = (Double)o1.get("level");
		double value2 = (Double)o2.get("level");
		double r = (value1 - value2);
		if(r == 0)
			return 0;
		else if(r>0)
			return 1;
		else
			return -1;
	}

}
