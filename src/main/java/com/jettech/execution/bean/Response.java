package com.jettech.execution.bean;

import java.util.HashMap;
import java.util.Map;

public class Response {
	
	private Integer code = 1;
	
	private String message = "操作成功";
	
	private Map<String,Object> data = new HashMap<>();

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
	
	public void setData(Map<String,Object> data)
	{
		this.data = data;
	}
	
	public Map<String,Object> getData()
	{
		return this.data;
	}

	public void addValue(String key ,Object value)
	{
		this.data.put(key, value);
	}
	
	public Response success()
	{
		return this;
	}
	
	public Response failure()
	{
		this.code = 2;
		this.message = "操作失败";
		return this;
	}
	
	public Response failure(String msg)
	{
		this.code = 2;
		this.message = msg;
		return this;
	}
	
	public Response netFailure()
	{
		this.code = 2;
		this.message = "网络出现问题";
		return this;
	}
	
	public Response netFailure(String msg)
	{
		this.code = 2;
		this.message = msg;
		return this;
	}
	
	public Response of(String key,Object value)
	{
		this.addValue(key, value);
		return this;
	}

	@Override
	public String toString() {
		return "Response [code=" + code + ", message=" + message + ", data=" + data + "]";
	}
	
	
}
