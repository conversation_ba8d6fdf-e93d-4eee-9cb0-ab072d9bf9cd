package com.jettech.enums;

/**
 * 
* <AUTHOR>
* @version 20230505
* @Description 模型审核状态
 */
public enum ReviewStatusEnums {
	/**
	 * 状态0-新建,1-审核中,2-通过,3-已拒绝,4-已变更,5-修改,6-撤回
	 */
	ADD(0,"新建"),
	REVIEW(1,"审核中"),
	PASS(2,"通过"),
	REFUSE(3,"已拒绝"),
	CHANGED(4,"已变更"),
	UPDATE(5,"修改"),
	WITHDRAW(6,"撤回");



	private Integer status;

	private String statusDesc;

	public Integer getStatus() {
		return status;
	}

	public String getStatusDesc() {
		return statusDesc;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

	ReviewStatusEnums(Integer status, String statusDesc) {
		this.status = status;
		this.statusDesc = statusDesc;
	}


	public static String getDesc(Integer status) {
		if(status != null){
			for (ReviewStatusEnums enums : ReviewStatusEnums.values()) {
				if (status.equals(enums.getStatus())) {
					return enums.getStatusDesc();
				}
			}
		}
		return "";
	}
}
