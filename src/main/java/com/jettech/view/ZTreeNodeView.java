package com.jettech.view;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ZTreeNodeView {

	public String id;
	public String pId;
	public String name;
	public String type;
	public String iconSkin;
	public String resourceID;
	public Boolean open = true;
	public boolean isParent;
	public Map<String,String> font = new HashMap<String,String>();;
	public List<ZTreeNodeView> children = new ArrayList<ZTreeNodeView>();
	public Map<String,String> attributes = new HashMap<String, String>();
	
}
