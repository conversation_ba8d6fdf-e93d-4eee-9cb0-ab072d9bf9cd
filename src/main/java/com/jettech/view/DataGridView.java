/**
 * 
 */
package com.jettech.view;
 
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class DataGridView extends BaseView{

	/**
	 * 
	 */
	private static final long serialVersionUID = 7397771016942726713L;
	private List<String> columnNames = new LinkedList<String>();//有序列
	private List<List<Object>> columnValues = new LinkedList<List<Object>>();//每列对应的数据
	private boolean pageable = false; // 是否开启分页
	private long totalPage = 0L;  // 总页
	private long totalElement = 0L; // 总条数
	private long size = 0L; // 每页容量
	private long number = 0L; // 当前页码
	private long numberOfElement = 0L; //当前页条数
	/**
	 * @return the columnNames
	 */
	public List<String> getColumnNames() {
		return columnNames;
	}
	/**
	 * @param columnNames the columnNames to set
	 */
	public void setColumnNames(List<String> columnNames) {
		this.columnNames = columnNames;
	}
	/**
	 * @return the columnValues
	 */
	public List<List<Object>> getColumnValues() {
		return columnValues;
	}
	/**
	 * @param columnValues the columnValues to set
	 */
	public void setColumnValues(List<List<Object>> columnValues) {
		this.columnValues = columnValues;
	}
	/**
	 * @return the pageable
	 */
	public boolean isPageable() {
		return pageable;
	}
	/**
	 * @param pageable the pageable to set
	 */
	public void setPageable(boolean pageable) {
		this.pageable = pageable;
	}
	/**
	 * @return the totalPage
	 */
	public long getTotalPage() {
		return totalPage;
	}
	/**
	 * @param totalPage the totalPage to set
	 */
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	/**
	 * @return the totalElement
	 */
	public long getTotalElement() {
		return totalElement;
	}
	/**
	 * @param totalElement the totalElement to set
	 */
	public void setTotalElement(long totalElement) {
		this.totalElement = totalElement;
	}
	/**
	 * @return the size
	 */
	public long getSize() {
		return size;
	}
	/**
	 * @param size the size to set
	 */
	public void setSize(long size) {
		this.size = size;
	}
	/**
	 * @return the number
	 */
	public long getNumber() {
		return number;
	}
	/**
	 * @param number the number to set
	 */
	public void setNumber(long number) {
		this.number = number;
	}
	/**
	 * @return the numberOfElement
	 */
	public long getNumberOfElement() {
		return numberOfElement;
	}
	/**
	 * @param numberOfElement the numberOfElement to set
	 */
	public void setNumberOfElement(long numberOfElement) {
		this.numberOfElement = numberOfElement;
	}
	
}
