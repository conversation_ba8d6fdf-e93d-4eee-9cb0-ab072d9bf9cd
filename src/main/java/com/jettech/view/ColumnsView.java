/**
 * 
 */
package com.jettech.view;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class ColumnsView implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3317364834116871976L;
	private String id;//数据ID
	private boolean isCheck =false; //有一些复选框服务
	private String name;//显示名称
	private String tag;//类型标示--一般是model名称
	private String errorMsg; //错误信息
	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}
	/**
	 * @return the isCheck
	 */
	public boolean isCheck() {
		return isCheck;
	}
	/**
	 * @param isCheck the isCheck to set
	 */
	public void setCheck(boolean isCheck) {
		this.isCheck = isCheck;
	}
	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	/**
	 * @param name the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}
	/**
	 * @return the tag
	 */
	public String getTag() {
		return tag;
	}
	/**
	 * @param tag the tag to set
	 */
	public void setTag(String tag) {
		this.tag = tag;
	}
	/**
	 * @return the errorMsg
	 */
	public String getErrorMsg() {
		return errorMsg;
	}
	/**
	 * @param errorMsg the errorMsg to set
	 */
	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

}
