/**
 * 
 */
package com.jettech.view;

import java.io.Serializable;

import com.jettech.model.BaseModel;
/**
 * <AUTHOR>
 *
 */
public abstract class BaseView implements Serializable,Comparable<BaseView>{

	private static final long serialVersionUID = -7419279165724451941L;
	private int id;
	private String resourceID;
	private String editUser; //来源用户
	/**
	 *无参数构造
	 */
	public BaseView(){
		// do nothing
	}
	
	public BaseView(BaseModel baseModel) {
		this.id = baseModel.getId();
		this.resourceID = String.valueOf(baseModel.getResourceID());
		this.editUser = baseModel.getEditUser();
	}
	
    /**
	 * @return the id
	 */
	public int getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(int id) {
		this.id = id;
	}

	/**
	 * @return the resourceID
	 */
	public String getResourceID() {
		return resourceID;
	}

	/**
	 * @param resourceID the resourceID to set
	 */
	public void setResourceID(String resourceID) {
		this.resourceID = resourceID;
	}

	/**
	 * @return the editUser
	 */
	public String getEditUser() {
		return editUser;
	}

	/**
	 * @param editUser the editUser to set
	 */
	public void setEditUser(String editUser) {
		this.editUser = editUser;
	}


	@Override
	public int hashCode() {
 		return resourceID.hashCode();
	}

	@Override
    public boolean equals(Object object) {
		if (object == null) {
            return false;
        }
        if (this == object &&  this.hashCode() == object.hashCode()) {
            return true;
        } 
        if (!(object instanceof BaseView)) {
            return false;
        }

        final BaseView other = (BaseView) object;
        Serializable szbresourceID = this.resourceID;
        Serializable otherResourceID = other.resourceID;
        boolean isFlag = true;
        if ((szbresourceID == null && otherResourceID != null) || (szbresourceID != null && !szbresourceID.equals(otherResourceID))) {
            isFlag = false;
        }
        if(isFlag){
	        Serializable szbId = this.id;
	        Serializable otherID = other.id;
	        if ((szbId == null && otherID != null) || (szbId != null && !szbId.equals(otherID))) {
	        	isFlag = false;
	        }
        }
        return isFlag;
    }
	
	@Override
	public int compareTo(BaseView another){
		if(another==null)
			return -1;
		if(this.resourceID==null)
			return -1; 
		if(this.resourceID.equals(another.resourceID) && this.resourceID.hashCode() == another.resourceID.hashCode()){
			return 0;
		} 
		return -1;
	}
}
