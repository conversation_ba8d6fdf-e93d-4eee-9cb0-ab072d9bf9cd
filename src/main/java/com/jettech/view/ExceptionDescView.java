package com.jettech.view;

import java.io.Serializable;
import java.util.List;

public class ExceptionDescView implements Serializable,Comparable<ExceptionDescView> {

	private static final long serialVersionUID = -5609271939209049371L;

	public int id;
	
	public String desc;//:错误描述
	
	public List<String> obj;//:错误详细信息

	@Override
	public int compareTo(ExceptionDescView o) {
		if(o.desc==null)
			return -1;
		
		String anotherDesc=o.desc;
		
		return this.desc.compareTo(anotherDesc);
	}
	
	

}
