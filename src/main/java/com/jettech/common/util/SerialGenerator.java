package com.jettech.common.util;

import java.util.Objects;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 序列号生成器
 * 线程安全的序列号生成器，使用Redis作为存储后端
 * <AUTHOR>
 * @Modified 修复线程安全和空指针问题
 */
@Component
public class SerialGenerator {

    private static final Logger logger = LoggerFactory.getLogger(SerialGenerator.class);

    // 常量定义
    public static final Integer DEFAULT_LENGTH = 7;
    public static final Integer INCREMENT_STEP = 1;
    public static final String DEFAULT_REDIS_KEY = "increment";

    // 实例变量，非静态
    private HashOperations<String, String, Long> redisHash;
    private RedisTemplate<String, Object> redisTemplate;

    // 线程安全锁
    private final ReentrantLock lock = new ReentrantLock();

    // 初始化标志
    private volatile boolean initialized = false;

    @Autowired
    public SerialGenerator(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = Objects.requireNonNull(redisTemplate, "RedisTemplate不能为空");
    }

    @PostConstruct
    private void init() {
        try {
            this.redisHash = redisTemplate.opsForHash();
            this.initialized = true;
            logger.info("SerialGenerator初始化完成");
        } catch (Exception e) {
            logger.error("SerialGenerator初始化失败", e);
            throw new RuntimeException("SerialGenerator初始化失败", e);
        }
    }

    /**
     * 生成带前缀的序列号
     * @param hashKey Redis hash中的字段名
     * @param length 序列号长度，null时使用默认长度
     * @param init 初始值提供者
     * @return 格式化的序列号字符串
     */
    public String nextSerialWithPrefix(String hashKey, Integer length, Supplier<Long> init) {
        validateParameters(hashKey, init);
        String[] serials = batchNextSerialWithPrefix(hashKey, INCREMENT_STEP, length, init);
        return serials[0];
    }

    /**
     * 生成下一个序列号
     * @param hashKey Redis hash中的字段名
     * @param init 初始值提供者
     * @return 序列号
     */
    public Long nextSerial(String hashKey, Supplier<Long> init) {
        validateParameters(hashKey, init);
        ensureInitialized();

        lock.lock();
        try {
            return getOrInitializeAndIncrement(hashKey, INCREMENT_STEP, init);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 批量生成序列号的内部方法
     * @param hashKey Redis hash中的字段名
     * @param batchSize 批量大小
     * @param init 初始值提供者
     * @return 最大的序列号值
     */
    private Long nextSerial(String hashKey, int batchSize, Supplier<Long> init) {
        validateParameters(hashKey, init);
        ensureInitialized();

        if (batchSize <= 0) {
            throw new IllegalArgumentException("批量大小必须大于0");
        }

        lock.lock();
        try {
            return getOrInitializeAndIncrement(hashKey, batchSize, init);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 批量生成序列号
     * @param hashKey Redis hash中的字段名
     * @param batchSize 批量大小
     * @param init 初始值提供者
     * @return 序列号数组
     */
    public Long[] batchNextSerial(String hashKey, int batchSize, Supplier<Long> init) {
        validateParameters(hashKey, init);
        if (batchSize <= 0) {
            throw new IllegalArgumentException("批量大小必须大于0");
        }

        long maxValue = nextSerial(hashKey, batchSize, init);
        Long[] serials = new Long[batchSize];

        // 从最大值开始递减生成序列号
        for(int i = batchSize - 1; i >= 0; i--) {
            serials[i] = maxValue;
            maxValue -= INCREMENT_STEP;
        }

        return serials;
    }

    /**
     * 批量生成带前缀的序列号
     * @param hashKey Redis hash中的字段名
     * @param batchSize 批量大小
     * @param length 序列号长度，null时使用默认长度
     * @param init 初始值提供者
     * @return 格式化的序列号字符串数组
     */
    public String[] batchNextSerialWithPrefix(String hashKey, int batchSize, Integer length, Supplier<Long> init) {
        validateParameters(hashKey, init);
        if (batchSize <= 0) {
            throw new IllegalArgumentException("批量大小必须大于0");
        }

        if (length == null) {
            length = DEFAULT_LENGTH;
        }
        if (length <= 0) {
            throw new IllegalArgumentException("序列号长度必须大于0");
        }

        long maxValue = nextSerial(hashKey, batchSize, init);
        String[] serials = new String[batchSize];

        // 从最大值开始递减生成序列号
        for(int i = batchSize - 1; i >= 0; i--) {
            serials[i] = StringUtils.leftPad(Long.toString(maxValue), length, '0');
            maxValue -= INCREMENT_STEP;
        }

        return serials;
    }

    /**
     * 参数验证
     */
    private void validateParameters(String hashKey, Supplier<Long> init) {
        if (StringUtils.isBlank(hashKey)) {
            throw new IllegalArgumentException("hashKey不能为空");
        }
        if (init == null) {
            throw new IllegalArgumentException("初始值提供者不能为空");
        }
    }

    /**
     * 确保组件已初始化
     */
    private void ensureInitialized() {
        if (!initialized) {
            throw new IllegalStateException("SerialGenerator尚未初始化完成");
        }
    }

    /**
     * 获取或初始化并递增序列号的原子操作
     */
    private Long getOrInitializeAndIncrement(String hashKey, int incrementBy, Supplier<Long> init) {
        try {
            // 使用Redis的原子操作避免竞态条件
            if (!redisHash.hasKey(DEFAULT_REDIS_KEY, hashKey)) {
                Long initValue = init.get();
                if (initValue == null) {
                    throw new IllegalArgumentException("初始值不能为空");
                }
                // 使用putIfAbsent确保原子性
                redisHash.putIfAbsent(DEFAULT_REDIS_KEY, hashKey, initValue);
            }

            return redisHash.increment(DEFAULT_REDIS_KEY, hashKey, incrementBy);
        } catch (Exception e) {
            logger.error("Redis操作失败，hashKey: {}, incrementBy: {}", hashKey, incrementBy, e);
            throw new RuntimeException("序列号生成失败", e);
        }
    }
}
