package com.jettech.util;


import com.alibaba.fastjson.JSON;
import com.jettech.common.util.ParamUtil;
import com.jettech.vo.ColumnsView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import java.util.*;







/**
 * @ClassName: PagePermissionsUtil
 * @Description: 页面权限util
 * @author: WangHongFei
 * @date: 2017年8月16日 上午9:55:21
 */
@Component
public class BasicDataFindUtil {
	/**
	 * LOGGER
	 */
	private static final Logger LOGGER = LoggerFactory.getLogger(BasicDataFindUtil.class);
	@Autowired
	private RestTemplate restTemplate;

	/**
	 * @Title getPagePermissions
	 * @Description 取得页面权限
	 * @param user 用户
	 * @param code 编码
	 * @return List<ColumnsView>
	 * <AUTHOR>
	 */
	public Map<String, Boolean> getPagePermissions(String userNumber, String code) {
		Map<String, Boolean> result = new HashMap<String, Boolean>();
		try {
			if ("Admin".equals(userNumber)) {
				result.put(userNumber, false);
			} else {
				LOGGER.info("开始发送到基础平台！");
				String url = ParamUtil.getTrpUrl() + "/menuPrivilege/findAccessControlByUserAndCode/{userNumber}/{code}";
				RestTemplate rs = new RestTemplate();
				ResponseEntity<Map<String, Boolean>> response = rs.exchange(url, HttpMethod.POST, null, new ParameterizedTypeReference<Map<String, Boolean>>() {},userNumber, code);
				result = response.getBody();
				LOGGER.info("发送到基础平台执行结束！");
			}
		} catch (Exception e) {
			LOGGER.error("链接基础平台失败，取得页面权限失败！", e);
		}
		if(result == null) {
			result = new HashMap<String, Boolean>();
		}
		return result;
	}
	
	/**
	 * @Title getDataDictionaryFromBasic
	 * @Description 从基础平台取得数据字典
	 * @return List<ColumnsView>
	 * <AUTHOR>
	 */
	public @ResponseBody List<ColumnsView> getDataDictionaryFromBasic(String dirName) {
		List<ColumnsView> dicList = new ArrayList<ColumnsView>();
		try {
			LOGGER.info("开始连接基础平台！");
			String url = ParamUtil.getBasicUrl() + "/dataDictionary/getDataDictionaryData";
			RestTemplate rs = new RestTemplate();
			ColumnsView param = new ColumnsView();
			param.setName(dirName);
			HttpEntity<ColumnsView> requestEntity = new HttpEntity<ColumnsView>(param);
			ResponseEntity<List<ColumnsView>> response = rs.exchange(url, HttpMethod.POST, requestEntity, new ParameterizedTypeReference<List<ColumnsView>>() {});
			dicList = response.getBody();
			LOGGER.info("连接基础平台执行结束！");
		} catch (Exception e) {
			LOGGER.error("链接基础平台失败，取得页面权限失败！", e);
		}
		if(dicList == null) {
			dicList = new ArrayList<ColumnsView>();
		}
		return dicList;
	}
	
	/**
	 * cao_jinbao
	 * 获取当前位置
	 * 2019-06-10
	 */
	public Map<String,String> getPageLocation(String code) {
		Map<String,String> result = new HashMap<>();
		try {
			LOGGER.info("开始发送到基础平台！");
			HttpHeaders headers = new HttpHeaders();
			headers.set("Accept-Charset", "utf-8");  
			headers.set("Content-type", "application/json; charset=utf-8");
			headers.set("Accept","application/json"); 
			String url = ParamUtil.getTrpUrl() + "/menuStructure/getPageLocation/"+code;
			MultiValueMap<String, Object> reqMap = new LinkedMultiValueMap<String, Object>(); 
			ResponseEntity<Map<String,String>> response = restTemplate.exchange(
					url, HttpMethod.POST, new HttpEntity<>(JSON.toJSONString(reqMap),headers),
					new ParameterizedTypeReference<Map<String,String>>() {
			});
			
			
			 result = response.getBody();
			LOGGER.info("发送到基础平台执行结束！");
		} catch (Exception e) {
			LOGGER.error("链接基础平台失败，取得页面当前位置失败！", e);
		}
		if(result == null) {
			return null;
		}
		return result;
	}
	/**
	 * @Title: getRuleInfo
	 * @Description: 查询规则状态和类型
	 * @param name
	 * @return
	 * <AUTHOR>  
	 * @date 2019年3月25日 下午5:58:05
	 */
	public List<String> getRuleInfo(String name){
		
		List<String> result = new LinkedList<String>();
		try {
			LOGGER.info("开始连接到基础平台！");
			String url = ParamUtil.getBasicUrl() + "/dataDictionary/findDataByName/{name}";
			RestTemplate rs = new RestTemplate();
			ResponseEntity<List<String>> response = rs.exchange(url, HttpMethod.POST, null, new ParameterizedTypeReference<List<String>>() {}, name);
			result = response.getBody();
			LOGGER.info("发送到基础平台执行结束！");
		} catch (Exception e) {
			LOGGER.error("链接基础平台失败，取得规则信息失败！", e);
		}
		return result;
		
	}
	
}
