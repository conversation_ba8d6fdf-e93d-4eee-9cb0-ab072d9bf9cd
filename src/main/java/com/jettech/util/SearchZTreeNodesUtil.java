package com.jettech.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.jettech.view.ZTreeNodeView;
/**
 * Title: SearchZTreeNodes
 * Description:查询ZTree节点
 * <AUTHOR> 
 * @date 2019年5月9日 上午10:14:15
 */
public class SearchZTreeNodesUtil {

	/**
	 * @Description:根据传入名称查询节点 
	 * @param text
	 * @param dataList
	 * @return
	 * @author: cuisijie
	 * @date: 2019年5月9日 上午10:16:45
	 */
	public static List<ZTreeNodeView> search(String text,List<ZTreeNodeView> dataList){
		
 		if(text == null || "".equals(text)){
 			return dataList;
 		}
 		
 		Map<String,List<ZTreeNodeView>> pId_viewMap = new HashMap<>();
 		Map<String,ZTreeNodeView> rId_viewMap = new HashMap<>();
 		
 		List<ZTreeNodeView> searchNodeList = new ArrayList<>();
 		
 		Set<String> pIdSet = new HashSet<>();

 		for (ZTreeNodeView view : dataList) {
 			
 			if(pId_viewMap.keySet().contains(view.pId)){
 				List<ZTreeNodeView> viewList = pId_viewMap.get(view.pId);
 				viewList.add(view);
 			}else{
 				List<ZTreeNodeView> viewList = new ArrayList<>();
 				viewList.add(view);
 				pId_viewMap.put(view.pId, viewList);
 			}
 			
 			rId_viewMap.put(view.resourceID, view);
 			
 			//匹配名称的节点
 			if(view.name.indexOf(text) != -1){
 				searchNodeList.add(view);
 				pIdSet.add(view.pId);
 			}
 			
		}
		
 		Set<String> rIdSet = new HashSet<>();
 		
 		for (ZTreeNodeView view : searchNodeList) {
			findChildNodes(view,pId_viewMap,rIdSet);
		}
 		
 		for(String pId : pIdSet){
 			findParentNodes(pId,pIdSet,rId_viewMap);
 		}
 		
 		Set<String> idSet = new HashSet<>();
 		for (String id : rIdSet) {
 			idSet.add(id);
		}
 		for (String id : pIdSet) {
 			idSet.add(id);
 		}
 		
 		List<ZTreeNodeView> viewList = new ArrayList<>();
 		
 		for (String id : idSet) {
 			if(rId_viewMap.get(id) != null){
 				viewList.add(rId_viewMap.get(id));
 			}
 		}
 		
		return viewList;
	}	
	/**
	 * @Description:所有父节点 
	 * @param pId
	 * @param pIdSet
	 * @param rId_viewMap
	 * @author: cuisijie
	 * @date: 2019年5月9日 上午10:17:35
	 */
	public static void findParentNodes(String pId, Set<String> pIdSet, Map<String, ZTreeNodeView> rId_viewMap) {
		
		pIdSet.add(pId);
		
		for(String id : rId_viewMap.keySet()){
			if(id.equals(pId)){
				findParentNodes(rId_viewMap.get(id).pId, pIdSet, rId_viewMap);
			}
		}
		
		
	}
	/**
	 * @Description:所有子节点 
	 * @param view
	 * @param pId_viewMap
	 * @param rIdSet
	 * @author: cuisijie
	 * @date: 2019年5月9日 上午10:17:48
	 */
	public static void findChildNodes(ZTreeNodeView view, Map<String,List<ZTreeNodeView>> pId_viewMap, Set<String> rIdSet) {
		
		rIdSet.add(view.resourceID);
		
		for(String pid : pId_viewMap.keySet()){
			if(pid.equals(view.resourceID)){
				List<ZTreeNodeView> viewList = pId_viewMap.get(pid);
				for (ZTreeNodeView zTreeNodeView : viewList) {
					findChildNodes(zTreeNodeView, pId_viewMap, rIdSet);
				}
			}
		}
	}
}
