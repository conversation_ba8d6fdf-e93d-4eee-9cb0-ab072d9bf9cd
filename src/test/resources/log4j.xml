<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration PUBLIC "-//APACHE//DTD LOG4J 1.2//EN" "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

	<!-- Appenders -->
	<appender name="console" class="org.apache.log4j.ConsoleAppender">
		<param name="Target" value="System.out" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%-5p: %c - %m%n" />
		</layout>
	</appender>
	<appender name="file" class="org.apache.log4j.RollingFileAppender">  
      <param name="File" value="../logs/common/common.log"/>
      <param   name= "MaxFileSize"   value= "30MB"></param>
      <param name="MaxBackupIndex" value="50" />  
      <layout class="org.apache.log4j.PatternLayout">  
          <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n"/>  
      </layout>  
   </appender>  
   
    <appender name="deleteInfo" class="org.apache.log4j.RollingFileAppender">
    	<param name="Append" value="true"/>
    	<param name="File" value="../logs/common/common_delete.log"/>
    	<param name="MaxFileSize" value="30MB" />
    	<param name="MaxBackupIndex" value="50" />  
	      <layout class="org.apache.log4j.PatternLayout">  
	          <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n"/>  
	      </layout>
    </appender> 
	<!-- Application Loggers -->
	
	<logger name="com.jettech.srcbtestplatform">
		<level value="info" />
	</logger>
	
	
	<!-- <logger name="deleteInfo">
		<level value="info" />
	</logger> -->
	
	<!-- 3rdparty Loggers -->
	<logger name="org.springframework.core">
		<level value="info" />
	</logger>
	
	<logger name="org.springframework.beans">
		<level value="info" />
	</logger>
	
	<logger name="org.springframework.context">
		<level value="info" />
	</logger>

	<logger name="org.springframework.web">
		<level value="info" />
	</logger>

	<!-- 取消继承 -->
	<logger name="deleteLog" additivity="false">
		<level value="info" />
		<appender-ref ref="deleteInfo"/>
	</logger>
	<!-- Root Logger -->
	<root>
		<priority value="warn" />
		<appender-ref ref="file" />
		<appender-ref ref="console" />
	</root>
	
</log4j:configuration>
