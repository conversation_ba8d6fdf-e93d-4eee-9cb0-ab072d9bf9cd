//package com.jettech.service.impl;
//
//import AbstractJunit;
//import com.jettech.dto.Result;
//import com.jettech.model.TestCase;
//import com.jettech.model.Trade;
//import com.jettech.service.iservice.ITestCaseService;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.stream.Collectors;
//
//public class TestCaseServiceImplTest extends AbstractJunit {
//
//    //时间戳
//    private static final long Time = System.currentTimeMillis();
//
//    @Autowired
//    private ITestCaseService testCaseService;
//    @Autowired
//    private ITradeService tradeService;
//
//    private Trade trade = new Trade();
//
//    @Override
//    protected void init() {
//        //Trade trademodel = new Trade();
//        trade.setTestSystemResourceID(12300000000010101L);
//        trade.setModuleResourceID(12300000000010102L);
//        trade.setComment("测试数据");
//        trade.setName("测试交易");
//        trade.setNumber("001");
//        trade.setStatus("1");
//        trade = tradeService.save(trade, "1230000000001011L");
//        TestCase testCase1 = new TestCase();
//        TestCase testCase2 = new TestCase();
//        TestCase testCase3 = new TestCase();
//        testCase1.setCaseId("001");
//        testCase1.setCaseType("0");
//        testCase1.setIsNegative("0");
//        testCase1.setTradeResourceID(trade.getResourceID());
//        testCase1.setMaintainer("test0001");
//
//        testCase2.setCaseId("002");
//        testCase2.setCaseType("1");
//        testCase2.setIsNegative("1");
//        testCase2.setTradeResourceID(trade.getResourceID());
//        testCase2.setMaintainer("test0001");
//
//        testCase3.setCaseId("003");
//        testCase3.setCaseType("0");
//        testCase3.setIsNegative("1");
//        testCase3.setTradeResourceID(trade.getResourceID());
//        testCase3.setMaintainer("test0001");
//
//        testCaseService.save(testCase1, "1230000010101L");
//        testCaseService.save(testCase2, "1230000010101L");
//        testCaseService.save(testCase3, "1230000010101L");
//    }
//
//    /**
//     * @Title: addTestCase
//     * @Description: 新建案例单元测试
//     * @Param: "[]"
//     * @Return: "void"
//     * @Author: xpp
//     * @Date: 2019/11/14
//     */
//    @Test
//    public void addTestCase() {
//        TestCase testCase = testCase();
//        Result test = testCaseService.addTestCase(testCase, "test");
//        System.out.println(test.getCode() + "----" + test.getMsg());
//    }
//
//    /**
//     * @Title: updateTestCase
//     * @Description: 修改案例单元测试
//     * @Param: "[]"
//     * @Return: "void"
//     * @Author: xpp
//     * @Date: 2019/11/14
//     */
//    @Test
//    public void updateTestCase() {
//        TestCase testCase = testCase();
//        Result test = testCaseService.addTestCase(testCase, "test");
//        System.out.println(test.getCode() + "----" + test.getMsg());
//        if (test.getCode() == 20000) {
//            TestCase obj = (TestCase) test.getObj();
//            obj.setCaseId("001-test-修改");
//            Result result = testCaseService.updateTestCase(obj, "test");
//            System.out.println(result.getCode() + "---" + result.getMsg());
//            if (result.getCode() == 20000) {
//                System.out.println("修改成功：");
//            }
//        }
//    }
//
//    /**
//     * @Title: deleteTestCase
//     * @Description: 删除案例单元测试
//     * @Param: "[]"
//     * @Return: "void"
//     * @Author: xpp
//     * @Date: 2019/11/14
//     */
//    @Test
//    public void deleteTestCase() {
//        init();
//        List<TestCase> all = testCaseService.findAll();
//        List<String> collect = all.stream().map(s -> String.valueOf(s.getResourceID())).collect(Collectors.toList());
//        testCaseService.deleteTestCase(collect, "test");
//        List<TestCase> all1 = testCaseService.findAll();
//        if (all1.size() == 0) {
//            System.out.println("删除成功");
//        }
//    }
//
//    /**
//     * @Title: findByTestCase
//     * @Description: 查看单个案例详情
//     * @Param: "[]"
//     * @Return: "void"
//     * @Author: xpp
//     * @Date: 2019/11/14
//     */
//    @Test
//    public void findByTestCase() {
//        TestCase testCase = testCase();
//        Result byTestCase = testCaseService.findByTestCase(String.valueOf(testCase.getResourceID()));
//        if (byTestCase.getCode() == 20000) {
//            TestCase testCase1 = (TestCase) byTestCase.getObj();
//            if (testCase1.getCaseId().equals(testCase.getCaseId())) {
//                System.out.println("查询成功");
//            }
//        }
//    }
//
//    /**
//     * @Title: testCase
//     * @Description: 新建、修改、删除基础数据提供
//     * @Param: "[]"
//     * @Return: "com.jettech.model.TestCase"
//     * @Author: xpp
//     * @Date: 2019/11/14
//     */
//    private TestCase testCase() {
//        trade.setTestSystemResourceID(12300000000010101L);
//        trade.setModuleResourceID(12300000000010102L);
//        trade.setComment("测试数据");
//        trade.setName("测试交易");
//        trade.setNumber("001");
//        trade.setStatus("1");
//        trade = tradeService.save(trade, "1230000000001011L");
//
//        TestCase testCase1 = new TestCase();
//        testCase1.setCaseId("001-test");
//        testCase1.setCaseType("0");
//        testCase1.setIsNegative("0");
//        testCase1.setTradeResourceID(trade.getResourceID());
//        testCase1.setMaintainer("test0001");
//        testCase1.setIntent("测试意图");
//        testCase1.setCasetLevel("高");
//        testCase1.setTestStep("测试步骤");
//        testCase1.setExpectedResult("预期结果");
//        return testCase1;
//    }
//
//    @Test
//    public void findbyTradeResourceID() {
//    }
//
//    @Test
//    public void findbyTradeResourceIDList() {
//    }
//
//    @Test
//    public void findByTradeAndOptions() {
//        HashMap<String, Object> map = new HashMap<>();
//        map.put("tradeResourceID", trade.getResourceID());
//        map.put("maintainer", "test0001");
//        Result result1 = testCaseService.findByTradeAndOptions(map);
//        System.out.println(result1.getObj());
//        map.put("isNegative", "1");
//        Result result2 = testCaseService.findByTradeAndOptions(map);
//        System.out.println(result2.getObj());
//        map.put("caseType", "0");
//        Result result3 = testCaseService.findByTradeAndOptions(map);
//        System.out.println(result3.getObj());
//    }
//
//
//    @Test
//    public void findByCaseIds() {
//    }
//
//
//}