package com.jettech.service.impl;

import com.jettech.base.AbstractJunit;
import com.jettech.common.dto.Result;
import com.jettech.model.SystemModule;
import com.jettech.model.TestCase;
import com.jettech.model.TestSystem;
import com.jettech.model.Trade;
import com.jettech.service.iservice.ISystemModuleService;
import com.jettech.service.iservice.ITestCaseService;
import com.jettech.service.iservice.ITestSystemService;
import com.jettech.service.iservice.ITradeService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class TradeServiceImplTest extends AbstractJunit {
    @Autowired
    private ITradeService tradeService;

    @Autowired
    private ISystemModuleService moduleService;
    @Autowired
    private ITestSystemService systemService;

    @Autowired
    private ITestCaseService testCaseService;

    String userNumber = "liyajiao";
    @Test
    public void saveOrUpdateTrade() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试模块",testSystem.getResourceID(),testSystem.getResourceID());
        HashMap<String, String> tradeMap = new HashMap<>();
        tradeMap.put("name","测试新增交易");
        tradeMap.put("comment","测试新增交易");
        tradeMap.put("number","987456");
        tradeMap.put("status","0");
        tradeMap.put("resourceID","");
        tradeMap.put("moduleResourceID",String.valueOf(systemModule.getResourceID()));
        tradeMap.put("testSystemResourceID",String.valueOf(testSystem.getResourceID()));
        Result result = tradeService.saveOrUpdateTrade(tradeMap, userNumber);
        Assert.assertEquals(20000,result.getCode());

    }

    @Test
    public void verifyTradeNameNotRepeatedByModule() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试删除模块",testSystem.getResourceID(),testSystem.getResourceID());
        Trade trade = createTrade("测试交易重名", systemModule.getResourceID(), testSystem.getResourceID());

        List<Trade> l = tradeService.verifyTradeNameNotRepeatedByModule("测试交易重名", "", String.valueOf(systemModule.getResourceID()));
        Assert.assertEquals(1,l.size());
    }

    @Test
    public void verifyTradeNameNotRepeatedBySystem() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        Trade trade = createTrade("测试交易重名", testSystem.getResourceID(), testSystem.getResourceID());
        List<Trade> l = tradeService.verifyTradeNameNotRepeatedBySystem("测试交易重名123", "", String.valueOf(testSystem.getResourceID()));
        Assert.assertEquals(0,l.size());
    }

    @Test
    public void findBySystemModule() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试模块",testSystem.getResourceID(),testSystem.getResourceID());
        Trade trade = createTrade("测试查询模块下交易", systemModule.getResourceID(), testSystem.getResourceID());
        List<String> list  = new ArrayList<String>();
        list.add(String.valueOf(systemModule.getResourceID()));
        List<Trade> bySystemModule = tradeService.findBySystemModule(list);
        Assert.assertEquals(1,bySystemModule.size());
    }

    @Test
    public void deleteTradeByResourceID() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试模块",testSystem.getResourceID(),testSystem.getResourceID());
        Trade trade = createTrade("测试删除交易", systemModule.getResourceID(), testSystem.getResourceID());
        Result result = tradeService.deleteTradeByResourceID(String.valueOf(trade.getResourceID()), userNumber);
        Assert.assertEquals(20000,result.getCode());
    }

    @Test
    public void findResourceByTrade() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试模块",testSystem.getResourceID(),testSystem.getResourceID());
        Trade trade = createTrade("测试删除交易", systemModule.getResourceID(), testSystem.getResourceID());


        TestCase testCase = new TestCase();
        testCase.setCaseId("001");
        testCase.setCaseType("0");
        testCase.setIsNegative("0");
        testCase.setTradeResourceID(trade.getResourceID());
        testCase.setMaintainer("test0001");
        testCaseService.save(testCase, userNumber);

        Result result = tradeService.findResourceByTrade(String.valueOf(trade.getResourceID()));
        Assert.assertEquals("当前交易下已维护案例数据，请确认删除",result.getMsg());
    }


    private Trade createTrade(String name, Long moduleResourceID, Long testSystemResourceID) {
        Trade trade = new Trade();
        trade.setName(name);
        trade.setModuleResourceID(moduleResourceID);
        trade.setTestSystemResourceID(testSystemResourceID);
        //交易
        tradeService.save(trade,userNumber);
        return trade;
    }

    private SystemModule createSystemModule(String name, Long testSystemResourceID, Long parentResourceID) {
        SystemModule module = new SystemModule();
        module.setName(name);
        module.setTestSystemResourceID(testSystemResourceID);
        module.setParentResourceID(parentResourceID);
        //模块
        module = moduleService.save(module, userNumber);
        return module;
    }

    private TestSystem createTestSystem(String name, String describes, String number) {
        TestSystem system = new TestSystem();
        system.setDescribes(describes);
        system.setName(name);
        system.setNumber(number);
        //被测系统
        system = systemService.save(system, userNumber);
        return system;
    }

    @Override
    protected void init() {

    }
}