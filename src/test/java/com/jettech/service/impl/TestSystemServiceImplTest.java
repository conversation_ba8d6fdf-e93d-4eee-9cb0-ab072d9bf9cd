package com.jettech.service.impl;

import com.jettech.base.AbstractJunit;
import com.jettech.common.dto.Result;
import com.jettech.model.SystemModule;
import com.jettech.model.TestSystem;
import com.jettech.model.Trade;
import com.jettech.service.iservice.ISystemModuleService;
import com.jettech.service.iservice.ITestSystemService;
import com.jettech.service.iservice.ITradeService;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class TestSystemServiceImplTest extends AbstractJunit {
    @Autowired
    private ITestSystemService testSystemService;
    @Autowired
    private ISystemModuleService systemModuleService;
    @Autowired
    private ITradeService tradeService;
    //时间戳
    private static final long Time = System.currentTimeMillis();


    @Before
    public void before(){
        super.transactionBegin();
    }
    @After
    public void after(){
        super.transactionRollback();
    }

    String userNumber = "liyajiao";
    Map<String,String> testSystemMap  = new HashMap<String,String>();
    Map<String,String> moduleMap  = new HashMap<String,String>();

    @Override	
    protected void init() {
        TestSystem testSystem = new TestSystem();
        testSystem.setDescribes("测试数据");
        testSystem.setManager("1230000000101");
        testSystem.setName("测试系统");
        testSystem.setNumber("001");
        TestSystem system = testSystemService.save(testSystem, "1230000000001011L");
        SystemModule systemModule = new SystemModule();
        systemModule.setTestSystemResourceID(system.getResourceID());
        systemModule.setParentResourceID(system.getResourceID());
        systemModule.setName("测试模块");
        SystemModule module = systemModuleService.save(systemModule, "1230000000001011L");
        Trade trade = new Trade();
        trade.setTestSystemResourceID(system.getResourceID());
        trade.setModuleResourceID(module.getResourceID());
        trade.setComment("测试数据");
        trade.setName("测试交易");
        trade.setNumber("001");
        trade.setStatus("1");
        tradeService.save(trade,"1230000000001011L");
    }


    private void initAddData(){
        testSystemMap.put("name","双十一优惠");
        testSystemMap.put("testProjectResourceID","1");
        testSystemMap.put("number","1111");
        testSystemMap.put("describes","双十一红包雨");
        testSystemMap.put("resourceID","");
        testSystemMap.put("manager","liyajiao");
    }

    @Test
    public void findSinglePointLeftTreeByProjectResourceID() {
        Result<?> data = testSystemService.findSinglePointLeftTreeByProjectResourceID("12300000000102");
        System.out.println(data.getObj());
    }

    @Test
    public void saveorUpdateTestSystem() {
//        initAddData();
//        Result result = testSystemService.saveorUpdateTestSystem(testSystemMap, userNumber);
//        Assert.assertEquals(20000,result.getCode());
    }

    @Test
    public void deleteTestSystemByResourceID() {
//        initAddData();
//        Result result = testSystemService.saveorUpdateTestSystem(testSystemMap, userNumber);
//        TestSystem obj = (TestSystem)result.getObj();
//        Result result1 = testSystemService.deleteTestSystemByResourceID(String.valueOf(obj.getResourceID()), userNumber);

    }

    @Test
    public void findResourceBySystemResourceID() {
        TestSystem testSystem = createTestSystem("被测系统","被测系统描述","system1");
        SystemModule systemModule = createSystemModule("模块",testSystem.getResourceID(),testSystem.getResourceID());

        Result message = testSystemService.findResourceBySystemResourceID(String.valueOf(testSystem.getResourceID()));
        Assert.assertEquals("当前被测系统下已维护数据，请确认删除",message.getMsg());

    }

    @Test
    public void updateNodePosition() {
        //被测系统
        TestSystem system = createTestSystem("被测系统","被测系统描述","system1");
        TestSystem system2 = createTestSystem("被测系统2","被测系统描述","system1");
        //模块
        SystemModule module = createSystemModule("模块",system.getResourceID(),system.getResourceID());
        SystemModule module2 = createSystemModule("模块2",system2.getResourceID(),system2.getResourceID());
        //交易1
        Trade trade1 = createTrade("交易1",module.getResourceID(),system.getResourceID());
        Trade trade2 = createTrade("交易2",module2.getResourceID(),system2.getResourceID());


        HashMap<String, Object> map = new HashMap<>();
        map.put("userNumber","wws");
        map.put("type","module");
        map.put("parentType","system");
        map.put("resourceID",module.getResourceID());
        map.put("newParentResourceID",system2.getResourceID());

        Result result = testSystemService.updateNodePosition(map);
        Assert.assertEquals(20000,result.getCode());
    }

    @Test
    public void findSystemManagement() {
    }

    private Trade createTrade(String name,Long moduleResourceID,Long testSystemResourceID) {
        Trade trade = new Trade();
        trade.setName(name);
        trade.setModuleResourceID(moduleResourceID);
        trade.setTestSystemResourceID(testSystemResourceID);
        //交易
        tradeService.save(trade,"wws");
        return trade;
    }

    private SystemModule createSystemModule(String name,Long testSystemResourceID,Long parentResourceID) {
        SystemModule module = new SystemModule();
        module.setName(name);
        module.setTestSystemResourceID(testSystemResourceID);
        module.setParentResourceID(parentResourceID);
        //模块
        module = systemModuleService.save(module, "wws");
        return module;
    }

    private TestSystem createTestSystem(String name,String describes,String number) {
        TestSystem system = new TestSystem();
        system.setDescribes(describes);
        system.setName(name);
        system.setNumber(number);
        //被测系统
        system = testSystemService.save(system, "wws");
        return system;
    }
}