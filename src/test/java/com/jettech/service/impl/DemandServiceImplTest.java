package com.jettech.service.impl;

import com.jettech.base.AbstractJunit;
import com.jettech.common.dto.Result;
import com.jettech.service.iservice.IDemandService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * Created by huaguob<PERSON> on 2020/2/11
 *
 * @Description:
 * <AUTHOR>
 */
public class DemandServiceImplTest extends AbstractJunit {


    @Autowired
    private IDemandService iDemandService;


    @Before
    public void before(){
        super.transactionBegin();
    }
    @After
    public void after(){
        super.transactionRollback();
    }

    @Override
    protected void init() {

    }

    @Test
    public void testVenture() {
        Result result = iDemandService.testVenture("10354413884163072");
        Map obj = (Map) result.getObj();
        List sitList = (List) obj.get("sitList");
        List uatList = (List) obj.get("uatList");
        for (Object o : sitList) {
            System.out.println(o);
        }
        System.out.println("********************************");
        for (Object o : uatList) {
            System.out.println(o);
        }

    }
    @Test
    public void test1(){
        String ids = "10263760491267072,10263760491267073,10263760491267074,10263760491267075,10263760491267076";
        List<Map<String, String>> demandIDs = iDemandService.findDemandIDs(ids);
        for (Map<String, String> demandID : demandIDs) {
            System.out.println(demandID);
        }
    }



}