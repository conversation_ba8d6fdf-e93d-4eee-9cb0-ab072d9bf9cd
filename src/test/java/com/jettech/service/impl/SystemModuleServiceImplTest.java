package com.jettech.service.impl;

import com.jettech.base.AbstractJunit;
import com.jettech.common.dto.Result;
import com.jettech.model.SystemModule;
import com.jettech.model.TestSystem;
import com.jettech.model.Trade;
import com.jettech.service.iservice.ISystemModuleService;
import com.jettech.service.iservice.ITestSystemService;
import com.jettech.service.iservice.ITradeService;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;

public class SystemModuleServiceImplTest extends AbstractJunit{
    @Autowired
    private ITradeService tradeService;

    @Autowired
    private ISystemModuleService moduleService;
    @Autowired
    private ITestSystemService systemService;

    String userNumber = "liyajiao";
    HashMap<String, String> testSystemMap = new HashMap<>();
    HashMap<String, String> moduleMap = new HashMap<>();

    private void initAdd(){
    }

    @Test
    public void saveorUpdateSystemModule() {
        // 保存系统
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");

        // 新增模块
        moduleMap.put("name","测试模块新增");
        moduleMap.put("resourceID","");
        moduleMap.put("parentResourceID",String.valueOf(testSystem.getResourceID()));
        moduleMap.put("testSystemResourceID",String.valueOf(testSystem.getResourceID()));
        moduleMap.put("type","system");
        Result result1 = moduleService.saveorUpdateSystemModule(moduleMap, userNumber);
        Assert.assertEquals(20000,result1.getCode());
    }

    @Test
    public void verifyModuleNameNotRepeatedOfSystem() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        // 新增模块
        moduleMap.put("name","测试系统下模块不重名");
        moduleMap.put("resourceID","");
        moduleMap.put("parentResourceID",String.valueOf(testSystem.getResourceID()));
        moduleMap.put("testSystemResourceID",String.valueOf(testSystem.getResourceID()));
        Result result1 = moduleService.saveorUpdateSystemModule(moduleMap, userNumber);
//        moduleMap.put("name","测试模块1");
//        String systemModules = moduleService.verifyModuleNameNotRepeated(moduleMap);
//        Assert.assertEquals(0,systemModules.size());
    }

    @Test
    public void verifyModuleNameNotRepeatedOfModule() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        // 新增模块
        moduleMap.put("name","测试系统下模块不重名");
        moduleMap.put("resourceID","");
        moduleMap.put("parentResourceID",String.valueOf(testSystem.getResourceID()));
        moduleMap.put("testSystemResourceID",String.valueOf(testSystem.getResourceID()));
        Result result1 = moduleService.saveorUpdateSystemModule(moduleMap, userNumber);
        SystemModule systemModule =(SystemModule)result1.getObj();
       // Assert.assertEquals(0,systemModules.size());
    }

    @Test
    public void findbyTestSystemResourceID() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试所有模块",testSystem.getResourceID(),testSystem.getResourceID());

        List<SystemModule> systemModules = moduleService.findbyTestSystemResourceID(String.valueOf(testSystem.getResourceID()));
    }

    @Test
    public void deleteSystemModuleByResourceID() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试删除模块",testSystem.getResourceID(),testSystem.getResourceID());
        Trade trade = createTrade("删除模块下交易资源", systemModule.getResourceID(), testSystem.getResourceID());

        Result result = moduleService.deleteSystemModuleByResourceID(String.valueOf(systemModule.getResourceID()), userNumber);
        Assert.assertEquals(20000,result.getCode());
    }

    @Test
    public void checkSonModuleORTradeForAdd() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试删除模块",testSystem.getResourceID(),testSystem.getResourceID());
        Trade trade = createTrade("模块下已维护交易资源新建模块", systemModule.getResourceID(), testSystem.getResourceID());

        Result result = moduleService.checkSonModuleORTradeForAdd("systemModule", String.valueOf(systemModule.getResourceID()));
        Assert.assertEquals("当前模块下已维护交易，不能再建子模块！",result.getMsg());
    }

    @Test
    public void findResourceBySystemModule() {
        TestSystem testSystem = createTestSystem("被测系统测试","被测系统测试描述","123456");
        SystemModule systemModule = createSystemModule("测试删除模块",testSystem.getResourceID(),testSystem.getResourceID());
        Trade trade = createTrade("模块下已维护交易资源新建模块", systemModule.getResourceID(), testSystem.getResourceID());
        Result result = moduleService.findResourceBySystemModule(String.valueOf(systemModule.getResourceID()));
        Assert.assertEquals("当前模块下已维护数据，请确认删除",result.getMsg());
    }

    @Override
    protected void init() {
    }

    private Trade createTrade(String name, Long moduleResourceID, Long testSystemResourceID) {
        Trade trade = new Trade();
        trade.setName(name);
        trade.setModuleResourceID(moduleResourceID);
        trade.setTestSystemResourceID(testSystemResourceID);
        //交易
        tradeService.save(trade,userNumber);
        return trade;
    }

    private SystemModule createSystemModule(String name,Long testSystemResourceID,Long parentResourceID) {
        SystemModule module = new SystemModule();
        module.setName(name);
        module.setTestSystemResourceID(testSystemResourceID);
        module.setParentResourceID(parentResourceID);
        //模块
        module = moduleService.save(module, userNumber);
        return module;
    }

    private TestSystem createTestSystem(String name,String describes,String number) {
        TestSystem system = new TestSystem();
        system.setDescribes(describes);
        system.setName(name);
        system.setNumber(number);
        //被测系统
        system = systemService.save(system, userNumber);
        return system;
    }
}