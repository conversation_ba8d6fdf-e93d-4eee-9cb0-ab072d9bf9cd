package com.jettech.base;


import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.application.TestApplication;
 
@RunWith(SpringRunner.class)
@SpringBootTest(classes=TestApplication.class)// 指定spring-boot的启动类 
@ContextConfiguration // 不加此注解，bean会注入不进去  
@EnableTransactionManagement // 启注解事务管理，等同于xml配置方式的 <tx:annotation-driven />
@PowerMockIgnore({"javax.management.*"}) //忽略一些mock异常 
public class BaseJunit implements ApplicationContextAware{
	@Autowired
	private PlatformTransactionManager transactionManager;
	private TransactionStatus transaction;
	protected void transactionBegin(){
		transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
	}
	protected void transactionRollback(){
		org.springframework.util.Assert.notNull(transaction, "请在@Begin中调用transactionBegin方法");
		transactionManager.rollback(transaction);
	}
	protected ApplicationContext applicationContext ;
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}
	/**
	 * @return the applicationContext
	 */
	public ApplicationContext getApplicationContext() {
		return applicationContext;
	} 
	
}
