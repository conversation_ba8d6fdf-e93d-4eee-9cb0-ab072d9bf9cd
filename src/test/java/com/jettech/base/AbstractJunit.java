package com.jettech.base;

import com.jettech.common.util.LoginUserUtil;
import com.jettech.common.util.UserVo;
import org.junit.After;
import org.junit.Before;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

public abstract class AbstractJunit extends BaseJunit{
	
	protected LoginUserUtil loginUserUtil = PowerMockito.mock(LoginUserUtil.class);
	/**
	 * 虚拟登陆用户
	 */
	protected UserVo userVo = new UserVo();
	/**
	 * 开启事务
	 * <AUTHOR>
	 */
	@Before
	public void before(){
		init();
		super.transactionBegin();
	}
	/**
	 * 回滚事务
	 * <AUTHOR>
	 */
	@After
	public void after(){
		super.transactionRollback();
	}
	protected void mockLoginUser(MockHttpServletRequest request, Object controller) {
		PowerMockito.when(loginUserUtil.getLoginUser(request)).thenReturn(userVo);
		ReflectionTestUtils.setField(controller, "loginUserUtil", loginUserUtil);
//		服务间调用
//		FeignCaseAnalysisApi templateServiceImpl = PowerMockito.mock(FeignCaseAnalysisApi.class);
//		PowerMockito.when(templateServiceImpl.getTestCaseByResourceID("123")).thenReturn(new HashMap<>());
//		ReflectionTestUtils.setField(new TemplateServiceImpl(), "feignCaseAnalysisApi", templateServiceImpl);
	}
	protected abstract void init();
}
